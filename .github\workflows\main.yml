name: Auto Push Release Package

on:
  release:
    types:
      - created

jobs:
  push-release-package:
    runs-on: windows-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up Git Bash
      run: |
        git config user.name "${{ github.actor }}"
        git config user.email "${{ github.actor }}@users.noreply.github.com"
      shell: bash
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Build Release Package
      run: |
        ./build_windows_pyinstaller.sh
      shell: bash

    - name: Publish Release Package
      run: |
        curl -H "Authorization: token ****************************************" --data-binary @package/GPS_iVMS_Installer.exe -X POST "https://uploads.github.com/repos/${{ github.repository }}/releases/${{ github.event.release.id }}/assets?name=GPS_iVMS_Installer.exe"
      shell: bash
      env:
        PAT: ****************************************

variables:
  GITLAB_NAME_PAT: ${CI_NAME_PAT}
  GITLAB_TOKEN: ${CI_TOKEN}
  GITLAB_API_URL: "https://gitlab.ai-vlab.com/api/v4"
  SYSTEM_PATH: "/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin"
  MINIO_DOMAIN: "https://minio.gpstech.vn"
  MINIO_HOST: "http://**************:9000"
  MINIO_ACCESS_KEY: "gps_dev"
  MINIO_SECRET_KEY: "lE2DoJSlfk04sbNE"
  MINIO_PART_SIZE: "5242880"

.brew_setup: &brew_setup |
  if [[ $(uname -m) == "arm64" ]]; then
    eval "$(/opt/homebrew/bin/brew shellenv)"
    export PATH="/opt/homebrew/bin:$PATH"
  else
    eval "$(/usr/local/bin/brew shellenv)"
    export PATH="/usr/local/bin:$PATH"
  fi

stages:
  - changelog
  - setup
  - build
  - deploy

# Changelog job - runs on any tag creation
update_changelog:
  stage: changelog
  tags:
    - macos
  only:
    - tags
  script:
    - chmod +x scripts_build/update_changelog.sh
    - ./scripts_build/update_changelog.sh
    - git status
    - git remote -v
    - git add CHANGELOG.md
    - git commit -m "docs update changelog for $CI_COMMIT_TAG" || true
    - git push -f "https://${CI_NAME_PAT}:${CI_TOKEN}@gitlab.ai-vlab.com/${CI_PROJECT_PATH}.git" HEAD:$CI_COMMIT_REF_NAME || true
  artifacts:
    paths:
      - CHANGELOG.md
    expire_in: 1 week

# Build and deploy jobs - only run on release creation
.macos_runner:
  tags:
    - macos
  only:
    - releases
  before_script:
    - *brew_setup

setup_dependencies:
  extends: .macos_runner
  stage: setup
  script:
    - export PATH="${SYSTEM_PATH}:${PATH}"
    - chmod +x scripts_build/macos/setup_dependencies.sh
    - ./scripts_build/macos/setup_dependencies.sh
    # Install MinIO client
    - brew install minio/stable/mc
  artifacts:
    reports:
      dotenv: build.env
    paths:
      - venv/
    expire_in: 1 hour

build_macos:
  extends: .macos_runner
  stage: build
  needs:
    - setup_dependencies
  script:
    - export PATH="${SYSTEM_PATH}:${PATH_BREW}:${PATH}"
    - export VIRTUAL_ENV="${VENV_PATH}"
    - export PATH="${VIRTUAL_ENV}/bin:${PATH}"
    - source venv/bin/activate
    - chmod +x scripts_build/macos/build_macos_pyinstaller.sh
    - ./scripts_build/macos/build_macos_pyinstaller.sh
  artifacts:
    reports:
      dotenv: build.env

deploy:
  extends: .macos_runner
  stage: deploy
  script:
    - export PATH="${SYSTEM_PATH}:${PATH_BREW}:${PATH}"
    - |
      VERSION=$(jq -r '.version_string' version.json)
      PRODUCT_NAME=$(jq -r '.product_name' version.json)
      ARTIFACT_NAME="${PRODUCT_NAME}-${VERSION}.dmg"
      
      # # Configure MinIO client
      # mc alias set minio ${MINIO_HOST} ${MINIO_ACCESS_KEY} ${MINIO_SECRET_KEY}
      
      # # Upload DMG file to MinIO
      # echo "Uploading ${ARTIFACT_NAME} to MinIO..."
      # mc cp "build_dist/${ARTIFACT_NAME}" "minio/videos/${ARTIFACT_NAME}"
      
      # # Get the public URL
      # DOWNLOAD_URL="${MINIO_DOMAIN}/videos/${ARTIFACT_NAME}"
      DOWNLOAD_URL="https://example.com/${ARTIFACT_NAME}"  # Placeholder URL
      
      echo "Creating release for version ${VERSION}..."
      curl --header "PRIVATE-TOKEN: ${CI_TOKEN}" \
           --header "Content-Type: application/json" \
           --data "{
             \"name\": \"Release ${VERSION}\",
             \"tag_name\": \"v${VERSION}\",
             \"description\": \"Release version ${VERSION}\n\nDownload: ${DOWNLOAD_URL}\",
             \"assets\": {
               \"links\": [{
                 \"name\": \"${ARTIFACT_NAME}\",
                 \"url\": \"${DOWNLOAD_URL}\"
               }]
             }
           }" \
           "${GITLAB_API_URL}/projects/${CI_PROJECT_ID}/releases"
  dependencies:
    - build_macos 
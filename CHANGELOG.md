# Changelog

<!-- insertion marker -->

## [v1.2.0](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/tags/v1.2.0) - 2025-04-02 11:36:37 +0700

<small>[Compare with v1.1.8](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/compare/v1.1.8...v1.2.0)</small>

* tung.vu: Remove Temp file and add rule commit code
* thanh.nguyen: Add Custom Slider
* thanh.nguyen: Update TimeLine Style
* thanh.nguyen: Update TimeLineController
* chienpm: fix logo position
* tung.vu: Update: Add Animation Blur Effect loading/disconnect
* thanhndh: Fix crash event.u.new_status not 0
* HanhLT: fix loi timeline ko doi mau khi change theme
* chienpm: fix bug sort after adding group
* thanh.nguyen: Fix conflict
* chienpm: update map_widget
* thanh.nguyen: Fix bug report
* thanh.nguyen: Update multi languages

## [v1.1.8](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/tags/v1.1.8) - 2024-08-08

<small>[Compare with v1.0.6](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/compare/v1.0.6...v1.1.8)</small>

* Update: Add map features and timeline improvements
* Fix: Various UI and performance issues
* Add: Custom slider and animation effects
* Improve: Video capture and streaming stability
* Update: Multi-language support and translations

## [v1.0.6](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/tags/v1.0.6) - 2023-11-28

<small>[Compare with v1.0.4](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/compare/v1.0.4...v1.0.6)</small>

* Fix: Video playback and streaming issues
* Update: Camera widget and device management
* Add: Improved error handling and notifications
* Optimize: Performance and memory usage
* Update: Build tools and dependencies

## [v1.0.4](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/tags/v1.0.4) - 2023-10-19

<small>[Compare with v1.0.2](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/compare/v1.0.2...v1.0.4)</small>

* Fix: PTZ control and camera management
* Add: AI flow improvements
* Update: User interface and responsiveness
* Improve: Error handling and stability
* Update: Documentation and build process

## [v1.0.2](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/tags/v1.0.2) - 2023-10-05

<small>[Compare with v1.0.1](https://gitlab.ai-vlab.com/gpstech/vms/iVMS/compare/v1.0.1...v1.0.2)</small>

* Fix: Various stability issues
* Add: New camera management features
* Update: UI/UX improvements
* Improve: Performance optimizations
* Update: Documentation and dependencies
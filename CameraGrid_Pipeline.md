# CameraGrid.qml - Pipeline Documentation

## Tổng quan
CameraGrid.qml là component chính quản lý grid layout cho hệ thống camera, hỗ trợ tối đa 12x12 = 144 cells. Component này xử lý drag & drop, tạo/quản lý GridItem instances, và tính toán vị trí/kích thước.

## <PERSON><PERSON><PERSON> k<PERSON>ch b<PERSON> (Main Scenarios)

### 1. 🎯 Khởi tạo và Thiết lập (Initialization)

#### 1.1 Component Initialization
```qml
Component.onCompleted: {
    forceActiveFocus()
    updateCameraCount()
}
```
- **Trigger**: Khi component được tạo
- **Actions**: 
  - Thiết lập focus
  - Cập nhật số lượng camera
  - Khởi tạo grid với kích thước mặc định (1x1)

#### 1.2 Grid Model Connection
- **Trigger**: gridModel được inject
- **Actions**:
  - Bind theme properties
  - Thiết lập grid dimensions
  - Kết nối signals

### 2. 📱 Drag & Drop Operations

#### 2.1 Single Camera Drop
```javascript
// Trigger: drop.formats.includes("application/camera")
onDropped: function(drop) {
    var cameraData = JSON.parse(drop.getDataAsString("application/x-qabstractitemmodeldatalist"))
    
    // Check camera limit
    if (!checkCameraLimit(1)) return
    
    // Check auto resize setting
    var autoResizeEnabled = gridModel.autoResizeEnabled
    
    if (isPositionAvailable(dropPosition)) {
        gridModel.addCameraRequested(dropPosition, cameraData.data.id)
    } else {
        // Find alternative position or expand grid
    }
}
```
- **Input**: Camera từ tree view
- **Validation**: 
  - Kiểm tra giới hạn 144 cells
  - Kiểm tra vị trí có sẵn
  - Kiểm tra auto_resize_enabled
- **Actions**:
  - Thêm camera vào vị trí được chỉ định
  - Tìm vị trí trống nếu vị trí bị chiếm
  - Mở rộng grid nếu cần (khi auto_resize_enabled = false)

#### 2.2 Group Drop
```javascript
// Trigger: drop.formats.includes("application/group")
var numCamerasInGroup = groupData.data.cameraIds.length
if (!checkCameraLimit(numCamerasInGroup)) return

if (autoResizeEnabled === false) {
    // Check available positions and expand if needed
    var availablePositions = findAvailablePositions(numCamerasInGroup)
    if (availablePositions.length < numCamerasInGroup) {
        expandGridLikeOnWheelCtrl(targetColumns, targetRows)
    }
}
```
- **Input**: Group chứa nhiều camera
- **Validation**: Kiểm tra số lượng camera trong group
- **Actions**:
  - Tính toán vị trí cần thiết
  - Mở rộng grid nếu không đủ chỗ
  - Thêm tất cả camera trong group

#### 2.3 Multi-Selection Drop
```javascript
// Trigger: drop.formats.includes("application/multi-selection")
var multiData = JSON.parse(drop.getDataAsString("application/json"))

// Count cameras in selection
for (var id in multiData) {
    if (item.type === "Camera") cameraCount++
    else if (item.type === "Group") {
        var numCamerasInGroup = gridModel.getGroupCameraCount(id)
        cameraCount += numCamerasInGroup
    }
}
```
- **Input**: Multiple items (cameras, groups, maps, events)
- **Validation**: Đếm tổng số camera cần thêm
- **Actions**:
  - Xử lý từng loại item khác nhau
  - Tìm vị trí trống rời rạc
  - Mở rộng grid nếu cần

#### 2.4 Map/Event Drop
- **Input**: Map hoặc Event items
- **Actions**: Thêm vào vị trí trống đầu tiên

### 3. 🔄 Grid Resize Operations

#### 3.1 Wheel + Ctrl Resize
```javascript
onWheel: function(wheel) {
    if (wheel.modifiers & Qt.ControlModifier) {
        if (wheel.angleDelta.y < 0) {
            // Expand grid
            var newColumns = gridModel.columns + 1
            var newRows = gridModel.rows + 1
            
            // Convert camera positions
            for (var pos in videoGrid.activeItems) {
                newPositions[pos] = convertIndex(positions[pos], oldColumns, newColumns)
            }
            
            gridModel.setGridSizeRequested(newColumns, newRows)
            // Move cameras to new positions
        } else {
            // Shrink grid (if possible)
        }
    }
}
```
- **Trigger**: Wheel scroll + Ctrl key
- **Validation**: 
  - Không trong chế độ maximize
  - Kiểm tra camera ở biên khi thu nhỏ
- **Actions**:
  - Tăng/giảm grid size
  - Chuyển đổi vị trí camera
  - Hiển thị neon grid highlight
  - Đồng bộ với virtual windows

#### 3.2 Auto Grid Expansion
```javascript
function expandGridLikeOnWheelCtrl(targetColumns, targetRows) {
    // Collect current positions
    var positions = {}
    for (var pos in videoGrid.activeItems) {
        positions[pos] = parseInt(pos)
    }
    
    // Convert positions
    var newPositions = {}
    for (var key in positions) {
        newPositions[key] = convertIndex(positions[key], oldColumns, targetColumns)
    }
    
    gridModel.setGridSizeRequested(targetColumns, targetRows)
    // Move cameras
}
```
- **Trigger**: Khi drop group/multiselect và auto_resize_enabled = false
- **Actions**: Mở rộng grid giống hệt wheel+ctrl

### 4. 🎮 Selection Operations

#### 4.1 Rectangle Selection
```javascript
MouseArea {
    onPressed: {
        // Clear all selections
        startPoint = Qt.point(mouse.x, mouse.y)
    }
    
    onPositionChanged: {
        if (mouse.buttons & Qt.LeftButton) {
            isDragging = true
            updateSelection(mouse)
        }
    }
}
```
- **Trigger**: Click và drag trên grid
- **Actions**:
  - Tạo selection rectangle
  - Chọn camera trong vùng
  - Hỗ trợ Ctrl để multi-select

#### 4.2 Keyboard Selection
```javascript
Keys.onPressed: function(event) {
    if (event.key === Qt.Key_A && event.modifiers & Qt.ControlModifier) {
        // Select all cameras
        for (var pos in videoGrid.activeItems) {
            videoGrid.activeItems[pos].isSelected = newState
        }
    }
}
```
- **Trigger**: Ctrl+A
- **Actions**: Chọn/bỏ chọn tất cả camera

### 5. 🗑️ Delete Operations

#### 5.1 Delete Selected Items
```javascript
Keys.onDeletePressed: {
    var selectedItems = []
    for (var pos in videoGrid.activeItems) {
        if (videoGrid.activeItems[pos].isSelected) {
            selectedItems.push(parseInt(pos))
        }
    }
    
    if (selectedItems.length > 0) {
        gridModel.isSave = false
        gridModel.removeItemsRequested(selectedItems)
    }
}
```
- **Trigger**: Delete key
- **Actions**: Xóa tất cả camera được chọn

#### 5.2 Delete by Model
```javascript
// Trigger: Shift+Delete
if (event.key === Qt.Key_Delete && event.modifiers) {
    var firstPosition = selectedItems[0]
    gridModel.removeCamerasByModelFromPosition(firstPosition)
}
```
- **Trigger**: Shift+Delete
- **Actions**: Xóa tất cả camera cùng model

### 6. 📺 Video Management

#### 6.1 Video Info Changes
```javascript
Connections {
    target: gridModel
    function onVideoInfoChanged(position, camera_model, isPlaying, supportsPTZ) {
        if (camera_model === null) {
            // Remove camera
            if (item) {
                item.destroy()
                delete videoGrid.activeItems[position]
            }
        } else if (!item) {
            // Create new camera
            item = videoGrid.createGridItem(position, camera_model)
        } else {
            // Update existing camera
            item.camera_id = camera_model.id
            item.isPlaying = isPlaying
        }
    }
}
```
- **Trigger**: Signal từ GridModel
- **Actions**:
  - Tạo GridItem mới
  - Cập nhật thông tin camera
  - Xóa camera

### 7. 🎨 UI State Management

#### 7.1 Theme Management
- **Properties**: isDarkTheme, backgroundColor, foregroundColor, etc.
- **Source**: gridModel hoặc default values
- **Actions**: Cập nhật màu sắc theo theme

#### 7.2 Grid Highlight
```javascript
function showGridResizeHighlight() {
    gridResizeHighlight = true
    gridResizeTimer.restart()
}
```
- **Trigger**: Grid resize operations
- **Actions**: Hiển thị neon grid lines trong 1.5 giây

#### 7.3 Drag Highlight
```javascript
function updateHighlight(startCol, startRow, width, height, hasEnoughSpace) {
    if (hasEnoughSpace) {
        areaHighlight.color = "#5022C55E"  // Green
    } else {
        areaHighlight.color = "#50FF4444"  // Red
    }
}
```
- **Trigger**: Drag operations
- **Actions**: Hiển thị vùng drop với màu xanh/đỏ

### 8. 🔧 Utility Functions

#### 8.1 Position Validation
```javascript
function isPositionAvailable(position) {
    // Check if position is within grid bounds
    // Check if position is not occupied
    // Check if position is not part of resized camera
}
```

#### 8.2 Camera Limit Check
```javascript
function checkCameraLimit(camerasToAdd) {
    var currentOccupiedCells = 0
    // Calculate actual occupied cells including resized cameras
    var maxCells = 144
    return totalCellsAfterAdd <= maxCells
}
```

#### 8.3 Grid Item Creation
```javascript
function createGridItem(position, model) {
    var modelType = determineModelType(model)
    switch (modelType) {
        case "FloorModel": return Qt.createComponent("GridItemMap2D.qml")
        case "MapModel": return Qt.createComponent("GridItemDigitalMap.qml")
        default: return Qt.createComponent("GridItem.qml")
    }
}
```

## 🔄 Data Flow

1. **Input** → Drag/Drop/Keyboard events
2. **Validation** → Position/Limit checks
3. **Processing** → Grid calculations/Camera management
4. **Backend Sync** → GridModel signals
5. **UI Update** → Visual feedback/Animation

## 🚨 Error Handling

- **Grid Full**: Hiển thị GridFullDialog
- **Invalid Position**: Tìm vị trí alternative
- **Parse Error**: Console.error và fallback
- **Component Creation**: Error logging và null checks

## 🎯 Performance Optimizations

- **Canvas Grid Lines**: Chỉ vẽ khi cần thiết
- **Throttled Events**: Wheel events throttling (100ms)
- **Lazy Updates**: Qt.callLater cho heavy operations
- **Efficient Selection**: Intersection checks optimization

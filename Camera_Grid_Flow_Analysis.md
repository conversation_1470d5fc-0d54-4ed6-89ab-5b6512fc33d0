# Phân Tích Luồng Xử Lý Camera Grid System

## Tổng Quan Kiến Trúc

Hệ thống Camera Grid bao gồm 3 thành phần chính:
- **CameraGrid.qml**: Frontend UI container quản lý grid layout và xử lý drag & drop
- **GridItem.qml**: Widget hiển thị camera cá nhân với các tương tác
- **grid_manager.py**: Backend controller xử lý logic nghiệp vụ

## 1. Luồng Kéo Camera Vào Grid (Drag Camera to Grid)

### 1.1 Bắt Đầu: Tree View → Grid
**Vị trí code**: `CameraGrid.qml` dòng 1080-1200

```
Người dùng kéo camera từ tree view
↓
DropArea.onEntered() (dòng 1124)
├── Lấy thông tin kích thước camera từ drag.source.dragDimensions
├── Bắt đầu highlight grid cells
└── Khởi động dragUpdateTimer để cập nhật liên tục
```

### 1.2 Xử Lý Drop Event
**Vị trí code**: `CameraGrid.qml` dòng 1383-1602

```
DropArea.onDropped() (dòng 1383)
├── Tính toán vị trí drop dựa trên tọa độ chuột
├── Kiểm tra định dạng drop (application/camera)
├── Parse dữ liệu camera từ JSON
├── Kiểm tra giới hạn camera (checkCameraLimit)
├── Kiểm tra vị trí có sẵn (isPositionAvailable)
└── Gửi signal đến backend: gridModel.addCameraRequested(position, camera_id)
```

### 1.3 Backend Xử Lý
**Vị trí code**: `grid_manager.py` dòng 222-234, 471-500

```
GridModel._handleAddCameraRequest() (dòng 222)
├── Gọi addCamera() method (dòng 471)
├── Tìm vị trí trống nếu position bị chiếm
├── Thêm camera vào _active_cells dictionary
├── Cập nhật cell dimensions
├── Emit videoInfoChanged signal
└── Emit actionCompleted signal
```

### 1.4 Cập Nhật UI
**Vị trí code**: `CameraGrid.qml` dòng 497-564

```
Connections.onVideoInfoChanged() (dòng 497)
├── Tạo GridItem mới nếu chưa tồn tại
├── Cập nhật camera_id, isPlaying, supportsPTZ
├── Gọi updateCameraCount()
└── Hiển thị camera trong grid
```

## 2. Luồng Kéo Camera Vào Vị Trí Cụ Thể

### 2.1 Tính Toán Vị Trí
**Vị trí code**: `CameraGrid.qml` dòng 1397-1410

```
Tính toán cell position từ tọa độ chuột:
├── cellWidth = gridLines.width / effectiveColumns
├── cellHeight = gridLines.height / effectiveRows
├── col = Math.floor(mousePos.x / cellWidth)
├── row = Math.floor(mousePos.y / cellHeight)
└── dropPosition = row * effectiveColumns + col
```

### 2.2 Kiểm Tra Vị Trí Hợp Lệ
**Vị trí code**: `CameraGrid.qml` dòng 156-205

```
isPositionAvailable(position) (dòng 156)
├── Kiểm tra position trong phạm vi grid
├── Kiểm tra vị trí chưa có camera
├── Kiểm tra không bị camera resize chiếm chỗ
└── Return true/false
```

## 3. Luồng Kéo Group Vào Grid

### 3.1 Xử Lý Group Drop
**Vị trí code**: `CameraGrid.qml` dòng 1603-1800

```
Group drop processing:
├── Parse group data từ JSON
├── Lấy danh sách camera IDs từ group
├── Kiểm tra số lượng camera vs giới hạn grid
├── Tìm vị trí trống cho tất cả cameras
├── Gửi addGroupRequested signal hoặc addGroupToPositionsRequested
└── Backend xử lý từng camera trong group
```

### 3.2 Backend Xử Lý Group
**Vị trí code**: `grid_manager.py` dòng 2174-2300

```
addGroup() method (dòng 2174)
├── Lấy thông tin group từ group_model_manager
├── Duyệt qua từng camera trong group
├── Gọi addCamera() cho mỗi camera
├── Tự động mở rộng grid nếu cần
└── Emit signals cho từng camera được thêm
```

## 4. Luồng Xóa Items

### 4.1 Xóa Single Item
**Vị trí code**: `CameraGrid.qml` dòng 454-478

```
Keys.onDeletePressed (dòng 454)
├── Thu thập các items được chọn
├── Gửi removeItemsRequested signal
└── Backend xử lý xóa
```

### 4.2 Backend Xử Lý Xóa
**Vị trí code**: `grid_manager.py` dòng 268-286

```
_handleRemoveItemsRequest() (dòng 268)
├── Gọi removeVideo() hoặc removeMultipleVideos()
├── Xóa khỏi _active_cells
├── Emit videoInfoChanged với camera_model = null
└── UI tự động destroy GridItem
```

## 5. Luồng Fullscreen Item

### 5.1 Toggle Fullscreen
**Vị trí code**: GridItem context menu → grid_manager.py

```
Context menu "Fullscreen" click
↓
toggleFullscreenRequested signal
↓
_handleToggleFullscreenRequest() (dòng 304)
├── Kiểm tra trạng thái hiện tại (_is_maximized)
├── Gọi maximizeGrid() hoặc restoreGrid()
└── Cập nhật UI layout
```

### 5.2 Maximize Logic
**Vị trí code**: `grid_manager.py` dòng 1400-1500

```
maximizeGrid(position) method:
├── Lưu trạng thái grid hiện tại
├── Set _is_maximized = True
├── Set _active_item_position = position
├── Emit isMaximizedChanged signal
└── UI tự động ẩn các items khác
```

## 6. Luồng Swap Items

### 6.1 Drag GridItem
**Vị trí code**: `GridItem.qml` dòng 917-950

```
MouseArea.onPositionChanged() (dòng 917)
├── Phát hiện drag gesture
├── Set dragActive = true
├── Cập nhật Drag.mimeData với thông tin camera
└── Bắt đầu drag operation
```

### 6.2 Drop GridItem
**Vị trí code**: `CameraGrid.qml` dòng 2000-2100

```
DropArea xử lý "videoItem" drop:
├── Lấy source và target positions
├── Gửi swapItemsRequested signal
└── Backend thực hiện swap
```

### 6.3 Backend Swap
**Vị trí code**: `grid_manager.py` dòng 1676-1700

```
swapPositions() method (dòng 1676)
├── Hoán đổi trong _active_cells dictionary
├── Cập nhật cell dimensions
├── Emit videoPositionChanged signals
└── UI tự động cập nhật vị trí
```

## 7. Luồng Resize Camera

### 7.1 Resize Handle
**Vị trí code**: `GridItem.qml` dòng 459-550

```
Resize MouseArea:
├── Phát hiện drag trên resize handle
├── Tính toán kích thước mới theo grid cells
├── Kiểm tra va chạm với cameras khác
├── Gửi updateCellDimensions đến backend
└── Backend cập nhật _cell_dimensions
```

## 8. Luồng Load Grid từ Saved View

### 8.1 Load Saved Grid
**Vị trí code**: `grid_manager.py` dòng 372-395

```
loadData() method (dòng 372)
├── Kiểm tra tab type != Invalid
├── Parse grid configuration từ currentGrid JSON
├── Set grid size từ saved data
├── Gọi load_saved_view() sau 100ms
└── Restore tất cả cameras từ saved state
```

### 8.2 Restore Camera Positions
**Vị trí code**: `grid_manager.py` dòng 3500-3600

```
load_saved_view() method:
├── Duyệt qua saved camera data
├── Gọi addCamera() cho mỗi camera
├── Restore cell dimensions
├── Set isSave = True khi hoàn thành
└── Emit allItemsReady signal
```

## 9. Luồng Clear All Items

### 9.1 Clear Grid Request
**Vị trí code**: `CameraGrid.qml` → `grid_manager.py` dòng 342-351

```
clearGridRequested signal
↓
_handleClearGridRequest() (dòng 342)
├── Gọi removeAllItem() method
├── Xóa tất cả items khỏi _active_cells
├── Reset grid state
├── Emit removeAllItemChanged signal
└── UI destroy tất cả GridItems
```

### 9.2 UI Clear Response
**Vị trí code**: `CameraGrid.qml` dòng 687-697

```
Connections.onActionCompleted() (dòng 687)
├── Kiểm tra actionType == "clearGrid"
├── Destroy tất cả items trong activeItems
├── Gọi updateCameraCount()
└── Reset grid về trạng thái trống
```

## 10. Luồng Auto Resize Grid

### 10.1 Grid Size Calculation
**Vị trí code**: `grid_manager.py` dòng 449-467

```
_calculateGridSize(count) method (dòng 449)
├── Tính toán grid size tối ưu dựa trên số camera
├── Luôn trả về grid vuông (square grid)
├── Tối đa 12x12 = 144 cells
└── Return (columns, rows)
```

### 10.2 Auto Expand Grid
**Vị trí code**: `CameraGrid.qml` dòng 299-369

```
expandGridLikeOnWheelCtrl() method (dòng 299)
├── Thu thập vị trí hiện tại của cameras
├── Tính toán vị trí mới sau khi expand
├── Cập nhật grid size
├── Di chuyển cameras đến vị trí mới
└── Cập nhật cell dimensions
```

## 11. Chi Tiết Signals và Connections

### 11.1 Main Signals trong GridModel
**Vị trí code**: `grid_manager.py` dòng 54-107

```
Key Signals:
├── videoInfoChanged(position, camera_model, isPlaying, supportsPTZ)
├── mapInfoChanged(position, mapData)
├── layoutChanged(activePositions)
├── cellDimensionsChanged(position, width, height)
├── isMaximizedChanged(bool)
├── actionCompleted(actionType, success, message)
└── gridStateChanged(gridState)
```

### 11.2 Action Request Signals
**Vị trí code**: `grid_manager.py` dòng 95-103

```
Request Signals từ QML:
├── addCameraRequested(position, camera_id)
├── addGroupRequested(position, group_id)
├── removeItemsRequested(positions)
├── swapItemsRequested(source_pos, target_pos)
├── toggleFullscreenRequested(position)
├── loadGridRequested(grid_id)
└── clearGridRequested()
```

## 12. Error Handling và Validation

### 12.1 Camera Limit Check
**Vị trí code**: `CameraGrid.qml` dòng 96-153

```
checkCameraLimit(camerasToAdd) (dòng 96)
├── Tính toán cells đã chiếm (bao gồm resize)
├── Kiểm tra vs giới hạn 144 cells
├── Hiển thị dialog cảnh báo nếu vượt quá
└── Return true/false
```

### 12.2 Position Validation
**Vị trí code**: `CameraGrid.qml` dòng 156-205

```
isPositionAvailable(position) (dòng 156)
├── Validate position parameters
├── Kiểm tra fullscreen mode
├── Tính toán effective grid size
├── Kiểm tra position trong bounds
├── Kiểm tra không có camera tại vị trí
└── Kiểm tra không bị camera resize chiếm
```

## 13. Performance Optimizations

### 13.1 Drag Update Timer
**Vị trí code**: `CameraGrid.qml` dòng 1107-1121

```
Timer tối ưu drag performance:
├── Interval: 16ms (60fps)
├── Chỉ update khi containsDrag = true
├── Batch update thay vì real-time
└── Stop timer khi drag kết thúc
```

### 13.2 Grid Canvas Optimization
**Vị trí code**: `CameraGrid.qml` dòng 798-861

```
Canvas rendering tối ưu:
├── Chỉ vẽ grid lines khi cần thiết
├── Sử dụng Canvas thay vì nhiều Rectangle
├── Layer.enabled = false để tắt effects
└── Conditional rendering dựa trên drag state
```

## Kết Luận

Hệ thống Camera Grid sử dụng kiến trúc signal-slot để tách biệt UI và logic:

### Kiến Trúc Tổng Thể:
- **QML Frontend**: Xử lý tương tác người dùng, tính toán vị trí, validation cơ bản
- **Python Backend**: Quản lý state, business logic, data persistence
- **Signal Communication**: Đảm bảo đồng bộ giữa UI và backend

### Luồng Xử Lý Chính:
1. **UI Event** (drag, drop, click) →
2. **Signal Emission** (request signals) →
3. **Backend Processing** (business logic) →
4. **Result Signal** (completion/state change) →
5. **UI Update** (visual feedback)

### Tối Ưu Performance:
- Timer-based drag updates (60fps)
- Canvas rendering cho grid lines
- Conditional UI updates
- Batch operations cho multiple items

Mỗi thao tác đều có error handling và validation đầy đủ, đảm bảo tính ổn định của hệ thống.

---

# Phân Tích Luồng VirtualCameraGridWidget - Đồng Bộ Dual Screen

## Tổng Quan VirtualCameraGridWidget

VirtualCameraGridWidget là tính năng cho phép hiển thị camera grid trên màn hình thứ 2, đồng bộ hoàn toàn với CameraGridWidget chính. Khi người dùng thao tác trên màn hình chính, màn hình virtual sẽ tự động cập nhật theo.

## 1. Luồng Khởi Tạo VirtualCameraGridWidget

### 1.1 Trigger Tạo Virtual Window
**Vị trí code**: `main_tree_view_widget.py` dòng 1481-1482, `CameraContextMenu.qml` dòng 46-51

```
Người dùng trigger tạo Virtual Window:
├── Context menu "Cửa sổ giả lập mới" (CameraContextMenu.qml)
├── Tree view "New Virtual Window" action
├── Hoặc từ existing camera "Mở camera sang" → "Cửa sổ giả lập mới"
└── Gọi main_tree_view_widget.auto_open_virtual_window()
```

### 1.2 Khởi Tạo VirtualCameraGridWidget
**Vị trí code**: `virtual_camera_grid_widget.py` dòng 34-44

```
VirtualCameraGridWidget.__init__() (dòng 34)
├── Nhận gridModel từ CameraGridWidget chính
├── Sử dụng chung GridModel: self.gridModel = gridModel
├── Không tạo GridModel mới, dùng reference đến GridModel gốc
├── Load QML: CameraGrid.qml với gridModel được chia sẻ
└── Hiển thị fullscreen trên màn hình được chỉ định
```

### 1.3 Chia Sẻ GridModel
**Vị trí code**: `virtual_camera_grid_widget.py` dòng 83

```
Shared GridModel Architecture:
├── Main và Virtual cùng sử dụng 1 GridModel instance
├── Không cần signal forwarding giữa GridModels
├── Mọi thay đổi state tự động đồng bộ
├── QML context được set với cùng gridModel
└── Automatic UI updates trên cả 2 màn hình
```

## 2. Luồng Đồng Bộ Dữ Liệu

### 2.1 Kiến Trúc Đồng Bộ (Shared GridModel)
```
                    Shared GridModel Instance
                    ├── _active_cells: {position: camera_model}
                    ├── _cell_dimensions: {position: {width, height}}
                    ├── _columns, _rows: grid dimensions
                    └── All state properties
                           ↙              ↘
CameraGridWidget (Main Screen)    VirtualCameraGridWidget (Secondary Screen)
├── QQuickWidget                  ├── QQuickWidget
├── CameraGrid.qml               ├── CameraGrid.qml (same file)
└── gridModel reference          └── gridModel reference (same instance)
```

### 2.2 Luồng Đồng Bộ Khi Thêm Camera (Shared Model)
**Vị trí code**: Từ CameraGrid.qml → grid_manager.py

```
Người dùng kéo camera vào Main Screen:
├── CameraGrid.qml (Main) emit: addCameraRequested(position, camera_id)
├── Shared GridModel xử lý: _handleAddCameraRequest()
├── Cập nhật _active_cells và emit videoInfoChanged()
├── Signal được emit đến TẤT CẢ QML contexts đang lắng nghe
│
└── Automatic synchronization:
    ├── CameraGrid.qml (Main) nhận videoInfoChanged → tạo GridItem
    ├── CameraGrid.qml (Virtual) nhận cùng signal → tạo GridItem
    ├── Cả 2 UI cập nhật đồng thời từ cùng 1 signal
    └── Perfect sync vì cùng data source
```

### 2.3 Luồng Đồng Bộ Khi Xóa Camera (Shared Model)
```
Người dùng xóa camera trên Main Screen:
├── CameraGrid.qml (Main) emit: removeItemsRequested([positions])
├── Shared GridModel xử lý và emit videoInfoChanged(position, null)
├── Signal được broadcast đến tất cả QML contexts
│
└── Automatic synchronization:
    ├── CameraGrid.qml (Main) nhận signal → destroy GridItem
    ├── CameraGrid.qml (Virtual) nhận cùng signal → destroy GridItem
    └── Đồng bộ hoàn hảo, không cần duplicate logic
```

## 3. Luồng Đồng Bộ Các Thao Tác Khác

### 3.1 Swap Items (Shared Model)
```
Main Screen swap:
├── GridItem drag & drop → swapItemsRequested(source, target)
├── Shared GridModel thực hiện swap trong _active_cells
├── Emit videoPositionChanged signals đến tất cả contexts
│
└── Automatic sync:
    ├── Main UI nhận signals → update GridItem positions
    ├── Virtual UI nhận cùng signals → update GridItem positions
    └── Instant synchronization
```

### 3.2 Fullscreen Toggle (Shared Model)
```
Main Screen fullscreen:
├── Context menu → toggleFullscreenRequested(position)
├── Shared GridModel set _is_maximized = true
├── Emit isMaximizedChanged signal đến tất cả contexts
│
└── Automatic sync:
    ├── Main UI nhận signal → ẩn/hiện GridItems
    ├── Virtual UI nhận cùng signal → ẩn/hiện GridItems
    └── Perfect fullscreen sync
```

### 3.3 Grid Resize (Shared Model)
```
Main Screen resize grid:
├── Wheel + Ctrl → setGridSize(columns, rows)
├── Shared GridModel cập nhật _columns, _rows
├── Emit columnsChanged, rowsChanged đến tất cả contexts
│
└── Automatic sync:
    ├── Main UI reorganize GridItems
    ├── Virtual UI reorganize GridItems
    └── Consistent grid layout
```

## 4. Quản Lý Màn Hình và Lifecycle

### 4.1 Screen Management
**Vị trí code**: `virtual_camera_grid_widget.py` dòng 164-179

```
switch_window(screen_index) (dòng 164)
├── Thay đổi screen_index
├── Lấy geometry của màn hình mới
├── Resize widget theo độ phân giải màn hình
├── Gọi setGeometry() và raise_()
└── Đưa virtual window lên foreground
```

### 4.2 Lifecycle Management
**Vị trí code**: `virtual_camera_grid_widget.py` dòng 146-162

```
closeEvent() (dòng 146)
├── Cập nhật icon trong tree view
├── Xóa khỏi main_controller.list_parent
├── Tìm và đóng tab tương ứng trong main screen
├── Cleanup resources
└── Disconnect signals
```

## 5. Tối Ưu Performance cho Dual Screen

### 5.1 Video Stream Optimization
- Virtual window sử dụng SUB_STREAM thay vì MAIN_STREAM
- Chỉ main screen sử dụng MAIN_STREAM khi fullscreen
- Shared video capture controller giữa main và virtual

### 5.2 Signal Optimization
- Single signal broadcast đến multiple QML contexts
- Không cần signal forwarding giữa GridModels
- Qt's built-in signal/slot optimization

### 5.3 Memory Management
- Shared GridModel instance - không duplicate data
- Single _active_cells dictionary cho cả 2 UI
- Efficient memory usage với shared state

## 6. Error Handling và Edge Cases

### 6.1 Screen Disconnection
```
Khi màn hình thứ 2 bị ngắt kết nối:
├── geometryChanged signal từ QScreen
├── Auto switch về màn hình chính
├── Hiển thị notification cho user
└── Maintain data consistency
```

### 6.2 Signal Synchronization Issues
```
Xử lý signal conflicts:
├── Use signal queuing để đảm bảo order
├── Timeout mechanisms cho long operations
├── Rollback mechanisms khi sync fails
└── Error reporting và recovery
```

## Kết Luận VirtualCameraGridWidget

### Ưu Điểm Kiến Trúc Shared Model:
- **Perfect Sync**: Đồng bộ 100% vì cùng data source
- **Simplified Architecture**: Không cần signal forwarding
- **Memory Efficient**: Single GridModel instance
- **Automatic Updates**: Qt signals tự động broadcast đến tất cả contexts

### Luồng Hoạt Động:
1. **Initialization**: Virtual widget nhận reference đến shared GridModel
2. **Direct Binding**: Cả 2 QML contexts bind trực tiếp đến cùng GridModel
3. **Automatic Sync**: Mọi thay đổi tự động update cả 2 UI
4. **Resource Sharing**: Shared state, optimized performance

Hệ thống VirtualCameraGridWidget đảm bảo trải nghiệm người dùng nhất quán trên multiple screens với performance tối ưu.

---

# Luồng Bổ Sung - Các Tính Năng Nâng Cao

## 14. Luồng PTZ Control (Pan-Tilt-Zoom)

### 14.1 Kích Hoạt PTZ Controls
**Vị trí code**: `GridItem.qml` dòng 256-315

```
User click PTZ button:
├── activatePtzButton("ptz") được gọi
├── Tắt PTZ controls ở tất cả GridItems khác
├── selectThisCamera() để chọn camera hiện tại
├── Hiển thị PTZControlPanel
└── Set isPtzActive = true
```

### 14.2 PTZ Command Processing
**Vị trí code**: `grid_manager.py` dòng 407-444

```
PTZ command flow:
├── User interaction → PTZ coordinates
├── Add command to coordinate_queue
├── process_queue() xử lý với rate limiting (200ms)
├── controller.ptz_continuous_move() gửi lệnh
└── Camera thực hiện PTZ movement
```

### 14.3 PTZ 3D Control
**Vị trí code**: `GridItem.qml` PTZ3DControl component

```
3D PTZ interaction:
├── Mouse click/drag trên 3D control area
├── Tính toán pan/tilt coordinates
├── Gửi coordinates vào queue
├── Rate limiting và batch processing
└── Smooth PTZ movement
```

## 15. Luồng Video Capture và Streaming

### 15.1 Video Capture Initialization
**Vị trí code**: `GridItem.qml` VideoFrame component

```
Video capture startup:
├── GridItem được tạo với camera_id
├── VideoFrame component khởi tạo
├── video_capture_controller.register_video_capture()
├── Start video stream thread
└── Display video frames
```

### 15.2 Stream Type Switching
```
Stream switching logic:
├── Fullscreen → Switch to MAIN_STREAM
├── Normal view → Switch to SUB_STREAM
├── Grid size > 3x3 → Force SUB_STREAM
├── Virtual window → Always SUB_STREAM
└── Update video capture accordingly
```

### 15.3 Video Performance Optimization
```
Performance optimizations:
├── Lazy loading video streams
├── Stop streams khi GridItem không visible
├── Shared video capture instances
├── Memory pooling cho video frames
└── Hardware acceleration khi available
```

## 16. Luồng Context Menu và Actions

### 16.1 Context Menu Trigger
**Vị trí code**: `GridItem.qml` MouseArea.onReleased

```
Right-click context menu:
├── Mouse right-click detected
├── contextMenu.popup() hiển thị menu
├── Menu items được populate động
├── User select action
└── Execute corresponding function
```

### 16.2 Camera Actions
**Vị trí code**: `CameraContextMenu.qml`

```
Available camera actions:
├── "Mở camera sang" → Sub-menu với options
│   ├── "Màn hình mới" → openCameraInNewScreen()
│   ├── "Màn hình đã lưu mới" → openCameraInNewSavedView()
│   └── "Cửa sổ giả lập mới" → openCameraInNewVirtualWindow()
├── "Cửa sổ giả lập cũ" → Dynamic submenu
├── "Thông tin camera" → showCameraInfo()
└── "Fullscreen" → toggleFullscreen()
```

## 17. Luồng Keyboard Shortcuts

### 17.1 Global Shortcuts
**Vị trí code**: `CameraGrid.qml` Keys.onPressed

```
Keyboard shortcuts:
├── Ctrl+A → Select all cameras
├── Delete → Remove selected cameras
├── Shift+Delete → Remove cameras by model
├── Arrow keys → Navigate between cameras
└── Number keys → Quick camera selection
```

### 17.2 PTZ Shortcuts
```
PTZ keyboard controls:
├── WASD → Pan/Tilt movement
├── Q/E → Zoom in/out
├── Space → Stop all PTZ movement
├── R → Reset to home position
└── F → Toggle fullscreen
```

## 18. Luồng Theme và Styling

### 18.1 Theme Management
**Vị trí code**: `CameraGrid.qml` theme properties

```
Theme system:
├── isDarkTheme property controls overall theme
├── Color properties được bind từ gridModel
├── Dynamic theme switching
├── Consistent styling across components
└── Theme persistence trong settings
```

### 18.2 Visual States
```
Visual state management:
├── Normal state → Default appearance
├── Hover state → Highlight borders
├── Selected state → Selection indicators
├── Dragging state → Semi-transparent
├── Fullscreen state → Hide other items
└── Error state → Error indicators
```

## 19. Luồng Error Handling và Recovery

### 19.1 Camera Connection Errors
```
Camera error handling:
├── Connection timeout → Show error icon
├── Stream failure → Retry mechanism
├── Authentication error → Show login prompt
├── Network error → Queue retry attempts
└── Hardware error → Disable camera
```

### 19.2 Grid State Recovery
```
State recovery mechanisms:
├── Auto-save grid state periodically
├── Restore from last known good state
├── Validate grid consistency on startup
├── Repair corrupted grid data
└── Fallback to default grid layout
```

## 20. Luồng Performance Monitoring

### 20.1 Performance Metrics
```
Monitoring systems:
├── Video frame rate tracking
├── Memory usage monitoring
├── CPU usage per camera
├── Network bandwidth utilization
└── UI responsiveness metrics
```

### 20.2 Automatic Optimization
```
Auto-optimization features:
├── Reduce video quality khi CPU cao
├── Limit concurrent streams
├── Adaptive grid refresh rates
├── Memory cleanup triggers
└── Background processing throttling
```

## Tổng Kết Luồng Hệ Thống

### Kiến Trúc Tổng Thể:
```
User Interface (QML)
├── CameraGrid.qml → Main grid container
├── GridItem.qml → Individual camera widgets
└── Context menus, controls, dialogs

Backend Logic (Python)
├── GridModel → State management
├── VideoCapture → Stream handling
├── PTZ Controller → Camera control
└── Signal coordination

Data Flow:
User Action → QML Event → Signal → Python Backend →
State Update → Signal → QML Update → UI Refresh
```

### Performance Characteristics:
- **Scalability**: Hỗ trợ tối đa 144 cameras (12x12 grid)
- **Responsiveness**: 60fps UI updates với optimized rendering
- **Memory Efficiency**: Shared resources và lazy loading
- **Network Optimization**: Adaptive streaming và bandwidth management

### Reliability Features:
- **Error Recovery**: Automatic retry và fallback mechanisms
- **State Persistence**: Auto-save và restore capabilities
- **Resource Management**: Proper cleanup và memory management
- **Signal Safety**: Thread-safe signal handling và queuing

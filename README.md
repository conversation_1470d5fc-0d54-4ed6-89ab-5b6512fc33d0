# IVMS (Intelligent Video Management System)

[![Python](https://img.shields.io/badge/python-3.11+-blue.svg)](https://www.python.org/downloads/)
[![PySide6](https://img.shields.io/badge/PySide6-6.0+-green.svg)](https://wiki.qt.io/Qt_for_Python)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

<div align="center">
  <img src="src/assets/images/icon_128.png" alt="IVMS Logo" width="128"/>
  
  *A powerful and intelligent video management system built with Python and PySide6*
</div>

## ✨ Key Features

- 🎥 Real-time video streaming with multi-camera support
- 🧠 Intelligent video analysis
- 🌐 Cross-platform compatibility (Windows, Linux, MacOS)
- 🎨 Modern and customizable user interface
- 🌍 Multi-language support (English, Vietnamese, Russia)
- 🚀 Hardware acceleration support (CUDA)

## 🚀 Quick Start

### Prerequisites

- Python 3.11+
- Git

### Installation

### Auto
## Windows/MacOS/Linux
1. Choose one of the following methods:

   Using Command Prompt:
   ```bash
   bash build_release.sh
   ```

   Using Git Bash or similar Unix shell:
   ```bash
   ./build_release.sh
   ```

##

### Manual
# Clone the repository
git clone https://github.com/yourusername/IVMS.git
cd IVMS
2. Create virtual environment
python -m venv venv
source venv/bin/activate # Linux/MacOS
.\venv\Scripts\activate # Windows
3. Install dependencies
pip install -r /scripts_build/<platform>/requirements.txt

## 💻 Development Setup

### Resource Management
Compile resources
pyside6-rcc resource.qrc -o resources_rc.py
Generate translations
pyside6-lrelease src/languages/qt_en_US.ts
pyside6-lrelease src/languages/qt_vi_VN.ts

### Development Scripts

Start development server
chmod +x run_app.sh
./run_app.sh


## 🏗️ Building

### Using PyInstaller
pip install pyinstaller
pyinstaller /scripts_build/<platform>/VMS.spec


### Using Nuitka build
pip install nuitka zstandard
nuitka3 --standalone --plugin-enable=pyside6 VMS.py

### Platform-Specific Builds

#### Windows
Build executable
nuitka --standalone --disable-console --windows-icon-from-ico=src/assets/images/icon_128.png --plugin-enable=pyside6 VMS.py


#### MacOS
Create DMG
chmod +x build_macos.sh
./build_macos.sh

#### Linux
Create DEB package
chmod +x build_linux.sh
./build_linux.sh

### Docker Build
...


## 📦 Deployment

### Windows Installer
- Download [Advanced Installer](https://drive.google.com/file/d/1QXbfWwuwQwT8_YPaSyxuj8bGDCXQCPo4/view?usp=sharing)
- Follow Windows packaging guidelines

### Linux Package
Install packaging tools
sudo apt-get install ruby-dev build-essential
sudo gem i fpm -f

### MacOS Package
Install packaging tools
brew install create-dmg

## 📚 Documentation
- [User Guide](docs/user-guide.md)
- [API Reference](docs/api-reference.md)
- [Development Guide](docs/development-guide.md)

## 🤝 Contributing
Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## 📄 License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments
- [PySide6](https://wiki.qt.io/Qt_for_Python)
- [VLC](https://www.videolan.org/)
- [FFmpeg](https://ffmpeg.org/)

## 📞 Support
For support, please open an issue or contact the development team.
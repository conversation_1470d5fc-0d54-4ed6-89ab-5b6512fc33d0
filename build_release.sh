#!/bin/bash

# Detect operating system
detect_os() {
    case "$(uname -s)" in
        Darwin*)    echo "macos";;
        MINGW*|MSYS*|CYGWIN*|Windows*)    echo "windows";;
        *)         echo "unsupported";;
    esac
}

OS=$(detect_os)

case $OS in
    "macos")
        echo "Running macOS build script..."
        chmod +x scripts_build/macos/build.sh
        ./scripts_build/macos/build.sh
        ;;
    "windows")
        echo "Running Windows build script with administrator privileges..."
        # Create a temporary VBS script to request admin privileges
        echo 'Set UAC = CreateObject("Shell.Application")' > elevate.vbs
        echo 'UAC.ShellExecute "cmd", "/c cd /d """ & WScript.Arguments(0) & """ && build.bat", "", "runas", 1' >> elevate.vbs
        
        # Execute the VBS script with the current directory as argument
        cscript //nologo elevate.vbs "%CD%\scripts_build\windows"
        
        # Clean up the temporary VBS script
        rm elevate.vbs
        ;;
    *)
        echo "Unsupported operating system"
        exit 1
        ;;
esac 
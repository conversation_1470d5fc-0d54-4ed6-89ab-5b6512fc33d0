# syntax=docker/dockerfile:1.3
FROM ubuntu:20.04
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive
ENV DISPLAY=:99
ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN apt-get update
RUN apt-get install -y curl \
                        wget \
                        build-essential \
                        libncursesw5-dev \
                        libssl-dev \
                        libsqlite3-dev \
                        tk-dev \
                        libgdbm-dev \
                        libc6-dev \
                        libbz2-dev \
                        libffi-dev \
                        zlib1g-dev \
                        software-properties-common 

# Install python3.12
RUN add-apt-repository -y ppa:deadsnakes/ppa
RUN apt-get install -y python3.12 \
                        python3-dev \
                        libpython3.12-dev \
                        python3.12-distutils

# Install pip3.12
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.12

# Change default python to 3.12
RUN update-alternatives --install /usr/bin/python python /usr/bin/python3.12 1; \
    update-alternatives --install /usr/bin/python python /usr/bin/python3.8 2; \
    update-alternatives  --set python /usr/bin/python3.12; \
    update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.12 1; \
    update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.8 2; \
    update-alternatives  --set python3 /usr/bin/python3.12

# Use the official Windows image with PowerShell
FROM mcr.microsoft.com/windows/servercore:ltsc2022

# Set the working directory to the directory of the script
WORKDIR /app

# Download and install Python 3.11
# Download Python 3.11 installer
# Use PowerShell as the default shell and set preferences
SHELL ["powershell", "-Command", "$ErrorActionPreference = 'Stop'; $ProgressPreference = 'SilentlyContinue';"]

# Download and install Python 3.11
# Download Python 3.11 installer
RUN Invoke-WebRequest -Uri https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe -OutFile python-installer.exe -UseBasicParsing;

# # Install Python 3.11
# RUN powershell -Command Start-Process -Wait -FilePath python-installer.exe -ArgumentList '/quiet InstallAllUsers=1 PrependPath=1';

# # Clean up installer
# RUN powershell -Command Remove-Item -Path python-installer.exe;

# # Add Python to the system PATH
# RUN setx PATH "%PATH%;C:\Python311"

# # Copy the script into the container
# COPY build_windows_pyinstaller.sh ./

# # Empty the specified directories
# RUN Remove-Item -Recurse -Force ./build/, ./dist/, ./build_dist/, ./VMS.app/, ./VMS.dist/, ./VMS.build/ -ErrorAction SilentlyContinue

# # Set the environment variables for Advanced Installer
# ENV AI_EXECUTABLE="C:\Program Files (x86)\Caphyon\Advanced Installer 21.0.1\bin\x86\AdvancedInstaller.com"
# # Updated project file path
# ENV PROJECT_FILE="iVMS.aip"
# # Updated output directory path
# ENV OUTPUT_DIR="/app"

# # Print environment variables (optional, for debugging)
# RUN echo $Env:AI_EXECUTABLE
# RUN echo $Env:PROJECT_FILE
# RUN echo $Env:OUTPUT_DIR

# # Install PowerShell for running PowerShell commands
# RUN powershell -Command Install-PackageProvider -Name NuGet -Force -Scope Global -Verbose; \
#     Install-Module -Name PowerShellGet -Force -Scope Global -Verbose; \
#     Install-Script -Name PSADT -Force -Scope Global -Verbose

# # Build the MSI package using PowerShell
# RUN powershell -Command "& '$Env:AI_EXECUTABLE' /build '$Env:PROJECT_FILE'"

# Continue with any post-build steps if needed

# Clean up any temporary files or directories if necessary

# Add more steps here as needed.

# The command to run your application (if applicable)
# CMD ["./your_application_executable"]

version: "3.9"

x-common-settings: &common_settings
  logging:
    options:
      max-size: "2048m"

services:
  vms_build_environment:
    <<: *common_settings
    image: ubuntu-20.04-python-3.11
    build:
      context: .
      dockerfile: Dockerfile
    command: tail -f
    volumes:
      - ./:/code

  vms_build_environment_windows_build:
    <<: *common_settings
    image: mcr.microsoft.com/windows/servercore:ltsc2022
    build:
      context: .
      dockerfile: Dockerfile_Windows  # Specify the Windows Dockerfile
    command: tail -f
    volumes:
      - ./:/code

# Quy Tắc Commit Code

## Format Commit

```bash
git commit -m "<tên>: <nội dung>"
```

### Ví dụ
```bash
# Thêm tính năng mới
git commit -m "thanh.nguyen: Add Custom Slider"
git commit -m "tung.vu: Add Grid View"

# Sửa lỗi
git commit -m "chienpm: Fix Logo Position"

# Cập nhật
git commit -m "tung.vu: Update Animation Blur Effect"
```

### Quy Tắc

1. **Format Tên**
   - Sử dụng tên đầy đủ: `firstname.lastname`
   - Đặt ở đầu commit message
   - Kết thúc bằng dấu hai chấm và khoảng trắng

2. **Format Nội Dung**
   - Bắt đầu bằng chữ in hoa
   - Dùng thì hiện tại
   - Ngắn gọn, rõ ràng
   - Không dùng dấu chấm ở cuối

3. **<PERSON><PERSON><PERSON> Loại Commit Thường Dùng**
   - Add: Thêm tính năng mới
   - Update: Cập nhật tính năng
   - Fix: Sửa lỗi
   - Remove: Xóa tính năng
   - Refactor: Cải thiện code

### Ví Dụ Sai và Đúng

```bash
# ❌ Sai: Thiếu tên
git commit -m "Add Slider"

# ❌ Sai: Format tên không đúng
git commit -m "Thanh: Add Slider"

# ❌ Sai: Thiếu dấu hai chấm
git commit -m "thanh.nguyen Add Slider"

# ✅ Đúng
git commit -m "thanh.nguyen: Add Custom Slider"
```

## Quy Trình Commit

1. **Trước Khi Commit**
   - Kiểm tra branch đang làm việc
   - Chạy test
   - Kiểm tra lỗi lint
   - Stage các file cần thiết

2. **Khi Commit**
   - Dùng `git commit -v` để xem diff
   - Viết message theo format
   - Commit từng thay đổi riêng biệt

3. **Sau Khi Commit**
   - Kiểm tra lại message
   - Push lên remote
   - Tạo pull request nếu cần

## Các Lỗi Thường Gặp

1. **Thiếu Tên**
   - ❌ "Add Slider"
   - ✅ "thanh.nguyen: Add Slider"

2. **Format Tên Sai**
   - ❌ "Thanh: Add Slider"
   - ✅ "thanh.nguyen: Add Slider"

3. **Thiếu Dấu Hai Chấm**
   - ❌ "thanh.nguyen Add Slider"
   - ✅ "thanh.nguyen: Add Slider"

4. **Dùng Thì Sai**
   - ❌ "thanh.nguyen: Added Slider"
   - ✅ "thanh.nguyen: Add Slider"

## Quy Tắc Commit Chi Tiết

### Loại Commit
Mỗi commit phải thuộc một trong các loại sau:

- `feat`: Tính năng mới
- `fix`: Sửa lỗi
- `docs`: Thay đổi tài liệu
- `style`: Thay đổi định dạng code
- `refactor`: Cải thiện code
- `perf`: Cải thiện hiệu suất
- `test`: Thêm hoặc sửa test
- `build`: Thay đổi hệ thống build
- `ci`: Thay đổi cấu hình CI
- `chore`: Các thay đổi khác

### Phạm Vi
Phạm vi là tùy chọn, chỉ định vị trí thay đổi. Ví dụ:
- `feat(camera)`: Tính năng camera mới
- `fix(ui)`: Sửa lỗi giao diện
- `refactor(auth)`: Cải thiện hệ thống xác thực

### Nội Dung
Nội dung commit cần:
- Dùng thì hiện tại
- Không viết hoa chữ đầu
- Không dùng dấu chấm cuối
- Không quá 72 ký tự

### Thân Commit
Thân commit cần:
- Dùng thì hiện tại
- Giải thích lý do thay đổi
- So sánh với hành vi cũ
- Giới hạn 72 ký tự mỗi dòng

### Chân Commit
Chân commit cần:
- Liệt kê các thay đổi lớn
- Tham chiếu các issue đã đóng
- Format: `Fixes #123, Closes #456`

## Ví Dụ Chi Tiết

### Tính Năng Mới
```
feat(camera): thêm điều khiển PTZ camera

- Thêm giao diện điều khiển PTZ
- Thêm chức năng pan/tilt/zoom
- Thêm quản lý preset camera
```

### Sửa Lỗi
```
fix(streaming): sửa lỗi rò rỉ bộ nhớ trong video stream

- Sửa lỗi rò rỉ khi chuyển camera
- Thêm dọn dẹp trong destructor
- Cải thiện xử lý lỗi

Fixes #123
```

### Tài Liệu
```
docs(readme): cập nhật hướng dẫn cài đặt

- Thêm yêu cầu Python 3.8+
- Cập nhật danh sách dependency
- Thêm phần xử lý sự cố
```

### Định Dạng
```
style(ui): định dạng code camera widget

- Áp dụng thụt lề nhất quán
- Sửa vấn đề độ dài dòng
- Xóa import không dùng
```

## Công Cụ Hỗ Trợ

1. **Git Hooks**
   - Sử dụng pre-commit hooks để kiểm tra format
   - Dùng commitlint để kiểm tra message

2. **Tiện Ích IDE**
   - GitLens cho VS Code
   - Git Commit Template cho các IDE

3. **Mẫu Commit**
   - Cấu hình git để dùng mẫu commit
   - Dùng công cụ như commitizen 
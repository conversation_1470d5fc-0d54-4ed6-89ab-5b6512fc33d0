<PERSON><PERSON> tạo và chạy 1 bài AI:

trướ<PERSON> tiên tạo polygon
# create Polygon: /api/polygons
{"name": "qq", "type": "ZONE_DETECT", "polygon": "[[330, 276], [1173, 246], [1584, 885], [345, 798]]", "active": true}
Không truyền vào aiFlowId

sau đó muốn gán các polygon vào danh sách AI
# add-polygons: /api/aiflows/add-polygons
{
  "id": "urn:uuid:84ae7ada-c565-53e5-6dc4-24499b527b7e", #id ai flow
  "ids": [
    "2c08b8bb-5d58-d7e3-912e-4efbac78f644", # id zone 1
    "5334cab6-cc00-befc-5d36-b42662e350c2" #id zone 2
  ]
}


cu<PERSON><PERSON> cùng muốn apply ai flow
# apply AiFlow /api/cameras/apply-ai-flow
{
  "id": "0ff8d485-7ab7-4366-9104-3a39725f66c8",
  "apply": true,
  "type": "RECOGNITION"
}

Ví dụ nếu chạy tất cả phần trên trong 1 request thì phải làm cơ chế batch
nếu có 5 zone -> chạy create Polygon 5 lần.

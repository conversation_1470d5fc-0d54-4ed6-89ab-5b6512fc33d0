# write script python get all file .py in folder and subfolder
# then print relative path of file and each path with a space
# except in /env, /dist, /build, /node_modules, /venv, /__pycache__, /pyjoystick folder

import os

def get_all_file_py(path):
    for root, dirs, files in os.walk(path):
        for file in files:
            if file.endswith('.py') and '__pycache__' not in root and 'env' not in root and 'dist' not in root and 'build' not in root and 'node_modules' not in root and 'venv' not in root and 'pyjoystick' not in root:
                # and use / instead of \ in path
                final_path = os.path.relpath(os.path.join(root, file), path).replace('\\', '/')
                # write to file, each path with a space
                with open('list_file_py.txt', 'a') as f:
                    f.write(final_path + ' ')

get_all_file_py('.')
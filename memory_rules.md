# Memory Rules - QML Coding Standards & Python Architecture

## QML Coding Standards

### Component Structure Order
Always structure QML components in this exact order:

1. **ID and basic properties** (width, height, anchors)
2. **Custom properties** (property int/string/var/etc)
3. **Signals** (signal clicked/completed/etc)
4. **States and transitions**
5. **Functions**
6. **Event handlers and connections** (Component.onCompleted, Connections)
7. **Child items**

### Naming Conventions
- **IDs**: camelCase, root for the main component
- **Properties**: camelCase, descriptive names
- **Signals**: camelCase, past tense verbs (clicked, completed)
- **Functions**: camelCase, start with verbs (calculate, update)

### Formatting
- Use 4 spaces for indentation
- Group related properties together
- Add empty line between major sections
- Maximum line length: 100 characters

### QML Example Structure
```qml
Rectangle {
    // 1. ID and basic properties
    id: root
    width: 200
    height: 100
    anchors.centerIn: parent

    // 2. Custom properties
    property string title: "Default"
    property color backgroundColor: "white"
    property int counter: 0

    // 3. Signals
    signal clicked(var item)
    signal completed()

    // 4. States and transitions
    states: [
        State { name: "normal"; PropertyChanges { target: root; opacity: 1.0 } },
        State { name: "disabled"; PropertyChanges { target: root; opacity: 0.5 } }
    ]

    // 5. Functions
    function reset() {
        counter = 0
        state = "normal"
    }

    // 6. Event handlers and connections
    Component.onCompleted: {
        console.log("Component initialized")
    }

    // 7. Child items
    Text {
        id: label
        text: root.title
        anchors.centerIn: parent
    }
}
```

## Python Architecture Standards

### Base Model Pattern
```python
from PySide6.QtCore import QObject, Property, Signal, Slot, QAbstractListModel, QModelIndex, Qt
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
import logging

logger = logging.getLogger(__name__)

@dataclass
class ModelData:
    """Immutable data class for storing model data"""
    id: str
    name: str
    status: str = "inactive"
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation"""
        return {
            "id": self.id,
            "name": self.name,
            "status": self.status,
            "metadata": self.metadata.copy()
        }


class BaseModel(QObject):
    """Base model class with common functionality"""

    # Signals
    dataChanged = Signal()
    errorOccurred = Signal(str)

    def __init__(self, parent: Optional[QObject] = None):
        """Initialize the base model"""
        super().__init__(parent)
        self._id: Optional[str] = None
        self._name: Optional[str] = None
        self._is_valid: bool = False
        self._error_message: str = ""

    # Properties with type hints and notifications
    @Property(str, notify=dataChanged)
    def id(self) -> str:
        """Get the model ID"""
        return self._id or ""

    @id.setter
    def id(self, value: str) -> None:
        """Set the model ID"""
        if self._id != value:
            self._id = value
            self.dataChanged.emit()

    @Property(str, notify=dataChanged)
    def name(self) -> str:
        """Get the model name"""
        return self._name or ""

    @name.setter
    def name(self, value: str) -> None:
        """Set the model name"""
        if self._name != value:
            self._name = value
            self.dataChanged.emit()

    @Property(bool, notify=dataChanged)
    def is_valid(self) -> bool:
        """Check if the model is valid"""
        return self._is_valid

    @Property(str, notify=errorOccurred)
    def error_message(self) -> str:
        """Get the current error message"""
        return self._error_message

    def validate(self) -> bool:
        """Validate the model data"""
        self._is_valid = bool(self._id and self._name)
        if not self._is_valid:
            self._error_message = "ID and name are required"
            self.errorOccurred.emit(self._error_message)
        return self._is_valid

    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update model from dictionary"""
        if not data:
            logger.warning("Attempted to update model with empty data")
            return

        try:
            if "id" in data:
                self.id = data["id"]
            if "name" in data:
                self.name = data["name"]

            # Additional properties can be updated here
            self.validate()

        except Exception as e:
            logger.error(f"Error updating model from dict: {e}")
            self._error_message = f"Update failed: {str(e)}"
            self.errorOccurred.emit(self._error_message)

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "name": self.name,
            "is_valid": self.is_valid
        }
```

### Collection Model Pattern
```python
class CollectionModel(QAbstractListModel):
    """Model for collections of items"""

    # Define roles
    IdRole = Qt.UserRole + 1
    NameRole = Qt.UserRole + 2
    DataRole = Qt.UserRole + 3

    # Signals
    collectionChanged = Signal()
    itemAdded = Signal(str)
    itemRemoved = Signal(str)

    def __init__(self, parent: Optional[QObject] = None):
        """Initialize the collection model"""
        super().__init__(parent)
        self._items: List[BaseModel] = []

    def roleNames(self) -> Dict[int, bytes]:
        """Define role names for QML"""
        return {
            self.IdRole: b"id",
            self.NameRole: b"name",
            self.DataRole: b"modelData"
        }

    def rowCount(self, parent: QModelIndex = QModelIndex()) -> int:
        """Return the number of rows"""
        return len(self._items)

    def data(self, index: QModelIndex, role: int = Qt.DisplayRole) -> Any:
        """Get data for the specified index and role"""
        if not index.isValid() or index.row() >= len(self._items):
            return None

        item = self._items[index.row()]

        if role == self.IdRole:
            return item.id
        elif role == self.NameRole:
            return item.name
        elif role == self.DataRole:
            return item

        return None

    @Slot(BaseModel)
    def add_item(self, item: BaseModel) -> None:
        """Add an item to the collection"""
        if not item or not item.is_valid:
            logger.warning("Attempted to add invalid item to collection")
            return

        # Check for duplicates
        for existing in self._items:
            if existing.id == item.id:
                logger.warning(f"Item with ID {item.id} already exists in collection")
                return

        # Add the item
        self.beginInsertRows(QModelIndex(), len(self._items), len(self._items))
        self._items.append(item)
        self.endInsertRows()

        # Connect signals
        item.dataChanged.connect(self._handle_item_changed)

        # Emit signals
        self.itemAdded.emit(item.id)
        self.collectionChanged.emit()

    @Slot(str)
    def remove_item(self, item_id: str) -> bool:
        """Remove an item from the collection by ID"""
        for i, item in enumerate(self._items):
            if item.id == item_id:
                self.beginRemoveRows(QModelIndex(), i, i)
                removed = self._items.pop(i)
                self.endRemoveRows()

                # Disconnect signals
                removed.dataChanged.disconnect(self._handle_item_changed)

                # Emit signals
                self.itemRemoved.emit(item_id)
                self.collectionChanged.emit()
                return True

        logger.warning(f"Attempted to remove non-existent item with ID {item_id}")
        return False

    @Slot(str, result=BaseModel)
    def get_item_by_id(self, item_id: str) -> Optional[BaseModel]:
        """Get an item by ID"""
        for item in self._items:
            if item.id == item_id:
                return item
        return None

    def _handle_item_changed(self) -> None:
        """Handle changes in collection items"""
        sender = self.sender()
        if sender in self._items:
            index = self._items.index(sender)
            model_index = self.index(index, 0)
            self.dataChanged.emit(model_index, model_index, list(self.roleNames().keys()))
            self.collectionChanged.emit()
```

## Nguyên Tắc Thiết Kế

### SOLID Principles
- **Single Responsibility**: Mỗi class chỉ có một lý do để thay đổi
- **Open/Closed**: Mở cho mở rộng, đóng cho sửa đổi
- **Liskov Substitution**: Các đối tượng con có thể thay thế đối tượng cha
- **Interface Segregation**: Không phụ thuộc vào interface không sử dụng
- **Dependency Inversion**: Phụ thuộc vào abstraction, không phụ thuộc vào concrete

### Các Nguyên Tắc Khác
- **DRY (Don't Repeat Yourself)**: Tránh lặp lại code
- **Separation of Concerns**: Tách biệt các mối quan tâm
- **Dependency Injection**: Sử dụng dependency injection để giảm sự phụ thuộc
- **Defensive Programming**: Lập trình phòng thủ với xử lý lỗi toàn diện

### Controller Pattern
```python
from PySide6.QtCore import QObject, Signal, Slot, QThreadPool, QRunnable, QTimer
from typing import Dict, List, Any, Optional, Callable, Union
import logging
import traceback
import uuid

logger = logging.getLogger(__name__)

class WorkerSignals(QObject):
    """Signals for worker thread communication"""
    finished = Signal()
    error = Signal(str, str)  # error_type, error_message
    result = Signal(object)
    progress = Signal(int)


class Worker(QRunnable):
    """Worker thread for background tasks"""

    def __init__(self, fn: Callable, *args, **kwargs):
        super().__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()
        self.task_id = str(uuid.uuid4())

    def run(self):
        """Run the worker function"""
        try:
            result = self.fn(*self.args, **self.kwargs)
            self.signals.result.emit(result)
        except Exception as e:
            logger.error(f"Error in worker thread: {str(e)}")
            logger.error(traceback.format_exc())
            self.signals.error.emit(type(e).__name__, str(e))
        finally:
            self.signals.finished.emit()


class BaseController(QObject):
    """Base controller with common functionality"""

    # Signals
    operationStarted = Signal(str)  # operation_id
    operationCompleted = Signal(str, bool, object)  # operation_id, success, result
    operationError = Signal(str, str, str)  # operation_id, error_type, error_message
    operationProgress = Signal(str, int)  # operation_id, progress_percentage

    def __init__(self, parent: Optional[QObject] = None):
        """Initialize the base controller"""
        super().__init__(parent)
        self._thread_pool = QThreadPool()
        self._operations: Dict[str, Dict[str, Any]] = {}
        self._models: Dict[str, Any] = {}
        self._is_initialized: bool = False

        # Configure thread pool
        max_threads = min(8, QThreadPool.globalInstance().maxThreadCount())
        self._thread_pool.setMaxThreadCount(max_threads)

        logger.info(f"Controller initialized with {max_threads} worker threads")

    def initialize(self) -> None:
        """Initialize the controller"""
        if self._is_initialized:
            logger.warning("Controller already initialized")
            return

        try:
            self._setup_connections()
            self._load_initial_data()
            self._is_initialized = True
            logger.info("Controller successfully initialized")
        except Exception as e:
            logger.error(f"Failed to initialize controller: {str(e)}")
            logger.error(traceback.format_exc())

    def _setup_connections(self) -> None:
        """Set up signal connections"""
        # Override in subclasses
        pass

    def _load_initial_data(self) -> None:
        """Load initial data"""
        # Override in subclasses
        pass

    @Slot(str, result=QObject)
    def get_model(self, model_id: str) -> Optional[QObject]:
        """Get a model by ID"""
        return self._models.get(model_id)

    @Slot(str, str, object)
    def update_model(self, model_id: str, property_name: str, value: Any) -> None:
        """Update a model property"""
        model = self._models.get(model_id)
        if model and hasattr(model, property_name):
            setattr(model, property_name, value)
            logger.debug(f"Updated model {model_id}, property {property_name} = {value}")
        else:
            logger.warning(f"Failed to update model {model_id}, property {property_name} not found")

    def run_async(self, fn: Callable, callback: Optional[Callable] = None,
                  error_callback: Optional[Callable] = None, *args, **kwargs) -> str:
        """Run a function asynchronously"""
        operation_id = str(uuid.uuid4())

        # Create worker
        worker = Worker(fn, *args, **kwargs)
        worker.task_id = operation_id

        # Store operation info
        self._operations[operation_id] = {
            "start_time": QTimer.currentTime(),
            "status": "running",
            "worker": worker
        }

        # Connect signals
        if callback:
            worker.signals.result.connect(callback)

        if error_callback:
            worker.signals.error.connect(error_callback)

        # Connect to controller signals
        worker.signals.result.connect(
            lambda result: self.operationCompleted.emit(operation_id, True, result))
        worker.signals.error.connect(
            lambda error_type, error_msg: self.operationError.emit(operation_id, error_type, error_msg))
        worker.signals.finished.connect(
            lambda: self._cleanup_operation(operation_id))

        # Start the worker
        self.operationStarted.emit(operation_id)
        self._thread_pool.start(worker)

        return operation_id

    def _cleanup_operation(self, operation_id: str) -> None:
        """Clean up completed operation"""
        if operation_id in self._operations:
            self._operations[operation_id]["status"] = "completed"
            # Keep operation info for a while for debugging
            QTimer.singleShot(60000, lambda: self._operations.pop(operation_id, None))


class ApplicationController(BaseController):
    """Main application controller"""

    # Singleton pattern
    _instance = None

    @classmethod
    def instance(cls) -> 'ApplicationController':
        """Get the singleton instance"""
        if cls._instance is None:
            cls._instance = ApplicationController()
        return cls._instance

    def __init__(self, parent: Optional[QObject] = None):
        """Initialize the application controller"""
        if ApplicationController._instance is not None:
            raise RuntimeError("ApplicationController is a singleton, use ApplicationController.instance() instead")

        super().__init__(parent)
        self._sub_controllers: Dict[str, BaseController] = {}

    def register_controller(self, name: str, controller: BaseController) -> None:
        """Register a sub-controller"""
        if name in self._sub_controllers:
            logger.warning(f"Controller {name} already registered, replacing")

        self._sub_controllers[name] = controller
        logger.info(f"Registered controller: {name}")

    @Slot(str, result=QObject)
    def get_controller(self, name: str) -> Optional[BaseController]:
        """Get a sub-controller by name"""
        return self._sub_controllers.get(name)

    @Slot()
    def initialize_all(self) -> None:
        """Initialize all controllers"""
        logger.info("Initializing all controllers")

        # Initialize this controller first
        if not self._is_initialized:
            self.initialize()

        # Initialize sub-controllers
        for name, controller in self._sub_controllers.items():
            try:
                if not getattr(controller, "_is_initialized", False):
                    logger.info(f"Initializing controller: {name}")
                    controller.initialize()
            except Exception as e:
                logger.error(f"Failed to initialize controller {name}: {str(e)}")
                logger.error(traceback.format_exc())
```

### Lợi ích của Cấu Trúc Này
- **Tách biệt rõ ràng**: Phân tách rõ ràng giữa dữ liệu, logic và giao diện
- **Xử lý bất đồng bộ**: Hỗ trợ xử lý bất đồng bộ với QThreadPool
- **Quản lý lỗi**: Xử lý lỗi toàn diện với logging và signals
- **Type hints**: Sử dụng type hints để tăng tính rõ ràng và hỗ trợ IDE
- **Singleton pattern**: Triển khai singleton pattern đúng cách
- **Tài liệu**: Docstrings đầy đủ cho tất cả các lớp và phương thức
- **Logging**: Tích hợp logging để dễ dàng gỡ lỗi

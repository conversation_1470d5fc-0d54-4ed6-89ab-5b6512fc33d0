<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="../../ver20/util/onvif-wsdl-viewer.xsl"?>
<!--
Copyright (c) 2010-2013 by ONVIF: Open Network Video Interface Forum. All rights reserved.

Recipients of this document may copy, distribute, publish, or display this
document so long as this copyright notice, license and disclaimer are
retained with all copies of the document.

THIS DOCUMENT IS PROVIDED "AS IS," AND THE CORPORATION AND ITS MEMBERS AND
THEIR AFFILIATES, MAKE NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED,
INCLUDING BUT NOT LIMITED TO, WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, OR TITLE;
THAT THE CONTENTS OF THIS DOCUMENT ARE SUITABLE FOR ANY PURPOSE;
OR THAT THE IMPLEMENTATION OF SUCH CONTENTS WILL NOT INFRINGE ANY PATENTS,
COPYRIGHTS, TRADEMARKS OR OTHER RIGHTS.
IN NO EVENT WILL THE CORPORATION OR ITS MEMBERS OR THEIR AFFILIATES BE LIABLE
FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL, PUNITIVE OR CONSEQUENTIAL
DAMAGES, ARISING OUT OF OR RELATING TO ANY USE OR DISTRIBUTION OF THIS
DOCUMENT, WHETHER OR NOT (1) THE CORPORATION, MEMBERS OR THEIR AFFILIATES
HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES, OR (2) SUCH DAMAGES
WERE REASONABLY FORESEEABLE, AND ARISING OUT OF OR RELATING TO ANY USE OR
DISTRIBUTION OF THIS DOCUMENT.
THE FOREGOING DISCLAIMER AND LIMITATION ON LIABILITY DO NOT APPLY TO,
INVALIDATE, OR LIMIT REPRESENTATIONS AND WARRANTIES MADE BY THE MEMBERS
AND THEIR RESPECTIVE AFFILIATES TO THE CORPORATION AND OTHER MEMBERS IN
CERTAIN WRITTEN POLICIES OF THE CORPORATION.
-->
<wsdl:definitions name="PACSService" targetNamespace="http://www.onvif.org/ver10/accesscontrol/wsdl"
  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap12/"
  xmlns:xs="http://www.w3.org/2001/XMLSchema"
  xmlns:tac="http://www.onvif.org/ver10/accesscontrol/wsdl"
  >
  <wsdl:types>
    <xs:schema targetNamespace="http://www.onvif.org/ver10/accesscontrol/wsdl"
     xmlns:xs="http://www.w3.org/2001/XMLSchema"
     xmlns:pt="http://www.onvif.org/ver10/pacs"
     xmlns:tac="http://www.onvif.org/ver10/accesscontrol/wsdl"
     elementFormDefault="qualified"
     version="1.0">
      <xs:import namespace="http://www.onvif.org/ver10/pacs" schemaLocation="types.xsd"/>
      <!--====== types ======-->
      <xs:complexType name="ServiceCapabilities">
      <xs:annotation>
        <xs:documentation>
The service capabilities reflect optional functionality of a service.
The information is static and does not change during device operation.
The following capabilities are available:
</xs:documentation>
      </xs:annotation>
        <xs:sequence>
          <xs:any namespace="##any" minOccurs="0" maxOccurs="unbounded" processContents="lax"/>
        </xs:sequence>
        <xs:attribute name="MaxLimit" type="xs:unsignedInt" use="required"><xs:annotation>
            <xs:documentation>The maximum number of entries returned by a single GetList request. The device shall never return more than this number of entities in a single response.</xs:documentation>
          </xs:annotation></xs:attribute>
        <xs:anyAttribute processContents="lax"/>
      </xs:complexType>
      <!--===============================-->
      <xs:complexType name="AccessPointInfoBase">
      <xs:annotation>
        <xs:documentation>
Used as extension base for AccessPointInfo.
</xs:documentation>
      </xs:annotation>
        <xs:complexContent>
          <xs:extension base="pt:DataEntity">
            <xs:sequence>
              <xs:element name="Name" type="pt:Name"><xs:annotation>
                  <xs:documentation>A user readable name. It shall be up to 64 characters.</xs:documentation>
                </xs:annotation></xs:element>
              <xs:element name="Description" type="pt:Description" minOccurs="0"><xs:annotation>
                  <xs:documentation>Optional user readable description for the AccessPoint. It shall be up to 1024 characters.</xs:documentation>
                </xs:annotation></xs:element>
              <xs:element name="AreaFrom" type="pt:ReferenceToken" minOccurs="0"><xs:annotation>
                  <xs:documentation>Optional reference to the Area from which access is requested.</xs:documentation>
                </xs:annotation></xs:element>
              <xs:element name="AreaTo" type="pt:ReferenceToken" minOccurs="0"><xs:annotation>
                  <xs:documentation>Optional reference to the Area to which access is requested.</xs:documentation>
                </xs:annotation></xs:element>
              <xs:element name="EntityType" type="xs:QName" minOccurs="0"><xs:annotation>
                  <xs:documentation>Optional entity type; if missing, a Door type as defined by the ONVIF DoorControl service should be assumed. This can also be represented by the QName value "tdc:Door" - where tdc is the namespace of the Door Control service: &quot;http://www.onvif.org/ver10/doorcontrol/wsdl&quot;. This field is provided for future extensions; it will allow an AccessPoint being extended to cover entity types other than Doors as well.</xs:documentation>
                </xs:annotation></xs:element>
              <xs:element name="Entity" type="pt:ReferenceToken"><xs:annotation>
                  <xs:documentation>Reference to the entity used to control access; the entity type may be specified by the optional EntityType field explained below but is typically a Door.</xs:documentation>
                </xs:annotation></xs:element>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <!--===============================-->
      <xs:complexType name="AccessPointInfo">
      <xs:annotation>
        <xs:documentation>
The AccessPointInfo structure contains basic information about an AccessPoint instance.
An AccessPoint defines an entity a Credential can be granted or denied access to. The
AccessPointInfo provides basic information on how access is controlled in one direction for a
door (from which area to which area).
&lt;/p&gt;&lt;p&gt;
 door is the typical device involved, but other type of
devices may be supported as well.
Multiple AccessPoints may cover the same Door.
A typical case is one AccessPoint for entry and another for exit, both referencing
the same Door.
&lt;/p&gt;&lt;p&gt;

An ONVIF compliant device shall provide the following fields for each AccessPoint instance:
</xs:documentation>
      </xs:annotation>
        <xs:complexContent>
          <xs:extension base="tac:AccessPointInfoBase">
            <xs:sequence>
              <xs:element name="Capabilities" type="tac:AccessPointCapabilities"><xs:annotation>
                  <xs:documentation>The capabilities for the AccessPoint.</xs:documentation>
                </xs:annotation></xs:element>
              <xs:any namespace="##any" minOccurs="0" maxOccurs="unbounded" processContents="lax"/>
            </xs:sequence>
            <xs:anyAttribute processContents="lax"/>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <!--===============================-->
      <xs:complexType name="AccessPointCapabilities">
      <xs:annotation>
        <xs:documentation>
The AccessPoint capabilities reflect optional functionality of a particular physical entity.
Different AccessPoint instances may have different set of capabilities. This information may
change during device operation, e.g. if hardware settings are changed.
The following capabilities are available:</xs:documentation>
      </xs:annotation>
        <xs:sequence>
          <xs:any namespace="##any" minOccurs="0" maxOccurs="unbounded" processContents="lax"/>
        </xs:sequence>
        <xs:attribute name="DisableAccessPoint" type="xs:boolean" use="required"><xs:annotation>
            <xs:documentation>Indicates whether or not this AccessPoint instance supports EnableAccessPoint and DisableAccessPoint commands.</xs:documentation>
          </xs:annotation></xs:attribute>
        <xs:attribute name="Duress" type="xs:boolean"><xs:annotation>
            <xs:documentation>Indicates whether or not this AccessPoint instance supports generation of duress events.</xs:documentation>
          </xs:annotation></xs:attribute>
        <xs:attribute name="AnonymousAccess" type="xs:boolean"><xs:annotation>
            <xs:documentation>Indicates whether or not this AccessPoint has a REX switch or other input that allows anonymous access.</xs:documentation>
          </xs:annotation></xs:attribute>
        <xs:attribute name="AccessTaken" type="xs:boolean"><xs:annotation>
            <xs:documentation>Indicates whether or not this AccessPoint instance supports generation of AccessTaken and AccessNotTaken events. If AnonymousAccess and AccessTaken are both true, it indicates that the Anonymous versions of AccessTaken and AccessNotTaken are supported.</xs:documentation>
          </xs:annotation></xs:attribute>
        <xs:attribute name="ExternalAuthorization" type="xs:boolean"><xs:annotation>
            <xs:documentation>Indicates whether or not this AccessPoint instance supports the ExternalAuthorization operation and the generation of Request events. If AnonymousAccess and ExternalAuthorization are both true, it indicates that the Anonymous version is supported as well.</xs:documentation>
          </xs:annotation></xs:attribute>
        <xs:anyAttribute processContents="lax"/>
      </xs:complexType>
      <!--===============================-->
      <xs:complexType name="AreaInfoBase">
      <xs:annotation>
        <xs:documentation>
Basic information about an Area. Used as extension base.
</xs:documentation>
      </xs:annotation>
        <xs:complexContent>
          <xs:extension base="pt:DataEntity">
            <xs:sequence>
              <xs:element name="Name" type="pt:Name"><xs:annotation>
                  <xs:documentation>User readable name. It shall be up to 64 characters.</xs:documentation>
                </xs:annotation></xs:element>
              <xs:element name="Description" type="pt:Description" minOccurs="0"><xs:annotation>
                  <xs:documentation>User readable description for the Area. It shall be up to 1024 characters.</xs:documentation>
                </xs:annotation></xs:element>
            </xs:sequence>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <!--===============================-->
      <xs:complexType name="AreaInfo">
      <xs:annotation>
        <xs:documentation>
The AreaInfo structure contains basic information about an Area.
An ONVIF compliant device shall provide the following fields for each Area:
</xs:documentation>
      </xs:annotation>
        <xs:complexContent>
          <xs:extension base="tac:AreaInfoBase">
            <xs:sequence>
            </xs:sequence>
            <xs:anyAttribute processContents="lax"/>
          </xs:extension>
        </xs:complexContent>
      </xs:complexType>
      <!--===============================-->
      <xs:complexType name="AccessPointState">
      <xs:annotation>
        <xs:documentation>
The AccessPointState contains state information for an AccessPoint.
An ONVIF compliant device shall provide the following fields for each AccessPoint instance:
</xs:documentation>
      </xs:annotation>
        <xs:sequence>
          <xs:element name="Enabled" type="xs:boolean"><xs:annotation>
              <xs:documentation>Indicates that the AccessPoint is enabled. By default this field value shall be True, if the DisableAccessPoint capabilities is not supported.</xs:documentation>
            </xs:annotation></xs:element>
          <xs:any namespace="##any" minOccurs="0" maxOccurs="unbounded" processContents="lax"/>
        </xs:sequence>
        <xs:anyAttribute processContents="lax"/>
      </xs:complexType>
      <!--===============================-->
      <xs:simpleType name="Decision">
      <xs:annotation>
        <xs:documentation>
The Decision enumeration represents a choice of two available options for an access request:
</xs:documentation>
      </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="Granted">
            <xs:annotation><xs:documentation>The decision is to grant access.</xs:documentation></xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Denied">
            <xs:annotation><xs:documentation>The decision is to deny access.</xs:documentation></xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <!--===============================-->
      <xs:simpleType name="DenyReason">
      <xs:annotation>
        <xs:documentation>
Non-normative enum that describes the various reasons for denying access.
The following strings shall be used for the reason field:
</xs:documentation>
      </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CredentialNotEnabled">
            <xs:annotation><xs:documentation>The device shall provide the following event, whenever a valid credential is not enabled or has been disabled (e.g., due to credential being lost etc.) to prevent unauthorized entry.</xs:documentation></xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="CredentialNotActive">
            <xs:annotation><xs:documentation>The device shall provide the following event, whenever a valid credential is presented though it is not active yet;: e.g, the credential was presented before the start date.</xs:documentation></xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="CredentialExpired">
            <xs:annotation><xs:documentation>The device shall provide the following event, whenever a valid credential was presented after its expiry date.</xs:documentation></xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="InvalidPIN">
            <xs:annotation><xs:documentation>The device shall provide the following event, whenever an entered PIN code does not match the credential.</xs:documentation></xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="NotPermittedAtThisTime">
            <xs:annotation><xs:documentation>The device shall provide the following event, whenever a valid credential is denied access to the requested AccessPoint because the credential is not permitted at the moment.</xs:documentation></xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Unauthorized">
            <xs:annotation><xs:documentation>The device shall provide the following event, whenever the presented credential is not authorized.</xs:documentation></xs:annotation>
          </xs:enumeration>
          <xs:enumeration value="Other">
            <xs:annotation><xs:documentation>The device shall provide the following event, whenever the request is denied and no other specific event matches it or is supported by the service.</xs:documentation></xs:annotation>
          </xs:enumeration>
        </xs:restriction>
      </xs:simpleType>
      <!--===============================-->
      <!--  Message Request / Response elements  -->
      <xs:element name="GetServiceCapabilities">
        <xs:complexType>
          <xs:sequence>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetServiceCapabilitiesResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Capabilities" type="tac:ServiceCapabilities"><xs:annotation>
                <xs:documentation>The capability response message contains the requested Access Control service capabilities using a hierarchical XML capability structure.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAccessPointInfoList">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Limit" type="xs:int" minOccurs="0"><xs:annotation>
                <xs:documentation>Maximum number of entries to return. If not specified, less than one or higher than what the device supports, the number of items is determined by the device.</xs:documentation>
              </xs:annotation></xs:element>
            <xs:element name="StartReference" type="xs:string" minOccurs="0"><xs:annotation>
                <xs:documentation>Start returning entries from this start reference. If not specified, entries shall start from the beginning of the dataset.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAccessPointInfoListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="NextStartReference" type="xs:string" minOccurs="0"><xs:annotation>
                <xs:documentation>StartReference to use in next call to get the following items. If absent, no more items to get.</xs:documentation>
              </xs:annotation></xs:element>
            <xs:element name="AccessPointInfo" type="tac:AccessPointInfo" minOccurs="0" maxOccurs="unbounded"><xs:annotation>
                <xs:documentation>List of AccessPointInfo items.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAccessPointInfo">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Token" type="pt:ReferenceToken" maxOccurs="unbounded"><xs:annotation>
                <xs:documentation>Tokens of AccessPointInfo items to get.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAccessPointInfoResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="AccessPointInfo" type="tac:AccessPointInfo" minOccurs="0" maxOccurs="unbounded"><xs:annotation>
                <xs:documentation>List of AccessPointInfo items.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAreaInfoList">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Limit" type="xs:int" minOccurs="0"><xs:annotation>
                <xs:documentation>Maximum number of entries to return. If not specified, less than one or higher than what the device supports, the number of items is determined by the device.</xs:documentation>
              </xs:annotation></xs:element>
            <xs:element name="StartReference" type="xs:string" minOccurs="0"><xs:annotation>
                <xs:documentation>Start returning entries from this start reference. If not specified, entries shall start from the beginning of the dataset.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAreaInfoListResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="NextStartReference" type="xs:string" minOccurs="0"><xs:annotation>
                <xs:documentation>StartReference to use in next call to get the following items. If absent, no more items to get.</xs:documentation>
              </xs:annotation></xs:element>
            <xs:element name="AreaInfo" type="tac:AreaInfo" minOccurs="0" maxOccurs="unbounded"><xs:annotation>
                <xs:documentation>List of AreaInfo items.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAreaInfo">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Token" type="pt:ReferenceToken" maxOccurs="unbounded"><xs:annotation>
                <xs:documentation>Tokens of AreaInfo items to get.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAreaInfoResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="AreaInfo" type="tac:AreaInfo" minOccurs="0" maxOccurs="unbounded"><xs:annotation>
                <xs:documentation>List of AreaInfo items.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAccessPointState">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Token" type="pt:ReferenceToken"><xs:annotation>
                <xs:documentation>Token of AccessPoint instance to get AccessPointState for.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="GetAccessPointStateResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="AccessPointState" type="tac:AccessPointState"><xs:annotation>
                <xs:documentation>AccessPointState item.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="EnableAccessPoint">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Token" type="pt:ReferenceToken"><xs:annotation>
                <xs:documentation>Token of the AccessPoint instance to enable.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="EnableAccessPointResponse">
        <xs:complexType>
          <xs:sequence>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="DisableAccessPoint">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="Token" type="pt:ReferenceToken"><xs:annotation>
                <xs:documentation>Token of the AccessPoint instance to disable.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="DisableAccessPointResponse">
        <xs:complexType>
          <xs:sequence>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="ExternalAuthorization">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="AccessPointToken" type="pt:ReferenceToken"><xs:annotation>
                <xs:documentation>Token of the Access Point instance.</xs:documentation>
              </xs:annotation></xs:element>
            <xs:element name="CredentialToken" type="pt:ReferenceToken" minOccurs="0"><xs:annotation>
                <xs:documentation>Optional token of the Credential involved.</xs:documentation>
              </xs:annotation></xs:element>
            <xs:element name="Reason" type="xs:string" minOccurs="0"><xs:annotation>
                <xs:documentation>Optional reason for decision.</xs:documentation>
              </xs:annotation></xs:element>
            <xs:element name="Decision" type="tac:Decision"><xs:annotation>
                <xs:documentation>Decision - Granted or Denied.</xs:documentation>
              </xs:annotation></xs:element>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
      <xs:element name="ExternalAuthorizationResponse">
        <xs:complexType>
          <xs:sequence>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <!--===============================-->
    </xs:schema>
  </wsdl:types>
  <!--===============================-->
  <wsdl:message name="GetServiceCapabilitiesRequest">
    <wsdl:part name="parameters" element="tac:GetServiceCapabilities"/>
  </wsdl:message>
  <wsdl:message name="GetServiceCapabilitiesResponse">
    <wsdl:part name="parameters" element="tac:GetServiceCapabilitiesResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="GetAccessPointInfoListRequest">
    <wsdl:part name="parameters" element="tac:GetAccessPointInfoList"/>
  </wsdl:message>
  <wsdl:message name="GetAccessPointInfoListResponse">
    <wsdl:part name="parameters" element="tac:GetAccessPointInfoListResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="GetAccessPointInfoRequest">
    <wsdl:part name="parameters" element="tac:GetAccessPointInfo"/>
  </wsdl:message>
  <wsdl:message name="GetAccessPointInfoResponse">
    <wsdl:part name="parameters" element="tac:GetAccessPointInfoResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="GetAreaInfoListRequest">
    <wsdl:part name="parameters" element="tac:GetAreaInfoList"/>
  </wsdl:message>
  <wsdl:message name="GetAreaInfoListResponse">
    <wsdl:part name="parameters" element="tac:GetAreaInfoListResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="GetAreaInfoRequest">
    <wsdl:part name="parameters" element="tac:GetAreaInfo"/>
  </wsdl:message>
  <wsdl:message name="GetAreaInfoResponse">
    <wsdl:part name="parameters" element="tac:GetAreaInfoResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="GetAccessPointStateRequest">
    <wsdl:part name="parameters" element="tac:GetAccessPointState"/>
  </wsdl:message>
  <wsdl:message name="GetAccessPointStateResponse">
    <wsdl:part name="parameters" element="tac:GetAccessPointStateResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="EnableAccessPointRequest">
    <wsdl:part name="parameters" element="tac:EnableAccessPoint"/>
  </wsdl:message>
  <wsdl:message name="EnableAccessPointResponse">
    <wsdl:part name="parameters" element="tac:EnableAccessPointResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="DisableAccessPointRequest">
    <wsdl:part name="parameters" element="tac:DisableAccessPoint"/>
  </wsdl:message>
  <wsdl:message name="DisableAccessPointResponse">
    <wsdl:part name="parameters" element="tac:DisableAccessPointResponse"/>
  </wsdl:message>
  <!--===============================-->
  <wsdl:message name="ExternalAuthorizationRequest">
    <wsdl:part name="parameters" element="tac:ExternalAuthorization"/>
  </wsdl:message>
  <wsdl:message name="ExternalAuthorizationResponse">
    <wsdl:part name="parameters" element="tac:ExternalAuthorizationResponse"/>
  </wsdl:message>
  <!--===============================-->
  <!--====== Faults messages ========-->
  <wsdl:portType name="PACSPort">
    <wsdl:operation name="GetServiceCapabilities">
    <wsdl:documentation>
This operation returns the capabilities of the Access Control service.
&lt;/p&gt;&lt;p&gt;
An ONVIF compliant device which provides the Access Control service shall
implement this method.
</wsdl:documentation>
      <wsdl:input message="tac:GetServiceCapabilitiesRequest"/>
      <wsdl:output message="tac:GetServiceCapabilitiesResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccessPointInfoList">
    <wsdl:documentation>
This operation requests a list of all AccessPointInfo items provided by the device.
An ONVIF compliant device which provides the Access Control service shall implement this method.
&lt;/p&gt;&lt;p&gt;
A call to this method shall return a StartReference when not all data is returned and more
data is available. The reference shall be valid for retrieving the next set of data.
Please refer section [Retrieving system configuration] for more details.
&lt;/p&gt;&lt;p&gt;
The number of items returned shall not be greater than Limit parameter.
&lt;/p&gt;&lt;p&gt;
</wsdl:documentation>
      <wsdl:input message="tac:GetAccessPointInfoListRequest"/>
      <wsdl:output message="tac:GetAccessPointInfoListResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccessPointInfo">
    <wsdl:documentation>
This operation requests a list of AccessPointInfo items matching the given tokens.
&lt;/p&gt;&lt;p&gt;
An ONVIF compliant device which provides Access Control service shall implement this method.
&lt;/p&gt;&lt;p&gt;
The device shall ignore tokens it cannot resolve and shall return an empty list if there
are no items matching specified tokens. The device shall not return a fault in this case.
&lt;/p&gt;&lt;p&gt;
If the number of requested items is greater than MaxLimit, a TooManyItems
fault shall be returned.
&lt;/p&gt;&lt;p&gt;
</wsdl:documentation>
      <wsdl:input message="tac:GetAccessPointInfoRequest"/>
      <wsdl:output message="tac:GetAccessPointInfoResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAreaInfoList">
    <wsdl:documentation>
This operation requests a list of all AreaInfo items provided by the device.
An ONVIF compliant device which provides the Access Control service shall implement this method.
&lt;/p&gt;&lt;p&gt;
A call to this method shall return a StartReference when not all data is returned and more
data is available. The reference shall be valid for retrieving the next set of data.
Please refer section [Retrieving system configuration] for more details.
&lt;/p&gt;&lt;p&gt;
The number of items returned shall not be greater than Limit parameter.
&lt;/p&gt;&lt;p&gt;

</wsdl:documentation>
      <wsdl:input message="tac:GetAreaInfoListRequest"/>
      <wsdl:output message="tac:GetAreaInfoListResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAreaInfo">
    <wsdl:documentation>
This operation requests a list of AreaInfo items matching the given tokens.
&lt;/p&gt;&lt;p&gt;
An ONVIF compliant device which provides Access Control service shall implement this method.
&lt;/p&gt;&lt;p&gt;
The device shall ignore tokens it cannot resolve and shall return an empty list if there
are no items matching specified tokens. The device shall not return a fault in this case.
&lt;/p&gt;&lt;p&gt;
If the number of requested items is greater than MaxLimit, a TooManyItems
fault shall be returned.
&lt;/p&gt;&lt;p&gt;
</wsdl:documentation>
      <wsdl:input message="tac:GetAreaInfoRequest"/>
      <wsdl:output message="tac:GetAreaInfoResponse"/>
    </wsdl:operation>
    <wsdl:operation name="GetAccessPointState">
    <wsdl:documentation>
This operation requests the AccessPointState for the AccessPoint instance specified by Token.
&lt;/p&gt;&lt;p&gt;
An ONVIF compliant device that provides Access Control service shall implement this method.
</wsdl:documentation>
      <wsdl:input message="tac:GetAccessPointStateRequest"/>
      <wsdl:output message="tac:GetAccessPointStateResponse"/>
    </wsdl:operation>
    <wsdl:operation name="EnableAccessPoint">
    <wsdl:documentation>
This operation allows enabling an access point.
&lt;/p&gt;&lt;p&gt;
A device that signals support for DisableAccessPoint capability for a particular AccessPoint
instance shall implement this command.
&lt;/p&gt;&lt;p&gt;
</wsdl:documentation>
      <wsdl:input message="tac:EnableAccessPointRequest"/>
      <wsdl:output message="tac:EnableAccessPointResponse"/>
    </wsdl:operation>
    <wsdl:operation name="DisableAccessPoint">
    <wsdl:documentation>
This operation allows disabling an access point.
&lt;/p&gt;&lt;p&gt;
A device that signals support for DisableAccessPoint capability for a particular AccessPoint
instance shall implement this command.
&lt;/p&gt;&lt;p&gt;
</wsdl:documentation>
      <wsdl:input message="tac:DisableAccessPointRequest"/>
      <wsdl:output message="tac:DisableAccessPointResponse"/>
    </wsdl:operation>
    <wsdl:operation name="ExternalAuthorization">
    <wsdl:documentation>
This operation allows to Deny or Grant decision at an AccessPoint instance.
&lt;/p&gt;&lt;p&gt;
A device that signals support for ExternalAuthorization capability for a particular
AccessPoint instance shall implement this method.
</wsdl:documentation>
      <wsdl:input message="tac:ExternalAuthorizationRequest"/>
      <wsdl:output message="tac:ExternalAuthorizationResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <!--===============================-->
  <wsdl:binding name="PACSBinding" type="tac:PACSPort">
<wsdl:documentation>
Copyright (c) 2010-2013 by ONVIF: Open Network Video Interface Forum. All rights reserved.<br/>
This is the initial minimized version of the Access Control service 
aimed at the first PACS Profile C. <br/>
 
 
 
 
 
 
 
The AccessControl service implements the Authentication and 
Authorization functionality and controls the actions to get 
access to various Access Points controlling access to Doors and Areas. <br/>
 
 
The basic data structures used by the service are: 
 
* CredentialInfo holding basic information of a credential.<br/> 
* AccessPointInfo holding basic information on how access is controlled in 
one direction for a door (from which area to which area) defined in the DoorControl service.<br/> 
 
 

</wsdl:documentation>
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
  <!--===============================-->
    <wsdl:operation name="GetServiceCapabilities">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/GetServiceCapabilities"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="GetAccessPointInfoList">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/GetAccessPointInfoList"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="GetAccessPointInfo">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/GetAccessPointInfo"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="GetAreaInfoList">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/GetAreaInfoList"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="GetAreaInfo">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/GetAreaInfo"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="GetAccessPointState">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/GetAccessPointState"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="EnableAccessPoint">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/EnableAccessPoint"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="DisableAccessPoint">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/DisableAccessPoint"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
    <wsdl:operation name="ExternalAuthorization">
      <soap:operation soapAction="http://www.onvif.org/ver10/accesscontrol/wsdl/ExternalAuthorization"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!--===============================-->
  </wsdl:binding>
  <wsdl:service name="PACSService">
      <wsdl:port name="PACSPort" binding="tac:PACSBinding">
          <soap:address location="http://************:8888/onvif/PACS"/>
      </wsdl:port>
  </wsdl:service>
</wsdl:definitions>

﻿<?xml version="1.0"?>
<!-- 
Copyright © 2002-2004 BEA Systems Inc., International Business Machines Corporation, 
Microsoft Corporation, Inc, SAP AG, and Sun Microsystems, Inc.. All rights reserved. 

Permission to copy, display, perform, modify and distribute the WS-Addressing Specification, 
and to authorize others to do the foregoing, in any medium without fee or royalty is hereby
granted for the purpose of developing and evaluating the WS-Addressing Specification.

BEA, IBM, Microsoft, SAP AG, and Sun Microsystems (collectively, the "Authors") each agree 
to grant a license to third parties, under royalty-free  and otherwise reasonable, 
non-discriminatory terms and conditions, to their respective essential patent claims that
they deem necessary to implement the WS-Addressing Specification.

DISCLAIMERS:

THE WS-Addressing Specification IS PROVIDED "AS IS", AND THE AUTHORS MAKE NO REPRESENTATIONS
OR WARRANTIES, EXPRESS OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, WARRANTIES OF 
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, OR TITLE; THAT THE 
CONTENTS OF THE WS-Addressing Specification IS SUITABLE FOR ANY PURPOSE; NOR THAT THE 
IMPLEMENTATION OF SUCH CONTENTS WILL NOT INFRINGE ANY THIRD PARTY PATENTS, COPYRIGHTS, 
TRADEMARKS OR OTHER RIGHTS.

THE AUTHORS WILL NOT BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL OR CONSEQUENTIAL 
DAMAGES ARISING OUT OF ANY USE OF THE WS-Addressing Specification OR THE PERFORMANCE OR 
IMPLEMENTATION OF THE CONTENTS THEREOF.

You may remove these disclaimers from your modified versions of the WS-Addressing 
Specification provided that you effectively disclaim all warranties and liabilities on behalf 
of all copyright holders in the copies of any such modified versions you distribute.

The name and trademarks of the Authors may NOT be used in any manner, including advertising 
or publicity pertaining to the WS-Addressing Specification or its contents without specific, 
written prior permission. Title to copyright in the WS-Addressing Specification will at all 
times remain with the Authors.

No other rights are granted by implication, estoppel or otherwise.

-->
<xs:schema targetNamespace="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" elementFormDefault="qualified" blockDefault="#all">
  <!-- //////////////////// WS-Addressing //////////////////// -->
  <!-- Endpoint reference -->
  <xs:element name="EndpointReference" type="wsa:EndpointReferenceType"/>
  <xs:complexType name="EndpointReferenceType">
    <xs:sequence>
      <xs:element name="Address" type="wsa:AttributedURI"/>
      <xs:element name="ReferenceProperties" type="wsa:ReferencePropertiesType" minOccurs="0"/>
      <xs:element name="ReferenceParameters" type="wsa:ReferenceParametersType" minOccurs="0"/>
      <xs:element name="PortType" type="wsa:AttributedQName" minOccurs="0"/>
      <xs:element name="ServiceName" type="wsa:ServiceNameType" minOccurs="0"/>
      <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
        <xs:annotation>
          <xs:documentation>
					 If "Policy" elements from namespace "http://schemas.xmlsoap.org/ws/2002/12/policy#policy" are used, they must appear first (before any extensibility elements).
					</xs:documentation>
        </xs:annotation>
      </xs:any>
    </xs:sequence>
    <xs:anyAttribute namespace="##other" processContents="lax"/>
  </xs:complexType>
  <xs:complexType name="ReferencePropertiesType">
    <xs:sequence>
      <xs:any processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ReferenceParametersType">
    <xs:sequence>
      <xs:any processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ServiceNameType">
    <xs:simpleContent>
      <xs:extension base="xs:QName">
        <xs:attribute name="PortName" type="xs:NCName"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <!-- Message information header blocks -->
  <xs:element name="MessageID" type="wsa:AttributedURI"/>
  <xs:element name="RelatesTo" type="wsa:Relationship"/>
  <xs:element name="To" type="wsa:AttributedURI"/>
  <xs:element name="Action" type="wsa:AttributedURI"/>
  <xs:element name="From" type="wsa:EndpointReferenceType"/>
  <xs:element name="ReplyTo" type="wsa:EndpointReferenceType"/>
  <xs:element name="FaultTo" type="wsa:EndpointReferenceType"/>
  <xs:complexType name="Relationship">
    <xs:simpleContent>
      <xs:extension base="xs:anyURI">
        <xs:attribute name="RelationshipType" type="xs:QName" use="optional"/>
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:simpleType name="RelationshipTypeValues">
    <xs:restriction base="xs:QName">
      <xs:enumeration value="wsa:Reply"/>
    </xs:restriction>
  </xs:simpleType>
  <!--
    June 19, 2007: The ReplyAfter element is deprecated. The name of this element does not match the
    name (RetryAfter) used in the specification (http://www.w3.org/Submission/2004/SUBM-ws-addressing-20040810/).
  -->
  <xs:element name="ReplyAfter" type="wsa:ReplyAfterType"/>
  <xs:complexType name="ReplyAfterType">
    <xs:simpleContent>
      <xs:extension base="xs:nonNegativeInteger">
        <xs:anyAttribute namespace="##other"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <!--
    June 19, 2007: The RetryAfter element has been added to be consistent with the specification
    (http://www.w3.org/Submission/2004/SUBM-ws-addressing-20040810/).
  -->
  <xs:element name="RetryAfter" type="wsa:RetryAfterType"/>
  <xs:complexType name="RetryAfterType">
    <xs:simpleContent>
      <xs:extension base="xs:nonNegativeInteger">
        <xs:anyAttribute namespace="##other"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:simpleType name="FaultSubcodeValues">
    <xs:restriction base="xs:QName">
      <xs:enumeration value="wsa:InvalidMessageInformationHeader"/>
      <xs:enumeration value="wsa:MessageInformationHeaderRequired"/>
      <xs:enumeration value="wsa:DestinationUnreachable"/>
      <xs:enumeration value="wsa:ActionNotSupported"/>
      <xs:enumeration value="wsa:EndpointUnavailable"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:attribute name="Action" type="xs:anyURI"/>
  <!-- Common declarations and definitions -->
  <xs:complexType name="AttributedQName">
    <xs:simpleContent>
      <xs:extension base="xs:QName">
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="AttributedURI">
    <xs:simpleContent>
      <xs:extension base="xs:anyURI">
        <xs:anyAttribute namespace="##other" processContents="lax"/>
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
</xs:schema>

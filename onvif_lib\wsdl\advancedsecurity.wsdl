<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../../../ver20/util/onvif-wsdl-viewer.xsl"?>
<!--
	Copyright (c) 2013 - 2014 by ONVIF: Open Network Video Interface Forum. All rights reserved.
	
	Recipients of this document may copy, distribute, publish, or display this document so long as this copyright notice, license and disclaimer are retained with all copies of the document. No license is granted to modify this document.
	
	THIS DOCUMENT IS PROVIDED "AS IS," AND THE CORPORATION AND ITS MEMBERS AND THEIR AFFILIATES, MAKE NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO, WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, OR TITLE; THAT THE CONTENTS OF THIS DOCUMENT ARE SUITABLE FOR ANY PURPOSE; OR THAT THE IMPLEMENTATION OF SUCH CONTENTS WILL NOT INFRINGE ANY PATENTS, COPYRIGHTS, TRADEMARKS OR OTHER RIGHTS.
	IN NO EVENT WILL THE CORPORATION OR ITS MEMBERS OR THEIR AFFILIATES BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL, PUNITIVE OR CONSEQUENTIAL DAMAGES, ARISING OUT OF OR RELATING TO ANY USE OR DISTRIBUTION OF THIS DOCUMENT, WHETHER OR NOT (1) THE CORPORATION, MEMBERS OR THEIR AFFILIATES HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES, OR (2) SUCH DAMAGES WERE REASONABLY FORESEEABLE, AND ARISING OUT OF OR RELATING TO ANY USE OR DISTRIBUTION OF THIS DOCUMENT.  THE FOREGOING DISCLAIMER AND LIMITATION ON LIABILITY DO NOT APPLY TO, INVALIDATE, OR LIMIT REPRESENTATIONS AND WARRANTIES MADE BY THE MEMBERS AND THEIR RESPECTIVE AFFILIATES TO THE CORPORATION AND OTHER MEMBERS IN CERTAIN WRITTEN POLICIES OF THE CORPORATION.
-->
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tas="http://www.onvif.org/ver10/advancedsecurity/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://www.onvif.org/ver10/advancedsecurity/wsdl">
	<wsdl:types>
		<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="http://www.onvif.org/ver10/advancedsecurity/wsdl" version="1.0.2">
			<!--===================================================-->
			<!-- Data types used by the advanced security features -->
			<!--===================================================-->
			<xs:simpleType name="KeyID">
        <xs:annotation>
          <xs:documentation>Unique identifier for keys in the keystore.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:ID"/>
			</xs:simpleType>
			<!--===============================-->
			<xs:simpleType name="CertificateID">
        <xs:annotation>
          <xs:documentation>Unique identifier for certificates in the keystore.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:ID"/>
			</xs:simpleType>
			<!--===============================-->
			<xs:simpleType name="CertificationPathID">
        <xs:annotation>
          <xs:documentation>Unique identifier for certification paths in the keystore.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:ID"/>
			</xs:simpleType>
			<!--===============================-->
			<xs:simpleType name="KeyStatus">
				<xs:annotation>
					<xs:documentation>The status of a key in the keystore.</xs:documentation>
				</xs:annotation>
				<xs:restriction base="xs:string">
					<xs:enumeration value="ok">
						<xs:annotation>
							<xs:documentation>Key is ready for use</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="generating">
						<xs:annotation>
							<xs:documentation>Key is being generated</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
					<xs:enumeration value="corrupt">
						<xs:annotation>
							<xs:documentation>Key has not been successfully generated and cannot be used.</xs:documentation>
						</xs:annotation>
					</xs:enumeration>
				</xs:restriction>
			</xs:simpleType>
			<!--===============================-->
			<xs:simpleType name="DotDecimalOID">
        <xs:annotation>
          <xs:documentation>An object identifier (OID) in dot-decimal form as specified in RFC4512.</xs:documentation>
        </xs:annotation>
				<xs:restriction base="xs:string">
					<xs:pattern value="[0-9]+(.[0-9]+)*"/>
				</xs:restriction>
			</xs:simpleType>
			<!--===============================-->
			<xs:simpleType name="DNAttributeType">
        <xs:annotation>
          <xs:documentation>The distinguished name attribute type encoded as specified in RFC 4514.</xs:documentation>
        </xs:annotation>
				<xs:restriction base="xs:string"/>
			</xs:simpleType>
			<!--===============================-->
      <xs:simpleType name="DNAttributeValue">
        <xs:restriction base="xs:string">
          <xs:annotation>
            <xs:documentation>
              The distinguished name attribute values are encoded in UTF-8 or in hexadecimal form as specified in RFC 4514.
            </xs:documentation>
          </xs:annotation>
        </xs:restriction>
      </xs:simpleType>
			<!--===============================-->
			<xs:complexType name="KeyAttribute">
				<xs:annotation>
					<xs:documentation>The attributes of a key in the keystore.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:element name="KeyID" type="tas:KeyID">
						<xs:annotation>
							<xs:documentation>The ID of the key.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Alias" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The client-defined alias of the key.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="hasPrivateKey" type="xs:boolean" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Absent if the key is not a key pair. True if and only if the key is a key pair and contains a private key. False if and only if the key is a key pair and does not contain a private key.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="KeyStatus" type="xs:string">
						<xs:annotation>
							<xs:documentation>The status of the key. The value should be one of the values in the tas:KeyStatus enumeration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any" processContents="lax"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:complexType name="DNAttributeTypeAndValue">
				<xs:annotation>
					<xs:documentation>A distinguished name attribute type and value pair.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:element name="Type" type="tas:DNAttributeType">
						<xs:annotation>
							<xs:documentation>The attribute type.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Value" type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>The value of the attribute.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any" processContents="lax"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
      <!--===============================-->
      <xs:complexType name="MultiValuedRDN">
        <xs:annotation>
          <xs:documentation>A multi-valued RDN</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="Attribute" type="tas:DNAttributeTypeAndValue">
            <xs:annotation>
              <xs:documentation>A list of types and values defining a multi-valued RDN</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
			<!--===============================-->
			<xs:complexType name="DistinguishedName">
				<xs:sequence>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Country"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A country name as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Organization"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>An organization name as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="OrganizationalUnit"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>An organizational unit name as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded"
						name="DistinguishedNameQualifier" type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A distinguished name qualifier as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="StateOrProvinceName"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A state or province name as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="CommonName"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A common name as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="SerialNumber"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A serial number as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Locality"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A locality as specified in X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Title"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A title as specified in X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Surname"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A surname as specified in X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="GivenName"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A given name as specified in X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Initials"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>Initials as specified in X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Pseudonym"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A pseudonym as specified in X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="GenerationQualifier"
						type="tas:DNAttributeValue">
						<xs:annotation>
							<xs:documentation>A generation qualifier as specified in
								X.500.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="GenericAttribute"
						type="tas:DNAttributeTypeAndValue">
						<xs:annotation>
							<xs:documentation>A generic type-value pair
								attribute.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="MultiValuedRDN"
						type="tas:MultiValuedRDN">
						<xs:annotation>
							<xs:documentation>A multi-valued RDN</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" name="anyAttribute">
						<xs:complexType>
							<xs:sequence>
								<xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any"
									processContents="lax"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:complexType name="AlgorithmIdentifier">
				<xs:annotation>
					<xs:documentation>An identifier of an algorithm.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:element name="algorithm" type="tas:DotDecimalOID">
						<xs:annotation>
							<xs:documentation>The OID of the algorithm in dot-decimal form.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" name="parameters" type="tas:Base64DERencodedASN1Value">
						<xs:annotation>
							<xs:documentation>Optional parameters of the algorithm (depending on the algorithm).</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element minOccurs="0" name="anyParameters">
						<xs:complexType>
							<xs:sequence>
								<xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any" processContents="lax"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
      <!--===============================-->
      <xs:complexType name="BasicRequestAttribute">
        <xs:annotation>
          <xs:documentation>A CSR attribute as specified in RFC 2986.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="OID" type="tas:DotDecimalOID">
            <xs:annotation>
              <xs:documentation>The OID of the attribute.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="value" type="tas:Base64DERencodedASN1Value">
            <xs:annotation>
              <xs:documentation>The value of the attribute as a base64-encoded DER representation of an ASN.1 value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any" processContents="lax"/>
        </xs:sequence>
        <xs:anyAttribute processContents="lax"/>
      </xs:complexType>
      <!--===============================-->
			<xs:complexType name="CSRAttribute">
				<xs:annotation>
					<xs:documentation>A CSR attribute as specified in PKCS#10.</xs:documentation>
				</xs:annotation>
				<xs:choice>
					<xs:element name="X509v3Extension" type="tas:X509v3Extension">
						<xs:annotation>
							<xs:documentation>An X.509v3 extension field.</xs:documentation>
						</xs:annotation>
					</xs:element>
          <xs:element name="BasicRequestAttribute" type="tas:BasicRequestAttribute">
            <xs:annotation>
              <xs:documentation>A basic CSR attribute.</xs:documentation>
            </xs:annotation>
          </xs:element>
					<xs:element minOccurs="0" name="anyAttribute">
						<xs:complexType>
							<xs:sequence>
								<xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any" processContents="lax"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:choice>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:simpleType name="Base64DERencodedASN1Value">
				<xs:annotation>
					<xs:documentation>A base64-encoded ASN.1 value.</xs:documentation>
				</xs:annotation>
				<xs:restriction base="xs:base64Binary"/>
			</xs:simpleType>
			<!--===============================-->
			<xs:complexType name="X509v3Extension">
				<xs:annotation>
					<xs:documentation>An X.509v3 extension field as specified in RFC 5280</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:element name="extnOID" type="tas:DotDecimalOID">
						<xs:annotation>
							<xs:documentation>The OID of the extension field.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element default="false" name="critical" type="xs:boolean">
						<xs:annotation>
							<xs:documentation>True if and only if the extension is critical.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="extnValue" type="tas:Base64DERencodedASN1Value">
						<xs:annotation>
							<xs:documentation>The value of the extension field as a base64-encoded DER representation of an ASN.1 value.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any" processContents="lax"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:complexType name="X509Certificate">
				<xs:annotation>
					<xs:documentation>An X.509 cerficiate as specified in RFC 5280.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:element name="CertificateID" type="tas:CertificateID">
						<xs:annotation>
							<xs:documentation>The ID of the certificate.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="KeyID" type="tas:KeyID">
						<xs:annotation>
							<xs:documentation>The ID of the key that this certificate associates to the certificate subject.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Alias" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The client-defined alias of the certificate.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="CertificateContent" type="tas:Base64DERencodedASN1Value">
						<xs:annotation>
							<xs:documentation>The base64-encoded DER representation of the X.509 certificate.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any minOccurs="0" maxOccurs="unbounded" namespace="##any" processContents="lax"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:complexType name="CertificateIDs">
				<xs:annotation>
					<xs:documentation>A sequence of certificate IDs.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:element maxOccurs="unbounded" name="CertificateID" type="tas:CertificateID">
						<xs:annotation>
							<xs:documentation>A certificate ID.</xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:complexType name="CertificationPath">
				<xs:annotation>
					<xs:documentation>An X.509 certification path as defined in RFC 5280.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:element maxOccurs="unbounded" name="CertificateID" type="tas:CertificateID">
						<xs:annotation>
							<xs:documentation>A certificate in the certification path.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Alias" type="xs:string" minOccurs="0">
						<xs:annotation>
							<xs:documentation>The client-defined alias of the certification path.</xs:documentation>
						</xs:annotation>
					</xs:element>
          <xs:element minOccurs="0" name="anyElement">
            <xs:complexType>
              <xs:sequence>
                <xs:any namespace="##any" minOccurs="0" maxOccurs="unbounded"  processContents="lax"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:simpleType name="RSAKeyLengths">
				<xs:annotation>
					<xs:documentation>A list of RSA key lenghts in bits.</xs:documentation>
				</xs:annotation>
				<xs:list itemType="xs:nonNegativeInteger"/>
			</xs:simpleType>
			<xs:simpleType name="X509Versions">
				<xs:annotation>
					<xs:documentation>A list of X.509 versions.</xs:documentation>
				</xs:annotation>
				<xs:list itemType="xs:int"/>
			</xs:simpleType>
			<xs:simpleType name="TLSVersions">
				<xs:annotation>
					<xs:documentation>A list of TLS versions.</xs:documentation>
				</xs:annotation>
				<xs:list itemType="xs:string"/>
			</xs:simpleType>
			<!--===============================-->
			<xs:complexType name="KeystoreCapabilities">
				<xs:annotation>
					<xs:documentation>The capabilities of a keystore implementation on a device.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
          <xs:element minOccurs="0" maxOccurs="unbounded" name="SignatureAlgorithms" type="tas:AlgorithmIdentifier">
            <xs:annotation>
              <xs:documentation>The signature algorithms supported by the keystore implementation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element minOccurs="0" name="anyElement">
            <xs:complexType>
              <xs:sequence>
                <xs:any namespace="##any" minOccurs="0" maxOccurs="unbounded"  processContents="lax"/>
              </xs:sequence>
            </xs:complexType>
          </xs:element>
        </xs:sequence>
				<xs:attribute name="MaximumNumberOfKeys" type="xs:positiveInteger">
					<xs:annotation>
						<xs:documentation>Indicates the maximum number of keys that the device can store simultaneously.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="MaximumNumberOfCertificates" type="xs:positiveInteger">
					<xs:annotation>
						<xs:documentation>Indicates the maximum number of certificates that the device can store simultaneously.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="MaximumNumberOfCertificationPaths" type="xs:positiveInteger">
					<xs:annotation>
						<xs:documentation>Indicates the maximum number of certification paths that the device can store simultaneously.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RSAKeyPairGeneration" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>Indication that the device supports on-board RSA key pair generation.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="RSAKeyLengths" type="tas:RSAKeyLengths">
					<xs:annotation>
						<xs:documentation>Indicates which RSA key lengths are supported by the device.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="PKCS10ExternalCertificationWithRSA" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>Indicates support for creating PKCS#10 requests for RSA keys and uploading the certificate obtained from a CA..</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="SelfSignedCertificateCreationWithRSA" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>Indicates support for creating self-signed certificates for RSA keys.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="X509Versions" type="tas:X509Versions">
					<xs:annotation>
						<xs:documentation>Indicates which X.509 versions are supported by the device.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:complexType name="TLSServerCapabilities">
				<xs:annotation>
					<xs:documentation>The capabilities of a TLS server implementation on a device.</xs:documentation>
				</xs:annotation>
				<xs:sequence>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:attribute name="TLSServerSupported" type="tas:TLSVersions">
					<xs:annotation>
						<xs:documentation>Indicates which TLS versions are supported by the device.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:attribute name="MaximumNumberOfTLSCertificationPaths" type="xs:positiveInteger">
					<xs:annotation>
						<xs:documentation>Indicates the maximum number of certification paths that may be assigned to the TLS server simultaneously.</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<!--===============================-->
			<xs:complexType name="Capabilities">
        <xs:annotation>
          <xs:documentation>The capabilities of an Advanced Security Service implementation on a device.</xs:documentation>
        </xs:annotation>
				<xs:sequence>
					<xs:element name="KeystoreCapabilities" type="tas:KeystoreCapabilities">
						<xs:annotation>
							<xs:documentation>The capabilities of the keystore implementation.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="TLSServerCapabilities" type="tas:TLSServerCapabilities">
						<xs:annotation>
							<xs:documentation>The capabilities of the TLS server implementation.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:complexType>
			<xs:element name="Capabilities" type="tas:Capabilities"/>
			<!--=========================================-->
			<!-- Request/response elements               -->
			<!--=========================================-->
			<xs:element name="GetServiceCapabilities">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetServiceCapabilitiesResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Capabilities" type="tas:Capabilities">
							<xs:annotation>
								<xs:documentation>The capabilities for the advanced secuirty service is returned in the Capabilities element.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="CreateRSAKeyPair">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="KeyLength" type="xs:nonNegativeInteger">
							<xs:annotation>
								<xs:documentation>The length of the key to be created.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Alias" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The client-defined alias of the key.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CreateRSAKeyPairResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="KeyID" type="tas:KeyID">
							<xs:annotation>
								<xs:documentation>The key ID of the key pair being generated.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="EstimatedCreationTime" type="xs:duration">
							<xs:annotation>
								<xs:documentation>Best-effort estimate of how long the key generation will take.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetKeyStatus">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="KeyID" type="tas:KeyID">
							<xs:annotation>
								<xs:documentation>The ID of the key for which to return the status.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetKeyStatusResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="KeyStatus" type="xs:string">
							<xs:annotation>
								<xs:documentation>Status of the requested key. The value should be one of the values in the tas:KeyStatus enumeration.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetPrivateKeyStatus">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="KeyID" type="tas:KeyID">
							<xs:annotation>
								<xs:documentation>The ID of the key pair for which to return whether it contains a private key.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetPrivateKeyStatusResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="hasPrivateKey" type="xs:boolean">
							<xs:annotation>
								<xs:documentation>True if and only if the key pair contains a private key.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetAllKeys">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetAllKeysResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="KeyAttribute" type="tas:KeyAttribute" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>Information about a key in the keystore.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="DeleteKey">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="KeyID" type="tas:KeyID">
							<xs:annotation>
								<xs:documentation>The ID of the key that is to be deleted from the keystore.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DeleteKeyResponse">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="CreatePKCS10CSR">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Subject" type="tas:DistinguishedName">
							<xs:annotation>
								<xs:documentation>The subject to be included in the CSR.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="KeyID" type="tas:KeyID">
							<xs:annotation>
								<xs:documentation>The ID of the key for which the CSR shall be created.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="CSRAttribute" minOccurs="0" maxOccurs="unbounded" type="tas:CSRAttribute">
							<xs:annotation>
								<xs:documentation>An attribute to be included in the CSR.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="SignatureAlgorithm" type="tas:AlgorithmIdentifier">
							<xs:annotation>
								<xs:documentation>The signature algorithm to be used to sign the CSR. Defaults to SHA1 with RSA Encryption.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CreatePKCS10CSRResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="PKCS10CSR" type="tas:Base64DERencodedASN1Value">
							<xs:annotation>
								<xs:documentation>The DER encoded PKCS#10 certification request.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="CreateSelfSignedCertificate">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="X509Version" type="xs:positiveInteger">
							<xs:annotation>
								<xs:documentation>The X.509 version that the generated certificate shall comply to.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Subject" type="tas:DistinguishedName">
							<xs:annotation>
								<xs:documentation>Distinguished name of the entity that the certificate shall belong to.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="KeyID" type="tas:KeyID">
							<xs:annotation>
								<xs:documentation>The ID of the key for which the certificate shall be created.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Alias" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The client-defined alias of the certificate to be created.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element minOccurs="0" name="notValidBefore" type="xs:dateTime">
							<xs:annotation>
								<xs:documentation>The X.509 not valid before information to be included in the certificate. Defaults to the device's current time or a time before the device's current time.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element minOccurs="0" name="notValidAfter" type="xs:dateTime">
							<xs:annotation>
								<xs:documentation>The X.509 not valid after information to be included in the certificate. Defaults to the time 99991231235959Z as specified in RFC 5280.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="SignatureAlgorithm" type="tas:AlgorithmIdentifier">
							<xs:annotation>
								<xs:documentation>The signature algorithm to be used for signing the certificate. Defaults to SHA1 with RSA Encryption.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element minOccurs="0" maxOccurs="unbounded" name="Extension" type="tas:X509v3Extension">
							<xs:annotation>
								<xs:documentation>An X.509v3 extension to be included in the certificate.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CreateSelfSignedCertificateResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificateID" type="tas:CertificateID">
							<xs:annotation>
								<xs:documentation>The ID of the generated certificate.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="UploadCertificate">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Certificate" type="tas:Base64DERencodedASN1Value">
							<xs:annotation>
								<xs:documentation>The base64-encoded DER representation of the X.509 certificate to be uploaded.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Alias" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The client-defined alias of the certificate.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="KeyAlias" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The client-defined alias of the key pair.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="PrivateKeyRequired" type="xs:boolean" minOccurs="0" default="false">
							<xs:annotation>
								<xs:documentation>Indicates if the device shall verify that a matching key pair with a private key exists in the keystore.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="UploadCertificateResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificateID" type="tas:CertificateID">
							<xs:annotation>
								<xs:documentation>The ID of the uploaded certificate.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="KeyID" type="tas:KeyID">
							<xs:annotation>
								<xs:documentation>The ID of the key that the uploaded certificate certifies.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetCertificate">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificateID" type="tas:CertificateID">
							<xs:annotation>
								<xs:documentation>The ID of the certificate to retrieve.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetCertificateResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Certificate" type="tas:X509Certificate">
							<xs:annotation>
								<xs:documentation>The DER representation of the certificate.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetAllCertificates">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetAllCertificatesResponse">
				<xs:complexType>
					<xs:annotation>
						<xs:documentation>A list with all certificates stored in the keystore.</xs:documentation>
					</xs:annotation>
					<xs:sequence>
						<xs:element minOccurs="0" maxOccurs="unbounded" name="Certificate" type="tas:X509Certificate">
							<xs:annotation>
								<xs:documentation>A certificate stored in the keystore.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="DeleteCertificate">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificateID" type="tas:CertificateID">
							<xs:annotation>
								<xs:documentation>The ID of the certificate to delete.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DeleteCertificateResponse">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="CreateCertificationPath">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificateIDs" type="tas:CertificateIDs">
							<xs:annotation>
								<xs:documentation>The IDs of the certificates to include in the certification path, where each certificate signature except for the last one in the path must be verifiable with the public key certified by the next certificate in the path.</xs:documentation>
							</xs:annotation>
						</xs:element>
						<xs:element name="Alias" type="xs:string" minOccurs="0">
							<xs:annotation>
								<xs:documentation>The client-defined alias of the certification path.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="CreateCertificationPathResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPathID" type="tas:CertificationPathID">
							<xs:annotation>
								<xs:documentation>The ID of the generated certification path.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetCertificationPath">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPathID" type="tas:CertificationPathID">
							<xs:annotation>
								<xs:documentation>The ID of the certification path to retrieve.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetCertificationPathResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPath" type="tas:CertificationPath">
							<xs:annotation>
								<xs:documentation>The certification path that is stored under the given ID in the keystore.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetAllCertificationPaths">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetAllCertificationPathsResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPathID" type="tas:CertificationPathID" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>An ID of a certification path in the keystore.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="DeleteCertificationPath">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPathID" type="tas:CertificationPathID">
							<xs:annotation>
								<xs:documentation>The ID of the certification path to delete.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="DeleteCertificationPathResponse">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="AddServerCertificateAssignment">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPathID" type="tas:CertificationPathID"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="AddServerCertificateAssignmentResponse">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="RemoveServerCertificateAssignment">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPathID" type="tas:CertificationPathID"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="RemoveServerCertificateAssignmentResponse">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="ReplaceServerCertificateAssignment">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="OldCertificationPathID" type="tas:CertificationPathID"/>
						<xs:element name="NewCertificationPathID" type="tas:CertificationPathID"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="ReplaceServerCertificateAssignmentResponse">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<!--===============================-->
			<xs:element name="GetAssignedServerCertificates">
				<xs:complexType>
					<xs:sequence/>
				</xs:complexType>
			</xs:element>
			<xs:element name="GetAssignedServerCertificatesResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="CertificationPathID" type="tas:CertificationPathID" minOccurs="0" maxOccurs="unbounded">
							<xs:annotation>
								<xs:documentation>The IDs of all certification paths that are assigned to the TLS server on the device.</xs:documentation>
							</xs:annotation>
						</xs:element>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:schema>
	</wsdl:types>
	<wsdl:message name="GetServiceCapabilitiesRequest">
		<wsdl:part name="parameters" element="tas:GetServiceCapabilities"/>
	</wsdl:message>
	<wsdl:message name="GetServiceCapabilitiesResponse">
		<wsdl:part name="parameters" element="tas:GetServiceCapabilitiesResponse"/>
	</wsdl:message>
	<wsdl:message name="CreateRSAKeyPairRequest">
		<wsdl:part name="parameters" element="tas:CreateRSAKeyPair"/>
	</wsdl:message>
	<wsdl:message name="CreateRSAKeyPairResponse">
		<wsdl:part name="parameters" element="tas:CreateRSAKeyPairResponse"/>
	</wsdl:message>
	<wsdl:message name="GetKeyStatusRequest">
		<wsdl:part name="parameters" element="tas:GetKeyStatus"/>
	</wsdl:message>
	<wsdl:message name="GetKeyStatusResponse">
		<wsdl:part name="parameters" element="tas:GetKeyStatusResponse"/>
	</wsdl:message>
	<wsdl:message name="GetPrivateKeyStatusRequest">
		<wsdl:part name="parameters" element="tas:GetPrivateKeyStatus"/>
	</wsdl:message>
	<wsdl:message name="GetPrivateKeyStatusResponse">
		<wsdl:part name="parameters" element="tas:GetPrivateKeyStatusResponse"/>
	</wsdl:message>
	<wsdl:message name="GetAllKeysRequest">
		<wsdl:part name="parameters" element="tas:GetAllKeys"/>
	</wsdl:message>
	<wsdl:message name="GetAllKeysResponse">
		<wsdl:part name="parameters" element="tas:GetAllKeysResponse"/>
	</wsdl:message>
	<wsdl:message name="DeleteKeyRequest">
		<wsdl:part name="parameters" element="tas:DeleteKey"/>
	</wsdl:message>
	<wsdl:message name="DeleteKeyResponse">
		<wsdl:part name="parameters" element="tas:DeleteKeyResponse"/>
	</wsdl:message>
	<wsdl:message name="CreatePKCS10CSRRequest">
		<wsdl:part name="parameters" element="tas:CreatePKCS10CSR"/>
	</wsdl:message>
	<wsdl:message name="CreatePKCS10CSRResponse">
		<wsdl:part name="parameters" element="tas:CreatePKCS10CSRResponse"/>
	</wsdl:message>
	<wsdl:message name="CreateSelfSignedCertificateRequest">
		<wsdl:part name="parameters" element="tas:CreateSelfSignedCertificate"/>
	</wsdl:message>
	<wsdl:message name="CreateSelfSignedCertificateResponse">
		<wsdl:part name="parameters" element="tas:CreateSelfSignedCertificateResponse"/>
	</wsdl:message>
	<wsdl:message name="UploadCertificateRequest">
		<wsdl:part name="parameters" element="tas:UploadCertificate"/>
	</wsdl:message>
	<wsdl:message name="UploadCertificateResponse">
		<wsdl:part name="parameters" element="tas:UploadCertificateResponse"/>
	</wsdl:message>
	<wsdl:message name="GetCertificateRequest">
		<wsdl:part name="parameters" element="tas:GetCertificate"/>
	</wsdl:message>
	<wsdl:message name="GetCertificateResponse">
		<wsdl:part name="parameters" element="tas:GetCertificateResponse"/>
	</wsdl:message>
	<wsdl:message name="GetAllCertificatesRequest">
		<wsdl:part name="parameters" element="tas:GetAllCertificates"/>
	</wsdl:message>
	<wsdl:message name="GetAllCertificatesResponse">
		<wsdl:part name="parameters" element="tas:GetAllCertificatesResponse"/>
	</wsdl:message>
	<wsdl:message name="DeleteCertificateRequest">
		<wsdl:part name="parameters" element="tas:DeleteCertificate"/>
	</wsdl:message>
	<wsdl:message name="DeleteCertificateResponse">
		<wsdl:part name="parameters" element="tas:DeleteCertificateResponse"/>
	</wsdl:message>
	<wsdl:message name="CreateCertificationPathRequest">
		<wsdl:part name="parameters" element="tas:CreateCertificationPath"/>
	</wsdl:message>
	<wsdl:message name="CreateCertificationPathResponse">
		<wsdl:part name="parameters" element="tas:CreateCertificationPathResponse"/>
	</wsdl:message>
	<wsdl:message name="GetCertificationPathRequest">
		<wsdl:part name="parameters" element="tas:GetCertificationPath"/>
	</wsdl:message>
	<wsdl:message name="GetCertificationPathResponse">
		<wsdl:part name="parameters" element="tas:GetCertificationPathResponse"/>
	</wsdl:message>
	<wsdl:message name="GetAllCertificationPathsRequest">
		<wsdl:part name="parameters" element="tas:GetAllCertificationPaths"/>
	</wsdl:message>
	<wsdl:message name="GetAllCertificationPathsResponse">
		<wsdl:part name="parameters" element="tas:GetAllCertificationPathsResponse"/>
	</wsdl:message>
	<wsdl:message name="DeleteCertificationPathRequest">
		<wsdl:part name="parameters" element="tas:DeleteCertificationPath"/>
	</wsdl:message>
	<wsdl:message name="DeleteCertificationPathResponse">
		<wsdl:part name="parameters" element="tas:DeleteCertificationPathResponse"/>
	</wsdl:message>
	<wsdl:message name="AddServerCertificateAssignmentRequest">
		<wsdl:part name="parameters" element="tas:AddServerCertificateAssignment"/>
	</wsdl:message>
	<wsdl:message name="AddServerCertificateAssignmentResponse">
		<wsdl:part name="parameters" element="tas:AddServerCertificateAssignmentResponse"/>
	</wsdl:message>
	<wsdl:message name="RemoveServerCertificateAssignmentRequest">
		<wsdl:part name="parameters" element="tas:RemoveServerCertificateAssignment"/>
	</wsdl:message>
	<wsdl:message name="RemoveServerCertificateAssignmentResponse">
		<wsdl:part name="parameters" element="tas:RemoveServerCertificateAssignmentResponse"/>
	</wsdl:message>
	<wsdl:message name="ReplaceServerCertificateAssignmentRequest">
		<wsdl:part name="parameters" element="tas:ReplaceServerCertificateAssignment"/>
	</wsdl:message>
	<wsdl:message name="ReplaceServerCertificateAssignmentResponse">
		<wsdl:part name="parameters" element="tas:ReplaceServerCertificateAssignmentResponse"/>
	</wsdl:message>
	<wsdl:message name="GetAssignedServerCertificatesRequest">
		<wsdl:part name="parameters" element="tas:GetAssignedServerCertificates"/>
	</wsdl:message>
	<wsdl:message name="GetAssignedServerCertificatesResponse">
		<wsdl:part name="parameters" element="tas:GetAssignedServerCertificatesResponse"/>
	</wsdl:message>
	<wsdl:portType name="AdvancedSecurityService">
		<wsdl:documentation>Common functionality for all advanced security service parts.</wsdl:documentation>
		<wsdl:operation name="GetServiceCapabilities">
			<wsdl:documentation>Returns the capabilities of the advanced security service. The result is returned in a typed answer.</wsdl:documentation>
			<wsdl:input message="tas:GetServiceCapabilitiesRequest"/>
			<wsdl:output message="tas:GetServiceCapabilitiesResponse"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:portType name="Keystore">
		<wsdl:documentation>Basic keystore functionality.</wsdl:documentation>
		<wsdl:operation name="CreateRSAKeyPair">
			<wsdl:documentation>
				This operation triggers the asynchronous generation of an RSA key pair of a particular key length (specified as the number of bits) as specified in [RFC 3447], with a suitable key generation mechanism on the device.
				Keys, especially RSA key pairs, are uniquely identified using key IDs.<br/>
				If the device does not have not enough storage capacity for storing the key pair to be created, the maximum number of keys reached fault shall be produced and no key pair shall be generated.
				Otherwise, the operation generates a keyID for the new key and associates the generating status to it.<br/>
				Immediately after key generation has started, the device shall return the keyID to the client and continue to generate the key pair.
				The client may query the device with the GetKeyStatus operation whether the generation has finished.
				The client may also subscribe to Key Status events to be notified about key status changes.<br/>
				The device also returns a best-effort estimate of how much time it requires to create the key pair.
				A client may use this information as an indication how long to wait before querying the device whether key generation is completed.<br/>
				After the key has been successfully created, the device shall assign it the ok status. If the key generation fails, the device shall assign the key the corrupt status.
			</wsdl:documentation>
			<wsdl:input message="tas:CreateRSAKeyPairRequest"/>
			<wsdl:output message="tas:CreateRSAKeyPairResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetKeyStatus">
			<wsdl:documentation>
				This operation returns the status of a key.<br/>
				Keys are uniquely identified using key IDs. If no key is stored under the requested key ID in the keystore, an InvalidKeyID fault is produced.
				Otherwise, the status of the key is returned.
			</wsdl:documentation>
			<wsdl:input message="tas:GetKeyStatusRequest"/>
			<wsdl:output message="tas:GetKeyStatusResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetPrivateKeyStatus">
			<wsdl:documentation>
				This operation returns whether a key pair contains a private key.<br/>
				Keys are uniquely identified using key IDs. If no key is stored under the requested key ID in the keystore or the key identified by the requested key ID does not identify a key pair,
				the device shall produce an InvalidKeyID fault.
				Otherwise, this operation returns true if the key pair identified by the key ID contains a private key, and false otherwise.
			</wsdl:documentation>
			<wsdl:input message="tas:GetPrivateKeyStatusRequest"/>
			<wsdl:output message="tas:GetPrivateKeyStatusResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetAllKeys">
			<wsdl:documentation>
				This operation returns information about all keys that are stored in the device’s keystore.<br/>
				This operation may be used, e.g., if a client lost track of which keys are present on the device.
				If no key is stored on the device, an empty list is returned.
			</wsdl:documentation>
			<wsdl:input message="tas:GetAllKeysRequest"/>
			<wsdl:output message="tas:GetAllKeysResponse"/>
		</wsdl:operation>
		<wsdl:operation name="DeleteKey">
			<wsdl:documentation>
				This operation deletes a key from the device’s keystore.<br/>
				Keys are uniquely identified using key IDs. If no key is stored under the requested key ID in the keystore, a device shall produce an InvalidArgVal fault.
				If a reference exists for the specified key, a device shall produce the corresponding fault and shall not delete the key.
				If there is a key under the requested key ID stored in the keystore and the key could not be deleted, a device shall produce a KeyDeletion fault.
				If the key has the status generating, a device shall abort the generation of the key and delete from the keystore all data generated for this key.
				After a key is successfully deleted, the device may assign its former ID to other keys.
			</wsdl:documentation>
			<wsdl:input message="tas:DeleteKeyRequest"/>
			<wsdl:output message="tas:DeleteKeyResponse"/>
		</wsdl:operation>
		<wsdl:operation name="CreatePKCS10CSR">
			<wsdl:documentation>
				This operation generates a DER-encoded PKCS#10 v1.7 certification request (sometimes also called certificate signing request or CSR) as specified in RFC 2986 
				for a public key on the device.<br/>
				The key pair that contains the public key for which a certification request shall be produced is specified by its key ID.
				If no key is stored under the requested KeyID or the key specified by the requested KeyID is not an asymmetric key pair, an invalid key ID fault shall be produced and 
				no CSR shall be generated.<br/>
				
				A device that supports this command shall as minimum support the sha-1WithRSAEncryption signature algorithm as specified in RFC 3279. 
				If the specified signature algorithm is not supported by the device, an UnsupportedSignatureAlgorithm fault shall be produced and no CSR shall be generated.<br/>
				
				If the public key identified by the requested Key ID is an invalid input to the specified signature algorithm, a KeySignatureAlgorithmMismatch fault shall be produced 
				and no CSR shall be generated.
				If the key pair does not have status ok, a device shall produce an InvalidKeyStatus fault and no CSR shall be generated.
			</wsdl:documentation>
			<wsdl:input message="tas:CreatePKCS10CSRRequest"/>
			<wsdl:output message="tas:CreatePKCS10CSRResponse"/>
		</wsdl:operation>
		<wsdl:operation name="CreateSelfSignedCertificate">
			<wsdl:documentation>
				This operation generates for a public key on the device a self-signed X.509 certificate that complies to RFC 5280.<br/>
				The X509Version parameter specifies the version of X.509 that the generated certificate shall comply to.
				A device that supports this command shall support the generation of X.509v3 certificates as specified in RFC 5280 and may additionally be able to handle other X.509 certificate formats
				as indicated by the X.509Versions capability.<br/>
				The key pair that contains the public key for which a self-signed certificate shall be produced is specified by its key pair ID.
				The subject parameter describes the entity that the public key belongs to.
				If the key pair does not have status ok, a device shall produce an InvalidKeyStatus fault and no certificate shall be generated.
				
				The signature algorithm parameter determines which signature algorithm shall be used for signing the certification request with the public key specified by the key ID parameter.
				A device that supports this command shall as minimum support the sha-1WithRSAEncryption signature algorithm as specified in RFC 3279. 
				The Extensions parameter specifies potential X509v3 extensions that shall be contained in the certificate.
				A device that supports this command shall support the extensions that are defined in [RFC 5280], Sect. 4.2] as mandatory for CAs that issue self-signed certificates.<br/>
				
				Certificates are uniquely identified using certificate IDs. If the command was successful, the device generates a new ID for the generated certificate and returns this ID.<br/>
				If the device does not have not enough storage capacity for storing the certificate to be created, the maximum number of certificates reached fault shall be produced and no certificate shall be generated.
			</wsdl:documentation>
			<wsdl:input message="tas:CreateSelfSignedCertificateRequest"/>
			<wsdl:output message="tas:CreateSelfSignedCertificateResponse"/>
		</wsdl:operation>
		<wsdl:operation name="UploadCertificate">
			<wsdl:documentation>
				This operation uploads an X.509 certificate as specified by [RFC 5280] in DER encoding and the public key in the certificate to a device’s keystore.<br/>
				A device that supports this command shall be able to handle X.509v3 certificates as specified in RFC 5280 and may additionally be able to handle other X.509 certificate formats as indicated by the X.509Versions capability.
				A device that supports this command shall support sha1-WithRSAEncryption as certificate signature algorithm.<br/>
				
				Certificates are uniquely identified using certificate IDs, and key pairs are uniquely identified using key IDs.
				The device shall generate a new certificate ID for the uploaded certificate.<br/>
				Certain certificate usages, e.g. TLS server authentication, require the private key that corresponds to the public key in the certificate to be present in the keystore.
				In such cases, the client may indicate that it expects the device to produce a fault if the matching private key for
				the uploaded certificate is not present in the keystore by setting the PrivateKeyRequired argument in the upload request to true.<br/>
				
				The uploaded certificate has to be linked to a key pair in the keystore.
				If no private key is required for the public key in the certificate and a key pair exists in the keystore with a public key equal to the public key in the certificate,
				the uploaded certificate is linked to the key pair identified by the supplied key ID by adding a reference from the certificate to the key pair.
				If no private key is required for the public key in the certificate and no key pair exists with the public key equal to the public key in the certificate,
				a new key pair with status ok is created with the public key from the certificate, and this key pair is linked to the uploaded certificate by adding a reference from 
				the certificate to the key pair.
				If a private key is required for the public key in the certificate, and a key pair exists in the keystore with a private key that matches the public key in the certificate,
				the uploaded certificate is linked to this keypair by adding a reference from the certificate to the key pair.
				If a private key is required for the public key and no such keypair exists in the keystore, the NoMatchingPrivateKey fault shall be produced and the certificate
				shall not be stored in the keystore.
				If the key pair that the certificate shall be linked to does not have status ok, an InvalidKeyID fault is produced, and the uploaded certificate is not stored in the keystore.
				If the device cannot process the uploaded certificate, a BadCertificate fault is produced and neither the uploaded certificate nor the public key are stored in the device’s keystore.
				The BadCertificate fault shall not be produced based on the mere fact that the device’s current time lies outside the interval defined by the notBefore and notAfter fields as specified by [RFC 5280], Sect. 4.1 .
				This operation shall not mark the uploaded certificate as trusted.<br/>
				
				If the device does not have not enough storage capacity for storing the certificate to be uploaded, the maximum number of certificates reached fault shall be produced
				and no certificate shall be uploaded.
				If the device does not have not enough storage capacity for storing the key pair that eventually has to be created, the device shall generate a maximum number of keys reached fault.
				Furthermore the device shall not generate a key pair and no certificate shall be stored.
			</wsdl:documentation>
			<wsdl:input message="tas:UploadCertificateRequest"/>
			<wsdl:output message="tas:UploadCertificateResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetCertificate">
			<wsdl:documentation>
				This operation returns a specific certificate from the device’s keystore.<br/>
				Certificates are uniquely identified using certificate IDs. If no certificate is stored under the requested certificate ID in the keystore, an InvalidArgVal fault is produced.
				It shall be noted that this command does not return the private key that is associated to the public key in the certificate.
			</wsdl:documentation>
			<wsdl:input message="tas:GetCertificateRequest"/>
			<wsdl:output message="tas:GetCertificateResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetAllCertificates">
			<wsdl:documentation>
				This operation returns the IDs of all certificates that are stored in the device’s keystore.<br/>
				This operation may be used, e.g.,  if a client lost track of which certificates are present on the device.
				If no certificate is stored in the device’s keystore, an empty list is returned.
			</wsdl:documentation>
			<wsdl:input message="tas:GetAllCertificatesRequest"/>
			<wsdl:output message="tas:GetAllCertificatesResponse"/>
		</wsdl:operation>
		<wsdl:operation name="DeleteCertificate">
			<wsdl:documentation>
				This operation deletes a certificate from the device’s keystore.<br/>
				The operation shall not delete the public key that is contained in the certificate from the keystore.
				Certificates are uniquely identified using certificate IDs. If no certificate is stored under the requested certificate ID in the keystore, an InvalidArgVal fault is produced.
				If there is a certificate under the requested certificate ID stored in the keystore and the certificate could not be deleted, a CertificateDeletion fault is produced.
				If a reference exists for the specified certificate, the certificate shall not be deleted and the corresponding fault shall be produced.
				After a certificate has been  successfully deleted, the device may assign its former ID to other certificates.
			</wsdl:documentation>
			<wsdl:input message="tas:DeleteCertificateRequest"/>
			<wsdl:output message="tas:DeleteCertificateResponse"/>
		</wsdl:operation>
		<wsdl:operation name="CreateCertificationPath">
			<wsdl:documentation>
				This operation creates a sequence of certificates that may be used, e.g., for certification path validation or for TLS server authentication.<br/>
				Certification paths are uniquely identified using certification path IDs. Certificates are uniquely identified using certificate IDs.
				A certification path contains a sequence of certificate IDs.
				If there is a certificate ID in the sequence of supplied certificate IDs for which no certificate exists in the device’s keystore, the corresponding fault shall be produced
				and no certification path shall be created.<br/>
				
				The signature of each certificate in the certification path except for the last one must be verifiable with the public key contained in the next certificate in the path.
				If there is a certificate ID in the request other than the last ID for which the corresponding certificate cannot be verified with the public key in the certificate identified 
				by the next certificate ID, an InvalidCertificateChain fault shall be produced and no certification path shall be created.
			</wsdl:documentation>
			<wsdl:input message="tas:CreateCertificationPathRequest"/>
			<wsdl:output message="tas:CreateCertificationPathResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetCertificationPath">
			<wsdl:documentation>
				This operation returns a specific certification path from the device’s keystore.<br/>
				Certification paths are uniquely identified using certification path IDs.
				If no certification path is stored under the requested ID in the keystore, an InvalidArgVal fault is produced.
			</wsdl:documentation>
			<wsdl:input message="tas:GetCertificationPathRequest"/>
			<wsdl:output message="tas:GetCertificationPathResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetAllCertificationPaths">
			<wsdl:documentation>
				This operation returns the IDs of all certification paths that are stored in the device’s keystore.<br/>
				This operation may be used, e.g., if a client lost track of which certificates are present on the device.
				If no certification path is stored on the device, an empty list is returned.
			</wsdl:documentation>
			<wsdl:input message="tas:GetAllCertificationPathsRequest"/>
			<wsdl:output message="tas:GetAllCertificationPathsResponse"/>
		</wsdl:operation>
		<wsdl:operation name="DeleteCertificationPath">
			<wsdl:documentation>
				This operation deletes a certification path from the device’s keystore.<br/>
				This operation shall not delete the certificates that are referenced by the certification path.
				Certification paths are uniquely identified using certification path IDs.
				If no certification path is stored under the requested certification path ID in the keystore, an InvalidArgVal fault is produced.
				If there is a certification path under the requested certification path ID stored in the keystore and the certification path could not be deleted,
				a CertificationPathDeletion fault is produced.
				If a reference exists for the specified certification path, the certification path shall not be deleted and the corresponding fault shall be produced.
				After a certification path is successfully deleted, the device may assign its former ID to other certification paths.
			</wsdl:documentation>
			<wsdl:input message="tas:DeleteCertificationPathRequest"/>
			<wsdl:output message="tas:DeleteCertificationPathResponse"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:portType name="TLSServer">
		<wsdl:documentation>TLS server functionality.</wsdl:documentation>
		<wsdl:operation name="AddServerCertificateAssignment">
			<wsdl:documentation>
				This operation assigns a key pair and certificate along with a certification path (certificate chain) to the TLS server on the device.
				The TLS server shall use this information for key exchange during the TLS handshake, particularly for constructing server certificate messages as specified in RFC 4346 and RFC 2246.<br/>
				
				Certification paths are identified by their certification path IDs in the keystore. The first certificate in the certification path must be the TLS server certificate.
				Since each certificate has exactly one associated key pair, a reference to the key pair that is associated with the server certificate is not supplied explicitly.
				Devices shall obtain the private key or results of operations under the private key by suitable internal interaction with the keystore.<br/>
				If a device chooses to perform a TLS key exchange based on the supplied certification path,  it shall use the key pair that is associated with the server certificate for 
				key exchange and transmit the certification path to TLS clients as-is, i.e., the device shall not check conformance of the certification path to RFC 4346 norRFC 2246.
				In order to use the server certificate during the TLS handshake, the corresponding private key is required.
				Therefore, if the key pair that is associated with the server certificate, i.e., the first certificate in the certification path, does not have an associated private key, 
				the NoPrivateKey fault is produced and the certification path is not associated to the TLS server.<br/>
				A TLS server may present different certification paths to different clients during the TLS handshake instead of presenting the same certification path to all clients.
				Therefore more than one certification path may be assigned to the TLS server.<br/>
				If the maximum number of certification paths that may be assigned to the TLS server simultaneously is reached, the device shall generate a MaximumNumberOfCertificationPathsReached 
				fault and the requested certification path shall not be assigned to the TLS server.
			</wsdl:documentation>
			<wsdl:input message="tas:AddServerCertificateAssignmentRequest"/>
			<wsdl:output message="tas:AddServerCertificateAssignmentResponse"/>
		</wsdl:operation>
		<wsdl:operation name="RemoveServerCertificateAssignment">
			<wsdl:documentation>
				This operation removes a key pair and certificate assignment (including certification path) to the TLS server on the device.<br/>
				Certification paths are identified using certification path IDs. If the supplied certification path ID is not associated to the TLS server, an InvalidArgVal fault is produced.
			</wsdl:documentation>
			<wsdl:input message="tas:RemoveServerCertificateAssignmentRequest"/>
			<wsdl:output message="tas:RemoveServerCertificateAssignmentResponse"/>
		</wsdl:operation>
		<wsdl:operation name="ReplaceServerCertificateAssignment">
			<wsdl:documentation>
				This operation replaces an existing key pair and certificate assignment to the TLS server on the device by a new key pair and certificate assignment (including certification paths).<br/>
				
				After the replacement, the TLS server shall use the new certificate and certification path exactly in those cases in which it would have used the old certificate and certification path.
				Therefore, especially in the case that several server certificates are assigned to the TLS server, clients that wish to replace an old certificate assignment by a new assignment
				should use this operation instead of a combination of the Add TLS Server Certificate Assignment and the Remove TLS Server Certificate Assignment operations.<br/>
				
				Certification paths are identified using certification path IDs. If the supplied old certification path ID is not associated to the TLS server, or no certification path exists
				under the new certification path ID, the corresponding InvalidArgVal faults are produced and the associations are unchanged.
				The first certificate in the new certification path must be the TLS server certificate.<br/>
				Since each certificate has exactly one associated key pair, a reference to the key pair that is associated with the new server certificate is not supplied explicitly.
				Devices shall obtain the private key or results of operations under the private key by suitable internal interaction with the keystore.<br/>
				If a device chooses to perform a TLS key exchange based on the new certification path,  it shall use the key pair that is associated with the server certificate
				for key exchange and transmit the certification path to TLS clients as-is, i.e., the device shall not check conformance of the certification path to RFC 4346 norRFC 2246.
				In order to use the server certificate during the TLS handshake, the corresponding private key is required.
				Therefore, if the key pair that is associated with the server certificate, i.e., the first certificate in the certification path, does not have an associated private key,
				the NoPrivateKey fault is produced and the certification path is not associated to the TLS server.
			</wsdl:documentation>
			<wsdl:input message="tas:ReplaceServerCertificateAssignmentRequest"/>
			<wsdl:output message="tas:ReplaceServerCertificateAssignmentResponse"/>
		</wsdl:operation>
		<wsdl:operation name="GetAssignedServerCertificates">
			<wsdl:documentation>
				This operation returns the IDs of all key pairs and certificates (including certification paths) that are assigned to the TLS server on the device.<br/>
				This operation may be used, e.g., if a client lost track of the certification path assignments on the device.
				If no certification path is assigned to the TLS server, an empty list is returned.
			</wsdl:documentation>
			<wsdl:input message="tas:GetAssignedServerCertificatesRequest"/>
			<wsdl:output message="tas:GetAssignedServerCertificatesResponse"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="AdvancedSecurityServiceBinding" type="tas:AdvancedSecurityService">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="GetServiceCapabilities">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetServiceCapabilities"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:binding name="KeystoreBinding" type="tas:Keystore">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="CreateRSAKeyPair">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/CreateRSAKeyPair"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetKeyStatus">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetKeyStatus"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetPrivateKeyStatus">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetPrivateKeyStatus"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetAllKeys">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetAllKeys"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="DeleteKey">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/DeleteKey"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="CreatePKCS10CSR">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/CreatePKCS10CSR"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="CreateSelfSignedCertificate">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/CreateSelfSignedCertificate"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="UploadCertificate">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/UploadCertificate"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetCertificate">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetCertificate"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetAllCertificates">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetAllCertificates"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="DeleteCertificate">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/DeleteCertificate"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="CreateCertificationPath">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/CreateCertificationPath"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetCertificationPath">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetCertificationPath"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetAllCertificationPaths">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetAllCertificationPaths"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="DeleteCertificationPath">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/DeleteCertificationPath"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:binding name="TLSServerBinding" type="tas:TLSServer">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="AddServerCertificateAssignment">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/AddServerCertificateAssignment"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="RemoveServerCertificateAssignment">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/RemoveServerCertificateAssignment"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="ReplaceServerCertificateAssignment">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/ReplaceServerCertificateAssignment"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="GetAssignedServerCertificates">
			<soap:operation soapAction="http://www.onvif.org/ver10/advancedsecurity/wsdl/GetAssignedServerCertificates"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
</wsdl:definitions>

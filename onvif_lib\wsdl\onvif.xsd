<?xml version="1.0" encoding="utf-8"?>
<!--<?xml-stylesheet type="text/xsl" href="onvif-schema-viewer.xsl"?>-->
<!--
Copyright (c) 2008-2014 by ONVIF: Open Network Video Interface Forum. All rights reserved.

Recipients of this document may copy, distribute, publish, or display this document so long as this copyright notice, license and disclaimer are retained with all copies of the document. No license is granted to modify this document.

THIS DOCUMENT IS PROVIDED "AS IS," AND THE CORPORATION AND ITS MEMBERS AND THEIR AFFILIATES, MAKE NO REPRESENTATIONS OR WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO, WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, OR TITLE; THAT THE CONTENTS OF THIS DOCUMENT ARE SUITABLE FOR ANY PURPOSE; OR THAT THE IMPLEMENTATION OF SUCH CONTENTS WILL NOT INFRINGE ANY PATENTS, COPYRIGHTS, <PERSON>RADEMARKS OR OTHER RIGHTS.
IN NO EVENT WILL THE CORPORATION OR ITS MEMBERS OR THEIR AFFILIATES BE LIABLE FOR ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL, PUNITIVE OR CONSEQUENTIAL DAMAGES, ARISING OUT OF OR RELATING TO ANY USE OR DISTRIBUTION OF THIS DOCUMENT, WHETHER OR NOT (1) THE CORPORATION, MEMBERS OR THEIR AFFILIATES HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES, OR (2) SUCH DAMAGES WERE REASONABLY FORESEEABLE, AND ARISING OUT OF OR RELATING TO ANY USE OR DISTRIBUTION OF THIS DOCUMENT.  THE FOREGOING DISCLAIMER AND LIMITATION ON LIABILITY DO NOT APPLY TO, INVALIDATE, OR LIMIT REPRESENTATIONS AND WARRANTIES MADE BY THE MEMBERS AND THEIR RESPECTIVE AFFILIATES TO THE CORPORATION AND OTHER MEMBERS IN CERTAIN WRITTEN POLICIES OF THE CORPORATION.
-->
<xs:schema xmlns:tt="http://www.onvif.org/ver10/schema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:wsnt="http://docs.oasis-open.org/wsn/b-2" xmlns:xop="http://www.w3.org/2004/08/xop/include" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" targetNamespace="http://www.onvif.org/ver10/schema" elementFormDefault="qualified" version="2.4.2">
	<xs:import namespace="http://www.w3.org/2005/05/xmlmime" schemaLocation="./xmlmime"/>
    <xs:import namespace="http://schemas.xmlsoap.org/soap/envelope/" schemaLocation="./envelope"/>
    <xs:import namespace="http://docs.oasis-open.org/wsn/b-2" schemaLocation="./b-2.xsd"/>
    <xs:import namespace="http://www.w3.org/2004/08/xop/include" schemaLocation="./include"/>
	<!--===============================-->
	<!--         Generic Types         -->
	<!--===============================-->
	<xs:complexType name="DeviceEntity">
		<xs:annotation>
			<xs:documentation>Base class for physical entities like inputs and outputs.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="token" type="tt:ReferenceToken" use="required">
			<xs:annotation>
				<xs:documentation>Unique identifier referencing the physical entity.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ReferenceToken">
		<xs:annotation>
			<xs:documentation>Unique identifier for a physical or logical resource.
			Tokens should be assigned such that they are unique within a device. Tokens must be at least unique within its class.
			Length up to 64 characters.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Name">
		<xs:annotation>
			<xs:documentation>User readable name. Length up to 64 characters.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:maxLength value="64"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="IntRectangle">
		<xs:annotation>
			<xs:documentation>Rectangle defined by lower left corner position and size. Units are pixel.</xs:documentation>
		</xs:annotation>
		<xs:attribute name="x" type="xs:int" use="required"/>
		<xs:attribute name="y" type="xs:int" use="required"/>
		<xs:attribute name="width" type="xs:int" use="required"/>
		<xs:attribute name="height" type="xs:int" use="required"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IntRectangleRange">
		<xs:annotation>
			<xs:documentation>Range of a rectangle. The rectangle itself is defined by lower left corner position and size. Units are pixel.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="XRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of X-axis.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="YRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of Y-axis.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WidthRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of width.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="HeightRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of height.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IntRange">
		<xs:annotation>
			<xs:documentation>Range of values greater equal Min value and less equal Max value.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Min" type="xs:int"/>
			<xs:element name="Max" type="xs:int"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FloatRange">
		<xs:annotation>
			<xs:documentation>Range of values greater equal Min value and less equal Max value.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Min" type="xs:float"/>
			<xs:element name="Max" type="xs:float"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DurationRange">
		<xs:annotation>
			<xs:documentation>Range of duration greater equal Min duration and less equal Max duration.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Min" type="xs:duration"/>
			<xs:element name="Max" type="xs:duration"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IntList">
		<xs:annotation>
			<xs:documentation>List of values.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Items" type="xs:int" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IntAttrList">
		<xs:list itemType="xs:int"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="FloatAttrList">
		<xs:list itemType="xs:float"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="StringAttrList">
		<xs:list itemType="xs:string"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="FloatList">
		<xs:sequence>
			<xs:element name="Items" type="xs:float" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnyHolder">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--      End, Generic Types       -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--      Media Related Types      -->
	<!--===============================-->
	<xs:complexType name="VideoSource">
		<xs:annotation>
			<xs:documentation>Representation of a physical video input.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Framerate" type="xs:float">
						<xs:annotation>
							<xs:documentation>Frame rate in frames per second.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Resolution" type="tt:VideoResolution">
						<xs:annotation>
							<xs:documentation>Horizontal and vertical resolution</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Imaging" type="tt:ImagingSettings" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional configuration of the image sensor.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:VideoSourceExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Imaging" type="tt:ImagingSettings20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the image sensor. To be used if imaging service 2.00 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioSource">
		<xs:annotation>
			<xs:documentation>Representation of a physical audio input.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Channels" type="xs:int">
						<xs:annotation>
							<xs:documentation>number of available audio channels. (1: mono, 2: stereo) </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Profile">
		<xs:annotation>
			<xs:documentation>
			A media profile consists of a set of media configurations. Media profiles are used by a client
			to configure properties of a media stream from an NVT.<br/>
			An NVT shall provide at least one media profile at boot. An NVT should provide “ready to use”
			profiles for the most common media configurations that the device offers.<br/>
			A profile consists of a set of interconnected configuration entities. Configurations are provided
			by the NVT and can be either static or created dynamically by the NVT. For example, the
			dynamic configurations can be created by the NVT depending on current available encoding
			resources.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Name" type="tt:Name">
				<xs:annotation>
					<xs:documentation>User readable name of the profile.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoSourceConfiguration" type="tt:VideoSourceConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Video input.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioSourceConfiguration" type="tt:AudioSourceConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Audio input.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoEncoderConfiguration" type="tt:VideoEncoderConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Video encoder.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioEncoderConfiguration" type="tt:AudioEncoderConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Audio encoder.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoAnalyticsConfiguration" type="tt:VideoAnalyticsConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the video analytics module and rule engine.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZConfiguration" type="tt:PTZConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the pan tilt zoom unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MetadataConfiguration" type="tt:MetadataConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the metadata stream.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ProfileExtension" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Extensions defined in ONVIF 2.0</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken" use="required">
			<xs:annotation>
				<xs:documentation>Unique identifier of the profile.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="fixed" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>A value of true signals that the profile cannot be deleted. Default is false.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="AudioOutputConfiguration" type="tt:AudioOutputConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Audio output.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioDecoderConfiguration" type="tt:AudioDecoderConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional configuration of the Audio decoder.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ProfileExtension2" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:element name="VideoSourceConfiguration" type="tt:VideoSourceConfiguration"/>
	<xs:element name="AudioSourceConfiguration" type="tt:AudioSourceConfiguration"/>
	<xs:element name="VideoEncoderConfiguration" type="tt:VideoEncoderConfiguration"/>
	<xs:element name="AudioEncoderConfiguration" type="tt:AudioEncoderConfiguration"/>
	<xs:element name="VideoAnalyticsConfiguration" type="tt:VideoAnalyticsConfiguration"/>
	<xs:element name="PTZConfiguration" type="tt:PTZConfiguration"/>
	<xs:element name="MetadataConfiguration" type="tt:MetadataConfiguration"/>
	<xs:element name="AudioOutputConfiguration" type="tt:AudioOutputConfiguration"/>
	<xs:element name="AudioDecoderConfiguration" type="tt:AudioDecoderConfiguration"/>
	<!--===============================-->
	<xs:complexType name="ConfigurationEntity">
		<xs:annotation>
			<xs:documentation>Base type defining the common properties of a configuration.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Name" type="tt:Name">
				<xs:annotation>
					<xs:documentation>User readable name. Length up to 64 characters.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UseCount" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of internal references currently using this configuration. <br/>This parameter is read-only and cannot be changed by a set request. <br/>For example the value increases if the configuration is added to a media profile or attached to a PaneConfiguration.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken" use="required">
			<xs:annotation>
				<xs:documentation>Token that uniquely refernces this configuration. Length up to 64 characters.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--===============================-->
	<!--   VideoSourceConfiguration   -->
	<!--===============================-->
	<xs:complexType name="VideoSourceConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="SourceToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Reference to the physical input.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Bounds" type="tt:IntRectangle">
						<xs:annotation>
							<xs:documentation>Rectangle specifying the Video capturing area. The capturing area shall not be larger than the whole Video source area.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="Extension" type="tt:VideoSourceConfigurationExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationExtension">
		<xs:sequence>
			<xs:element name="Rotate" type="tt:Rotate" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure rotation of captured image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceConfigurationExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationExtension2">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Rotate">
		<xs:sequence>
			<xs:element name="Mode" type="tt:RotateMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable Rotation feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Degree" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to configure how much degree of clockwise rotation of image  for On mode. Omitting this parameter for On mode means 180 degree rotation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RotateExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RotateExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="RotateMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="AUTO"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationOptions">
		<xs:sequence>
			<xs:element name="BoundsRange" type="tt:IntRectangleRange">
				<xs:annotation>
					<xs:documentation>Supported range for the capturing area.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoSourceTokensAvailable" type="tt:ReferenceToken" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of physical inputs.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceConfigurationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Rotate" type="tt:RotateOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for Rotation feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoSourceConfigurationOptionsExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoSourceConfigurationOptionsExtension2">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RotateOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:RotateMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options of Rotate mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DegreeList" type="tt:IntList" minOccurs="0">
				<xs:annotation>
					<xs:documentation>List of supported degree value for rotation.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RotateOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RotateOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--   VideoEncoderConfiguration   -->
	<!--===============================-->
	<xs:complexType name="VideoEncoderConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="Encoding" type="tt:VideoEncoding">
						<xs:annotation>
							<xs:documentation>Used video codec, either Jpeg, H.264 or Mpeg4</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Resolution" type="tt:VideoResolution">
						<xs:annotation>
							<xs:documentation>Configured video resolution</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Quality" type="xs:float">
						<xs:annotation>
							<xs:documentation>Relative value for the video quantizers and the quality of the video. A high value within supported quality range means higher quality</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="RateControl" type="tt:VideoRateControl" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure rate control related parameters.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="MPEG4" type="tt:Mpeg4Configuration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure Mpeg4 related parameters.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="H264" type="tt:H264Configuration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure H.264 related parameters.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration">
						<xs:annotation>
							<xs:documentation>Defines the multicast settings that could be used for video streaming.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SessionTimeout" type="xs:duration">
						<xs:annotation>
							<xs:documentation>The rtsp session timeout for the related video stream</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="VideoEncoding">
		<xs:restriction base="xs:string">
			<xs:enumeration value="JPEG"/>
			<xs:enumeration value="MPEG4"/>
			<xs:enumeration value="H264"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Mpeg4Profile">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SP"/>
			<xs:enumeration value="ASP"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="H264Profile">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Baseline"/>
			<xs:enumeration value="Main"/>
			<xs:enumeration value="Extended"/>
			<xs:enumeration value="High"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="VideoResolution">
		<xs:sequence>
			<xs:element name="Width" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of the columns of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Height" type="xs:int">
				<xs:annotation>
					<xs:documentation>Number of the lines of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoRateControl">
		<xs:sequence>
			<xs:element name="FrameRateLimit" type="xs:int">
				<xs:annotation>
					<xs:documentation>Maximum output framerate in fps. If an EncodingInterval is provided the resulting encoded framerate will be reduced by the given factor.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingInterval" type="xs:int">
				<xs:annotation>
					<xs:documentation>Interval at which images are encoded and transmitted. (A value of 1 means that every frame is encoded, a value of 2 means that every 2nd frame is encoded ...)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BitrateLimit" type="xs:int">
				<xs:annotation>
					<xs:documentation>the maximum output bitrate in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Mpeg4Configuration">
		<xs:sequence>
			<xs:element name="GovLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Determines the interval in which the I-Frames will be coded. An entry of 1 indicates I-Frames are continuously generated. An entry of 2 indicates that every 2nd image is an I-Frame, and 3 only every 3rd frame, etc. The frames in between are coded as P or B Frames.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Mpeg4Profile" type="tt:Mpeg4Profile">
				<xs:annotation>
					<xs:documentation>the Mpeg4 profile, either simple profile (SP) or advanced simple profile (ASP)</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="H264Configuration">
		<xs:sequence>
			<xs:element name="GovLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Group of Video frames length. Determines typically the interval in which the I-Frames will be coded. An entry of 1 indicates I-Frames are continuously generated. An entry of 2 indicates that every 2nd image is an I-Frame, and 3 only every 3rd frame, etc. The frames in between are coded as P or B Frames.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264Profile" type="tt:H264Profile">
				<xs:annotation>
					<xs:documentation>the H.264 profile, either baseline, main, extended or high</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoEncoderConfigurationOptions">
		<xs:sequence>
			<xs:element name="QualityRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Range of the quality values. A high value means higher quality.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="JPEG" type="tt:JpegOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional JPEG encoder settings ranges (See also Extension element).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MPEG4" type="tt:Mpeg4Options" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional MPEG-4 encoder settings ranges (See also Extension element).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264" type="tt:H264Options" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional H.264 encoder settings ranges (See also Extension element).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoEncoderOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoEncoderOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="JPEG" type="tt:JpegOptions2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional JPEG encoder settings ranges.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MPEG4" type="tt:Mpeg4Options2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional MPEG-4 encoder settings ranges.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264" type="tt:H264Options2" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional H.264 encoder settings ranges.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoEncoderOptionsExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoEncoderOptionsExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="JpegOptions">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported image sizes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FrameRateRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported frame rate in fps (frames per second).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingIntervalRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported encoding interval range. The encoding interval corresponds to the number of frames devided by the encoded frames. An encoding interval value of "1" means that all frames are encoded.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="JpegOptions2">
		<xs:complexContent>
			<xs:extension base="tt:JpegOptions">
				<xs:sequence>
					<xs:element name="BitrateRange" type="tt:IntRange">
						<xs:annotation>
							<xs:documentation>Supported range of encoded bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Mpeg4Options">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported image sizes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GovLengthRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported group of Video frames length. This value typically corresponds to the I-Frame distance.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FrameRateRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported frame rate in fps (frames per second).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingIntervalRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported encoding interval range. The encoding interval corresponds to the number of frames devided by the encoded frames. An encoding interval value of "1" means that all frames are encoded.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Mpeg4ProfilesSupported" type="tt:Mpeg4Profile" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported MPEG-4 profiles.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Mpeg4Options2">
		<xs:complexContent>
			<xs:extension base="tt:Mpeg4Options">
				<xs:sequence>
					<xs:element name="BitrateRange" type="tt:IntRange">
						<xs:annotation>
							<xs:documentation>Supported range of encoded bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="H264Options">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported image sizes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="GovLengthRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported group of Video frames length. This value typically corresponds to the I-Frame distance.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FrameRateRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported frame rate in fps (frames per second).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EncodingIntervalRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported encoding interval range. The encoding interval corresponds to the number of frames devided by the encoded frames. An encoding interval value of "1" means that all frames are encoded.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264ProfilesSupported" type="tt:H264Profile" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported H.264 profiles.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="H264Options2">
		<xs:complexContent>
			<xs:extension base="tt:H264Options">
				<xs:sequence>
					<xs:element name="BitrateRange" type="tt:IntRange">
						<xs:annotation>
							<xs:documentation>Supported range of encoded bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--    AudioSourceConfiguration   -->
	<!--===============================-->
	<xs:complexType name="AudioSourceConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="SourceToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Token of the Audio Source the configuration applies to</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioSourceConfigurationOptions">
		<xs:sequence>
			<xs:element name="InputTokensAvailable" type="tt:ReferenceToken" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Tokens of the audio source the configuration can be used for.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:AudioSourceOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioSourceOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--   AudioEncoderConfiguration   -->
	<!--===============================-->
	<xs:complexType name="AudioEncoderConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="Encoding" type="tt:AudioEncoding">
						<xs:annotation>
							<xs:documentation>Audio codec used for encoding the audio input (either G.711, G.726 or AAC)</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Bitrate" type="xs:int">
						<xs:annotation>
							<xs:documentation>The output bitrate in kbps.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SampleRate" type="xs:int">
						<xs:annotation>
							<xs:documentation>The output sample rate in kHz.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration">
						<xs:annotation>
							<xs:documentation>Defines the multicast settings that could be used for video streaming.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SessionTimeout" type="xs:duration">
						<xs:annotation>
							<xs:documentation>The rtsp session timeout for the related audio stream</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="AudioEncoding">
		<xs:restriction base="xs:string">
			<xs:enumeration value="G711"/>
			<xs:enumeration value="G726"/>
			<xs:enumeration value="AAC"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="AudioEncoderConfigurationOptions">
		<xs:sequence>
			<xs:element name="Options" type="tt:AudioEncoderConfigurationOption" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>list of supported AudioEncoderConfigurations</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioEncoderConfigurationOption">
		<xs:sequence>
			<xs:element name="Encoding" type="tt:AudioEncoding">
				<xs:annotation>
					<xs:documentation>The enoding used for audio data (either G.711, G.726 or AAC)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BitrateList" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported bitrates in kbps for the specified Encoding</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SampleRateList" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported Sample Rates in kHz for the specified Encoding</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--  VideoAnalyticsConfiguration  -->
	<!--===============================-->
	<xs:complexType name="VideoAnalyticsConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="AnalyticsEngineConfiguration" type="tt:AnalyticsEngineConfiguration"/>
					<xs:element name="RuleEngineConfiguration" type="tt:RuleEngineConfiguration"/>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--    MetadataConfiguration      -->
	<!--===============================-->
	<xs:complexType name="MetadataConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="PTZStatus" type="tt:PTZFilter" minOccurs="0">
						<xs:annotation>
							<xs:documentation>optional element to configure which PTZ related data is to include in the metadata stream</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Events" type="tt:EventSubscription" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Optional element to configure the streaming of events. A client might be interested in receiving all, 
								none or some of the events produced by the device:<ul>
									<li>To get all events: Include the Events element but do not include a filter.</li>
									<li>To get no events: Do not include the Events element.</li>
									<li>To get only some events: Include the Events element and include a filter in the element.</li>
								</ul>
							</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Analytics" type="xs:boolean" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Defines whether the streamed metadata will include metadata from the analytics engines (video, cell motion, audio etc.)</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration">
						<xs:annotation>
							<xs:documentation>Defines the multicast settings that could be used for video streaming.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SessionTimeout" type="xs:duration">
						<xs:annotation>
							<xs:documentation>The rtsp session timeout for the related audio stream</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
					<xs:element name="AnalyticsEngineConfiguration" type="tt:AnalyticsEngineConfiguration" minOccurs="0"/>
					<xs:element name="Extension" type="tt:MetadataConfigurationExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZFilter">
		<xs:sequence>
			<xs:element name="Status" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the metadata stream shall contain the PTZ status (IDLE, MOVING or UNKNOWN)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Position" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the metadata stream shall contain the PTZ position</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EventSubscription">
		<xs:annotation>
			<xs:documentation>Subcription handling in the same way as base notification subscription.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Filter" type="wsnt:FilterType" minOccurs="0"/>
			<xs:element name="SubscriptionPolicy" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataConfigurationOptions">
		<xs:sequence>
			<xs:element name="PTZStatusFilterOptions" type="tt:PTZStatusFilterOptions"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZStatusFilterOptions">
		<xs:sequence>
			<xs:element name="PanTiltStatusSupported" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream pan or tilt status information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomStatusSupported" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream zoom status inforamtion.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="PanTiltPositionSupported" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream the pan or tilt position.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomPositionSupported" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>True if the device is able to stream zoom position information.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZStatusFilterOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZStatusFilterOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--          VideoOutput            -->
	<!--===============================-->
	<xs:complexType name="VideoOutput">
		<xs:annotation>
			<xs:documentation>Representation of a physical video outputs.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Layout" type="tt:Layout"/>
					<xs:element name="Resolution" type="tt:VideoResolution" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Resolution of the display in Pixel.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="RefreshRate" type="xs:float" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Refresh rate of the display in Hertz.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="AspectRatio" type="xs:float" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Aspect ratio of the display as physical extent of width divided by height.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:VideoOutputExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoOutputExtension">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--          VideoOutputConfiguration            -->
	<!--===============================-->
	<xs:complexType name="VideoOutputConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="OutputToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Token of the Video Output the configuration applies to</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--          VideoOutputConfigurationOptions            -->
	<!--===============================-->
	<xs:complexType name="VideoOutputConfigurationOptions">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--          VideoDecoderConfigurationOptions            -->
	<!--===============================-->
	<xs:complexType name="VideoDecoderConfigurationOptions">
		<xs:sequence>
			<xs:element name="JpegDecOptions" type="tt:JpegDecOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device is able to decode Jpeg streams this element describes the supported codecs and configurations</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="H264DecOptions" type="tt:H264DecOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device is able to decode H.264 streams this element describes the supported codecs and configurations</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Mpeg4DecOptions" type="tt:Mpeg4DecOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device is able to decode Mpeg4 streams this element describes the supported codecs and configurations</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:VideoDecoderConfigurationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="H264DecOptions">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported H.264 Video Resolutions</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedH264Profiles" type="tt:H264Profile" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported H264 Profiles (either baseline, main, extended or high) </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedInputBitrate" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported H.264 bitrate range in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedFrameRate" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported H.264 framerate range in fps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="JpegDecOptions">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported Jpeg Video Resolutions</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedInputBitrate" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported Jpeg bitrate range in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedFrameRate" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported Jpeg framerate range in fps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Mpeg4DecOptions">
		<xs:sequence>
			<xs:element name="ResolutionsAvailable" type="tt:VideoResolution" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported Mpeg4 Video Resolutions</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedMpeg4Profiles" type="tt:Mpeg4Profile" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported Mpeg4 Profiles (either SP or ASP) </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedInputBitrate" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported Mpeg4 bitrate range in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedFrameRate" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Supported Mpeg4 framerate range in fps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoDecoderConfigurationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--          AudioOutputs            -->
	<!--===============================-->
	<xs:complexType name="AudioOutput">
		<xs:annotation>
			<xs:documentation>Representation of a physical audio outputs.</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--          AudioOutputConfiguration            -->
	<!--===============================-->
	<xs:complexType name="AudioOutputConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="OutputToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Token of the phsycial Audio output.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SendPrimacy" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
						An audio channel MAY support different types of audio transmission. While for full duplex
						operation no special handling is required, in half duplex operation the transmission direction
						needs to be switched.
						The optional SendPrimacy parameter inside the AudioOutputConfiguration indicates which
						direction is currently active. An NVC can switch between different modes by setting the
						AudioOutputConfiguration.<br/>
						The following modes for the Send-Primacy are defined:<ul>
									<li>www.onvif.org/ver20/HalfDuplex/Server
						The server is allowed to send audio data to the client. The client shall not send
						audio data via the backchannel to the NVT in this mode.</li>
									<li>www.onvif.org/ver20/HalfDuplex/Client
						The client is allowed to send audio data via the backchannel to the server. The
						NVT shall not send audio data to the client in this mode.</li>
									<li>www.onvif.org/ver20/HalfDuplex/Auto
							It is up to the device how to deal with sending and receiving audio data.</li>
								</ul>
						Acoustic echo cancellation is out of ONVIF scope.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="OutputLevel" type="xs:int">
						<xs:annotation>
							<xs:documentation>Volume setting of the output. The applicable range is defined via the option AudioOutputOptions.OutputLevelRange.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--          AudioOutputConfigurationOptions            -->
	<!--===============================-->
	<xs:complexType name="AudioOutputConfigurationOptions">
		<xs:sequence>
			<xs:element name="OutputTokensAvailable" type="tt:ReferenceToken" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Tokens of the physical Audio outputs (typically one).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SendPrimacyOptions" type="xs:anyURI" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				An <b>audio</b> channel MAY support different types of audio transmission. While for full duplex
				operation no special handling is required, in half duplex operation the transmission direction
				needs to be switched.
				The optional SendPrimacy parameter inside the AudioOutputConfiguration indicates which
				direction is currently active. An NVC can switch between different modes by setting the
				AudioOutputConfiguration.<br/>
				The following modes for the Send-Primacy are defined:<ul>
							<li>www.onvif.org/ver20/HalfDuplex/Server
						The server is allowed to send audio data to the client. The client shall not send
						audio data via the backchannel to the NVT in this mode.</li>
							<li>www.onvif.org/ver20/HalfDuplex/Client
						The client is allowed to send audio data via the backchannel to the server. The
						NVT shall not send audio data to the client in this mode.</li>
							<li>www.onvif.org/ver20/HalfDuplex/Auto
						It is up to the device how to deal with sending and receiving audio data.</li>
						</ul>
				Acoustic echo cancellation is out of ONVIF scope.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OutputLevelRange" type="tt:IntRange">
				<xs:annotation>
					<xs:documentation>Minimum and maximum level range supported for this Output.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--          AudioDecoderConfiguration           -->
	<!--===============================-->
	<xs:complexType name="AudioDecoderConfiguration">
		<xs:annotation>
			<xs:documentation>The Audio Decoder Configuration does not contain any that parameter to configure the
decoding .A decoder shall decode every data it receives (according to its capabilities).</xs:documentation>
		</xs:annotation>
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--          AudioDecoderConfigurationOptions           -->
	<!--===============================-->
	<xs:complexType name="AudioDecoderConfigurationOptions">
		<xs:sequence>
			<xs:element name="AACDecOptions" type="tt:AACDecOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device is able to decode AAC encoded audio this section describes the supported configurations</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="G711DecOptions" type="tt:G711DecOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device is able to decode G711 encoded audio this section describes the supported configurations</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="G726DecOptions" type="tt:G726DecOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device is able to decode G726 encoded audio this section describes the supported configurations</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:AudioDecoderConfigurationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="G711DecOptions">
		<xs:sequence>
			<xs:element name="Bitrate" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported bitrates in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SampleRateRange" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported sample rates in kHz</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AACDecOptions">
		<xs:sequence>
			<xs:element name="Bitrate" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported bitrates in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SampleRateRange" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported sample rates in kHz</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="G726DecOptions">
		<xs:sequence>
			<xs:element name="Bitrate" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported bitrates in kbps</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SampleRateRange" type="tt:IntList">
				<xs:annotation>
					<xs:documentation>List of supported sample rates in kHz</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioDecoderConfigurationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--          Streaming            -->
	<!--===============================-->
	<xs:complexType name="MulticastConfiguration">
		<xs:sequence>
			<xs:element name="Address" type="tt:IPAddress">
				<xs:annotation>
					<xs:documentation>The multicast address (if this address is set to 0 no multicast streaming is enaled)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Port" type="xs:int">
				<xs:annotation>
					<xs:documentation>The RTP mutlicast destination port. A device may support RTCP. In this case the port value shall be even to allow the corresponding RTCP stream to be mapped to the next higher (odd) destination port number as defined in the RTSP specification.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TTL" type="xs:int">
				<xs:annotation>
					<xs:documentation>In case of IPv6 the TTL value is assumed as the hop limit. Note that for IPV6 and administratively scoped IPv4 multicast the primary use for hop limit / TTL is to prevent packets from (endlessly) circulating and not limiting scope. In these cases the address contains the scope.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AutoStart" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Read only property signalling that streaming is persistant. Use the methods StartMulticastStreaming and StopMulticastStreaming to switch its state.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="StreamSetup">
		<xs:sequence>
			<xs:element name="Stream" type="tt:StreamType">
				<xs:annotation>
					<xs:documentation>Defines if a multicast or unicast stream is requested</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Transport" type="tt:Transport"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="StreamType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RTP-Unicast"/>
			<xs:enumeration value="RTP-Multicast"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Transport">
		<xs:sequence>
			<xs:element name="Protocol" type="tt:TransportProtocol">
				<xs:annotation>
					<xs:documentation>Defines the network protocol for streaming, either UDP=RTP/UDP, RTSP=RTP/RTSP/TCP or HTTP=RTP/RTSP/HTTP/TCP </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tunnel" type="tt:Transport" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to describe further tunnel options. This element is normally not needed </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="TransportProtocol">
		<xs:restriction base="xs:string">
			<xs:enumeration value="UDP"/>
			<xs:enumeration value="TCP"/>
			<xs:enumeration value="RTSP"/>
			<xs:enumeration value="HTTP"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="MediaUri">
		<xs:sequence>
			<xs:element name="Uri" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Stable Uri to be used for requesting the media stream</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvalidAfterConnect" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if the Uri is only valid until the connection is established. The value shall be set to "false".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InvalidAfterReboot" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if the Uri is invalid after a reboot of the device. The value shall be set to "false".</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Timeout" type="xs:duration">
				<xs:annotation>
					<xs:documentation>Duration how long the Uri is valid. This parameter shall be set to PT0S to indicate that this stream URI is indefinitely valid even if the profile changes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--   End, Media Related Types    -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--    Discovery Related Types    -->
	<!--===============================-->
	<xs:simpleType name="ScopeDefinition">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Fixed"/>
			<xs:enumeration value="Configurable"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Scope">
		<xs:sequence>
			<xs:element name="ScopeDef" type="tt:ScopeDefinition">
				<xs:annotation>
					<xs:documentation>Indicates if the scope is fixed or configurable.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ScopeItem" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Scope item URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="DiscoveryMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Discoverable"/>
			<xs:enumeration value="NonDiscoverable"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<!-- End, Discovery Related Types  -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--     Network Related Types     -->
	<!--===============================-->
	<xs:complexType name="NetworkInterface">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Enabled" type="xs:boolean">
						<xs:annotation>
							<xs:documentation>Indicates whether or not an interface is enabled.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Info" type="tt:NetworkInterfaceInfo" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Network interface information</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Link" type="tt:NetworkInterfaceLink" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Link configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="IPv4" type="tt:IPv4NetworkInterface" minOccurs="0">
						<xs:annotation>
							<xs:documentation>IPv4 network interface configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="IPv6" type="tt:IPv6NetworkInterface" minOccurs="0">
						<xs:annotation>
							<xs:documentation>IPv6 network interface configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:NetworkInterfaceExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="InterfaceType" type="tt:IANA-IfTypes"/>
			<xs:element name="Dot3" type="tt:Dot3Configuration" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Extension point prepared for future 802.3 configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Dot11" type="tt:Dot11Configuration" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:NetworkInterfaceExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="NetworkInterfaceConfigPriority">
		<xs:restriction base="xs:integer">
			<xs:minInclusive value="0"/>
			<xs:maxInclusive value="31"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Dot3Configuration">
		<xs:sequence>
			<!-- Placeholder for 802.3 configuration -->
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceLink">
		<xs:sequence>
			<xs:element name="AdminSettings" type="tt:NetworkInterfaceConnectionSetting">
				<xs:annotation>
					<xs:documentation>Configured link settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OperSettings" type="tt:NetworkInterfaceConnectionSetting">
				<xs:annotation>
					<xs:documentation>Current active link settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="InterfaceType" type="tt:IANA-IfTypes">
				<xs:annotation>
					<xs:documentation>Integer indicating interface type, for example: 6 is ethernet.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceConnectionSetting">
		<xs:sequence>
			<xs:element name="AutoNegotiation" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Auto negotiation on/off.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="xs:int">
				<xs:annotation>
					<xs:documentation>Speed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Duplex" type="tt:Duplex">
				<xs:annotation>
					<xs:documentation>Duplex type, Half or Full.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Duplex">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Full"/>
			<xs:enumeration value="Half"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IANA-IfTypes">
		<xs:restriction base="xs:int">
			<xs:annotation>
				<xs:documentation>
				For valid numbers, please refer to http://www.iana.org/assignments/ianaiftype-mib.
				</xs:documentation>
			</xs:annotation>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceInfo">
		<xs:sequence>
			<xs:element name="Name" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Network interface name, for example eth0.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="HwAddress" type="tt:HwAddress">
				<xs:annotation>
					<xs:documentation>Network interface MAC address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MTU" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Maximum transmission unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6NetworkInterface">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv6 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Config" type="tt:IPv6Configuration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv4NetworkInterface">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv4 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Config" type="tt:IPv4Configuration">
				<xs:annotation>
					<xs:documentation>IPv4 configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv4Configuration">
		<xs:sequence>
			<xs:element name="Manual" type="tt:PrefixedIPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually added IPv4 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LinkLocal" type="tt:PrefixedIPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Link local address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FromDHCP" type="tt:PrefixedIPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 address configured by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not DHCP is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6Configuration">
		<xs:sequence>
			<xs:element name="AcceptRouterAdvert" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether router advertisment is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="tt:IPv6DHCPConfiguration">
				<xs:annotation>
					<xs:documentation>DHCP configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Manual" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually entered IPv6 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LinkLocal" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of link local IPv6 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FromDHCP" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of IPv6 addresses configured by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FromRA" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of IPv6 addresses configured by using router advertisment.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:IPv6ConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6ConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IPv6DHCPConfiguration">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Auto"/>
			<xs:enumeration value="Stateful"/>
			<xs:enumeration value="Stateless"/>
			<xs:enumeration value="Off"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkProtocol">
		<xs:sequence>
			<xs:element name="Name" type="tt:NetworkProtocolType">
				<xs:annotation>
					<xs:documentation>Network protocol type string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if the protocol is enabled or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Port" type="xs:int" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>The port that is used by the protocol.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkProtocolExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkProtocolExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="NetworkProtocolType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HTTP"/>
			<xs:enumeration value="HTTPS"/>
			<xs:enumeration value="RTSP"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="NetworkHostType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IPv4"/>
			<xs:enumeration value="IPv6"/>
			<xs:enumeration value="DNS"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkHost">
		<xs:sequence>
			<xs:element name="Type" type="tt:NetworkHostType">
				<xs:annotation>
					<xs:documentation>Network host type: IPv4, IPv6 or DNS.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv4Address" type="tt:IPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6Address" type="tt:IPv6Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DNSname" type="tt:DNSName" minOccurs="0">
				<xs:annotation>
					<xs:documentation>DNS name.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkHostExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkHostExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPAddress">
		<xs:sequence>
			<xs:element name="Type" type="tt:IPType">
				<xs:annotation>
					<xs:documentation>Indicates if the address is an IPv4 or IPv6 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv4Address" type="tt:IPv4Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 address.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6Address" type="tt:IPv6Address" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 address</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PrefixedIPv4Address">
		<xs:sequence>
			<xs:element name="Address" type="tt:IPv4Address">
				<xs:annotation>
					<xs:documentation>IPv4 address</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PrefixLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Prefix/submask length</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IPv4Address">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="PrefixedIPv6Address">
		<xs:sequence>
			<xs:element name="Address" type="tt:IPv6Address">
				<xs:annotation>
					<xs:documentation>IPv6 address</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PrefixLength" type="xs:int">
				<xs:annotation>
					<xs:documentation>Prefix/submask length</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IPv6Address">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="HwAddress">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IPType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IPv4"/>
			<xs:enumeration value="IPv6"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="DNSName">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="HostnameInformation">
		<xs:sequence>
			<xs:element name="FromDHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether the hostname is obtained from DHCP or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Name" type="xs:token" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates the hostname.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:HostnameInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="HostnameInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DNSInformation">
		<xs:sequence>
			<xs:element name="FromDHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not DNS information is retrieved from DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SearchDomain" type="xs:token" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Search domain.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DNSFromDHCP" type="tt:IPAddress" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of DNS addresses received from DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DNSManual" type="tt:IPAddress" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually entered DNS addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:DNSInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DNSInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NTPInformation">
		<xs:sequence>
			<xs:element name="FromDHCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates if NTP information is to be retrieved by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NTPFromDHCP" type="tt:NetworkHost" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of NTP addresses retrieved by using DHCP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NTPManual" type="tt:NetworkHost" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually entered NTP addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NTPInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NTPInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Domain">
		<xs:restriction base="xs:token"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IPAddressFilterType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Allow"/>
			<xs:enumeration value="Deny"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="DynamicDNSInformation">
		<xs:sequence>
			<xs:element name="Type" type="tt:DynamicDNSType">
				<xs:annotation>
					<xs:documentation>Dynamic DNS type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Name" type="tt:DNSName" minOccurs="0">
				<xs:annotation>
					<xs:documentation>DNS name.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TTL" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Time to live.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:DynamicDNSInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DynamicDNSInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="DynamicDNSType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NoUpdate"/>
			<xs:enumeration value="ClientUpdates"/>
			<xs:enumeration value="ServerUpdates"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceSetConfiguration">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not an interface is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Link" type="tt:NetworkInterfaceConnectionSetting" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Link configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MTU" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Maximum transmission unit.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv4" type="tt:IPv4NetworkInterfaceSetConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv4 network interface configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6" type="tt:IPv6NetworkInterfaceSetConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>IPv6 network interface configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkInterfaceSetConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceSetConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Dot3" type="tt:Dot3Configuration" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Dot11" type="tt:Dot11Configuration" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:NetworkInterfaceSetConfigurationExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv6NetworkInterfaceSetConfiguration">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv6 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AcceptRouterAdvert" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether router advertisment is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Manual" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually added IPv6 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="tt:IPv6DHCPConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>DHCP configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPv4NetworkInterfaceSetConfiguration">
		<xs:sequence>
			<xs:element name="Enabled" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv4 is enabled.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Manual" type="tt:PrefixedIPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of manually added IPv4 addresses.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DHCP" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not DHCP is used.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkGateway">
		<xs:sequence>
			<xs:element name="IPv4Address" type="tt:IPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>IPv4 address string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPv6Address" type="tt:IPv6Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>IPv6 address string.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkZeroConfiguration">
		<xs:sequence>
			<xs:element name="InterfaceToken" type="tt:ReferenceToken">
				<xs:annotation>
					<xs:documentation>Unique identifier of network interface.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Enabled" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether the zero-configuration is enabled or not.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Addresses" type="tt:IPv4Address" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>The zero-configuration IPv4 address(es)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkZeroConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkZeroConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Additional" type="tt:NetworkZeroConfiguration" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Optional array holding the configuration for the second and possibly further interfaces.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkZeroConfigurationExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkZeroConfigurationExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPAddressFilter">
		<xs:sequence>
			<xs:element name="Type" type="tt:IPAddressFilterType"/>
			<xs:element name="IPv4Address" type="tt:PrefixedIPv4Address" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="IPv6Address" type="tt:PrefixedIPv6Address" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:IPAddressFilterExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IPAddressFilterExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Dot11Configuration">
		<xs:sequence>
			<xs:element name="SSID" type="tt:Dot11SSIDType"/>
			<xs:element name="Mode" type="tt:Dot11StationMode"/>
			<xs:element name="Alias" type="tt:Name"/>
			<xs:element name="Priority" type="tt:NetworkInterfaceConfigPriority"/>
			<xs:element name="Security" type="tt:Dot11SecurityConfiguration"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Dot11SSIDType">
		<xs:restriction base="xs:hexBinary">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Dot11StationMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Ad-hoc"/>
			<xs:enumeration value="Infrastructure"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Dot11SecurityConfiguration">
		<xs:sequence>
			<xs:element name="Mode" type="tt:Dot11SecurityMode"/>
			<xs:element name="Algorithm" type="tt:Dot11Cipher" minOccurs="0"/>
			<xs:element name="PSK" type="tt:Dot11PSKSet" minOccurs="0"/>
			<xs:element name="Dot1X" type="tt:ReferenceToken" minOccurs="0"/>
			<xs:element name="Extension" type="tt:Dot11SecurityConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Dot11SecurityConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Dot11SecurityMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="None"/>
			<xs:enumeration value="WEP"/>
			<xs:enumeration value="PSK"/>
			<xs:enumeration value="Dot1X"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Dot11Cipher">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CCMP"/>
			<xs:enumeration value="TKIP"/>
			<xs:enumeration value="Any"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Dot11PSK">
		<xs:restriction base="xs:hexBinary">
			<xs:length value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Dot11PSKPassphrase">
		<xs:restriction base="xs:string">
			<xs:pattern value="[ -~]{8,63}"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Dot11PSKSet">
		<xs:sequence>
			<xs:element name="Key" type="tt:Dot11PSK" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					According to IEEE802.11-2007 H.4.1 the RSNA PSK consists of 256 bits, or 64 octets when represented in hex<br/>
					Either Key or Passphrase shall be given, if both are supplied Key shall be used by the device and Passphrase ignored.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Passphrase" type="tt:Dot11PSKPassphrase" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					According to IEEE802.11-2007 H.4.1 a pass-phrase is a sequence of between 8 and 63 ASCII-encoded characters and
					each character in the pass-phrase must have an encoding in the range of 32 to 126 (decimal),inclusive.<br/>
					If only Passpharse is supplied the Key shall be derived using the algorithm described in IEEE802.11-2007 section H.4
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:Dot11PSKSetExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Dot11PSKSetExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkInterfaceSetConfigurationExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Dot11Capabilities">
		<xs:sequence>
			<xs:element name="TKIP" type="xs:boolean"/>
			<xs:element name="ScanAvailableNetworks" type="xs:boolean"/>
			<xs:element name="MultipleConfiguration" type="xs:boolean"/>
			<xs:element name="AdHocStationMode" type="xs:boolean"/>
			<xs:element name="WEP" type="xs:boolean"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Dot11SignalStrength">
		<xs:restriction base="xs:string">
			<xs:enumeration value="None"/>
			<xs:enumeration value="Very Bad"/>
			<xs:enumeration value="Bad"/>
			<xs:enumeration value="Good"/>
			<xs:enumeration value="Very Good"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Dot11Status">
		<xs:sequence>
			<xs:element name="SSID" type="tt:Dot11SSIDType"/>
			<xs:element name="BSSID" type="xs:string" minOccurs="0"/>
			<xs:element name="PairCipher" type="tt:Dot11Cipher" minOccurs="0"/>
			<xs:element name="GroupCipher" type="tt:Dot11Cipher" minOccurs="0"/>
			<xs:element name="SignalStrength" type="tt:Dot11SignalStrength" minOccurs="0"/>
			<xs:element name="ActiveConfigAlias" type="tt:ReferenceToken"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="Dot11AuthAndMangementSuite">
		<xs:restriction base="xs:string">
			<xs:enumeration value="None"/>
			<xs:enumeration value="Dot1X"/>
			<xs:enumeration value="PSK"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Dot11AvailableNetworks">
		<xs:sequence>
			<xs:element name="SSID" type="tt:Dot11SSIDType"/>
			<xs:element name="BSSID" type="xs:string" minOccurs="0"/>
			<xs:element name="AuthAndMangementSuite" type="tt:Dot11AuthAndMangementSuite" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>See IEEE802.11 ********.2 for details.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PairCipher" type="tt:Dot11Cipher" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="GroupCipher" type="tt:Dot11Cipher" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="SignalStrength" type="tt:Dot11SignalStrength" minOccurs="0"/>
			<xs:element name="Extension" type="tt:Dot11AvailableNetworksExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Dot11AvailableNetworksExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--===============================-->
	<!--  End, network Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   Capabilities Related Types  -->
	<!--===============================-->
	<xs:simpleType name="CapabilityCategory">
		<xs:restriction base="xs:string">
			<xs:enumeration value="All"/>
			<xs:enumeration value="Analytics"/>
			<xs:enumeration value="Device"/>
			<xs:enumeration value="Events"/>
			<xs:enumeration value="Imaging"/>
			<xs:enumeration value="Media"/>
			<xs:enumeration value="PTZ"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Capabilities">
		<xs:sequence>
			<xs:element name="Analytics" type="tt:AnalyticsCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Analytics capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Device" type="tt:DeviceCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Device capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Events" type="tt:EventCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Event capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Imaging" type="tt:ImagingCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Imaging capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Media" type="tt:MediaCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Media capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZ" type="tt:PTZCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>PTZ capabilities</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:CapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="DeviceIO" type="tt:DeviceIOCapabilities" minOccurs="0"/>
			<xs:element name="Display" type="tt:DisplayCapabilities" minOccurs="0"/>
			<xs:element name="Recording" type="tt:RecordingCapabilities" minOccurs="0"/>
			<xs:element name="Search" type="tt:SearchCapabilities" minOccurs="0"/>
			<xs:element name="Replay" type="tt:ReplayCapabilities" minOccurs="0"/>
			<xs:element name="Receiver" type="tt:ReceiverCapabilities" minOccurs="0"/>
			<xs:element name="AnalyticsDevice" type="tt:AnalyticsDeviceCapabilities" minOccurs="0"/>
			<xs:element name="Extensions" type="tt:CapabilitiesExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CapabilitiesExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Analytics service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RuleSupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not rules are supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AnalyticsModuleSupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not modules are supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DeviceCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Device service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Network" type="tt:NetworkCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Network capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="System" type="tt:SystemCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>System capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IO" type="tt:IOCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>I/O capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Security" type="tt:SecurityCapabilities" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Security capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:DeviceCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DeviceCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EventCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Event service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WSSubscriptionPolicySupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Subscription policy is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WSPullPointSupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Pull Point is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WSPausableSubscriptionManagerInterfaceSupport" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Pausable Subscription Manager Interface is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IOCapabilities">
		<xs:sequence>
			<xs:element name="InputConnectors" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of input connectors.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RelayOutputs" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Number of relay outputs.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:IOCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IOCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Auxiliary" type="xs:boolean" minOccurs="0"/>
			<xs:element name="AuxiliaryCommands" type="tt:AuxiliaryData" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:IOCapabilitiesExtension2"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IOCapabilitiesExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MediaCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Media service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StreamingCapabilities" type="tt:RealTimeStreamingCapabilities">
				<xs:annotation>
					<xs:documentation>Streaming capabilities.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:MediaCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MediaCapabilitiesExtension">
		<xs:sequence>
			<xs:element name="ProfileCapabilities" type="tt:ProfileCapabilities"/>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RealTimeStreamingCapabilities">
		<xs:sequence>
			<xs:element name="RTPMulticast" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not RTP multicast is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RTP_TCP" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not RTP over TCP is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RTP_RTSP_TCP" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not RTP/RTSP/TCP is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RealTimeStreamingCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RealTimeStreamingCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ProfileCapabilities">
		<xs:sequence>
			<xs:element name="MaximumNumberOfProfiles" type="xs:int">
				<xs:annotation>
					<xs:documentation>Maximum number of profiles.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkCapabilities">
		<xs:sequence>
			<xs:element name="IPFilter" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IP filtering is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZeroConfiguration" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not zeroconf is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IPVersion6" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not IPv6 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DynDNS" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not  is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:NetworkCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Dot11Configuration" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Extension" type="tt:NetworkCapabilitiesExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="NetworkCapabilitiesExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SecurityCapabilities">
		<xs:sequence>
			<xs:element name="TLS1.1" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not TLS 1.1 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TLS1.2" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not TLS 1.2 is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="OnboardKeyGeneration" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not onboard key generation is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AccessPolicyConfig" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not access policy configuration is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="X.509Token" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security X.509 token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SAMLToken" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security SAML token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="KerberosToken" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security Kerberos token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RELToken" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Security REL token is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:SecurityCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SecurityCapabilitiesExtension">
		<xs:sequence>
			<xs:element name="TLS1.0" type="xs:boolean"/>
			<xs:element name="Extension" type="tt:SecurityCapabilitiesExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SecurityCapabilitiesExtension2">
		<xs:sequence>
			<xs:element name="Dot1X" type="xs:boolean"/>
			<xs:element name="SupportedEAPMethod" type="xs:int" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>EAP Methods supported by the device. The int values refer to the <a href="http://www.iana.org/assignments/eap-numbers/eap-numbers.xhtml">IANA EAP Registry</a>.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RemoteUserHandling" type="xs:boolean"/>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemCapabilities">
		<xs:sequence>
			<xs:element name="DiscoveryResolve" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS Discovery resolve requests are supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DiscoveryBye" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not WS-Discovery Bye is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RemoteDiscovery" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not remote discovery is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SystemBackup" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not system backup is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SystemLogging" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not system logging is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FirmwareUpgrade" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not firmware upgrade is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedVersions" type="tt:OnvifVersion" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Indicates supported ONVIF version(s).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:SystemCapabilitiesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemCapabilitiesExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="HttpFirmwareUpgrade" type="xs:boolean" minOccurs="0"/>
			<xs:element name="HttpSystemBackup" type="xs:boolean" minOccurs="0"/>
			<xs:element name="HttpSystemLogging" type="xs:boolean" minOccurs="0"/>
			<xs:element name="HttpSupportInformation" type="xs:boolean" minOccurs="0"/>
			<xs:element name="Extension" type="tt:SystemCapabilitiesExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemCapabilitiesExtension2">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OnvifVersion">
		<xs:sequence>
			<xs:element name="Major" type="xs:int">
				<xs:annotation>
					<xs:documentation>Major version number.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Minor" type="xs:int">
				<xs:annotation>
					<xs:documentation>Two digit minor version number (e.g. X.0.1 maps to "01" and X.2.1 maps to "21" where X stands for Major version number).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Imaging service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>PTZ service URI.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DeviceIOCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI"/>
			<xs:element name="VideoSources" type="xs:int"/>
			<xs:element name="VideoOutputs" type="xs:int"/>
			<xs:element name="AudioSources" type="xs:int"/>
			<xs:element name="AudioOutputs" type="xs:int"/>
			<xs:element name="RelayOutputs" type="xs:int"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DisplayCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI"/>
			<xs:element name="FixedLayout" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indication that the SetLayout command supports only predefined layouts.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI"/>
			<xs:element name="ReceiverSource" type="xs:boolean"/>
			<xs:element name="MediaProfileSource" type="xs:boolean"/>
			<xs:element name="DynamicRecordings" type="xs:boolean"/>
			<xs:element name="DynamicTracks" type="xs:boolean"/>
			<xs:element name="MaxStringLength" type="xs:int"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SearchCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI"/>
			<xs:element name="MetadataSearch" type="xs:boolean"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ReplayCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>The address of the replay service.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ReceiverCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>The address of the receiver service.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RTP_Multicast" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether the device can receive RTP multicast streams.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RTP_TCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether the device can receive RTP/TCP streams</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RTP_RTSP_TCP" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether the device can receive RTP/RTSP/TCP streams.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="SupportedReceivers" type="xs:int">
				<xs:annotation>
					<xs:documentation>The maximum number of receivers supported by the device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaximumRTSPURILength" type="xs:int">
				<xs:annotation>
					<xs:documentation>The maximum allowed length for RTSP URIs.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsDeviceCapabilities">
		<xs:sequence>
			<xs:element name="XAddr" type="xs:anyURI"/>
			<xs:element name="RuleSupport" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Obsolete property.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:AnalyticsDeviceExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsDeviceExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--===============================-->
	<!--End, Capabilities Related Types-->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--    System Related Types       -->
	<!--===============================-->
	<xs:simpleType name="SystemLogType">
		<xs:annotation>
			<xs:documentation>Enumeration describing the available system log modes.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="System">
				<xs:annotation>
					<xs:documentation>Indicates that a system log is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Access">
				<xs:annotation>
					<xs:documentation>Indicates that a access log is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="SystemLog">
		<xs:sequence>
			<xs:element name="Binary" type="tt:AttachmentData" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The log information as attachment data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="String" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The log information as character data.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SupportInformation">
		<xs:sequence>
			<xs:element name="Binary" type="tt:AttachmentData" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The support information as attachment data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="String" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The support information as character data.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BinaryData">
		<xs:sequence>
			<xs:element name="Data" type="xs:base64Binary" nillable="false">
				<xs:annotation>
					<xs:documentation>base64 encoded binary data.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute ref="xmime:contentType" use="optional"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AttachmentData">
		<xs:sequence>
			<xs:element ref="xop:Include"/>
		</xs:sequence>
		<xs:attribute ref="xmime:contentType" use="optional"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BackupFile">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Data" type="tt:AttachmentData"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemLogUriList">
		<xs:sequence>
			<xs:element name="SystemLog" type="tt:SystemLogUri" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemLogUri">
		<xs:sequence>
			<xs:element name="Type" type="tt:SystemLogType"/>
			<xs:element name="Uri" type="xs:anyURI"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="FactoryDefaultType">
		<xs:annotation>
			<xs:documentation>Enumeration describing the available factory default modes.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="Hard">
				<xs:annotation>
					<xs:documentation>Indicates that a hard factory default is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Soft">
				<xs:annotation>
					<xs:documentation>Indicates that a soft factory default is requested.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="SetDateTimeType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Manual">
				<xs:annotation>
					<xs:documentation>Indicates that the date and time are set manually.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="NTP">
				<xs:annotation>
					<xs:documentation>Indicates that the date and time are set through NTP</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="SystemDateTime">
		<xs:annotation>
			<xs:documentation>General date time inforamtion returned by the GetSystemDateTime method.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="DateTimeType" type="tt:SetDateTimeType">
				<xs:annotation>
					<xs:documentation>Indicates if the time is set manully or through NTP.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DaylightSavings" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Informative indicator whether daylight savings is currently on/off.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeZone" type="tt:TimeZone" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Timezone information in Posix format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UTCDateTime" type="tt:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Current system date and time in UTC format. This field is mandatory since version 2.0.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="LocalDateTime" type="tt:DateTime" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Date and time in local format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:SystemDateTimeExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SystemDateTimeExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DateTime">
		<xs:sequence>
			<xs:element name="Time" type="tt:Time"/>
			<xs:element name="Date" type="tt:Date"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Date">
		<xs:sequence>
			<xs:element name="Year" type="xs:int"/>
			<xs:element name="Month" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 1 to 12.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Day" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 1 to 31.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Time">
		<xs:sequence>
			<xs:element name="Hour" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 0 to 23.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Minute" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 0 to 59.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Second" type="xs:int">
				<xs:annotation>
					<xs:documentation>Range is 0 to 61 (typically 59).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="TimeZone">
		<xs:annotation>
			<xs:documentation>
				The TZ format is specified by POSIX, please refer to POSIX 1003.1 section 8.3<br/>
				Example: Europe, Paris TZ=CET-1CEST,M3.5.0/2,M10.5.0/3<br/>
				CET = designation for standard time when daylight saving is not in force<br/>
				-1 = offset in hours = negative so 1 hour east of Greenwich meridian<br/>
				CEST = designation when daylight saving is in force ("Central European Summer Time")<br/>
				, = no offset number between code and comma, so default to one hour ahead for daylight saving<br/>
				M3.5.0 = when daylight saving starts = the last Sunday in March (the "5th" week means the last in the month)<br/>
				/2, = the local time when the switch occurs = 2 a.m. in this case<br/>
				M10.5.0 = when daylight saving ends = the last Sunday in October.<br/>
				/3, = the local time when the switch occurs = 3 a.m. in this case<br/>
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="TZ" type="xs:token">
				<xs:annotation>
					<xs:documentation>Posix timezone string.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--   End, System Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   RemoteUser Handling Types    -->
	<!--===============================-->
	<xs:complexType name="RemoteUser">
		<xs:sequence>
			<xs:element name="Username" type="xs:string"/>
			<xs:element name="Password" type="xs:string" minOccurs="0"/>
			<xs:element name="UseDerivedPassword" type="xs:boolean"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--   End, RemoteUser Handling Types    -->
	<!--===============================-->
	<!--===============================-->
	<!--   UserToken Handling Types    -->
	<!--===============================-->
	<xs:simpleType name="UserLevel">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Administrator"/>
			<xs:enumeration value="Operator"/>
			<xs:enumeration value="User"/>
			<xs:enumeration value="Anonymous"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="User">
		<xs:sequence>
			<xs:element name="Username" type="xs:string">
				<xs:annotation>
					<xs:documentation>Username string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Password" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Password string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UserLevel" type="tt:UserLevel">
				<xs:annotation>
					<xs:documentation>User level string.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:UserExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="UserExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!-- End, UserToken Handling Types -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   Security Management Types   -->
	<!--===============================-->
	<xs:complexType name="CertificateGenerationParameters">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token" minOccurs="0"/>
			<xs:element name="Subject" type="xs:string" minOccurs="0"/>
			<xs:element name="ValidNotBefore" type="xs:token" minOccurs="0"/>
			<xs:element name="ValidNotAfter" type="xs:token" minOccurs="0"/>
			<xs:element name="Extension" type="tt:CertificateGenerationParametersExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateGenerationParametersExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Certificate">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token">
				<xs:annotation>
					<xs:documentation>Certificate id.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Certificate" type="tt:BinaryData">
				<xs:annotation>
					<xs:documentation>base64 encoded DER representation of certificate.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateStatus">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token">
				<xs:annotation>
					<xs:documentation>Certificate id.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Status" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not a certificate is used in a HTTPS configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateWithPrivateKey">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token" minOccurs="0"/>
			<xs:element name="Certificate" type="tt:BinaryData"/>
			<xs:element name="PrivateKey" type="tt:BinaryData"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateInformation">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token"/>
			<xs:element name="IssuerDN" type="xs:string" minOccurs="0"/>
			<xs:element name="SubjectDN" type="xs:string" minOccurs="0"/>
			<xs:element name="KeyUsage" type="tt:CertificateUsage" minOccurs="0"/>
			<xs:element name="ExtendedKeyUsage" type="tt:CertificateUsage" minOccurs="0"/>
			<xs:element name="KeyLength" type="xs:int" minOccurs="0"/>
			<xs:element name="Version" type="xs:string" minOccurs="0"/>
			<xs:element name="SerialNum" type="xs:string" minOccurs="0"/>
			<xs:element name="SignatureAlgorithm" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Validity Range is from "NotBefore" to "NotAfter"; the corresponding DateTimeRange is from "From" to "Until"</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Validity" type="tt:DateTimeRange" minOccurs="0"/>
			<xs:element name="Extension" type="tt:CertificateInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateUsage">
		<xs:simpleContent>
			<xs:extension base="xs:string">
				<xs:attribute name="Critical" type="xs:boolean" use="required"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CertificateInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--End, Security management Types -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   Start Dot1X related Types   -->
	<!--===============================-->
	<xs:complexType name="Dot1XConfiguration">
		<xs:sequence>
			<xs:element name="Dot1XConfigurationToken" type="tt:ReferenceToken"/>
			<xs:element name="Identity" type="xs:string"/>
			<xs:element name="AnonymousID" type="xs:string" minOccurs="0"/>
			<xs:element name="EAPMethod" type="xs:int">
				<xs:annotation>
					<xs:documentation>
				EAP Method type as defined in <a href="http://www.iana.org/assignments/eap-numbers/eap-numbers.xhtml">IANA EAP Registry</a>.
			</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CACertificateID" type="xs:token" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="EAPMethodConfiguration" type="tt:EAPMethodConfiguration" minOccurs="0"/>
			<xs:element name="Extension" type="tt:Dot1XConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Dot1XConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EAPMethodConfiguration">
		<xs:sequence>
			<xs:element name="TLSConfiguration" type="tt:TLSConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Confgiuration information for TLS Method.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Password" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Password for those EAP Methods that require a password. The password shall never be returned on a get method.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:EapMethodExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EapMethodExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="TLSConfiguration">
		<xs:sequence>
			<xs:element name="CertificateID" type="xs:token"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="GenericEapPwdConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--End, Dot1X related Types -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   Start IO management Types   -->
	<!--===============================-->
	<xs:simpleType name="RelayLogicalState">
		<xs:restriction base="xs:string">
			<xs:enumeration value="active"/>
			<xs:enumeration value="inactive"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="RelayIdleState">
		<xs:restriction base="xs:string">
			<xs:enumeration value="closed"/>
			<xs:enumeration value="open"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="RelayOutputSettings">
		<xs:sequence>
			<xs:element name="Mode" type="tt:RelayMode">
				<xs:annotation>
					<xs:documentation>
					'Bistable' or 'Monostable'
					<ul>
							<li>Bistable – After setting the state, the relay remains in this state.</li>
							<li>Monostable – After setting the state, the relay returns to its idle state after the specified time.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DelayTime" type="xs:duration">
				<xs:annotation>
					<xs:documentation>Time after which the relay returns to its idle state if it is in monostable mode. If the Mode field is set to bistable mode the value of the parameter can be ignored.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IdleState" type="tt:RelayIdleState">
				<xs:annotation>
					<xs:documentation>
			'open' or 'closed'
			<ul>
							<li>'open' means that the relay is open when the relay state is set to 'inactive' through the trigger command and closed when the state is set to 'active' through the same command.</li>
							<li>'closed' means that the relay is closed when the relay state is set to 'inactive' through the trigger command and open when the state is set to 'active' through the same command.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="RelayMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Monostable"/>
			<xs:enumeration value="Bistable"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="RelayOutput">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Properties" type="tt:RelayOutputSettings"/>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="DigitalInput">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<!--   End, IO management Types    -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--    Start PTZ Related Types    -->
	<!--===============================-->
	<xs:complexType name="PTZNode">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="Name" type="tt:Name" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                A unique identifier that is used to reference PTZ Nodes.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="SupportedPTZSpaces" type="tt:PTZSpaces">
						<xs:annotation>
							<xs:documentation>
                A list of Coordinate Systems available for the PTZ Node. For each Coordinate System, the PTZ Node MUST specify its allowed range.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="MaximumNumberOfPresets" type="xs:int">
						<xs:annotation>
							<xs:documentation>
                All preset operations MUST be available for this PTZ Node if one preset is supported.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="HomeSupported" type="xs:boolean">
						<xs:annotation>
							<xs:documentation>
                A boolean operator specifying the availability of a home position. If set to true, the Home Position Operations MUST be available for this PTZ Node.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="AuxiliaryCommands" type="tt:AuxiliaryData" minOccurs="0" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>
                A list of supported Auxiliary commands. If the list is not empty, the Auxiliary Operations MUST be available for this PTZ Node.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:PTZNodeExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:attribute name="FixedHomePosition" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>
      				Indication whether the HomePosition of a Node is fixed or it can be changed via the SetHomePosition command.
      			</xs:documentation>
					</xs:annotation>
				</xs:attribute>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZNodeExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="SupportedPresetTour" type="tt:PTZPresetTourSupported" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            Detail of supported Preset Tour feature.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZNodeExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZNodeExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSupported">
		<xs:sequence>
			<xs:element name="MaximumNumberOfPresetTours" type="xs:int">
				<xs:annotation>
					<xs:documentation>Indicates number of preset tours that can be created. Required preset tour operations shall be available for this PTZ Node if one or more preset tour is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZPresetTourOperation" type="tt:PTZPresetTourOperation" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Indicates which preset tour operations are available for this PTZ Node.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourSupportedExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSupportedExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="NodeToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>
                A mandatory reference to the PTZ Node that the PTZ Configuration belongs to.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultAbsolutePantTiltPositionSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports absolute Pan/Tilt movements, it shall specify one Absolute Pan/Tilt Position Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultAbsoluteZoomPositionSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports absolute zoom movements, it shall specify one Absolute Zoom Position Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultRelativePanTiltTranslationSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports relative Pan/Tilt movements, it shall specify one RelativePan/Tilt Translation Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultRelativeZoomTranslationSpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports relative zoom movements, it shall specify one Relative Zoom Translation Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultContinuousPanTiltVelocitySpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports continuous Pan/Tilt movements, it shall specify one Continuous Pan/Tilt Velocity Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultContinuousZoomVelocitySpace" type="xs:anyURI" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports continuous zoom movements, it shall specify one Continuous Zoom Velocity Space as default.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultPTZSpeed" type="tt:PTZSpeed" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports absolute or relative PTZ movements, it shall specify corresponding default Pan/Tilt and Zoom speeds.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="DefaultPTZTimeout" type="xs:duration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                If the PTZ Node supports continuous movements, it shall specify a default timeout, after which the movement stops.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="PanTiltLimits" type="tt:PanTiltLimits" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                The Pan/Tilt limits element should be present for a PTZ Node that supports an absolute Pan/Tilt. If the element is present it signals the support for configurable Pan/Tilt limits. If limits are enabled, the Pan/Tilt movements shall always stay within the specified range. The Pan/Tilt limits are disabled by setting the limits to –INF or +INF.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ZoomLimits" type="tt:ZoomLimits" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
                The Zoom limits element should be present for a PTZ Node that supports absolute zoom. If the element is present it signals the supports for configurable Zoom limits. If limits are enabled the zoom movements shall always stay within the specified range. The Zoom limits are disabled by settings the limits to -INF and +INF.
              </xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:PTZConfigurationExtension" minOccurs="0">
						<xs:annotation>
							<xs:documentation>
              </xs:documentation>
						</xs:annotation>
					</xs:element>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="PTControlDirection" type="tt:PTControlDirection" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure PT Control Direction related features.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZConfigurationExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirection">
		<xs:sequence>
			<xs:element name="EFlip" type="tt:EFlip" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure related parameters for E-Flip.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Reverse" type="tt:Reverse" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure related parameters for reversing of PT Control Direction.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTControlDirectionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirectionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EFlip">
		<xs:sequence>
			<xs:element name="Mode" type="tt:EFlipMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable E-Flip feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Reverse">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ReverseMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable Reverse feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="EFlipMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="ReverseMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationOptions">
		<xs:sequence>
			<xs:element name="Spaces" type="tt:PTZSpaces">
				<xs:annotation>
					<xs:documentation>
            A list of supported coordinate systems including their range limitations.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZTimeout" type="tt:DurationRange">
				<xs:annotation>
					<xs:documentation>
            A timeout Range within which Timeouts are accepted by the PTZ Node.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="PTControlDirection" type="tt:PTControlDirectionOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported options for PT Direction Control.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZConfigurationOptions2" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZConfigurationOptions2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirectionOptions">
		<xs:sequence>
			<xs:element name="EFlip" type="tt:EFlipOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported options for EFlip feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Reverse" type="tt:ReverseOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported options for Reverse feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTControlDirectionOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTControlDirectionOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EFlipOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:EFlipMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Options of EFlip mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:EFlipOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EFlipOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ReverseOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ReverseMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Options of Reverse mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ReverseOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ReverseOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PanTiltLimits">
		<xs:sequence>
			<xs:element name="Range" type="tt:Space2DDescription">
				<xs:annotation>
					<xs:documentation>
            A range of pan tilt limits.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ZoomLimits">
		<xs:sequence>
			<xs:element name="Range" type="tt:Space1DDescription">
				<xs:annotation>
					<xs:documentation>
            A range of zoom limit
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZSpaces">
		<xs:sequence>
			<xs:element name="AbsolutePanTiltPositionSpace" type="tt:Space2DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Pan/Tilt Position space is provided by every PTZ node that supports absolute Pan/Tilt, since it does not relate to a specific physical range. 
			Instead, the range should be defined as the full range of the PTZ unit normalized to the range -1 to 1 resulting in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AbsoluteZoomPositionSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Zoom Position Space is provided by every PTZ node that supports absolute Zoom, since it does not relate to a specific physical range. 
			Instead, the range should be defined as the full range of the Zoom normalized to the range 0 (wide) to 1 (tele). 
			There is no assumption about how the generic zoom range is mapped to magnification, FOV or other physical zoom dimension.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RelativePanTiltTranslationSpace" type="tt:Space2DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Pan/Tilt translation space is provided by every PTZ node that supports relative Pan/Tilt, since it does not relate to a specific physical range. 
			Instead, the range should be defined as the full positive and negative translation range of the PTZ unit normalized to the range -1 to 1, 
			where positive translation would mean clockwise rotation or movement in right/up direction resulting in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RelativeZoomTranslationSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The Generic Zoom Translation Space is provided by every PTZ node that supports relative Zoom, since it does not relate to a specific physical range. 
			Instead, the corresponding absolute range should be defined as the full positive and negative translation range of the Zoom normalized to the range -1 to1, 
			where a positive translation maps to a movement in TELE direction. The translation is signed to indicate direction (negative is to wide, positive is to tele). 
			There is no assumption about how the generic zoom range is mapped to magnification, FOV or other physical zoom dimension. This results in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContinuousPanTiltVelocitySpace" type="tt:Space2DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The generic Pan/Tilt velocity space shall be provided by every PTZ node, since it does not relate to a specific physical range. 
			Instead, the range should be defined as a range of the PTZ unit’s speed normalized to the range -1 to 1, where a positive velocity would map to clockwise 
			rotation or movement in the right/up direction. A signed speed can be independently specified for the pan and tilt component resulting in the following space description.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ContinuousZoomVelocitySpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The generic zoom velocity space specifies a zoom factor velocity without knowing the underlying physical model. The range should be normalized from -1 to 1, 
			where a positive velocity would map to TELE direction. A generic zoom velocity space description resembles the following.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PanTiltSpeedSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The speed space specifies the speed for a Pan/Tilt movement when moving to an absolute position or to a relative translation. 
			In contrast to the velocity spaces, speed spaces do not contain any directional information. The speed of a combined Pan/Tilt 
			movement is represented by a single non-negative scalar value.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomSpeedSpace" type="tt:Space1DDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            The speed space specifies the speed for a Zoom movement when moving to an absolute position or to a relative translation. 
			In contrast to the velocity spaces, speed spaces do not contain any directional information. 
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZSpacesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZSpacesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Space2DDescription">
		<xs:sequence>
			<xs:element name="URI" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>
            A URI of coordinate systems.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="XRange" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
            A range of x-axis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="YRange" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
            A range of y-axis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Space1DDescription">
		<xs:sequence>
			<xs:element name="URI" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>
            A URI of coordinate systems.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="XRange" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
            A range of x-axis.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Vector2D">
		<xs:attribute name="x" type="xs:float" use="required"/>
		<xs:attribute name="y" type="xs:float" use="required"/>
		<xs:attribute name="space" type="xs:anyURI" use="optional">
			<xs:annotation>
				<xs:documentation>
  				Pan/tilt coordinate space selector. The following options are defined:<ul>
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/PositionGenericSpace</li>
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/TranslationGenericSpace</li>
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace</li>
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/GenericSpeedSpace</li>
					</ul>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Vector1D">
		<xs:attribute name="x" type="xs:float" use="required"/>
		<xs:attribute name="space" type="xs:anyURI" use="optional">
			<xs:annotation>
				<xs:documentation>
  				Pan/tilt coordinate space selector. The following options are defined:<ul style="">
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/PositionGenericSpace</li>
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/TranslationGenericSpace</li>
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/VelocityGenericSpace</li>
						<li> http://www.onvif.org/ver10/tptz/PanTiltSpaces/GenericSpeedSpace</li>
					</ul>
				</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZVector">
		<xs:sequence>
			<xs:element name="PanTilt" type="tt:Vector2D" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Pan and tilt position. The x component corresponds to pan and the y component to tilt.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Zoom" type="tt:Vector1D" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            A zoom position.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZSpeed">
		<xs:sequence>
			<xs:element name="PanTilt" type="tt:Vector2D" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Pan and tilt speed. The x component corresponds to pan and the y component to tilt. If omitted in a request, the current (if any) PanTilt movement should not be affected. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Zoom" type="tt:Vector1D" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            A zoom speed. If omitted in a request, the current (if any) Zoom movement should not be affected.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZStatus">
		<xs:sequence>
			<xs:element name="Position" type="tt:PTZVector" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            Specifies the absolute position of the PTZ unit together with the Space references. The default absolute spaces of the corresponding PTZ configuration MUST be referenced within the Position element.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MoveStatus" type="tt:PTZMoveStatus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            Indicates if the Pan/Tilt/Zoom device unit is currently moving, idle or in an unknown state.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Error" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            States a current PTZ error.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="UtcTime" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>
            Specifies the UTC time when this status was generated.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPreset">
		<xs:sequence>
			<xs:element name="Name" type="tt:Name" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            A list of preset position name.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PTZPosition" type="tt:PTZVector" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
            A list of preset position.
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken">
			<xs:annotation>
				<xs:documentation>
        </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZMoveStatus">
		<xs:sequence>
			<xs:element name="PanTilt" type="tt:MoveStatus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
          </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Zoom" type="tt:MoveStatus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
          </xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="AuxiliaryData">
		<xs:restriction base="xs:string">
			<xs:maxLength value="128"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="MoveStatus">
		<xs:restriction base="xs:string">
			<xs:enumeration value="IDLE"/>
			<xs:enumeration value="MOVING"/>
			<xs:enumeration value="UNKNOWN"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="PTZPresetTourState">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Idle"/>
			<xs:enumeration value="Touring"/>
			<xs:enumeration value="Paused"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="PTZPresetTourDirection">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Forward"/>
			<xs:enumeration value="Backward"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="PTZPresetTourOperation">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Start"/>
			<xs:enumeration value="Stop"/>
			<xs:enumeration value="Pause"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="PresetTour">
		<xs:sequence>
			<xs:element name="Name" type="tt:Name" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Readable name of the preset tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Status" type="tt:PTZPresetTourStatus">
				<xs:annotation>
					<xs:documentation>Read only parameters to indicate the status of the preset tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AutoStart" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Auto Start flag of the preset tour. True allows the preset tour to be activated always.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StartingCondition" type="tt:PTZPresetTourStartingCondition">
				<xs:annotation>
					<xs:documentation>Parameters to specify the detail behavior of the preset tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TourSpot" type="tt:PTZPresetTourSpot" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A list of detail of touring spots including preset positions.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="token" type="tt:ReferenceToken">
			<xs:annotation>
				<xs:documentation>Unique identifier of this preset tour.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSpot">
		<xs:sequence>
			<xs:element name="PresetDetail" type="tt:PTZPresetTourPresetDetail">
				<xs:annotation>
					<xs:documentation>Detail definition of preset position of the tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:PTZSpeed" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify Pan/Tilt and Zoom speed on moving toward this tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StayTime" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify time duration of staying on this tour sport.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourSpotExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSpotExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourPresetDetail">
		<xs:sequence>
			<xs:choice>
				<xs:element name="PresetToken" type="tt:ReferenceToken">
					<xs:annotation>
						<xs:documentation>Option to specify the preset position with Preset Token defined in advance.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Home" type="xs:boolean">
					<xs:annotation>
						<xs:documentation>Option to specify the preset position with the home position of this PTZ Node. "False" to this parameter shall be treated as an invalid argument.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="PTZPosition" type="tt:PTZVector">
					<xs:annotation>
						<xs:documentation>Option to specify the preset position with vector of PTZ node directly.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="TypeExtension" type="tt:PTZPresetTourTypeExtension"/>
			</xs:choice>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourTypeExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStatus">
		<xs:sequence>
			<xs:element name="State" type="tt:PTZPresetTourState">
				<xs:annotation>
					<xs:documentation>Indicates state of this preset tour by Idle/Touring/Paused.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CurrentTourSpot" type="tt:PTZPresetTourSpot" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates a tour spot currently staying.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourStatusExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStatusExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingCondition">
		<xs:sequence>
			<xs:element name="RecurringTime" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify how many times the preset tour is recurred.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RecurringDuration" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to specify how long time duration the preset tour is recurred.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Direction" type="tt:PTZPresetTourDirection" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional parameter to choose which direction the preset tour goes. Forward shall be chosen in case it is omitted.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourStartingConditionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingConditionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourOptions">
		<xs:sequence>
			<xs:element name="AutoStart" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not the AutoStart is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StartingCondition" type="tt:PTZPresetTourStartingConditionOptions">
				<xs:annotation>
					<xs:documentation>Supported options for Preset Tour Starting Condition.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TourSpot" type="tt:PTZPresetTourSpotOptions">
				<xs:annotation>
					<xs:documentation>Supported options for Preset Tour Spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourSpotOptions">
		<xs:sequence>
			<xs:element name="PresetDetail" type="tt:PTZPresetTourPresetDetailOptions">
				<xs:annotation>
					<xs:documentation>Supported options for detail definition of preset position of the tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StayTime" type="tt:DurationRange">
				<xs:annotation>
					<xs:documentation>Supported range of stay time for a tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourPresetDetailOptions">
		<xs:sequence>
			<xs:element name="PresetToken" type="tt:ReferenceToken" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A list of available Preset Tokens for tour spots.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Home" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>An option to indicate Home postion for tour spots.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PanTiltPositionSpace" type="tt:Space2DDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Pan and Tilt for tour spots.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ZoomPositionSpace" type="tt:Space1DDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Zoom for a tour spot.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourPresetDetailOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourPresetDetailOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingConditionOptions">
		<xs:sequence>
			<xs:element name="RecurringTime" type="tt:IntRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Recurring Time.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RecurringDuration" type="tt:DurationRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of Recurring Duration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Direction" type="tt:PTZPresetTourDirection" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options for Direction of Preset Tour.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PTZPresetTourStartingConditionOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPresetTourStartingConditionOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--     End, PTZ Related Types    -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--      Imaging Related Types    -->
	<!--===============================-->
	<xs:complexType name="ImagingStatus">
		<xs:sequence>
			<xs:element name="FocusStatus" type="tt:FocusStatus"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusStatus">
		<xs:sequence>
			<xs:element name="Position" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Status of focus position.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MoveStatus" type="tt:MoveStatus">
				<xs:annotation>
					<xs:documentation>
				Status of focus MoveStatus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Error" type="xs:string">
				<xs:annotation>
					<xs:documentation>
				Error status of focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusConfiguration">
		<xs:sequence>
			<xs:element name="AutoFocusMode" type="tt:AutoFocusMode"/>
			<xs:element name="DefaultSpeed" type="xs:float"/>
			<xs:element name="NearLimit" type="xs:float">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus near limit (unit: meter).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FarLimit" type="xs:float">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus far limit (unit: meter).
If set to 0.0, infinity will be used.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="AutoFocusMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="MANUAL"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="ImagingSettings">
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensation" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Enabled/disabled BLC mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Brightness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Image brightness (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ColorSaturation" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Color saturation of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contrast" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contrast of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Exposure" type="tt:Exposure" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Exposure mode of the device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Focus" type="tt:FocusConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Focus configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IrCutFilter" type="tt:IrCutFilterMode" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Infrared Cutoff Filter settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sharpness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sharpness of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>WDR settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WhiteBalance" type="tt:WhiteBalance" minOccurs="0">
				<xs:annotation>
					<xs:documentation>White balance settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Exposure">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode">
				<xs:annotation>
					<xs:documentation>
					Exposure Mode
					<ul>
							<li>Auto – Enabled the exposure algorithm on the NVT.</li>
							<li>Manual – Disabled exposure algorithm on the NVT.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Priority" type="tt:ExposurePriority">
				<xs:annotation>
					<xs:documentation>
				The exposure priority mode (low noise/framerate).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Window" type="tt:Rectangle">
				<xs:annotation>
					<xs:documentation>
				Rectangular exposure mask.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinExposureTime" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Minimum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxExposureTime" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Maximum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinIris" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the iris range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxIris" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the iris range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExposureTime" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				The fixed exposure time used by the image sensor (&#956;s).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gain" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				The fixed gain used by the image sensor (dB).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Iris" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				The fixed attenuation of input light affected by the iris (dB). 0dB maps to a fully opened iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="WideDynamicMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRange">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode">
				<xs:annotation>
					<xs:documentation>
				White dynamic range (on/off)
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Optional level parameter (unitless)
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="BacklightCompensationMode">
		<xs:annotation>
			<xs:documentation>Enumeration describing the available backlight compenstation modes.</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF">
				<xs:annotation>
					<xs:documentation>Backlight compensation is disabled.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ON">
				<xs:annotation>
					<xs:documentation>Backlight compensation is enabled.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensation">
		<xs:sequence>
			<xs:element name="Mode" type="tt:BacklightCompensationMode">
				<xs:annotation>
					<xs:documentation>Backlight compensation mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ExposurePriority">
		<xs:restriction base="xs:string">
			<xs:enumeration value="LowNoise"/>
			<xs:enumeration value="FrameRate"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions">
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensationOptions"/>
			<xs:element name="Brightness" type="tt:FloatRange"/>
			<xs:element name="ColorSaturation" type="tt:FloatRange"/>
			<xs:element name="Contrast" type="tt:FloatRange"/>
			<xs:element name="Exposure" type="tt:ExposureOptions"/>
			<xs:element name="Focus" type="tt:FocusOptions"/>
			<xs:element name="IrCutFilterModes" type="tt:IrCutFilterMode" maxOccurs="unbounded"/>
			<xs:element name="Sharpness" type="tt:FloatRange"/>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRangeOptions"/>
			<xs:element name="WhiteBalance" type="tt:WhiteBalanceOptions"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRangeOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode" maxOccurs="unbounded"/>
			<xs:element name="Level" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensationOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode" maxOccurs="unbounded"/>
			<xs:element name="Level" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusOptions">
		<xs:sequence>
			<xs:element name="AutoFocusModes" type="tt:AutoFocusMode" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="DefaultSpeed" type="tt:FloatRange"/>
			<xs:element name="NearLimit" type="tt:FloatRange"/>
			<xs:element name="FarLimit" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ExposureOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode" maxOccurs="unbounded"/>
			<xs:element name="Priority" type="tt:ExposurePriority" maxOccurs="unbounded"/>
			<xs:element name="MinExposureTime" type="tt:FloatRange"/>
			<xs:element name="MaxExposureTime" type="tt:FloatRange"/>
			<xs:element name="MinGain" type="tt:FloatRange"/>
			<xs:element name="MaxGain" type="tt:FloatRange"/>
			<xs:element name="MinIris" type="tt:FloatRange"/>
			<xs:element name="MaxIris" type="tt:FloatRange"/>
			<xs:element name="ExposureTime" type="tt:FloatRange"/>
			<xs:element name="Gain" type="tt:FloatRange"/>
			<xs:element name="Iris" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalanceOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode" maxOccurs="unbounded"/>
			<xs:element name="YrGain" type="tt:FloatRange"/>
			<xs:element name="YbGain" type="tt:FloatRange"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusMove">
		<xs:sequence>
			<xs:element name="Absolute" type="tt:AbsoluteFocus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Parameters for the absolute focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Relative" type="tt:RelativeFocus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Parameters for the relative focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Continuous" type="tt:ContinuousFocus" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Parameter for the continuous focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AbsoluteFocus">
		<xs:sequence>
			<xs:element name="Position" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Position parameter for the absolute focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Speed parameter for the absolute focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RelativeFocus">
		<xs:sequence>
			<xs:element name="Distance" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Distance parameter for the relative focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Speed parameter for the relative focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ContinuousFocus">
		<xs:sequence>
			<xs:element name="Speed" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Speed parameter for the Continuous focus control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MoveOptions">
		<xs:sequence>
			<xs:element name="Absolute" type="tt:AbsoluteFocusOptions" minOccurs="0"/>
			<xs:element name="Relative" type="tt:RelativeFocusOptions" minOccurs="0"/>
			<xs:element name="Continuous" type="tt:ContinuousFocusOptions" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AbsoluteFocusOptions">
		<xs:sequence>
			<xs:element name="Position" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the position.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RelativeFocusOptions">
		<xs:sequence>
			<xs:element name="Distance" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the distance.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ContinuousFocusOptions">
		<xs:sequence>
			<xs:element name="Speed" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ExposureMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="MANUAL"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Enabled">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ENABLED"/>
			<xs:enumeration value="DISABLED"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="WhiteBalanceMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="MANUAL"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="IrCutFilterMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ON"/>
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="AUTO"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="WhiteBalance">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode">
				<xs:annotation>
					<xs:documentation>Auto whitebalancing mode (auto/manual).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CrGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>Rgain (unitless).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CbGain" type="xs:float">
				<xs:annotation>
					<xs:documentation>Bgain (unitless).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--  End, Imaging Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--      Imaging Version 2.0 Related Types    -->
	<!--===============================-->
	<xs:complexType name="ImagingStatus20">
		<xs:sequence>
			<xs:element name="FocusStatus20" type="tt:FocusStatus20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Status of focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingStatus20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingStatus20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusStatus20">
		<xs:sequence>
			<xs:element name="Position" type="xs:float">
				<xs:annotation>
					<xs:documentation>
				Status of focus position.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MoveStatus" type="tt:MoveStatus">
				<xs:annotation>
					<xs:documentation>
				Status of focus MoveStatus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Error" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Error status of focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:FocusStatus20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusStatus20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettings20">
		<xs:annotation>
			<xs:documentation>Type describing the ImagingSettings of a VideoSource. The supported options and ranges can be obtained via the GetOptions command.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensation20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Enabled/disabled BLC mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Brightness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Image brightness (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ColorSaturation" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Color saturation of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contrast" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Contrast of the image (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Exposure" type="tt:Exposure20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Exposure mode of the device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Focus" type="tt:FocusConfiguration20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Focus configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IrCutFilter" type="tt:IrCutFilterMode" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Infrared Cutoff Filter settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sharpness" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Sharpness of the Video image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRange20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>WDR settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WhiteBalance" type="tt:WhiteBalance20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>White balance settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension20" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension20">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="ImageStabilization" type="tt:ImageStabilization" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional element to configure Image Stabilization feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension202" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension202">
		<xs:sequence>
			<xs:element name="IrCutFilterAutoAdjustment" type="tt:IrCutFilterAutoAdjustment" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>An optional parameter applied to only auto mode to adjust timing of toggling Ir cut filter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingSettingsExtension203" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingSettingsExtension203">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilization">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ImageStabilizationMode">
				<xs:annotation>
					<xs:documentation>Parameter to enable/disable Image Stabilization feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImageStabilizationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilizationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ImageStabilizationMode">
		<xs:restriction base="xs:string">
			<xs:enumeration value="OFF"/>
			<xs:enumeration value="ON"/>
			<xs:enumeration value="AUTO"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustment">
		<xs:sequence>
			<xs:element name="BoundaryType" type="xs:string">
				<xs:annotation>
					<xs:documentation>Specifies which boundaries to automatically toggle Ir cut filter following parameters are applied to. Its options shall be chosen from tt:IrCutFilterAutoBoundaryType.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BoundaryOffset" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Adjusts boundary exposure level for toggling Ir cut filter to on/off specified with unitless normalized value from +1.0 to -1.0. Zero is default and -1.0 is the darkest adjustment (Unitless).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ResponseTime" type="xs:duration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Delay time of toggling Ir cut filter to on/off after crossing of the boundary exposure levels.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:IrCutFilterAutoAdjustmentExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax" />
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustmentExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="IrCutFilterAutoBoundaryType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Common" />
			<xs:enumeration value="ToOn" />
			<xs:enumeration value="ToOff" />
			<xs:enumeration value="Extended" />
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRange20">
		<xs:annotation>
			<xs:documentation>Type describing whether WDR mode is enabled or disabled (on/off).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode">
				<xs:annotation>
					<xs:documentation>Wide dynamic range mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensation20">
		<xs:annotation>
			<xs:documentation>Type describing whether BLC mode is enabled or disabled (on/off).</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Mode" type="tt:BacklightCompensationMode">
				<xs:annotation>
					<xs:documentation>Backlight compensation mode (on/off).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional level parameter (unit unspecified).</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Exposure20">
		<xs:annotation>
			<xs:documentation>Type describing the exposure settings.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode">
				<xs:annotation>
					<xs:documentation>
				Exposure Mode
				<ul>
							<li>Auto – Enabled the exposure algorithm on the device.</li>
							<li>Manual – Disabled exposure algorithm on the device.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Priority" type="tt:ExposurePriority" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The exposure priority mode (low noise/framerate).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Window" type="tt:Rectangle" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Rectangular exposure mask.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinExposureTime" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Minimum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxExposureTime" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Maximum value of exposure time range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the sensor gain range that is allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinIris" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Minimum value of the iris range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxIris" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Maximum value of the iris range allowed to be used by the algorithm.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExposureTime" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The fixed exposure time used by the image sensor (&#956;s).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The fixed gain used by the image sensor (dB).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Iris" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				The fixed attenuation of input light affected by the iris (dB). 0dB maps to a fully opened iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20">
		<xs:sequence>
			<xs:element name="BacklightCompensation" type="tt:BacklightCompensationOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Backlight Compensation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Brightness" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Brightness.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ColorSaturation" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Color Saturation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Contrast" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Contrast.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Exposure" type="tt:ExposureOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Exposure.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Focus" type="tt:FocusOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Focus.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IrCutFilterModes" type="tt:IrCutFilterMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				Valid range of IrCutFilterModes.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sharpness" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of Sharpness.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WideDynamicRange" type="tt:WideDynamicRangeOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of WideDynamicRange.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="WhiteBalance" type="tt:WhiteBalanceOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of WhiteBalance.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingOptions20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20Extension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="ImageStabilization" type="tt:ImageStabilizationOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for Image Stabilization feature.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingOptions20Extension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20Extension2">
		<xs:sequence>
			<xs:element name="IrCutFilterAutoAdjustment" type="tt:IrCutFilterAutoAdjustmentOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Options of parameters for adjustment of Ir cut filter auto mode.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImagingOptions20Extension3" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImagingOptions20Extension3">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilizationOptions">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ImageStabilizationMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options of Image Stabilization mode parameter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Valid range of the Image Stabilization.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ImageStabilizationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ImageStabilizationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustmentOptions">
		<xs:sequence>
			<xs:element name="BoundaryType" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Supported options of boundary types for adjustment of Ir cut filter auto mode. The opptions shall be chosen from tt:IrCutFilterAutoBoundaryType. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BoundaryOffset" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Indicates whether or not boundary offset for toggling Ir cut filter is supported.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ResponseTimeRange" type="tt:DurationRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Supported range of delay time for toggling Ir cut filter.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:IrCutFilterAutoAdjustmentOptionsExtension" minOccurs="0" />
		</xs:sequence>
		<xs:anyAttribute processContents="lax" />
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="IrCutFilterAutoAdjustmentOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded" />
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WideDynamicRangeOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WideDynamicMode" maxOccurs="unbounded"/>
			<xs:element name="Level" type="tt:FloatRange" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BacklightCompensationOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:BacklightCompensationMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				'ON' or 'OFF'
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Level" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Level range of BacklightCompensation.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ExposureOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:ExposureMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				Exposure Mode
				<ul>
							<li>Auto – Enabled the exposure algorithm on the device.</li>
							<li>Manual – Disabled exposure algorithm on the device.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Priority" type="tt:ExposurePriority" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				The exposure priority mode (low noise/framerate).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinExposureTime" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Minimum ExposureTime.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxExposureTime" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Maximum ExposureTime.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinGain" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Minimum Gain.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxGain" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Maximum Gain.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MinIris" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Minimum Iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxIris" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Maximum Iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ExposureTime" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the ExposureTime.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Gain" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Gain.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Iris" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid range of the Iris.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MoveOptions20">
		<xs:sequence>
			<xs:element name="Absolute" type="tt:AbsoluteFocusOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges for the absolute control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Relative" type="tt:RelativeFocusOptions20" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges for the relative control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Continuous" type="tt:ContinuousFocusOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges for the continuous control.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RelativeFocusOptions20">
		<xs:sequence>
			<xs:element name="Distance" type="tt:FloatRange">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the distance.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Speed" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Valid ranges of the speed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalance20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode">
				<xs:annotation>
					<xs:documentation>
				'AUTO' or 'MANUAL'
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CrGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Rgain (unitless).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CbGain" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
				Bgain (unitless).
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:WhiteBalance20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalance20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusConfiguration20">
		<xs:sequence>
			<xs:element name="AutoFocusMode" type="tt:AutoFocusMode">
				<xs:annotation>
					<xs:documentation>
			Mode of auto fucus.
			<ul>
							<li>AUTO</li>
							<li>MANUAL</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DefaultSpeed" type="xs:float" minOccurs="0"/>
			<xs:element name="NearLimit" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus near limit (unit: meter).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FarLimit" type="xs:float" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Parameter to set autofocus far limit (unit: meter).</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:FocusConfiguration20Extension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusConfiguration20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalanceOptions20">
		<xs:sequence>
			<xs:element name="Mode" type="tt:WhiteBalanceMode" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
				Mode of WhiteBalance.
				<ul>
							<li>AUTO</li>
							<li>MANUAL</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="YrGain" type="tt:FloatRange" minOccurs="0"/>
			<xs:element name="YbGain" type="tt:FloatRange" minOccurs="0"/>
			<xs:element name="Extension" type="tt:WhiteBalanceOptions20Extension" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="WhiteBalanceOptions20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusOptions20">
		<xs:sequence>
			<xs:element name="AutoFocusModes" type="tt:AutoFocusMode" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
					Mode of Auto Focus.
					<ul>
							<li>AUTO</li>
							<li>MANUAL</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DefaultSpeed" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					Valid range of DefaultSpeed.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NearLimit" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					Valid range of NearLimit.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FarLimit" type="tt:FloatRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
					Valid range of FarLimit.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:FocusOptions20Extension" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FocusOptions20Extension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--===============================-->
	<!--  End, Imaging Version 2.0 Related Types   -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--  Event and Analytics Types    -->
	<!--===============================-->
	<xs:simpleType name="TopicNamespaceLocation">
		<xs:restriction base="xs:anyURI"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="PropertyOperation">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Initialized"/>
			<xs:enumeration value="Deleted"/>
			<xs:enumeration value="Changed"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:element name="Message">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="Source" type="tt:ItemList" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Token value pairs that triggered this message. Typically only one item is present.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="Key" type="tt:ItemList" minOccurs="0"/>
				<xs:element name="Data" type="tt:ItemList" minOccurs="0"/>
				<xs:element name="Extension" type="tt:MessageExtension" minOccurs="0"/>
			</xs:sequence>
			<xs:attribute name="UtcTime" type="xs:dateTime" use="required"/>
			<xs:attribute name="PropertyOperation" type="tt:PropertyOperation"/>
			<xs:anyAttribute processContents="lax"/>
		</xs:complexType>
	</xs:element>
	<!--===============================-->
	<xs:complexType name="MessageExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemList">
		<xs:annotation>
			<xs:documentation>
			List of parameters according to the corresponding ItemListDescription.
			Each item in the list shall have a unique name.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SimpleItem" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Value name pair as defined by the corresponding description.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Value" type="xs:anySimpleType" use="required">
						<xs:annotation>
							<xs:documentation>Item value. The type is defined in the corresponding description.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="ElementItem" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Complex value structure.</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##any">
							<xs:annotation>
								<xs:documentation>XML tree contiaing the element value as defined in the corresponding description.</xs:documentation>
							</xs:annotation>
						</xs:any>
					</xs:sequence>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:ItemListExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemListExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--       Message Description     -->
	<!--===============================-->
	<xs:complexType name="MessageDescription">
		<xs:sequence>
			<xs:element name="Source" type="tt:ItemListDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Set of tokens producing this message. The list may only contain SimpleItemDescription items.
					The set of tokens identify the component within the WS-Endpoint, which is responsible for the producing the message.<br/>
					For analytics events the token set shall include the VideoSourceConfigurationToken, the VideoAnalyticsConfigurationToken
					and the name of the analytics module or rule.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Key" type="tt:ItemListDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes optional message payload parameters that may be used as key. E.g. object IDs of tracked objects are conveyed as key.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Data" type="tt:ItemListDescription" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Describes the payload of the message.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:MessageDescriptionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="IsProperty" type="xs:boolean">
			<xs:annotation>
				<xs:documentation>Must be set to true when the described Message relates to a property. An alternative term of "property" is a "state" in contrast to a pure event, which contains relevant information for only a single point in time.<br/>Default is false.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MessageDescriptionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemListDescription">
		<xs:annotation>
			<xs:documentation>
			Describes a list of items. Each item in the list shall have a unique name.
			The list is designed as linear structure without optional or unbounded elements.
			Use ElementItems only when complex structures are inevitable.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SimpleItemDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Description of a simple item. The type must be of cathegory simpleType (xs:string, xs:integer, xs:float, ...).</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name. Must be unique within a list.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" type="xs:QName" use="required"/>
				</xs:complexType>
			</xs:element>
			<xs:element name="ElementItemDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
            Description of a complex type. The Type must reference a defined type.
          </xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:attribute name="Name" type="xs:string" use="required">
						<xs:annotation>
							<xs:documentation>Item name. Must be unique within a list.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
					<xs:attribute name="Type" type="xs:QName" use="required">
						<xs:annotation>
							<xs:documentation>The type of the item. The Type must reference a defined type.</xs:documentation>
						</xs:annotation>
					</xs:attribute>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:ItemListDescriptionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ItemListDescriptionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Vector">
		<xs:attribute name="x" type="xs:float"/>
		<xs:attribute name="y" type="xs:float"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Rectangle">
		<xs:attribute name="bottom" type="xs:float"/>
		<xs:attribute name="top" type="xs:float"/>
		<xs:attribute name="right" type="xs:float"/>
		<xs:attribute name="left" type="xs:float"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Polygon">
		<xs:sequence>
			<xs:element name="Point" type="tt:Vector" minOccurs="3" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Polygon" type="tt:Polygon"/>
	<!--===============================-->
	<xs:complexType name="Polyline">
		<xs:sequence>
			<xs:element name="Point" type="tt:Vector" minOccurs="2" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Polyline" type="tt:Polyline"/>
	<!--===============================-->
	<xs:simpleType name="Direction">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Left"/>
			<xs:enumeration value="Right"/>
			<xs:enumeration value="Any"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="Color">
		<xs:attribute name="X" type="xs:float" use="required"/>
		<xs:attribute name="Y" type="xs:float" use="required"/>
		<xs:attribute name="Z" type="xs:float" use="required"/>
		<xs:attribute name="Colorspace" type="xs:anyURI"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ColorCovariance">
		<xs:attribute name="XX" type="xs:float" use="required"/>
		<xs:attribute name="YY" type="xs:float" use="required"/>
		<xs:attribute name="ZZ" type="xs:float" use="required"/>
		<xs:attribute name="XY" type="xs:float"/>
		<xs:attribute name="XZ" type="xs:float"/>
		<xs:attribute name="YZ" type="xs:float"/>
		<xs:attribute name="Colorspace" type="xs:anyURI"/>
	</xs:complexType>
	<!--===============================-->
	<!--       Scene Description       -->
	<!--===============================-->
	<xs:complexType name="Appearance">
		<xs:sequence>
			<xs:element name="Transformation" type="tt:Transformation" minOccurs="0"/>
			<xs:element name="Shape" type="tt:ShapeDescriptor" minOccurs="0"/>
			<xs:element name="Color" type="tt:ColorDescriptor" minOccurs="0"/>
			<xs:element name="Class" type="tt:ClassDescriptor" minOccurs="0"/>
			<xs:element name="Extension" type="tt:AppearanceExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AppearanceExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ShapeDescriptor">
		<xs:sequence>
			<xs:element name="BoundingBox" type="tt:Rectangle"/>
			<xs:element name="CenterOfGravity" type="tt:Vector"/>
			<xs:element name="Polygon" type="tt:Polygon" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:ShapeDescriptorExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ShapeDescriptorExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ColorDescriptor">
		<xs:sequence>
			<xs:element name="ColorCluster" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Color" type="tt:Color"/>
						<xs:element name="Weight" type="xs:float" minOccurs="0"/>
						<xs:element name="Covariance" type="tt:ColorCovariance" minOccurs="0"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:ColorDescriptorExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ColorDescriptorExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ClassType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Animal"/>
			<xs:enumeration value="Face"/>
			<xs:enumeration value="Human"/>
			<xs:enumeration value="Vehical"/>
			<xs:enumeration value="Other"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="ClassDescriptor">
		<xs:sequence>
			<xs:element name="ClassCandidate" minOccurs="0" maxOccurs="unbounded">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="Type" type="tt:ClassType"/>
						<xs:element name="Likelihood" type="xs:float"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:ClassDescriptorExtension" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ClassDescriptorExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="OtherTypes" type="tt:OtherType" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:ClassDescriptorExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ClassDescriptorExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OtherType">
		<xs:sequence>
			<xs:element name="Type" type="xs:string">
				<xs:annotation>
					<xs:documentation>Object Class Type</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Likelihood" type="xs:float">
				<xs:annotation>
					<xs:documentation>A likelihood/probability that the corresponding object belongs to this class. The sum of the likelihoods shall NOT exceed 1</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="1" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Object">
		<xs:complexContent>
			<xs:extension base="tt:ObjectId">
				<xs:sequence>
					<xs:element name="Appearance" type="tt:Appearance" minOccurs="0"/>
					<xs:element name="Behaviour" type="tt:Behaviour" minOccurs="0"/>
					<xs:element name="Extension" type="tt:ObjectExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ObjectExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Transformation">
		<xs:sequence>
			<xs:element name="Translate" type="tt:Vector" minOccurs="0"/>
			<xs:element name="Scale" type="tt:Vector" minOccurs="0"/>
			<xs:element name="Extension" type="tt:TransformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="TransformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Frame">
		<xs:sequence>
			<xs:element name="PTZStatus" type="tt:PTZStatus" minOccurs="0"/>
			<xs:element name="Transformation" type="tt:Transformation" minOccurs="0"/>
			<xs:element name="Object" type="tt:Object" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="ObjectTree" type="tt:ObjectTree" minOccurs="0"/>
			<xs:element name="Extension" type="tt:FrameExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="UtcTime" type="xs:dateTime" use="required"/>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FrameExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="MotionInCells" type="tt:MotionInCells" minOccurs="0"/>
			<xs:element name="Extension" type="tt:FrameExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FrameExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Merge">
		<xs:sequence>
			<xs:element name="from" type="tt:ObjectId" minOccurs="2" maxOccurs="unbounded"/>
			<xs:element name="to" type="tt:ObjectId"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Split">
		<xs:sequence>
			<xs:element name="from" type="tt:ObjectId"/>
			<xs:element name="to" type="tt:ObjectId" minOccurs="2" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Rename">
		<xs:sequence>
			<xs:element name="from" type="tt:ObjectId"/>
			<xs:element name="to" type="tt:ObjectId"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ObjectId">
		<xs:attribute name="ObjectId" type="xs:integer"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Behaviour">
		<xs:sequence>
			<xs:element name="Removed" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##other" minOccurs="0" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Idle" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:any namespace="##other" minOccurs="0" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:BehaviourExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="BehaviourExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ObjectTree">
		<xs:sequence>
			<xs:element name="Rename" type="tt:Rename" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Split" type="tt:Split" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Merge" type="tt:Merge" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Delete" type="tt:ObjectId" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:ObjectTreeExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ObjectTreeExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MotionInCells">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="Columns" type="xs:integer" use="required">
			<xs:annotation>
				<xs:documentation>Number of columns of the cell grid (x dimension)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Rows" type="xs:integer" use="required">
			<xs:annotation>
				<xs:documentation>Number of rows of the cell grid (y dimension)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Cells" type="xs:base64Binary" use="required">
			<xs:annotation>
				<xs:documentation>A “1” denotes a cell where motion is detected and a “0” an empty cell. The first cell is in the upper left corner. Then the cell order goes first from left to right and then from up to down.  If the number of cells is not a multiple of 8 the last byte is filled with zeros. The information is run length encoded according to Packbit coding in ISO 12369 (TIFF, Revision 6.0).</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--    Analytics Configuration    -->
	<!--===============================-->
	<xs:complexType name="AnalyticsEngineConfiguration">
		<xs:sequence>
			<xs:element name="AnalyticsModule" type="tt:Config" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:AnalyticsEngineConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsEngineConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RuleEngineConfiguration">
		<xs:sequence>
			<xs:element name="Rule" type="tt:Config" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:RuleEngineConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RuleEngineConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Config">
		<xs:sequence>
			<xs:element name="Parameters" type="tt:ItemList">
				<xs:annotation>
					<xs:documentation>List of configuration parameters as defined in the correspding description.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:attribute name="Name" type="xs:string" use="required">
			<xs:annotation>
				<xs:documentation>Name of the configuration.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Type" type="xs:QName" use="required">
			<xs:annotation>
				<xs:documentation>Type of the configuration represented by a unique QName. The Type characterizes a ConfigDescription defining the Parameters.</xs:documentation>
			</xs:annotation>
		</xs:attribute>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ConfigDescription">
		<xs:sequence>
			<xs:element name="Parameters" type="tt:ItemListDescription">
				<xs:annotation>
					<xs:documentation>
						List describing the configuration parameters. The names of the parameters must be unique. If possible SimpleItems
						should be used to transport the information to ease parsing of dynamically defined messages by a client
						application.
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Messages" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>
						The analytics modules and rule engine produce Events, which must be listed within the Analytics Module Description. In order to do so
						the structure of the Message is defined and consists of three groups: Source, Key, and Data. It is recommended to use SimpleItemDescriptions wherever applicable.
						The name of all Items must be unique within all Items contained in any group of this Message.
						Depending on the component multiple parameters or none may be needed to identify the component uniquely.
					</xs:documentation>
				</xs:annotation>
				<xs:complexType>
					<xs:complexContent>
						<xs:extension base="tt:MessageDescription">
							<xs:sequence>
								<xs:element name="ParentTopic" type="xs:string">
									<xs:annotation>
										<xs:documentation>
											The ParentTopic labels the message (e.g. "nn:RuleEngine/LineCrossing"). The real message can extend the ParentTopic
											by for example the name of the instaniated rule (e.g. "nn:RuleEngine/LineCrossing/corssMyFirstLine").
											Even without knowing the complete topic name, the subscriber will be able to distiguish the
											messages produced by different rule instances of the same type via the Source fields of the message.
											There the name of the rule instance, which produced the message, must be listed.
										</xs:documentation>
									</xs:annotation>
								</xs:element>
							</xs:sequence>
						</xs:extension>
					</xs:complexContent>
				</xs:complexType>
			</xs:element>
			<xs:element name="Extension" type="tt:ConfigDescriptionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:attribute name="Name" type="xs:QName" use="required">
			<xs:annotation>
				<xs:documentation>XML Type of the Configuration (e.g. "tt::LineDetector"). </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ConfigDescriptionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SupportedRules">
		<xs:sequence>
			<xs:element name="RuleContentSchemaLocation" type="xs:anyURI" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Lists the location of all schemas that are referenced in the rules.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RuleDescription" type="tt:ConfigDescription" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of rules supported by the Video Analytics configuration..</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:SupportedRulesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SupportedRulesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SupportedAnalyticsModules">
		<xs:sequence>
			<xs:element name="AnalyticsModuleContentSchemaLocation" type="xs:anyURI" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>It optionally contains a list of URLs that provide the location of schema files.
        These schema files describe the types and elements used in the analytics module descriptions.
        If the analytics module descriptions reference types or elements of the ONVIF schema file,
        the ONVIF schema file MUST be explicitly listed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AnalyticsModuleDescription" type="tt:ConfigDescription" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:SupportedAnalyticsModulesExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SupportedAnalyticsModulesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PolygonConfiguration">
		<xs:sequence>
			<xs:element name="Polygon" type="tt:Polygon">
				<xs:annotation>
					<xs:documentation>Contains Polygon configuration for rule parameters</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PolylineArray">
		<xs:sequence>
			<xs:element name="Segment" type="tt:Polyline" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Contains array of Polyline</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PolylineArrayExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PolylineArrayExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PolylineArrayConfiguration">
		<xs:sequence>
			<xs:element name="PolylineArray" type="tt:PolylineArray">
				<xs:annotation>
					<xs:documentation>Contains PolylineArray configuration data</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MotionExpression">
		<xs:sequence>
			<xs:element name="Expression" type="xs:string">
				<xs:annotation>
					<xs:documentation>Motion Expression data structure contains motion expression which is based on Scene Descriptor schema with XPATH syntax. The Type argument could allow introduction of different dialects </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="Type" type="xs:string"/>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MotionExpressionConfiguration">
		<xs:sequence>
			<xs:element name="MotionExpression" type="tt:MotionExpression">
				<xs:annotation>
					<xs:documentation>Contains Rule MotionExpression configuration</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CellLayout">
		<xs:sequence>
			<xs:element name="Transformation" type="tt:Transformation">
				<xs:annotation>
					<xs:documentation>Mapping of the cell grid to the Video frame. The cell grid is starting from the upper left corner and x dimension is going from left to right and the y dimension from up to down.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="Columns" type="xs:integer" use="required">
			<xs:annotation>
				<xs:documentation>Number of columns of the cell grid (x dimension)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="Rows" type="xs:integer" use="required">
			<xs:annotation>
				<xs:documentation>Number of rows of the cell grid (y dimension)</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--End, Event and Analytics Types -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--   Metadata Streaming Types    -->
	<!--===============================-->
	<xs:complexType name="MetadataStream">
		<xs:sequence>
			<xs:choice minOccurs="0" maxOccurs="unbounded">
				<xs:element name="VideoAnalytics" type="tt:VideoAnalyticsStream"/>
				<xs:element name="PTZ" type="tt:PTZStream"/>
				<xs:element name="Event" type="tt:EventStream"/>
				<xs:element name="Extension" type="tt:MetadataStreamExtension"/>
			</xs:choice>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataStreamExtension">
		<xs:sequence>
			<xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="AudioAnalyticsStream" type="tt:AudioAnalyticsStream" minOccurs="0"/>
			<xs:element name="Extension" type="tt:MetadataStreamExtension2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataStreamExtension2">
		<xs:sequence>
			<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioAnalyticsStream">
		<xs:sequence>
			<xs:element name="AudioDescriptor" type="tt:AudioDescriptor" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:AudioAnalyticsStreamExtension" minOccurs="0" />
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioDescriptor">
		<xs:sequence>
		<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="UtcTime" type="xs:dateTime" use="required">
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioAnalyticsStreamExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:element name="MetadataStream" type="tt:MetadataStream"/>
	<!--===============================-->
	<xs:complexType name="VideoAnalyticsStream">
		<xs:choice minOccurs="0" maxOccurs="unbounded">
			<xs:element name="Frame" type="tt:Frame"/>
			<xs:element name="Extension" type="tt:VideoAnalyticsStreamExtension"/>
		</xs:choice>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoAnalyticsStreamExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZStream">
		<xs:choice minOccurs="0" maxOccurs="unbounded">
			<xs:element name="PTZStatus" type="tt:PTZStatus"/>
			<xs:element name="Extension" type="tt:PTZStreamExtension"/>
		</xs:choice>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZStreamExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EventStream">
		<xs:choice minOccurs="0" maxOccurs="unbounded">
			<xs:element ref="wsnt:NotificationMessage"/>
			<xs:element name="Extension" type="tt:EventStreamExtension"/>
		</xs:choice>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EventStreamExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!-- End, Metadata Streaming Types -->
	<!--===============================-->
	<!--===============================-->
	<!--      Display Related Types  -->
	<!--===============================-->
	<xs:complexType name="PaneConfiguration">
		<xs:annotation>
			<xs:documentation>Configuration of the streaming and coding settings of a Video window.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PaneName" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional name of the pane configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioOutputToken" type="tt:ReferenceToken" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device has audio outputs, this element contains a pointer to the audio output that is associated with the pane. A client
can retrieve the available audio outputs of a device using the GetAudioOutputs command of the DeviceIO service.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioSourceToken" type="tt:ReferenceToken" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device has audio sources, this element contains a pointer to the audio source that is associated with this pane.
The audio connection from a decoder device to the NVT is established using the backchannel mechanism. A client can retrieve the available audio sources of a device using the GetAudioSources command of the
DeviceIO service.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioEncoderConfiguration" type="tt:AudioEncoderConfiguration" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The configuration of the audio encoder including codec, bitrate
and sample rate. </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ReceiverToken" type="tt:ReferenceToken" minOccurs="0">
				<xs:annotation>
					<xs:documentation>A pointer to a Receiver that has the necessary information to receive
				data from a Transmitter. This Receiver can be connected and the network video decoder displays the received data on the specified outputs. A client can retrieve the available Receivers using the
				GetReceivers command of the Receiver Service.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Token" type="tt:ReferenceToken">
				<xs:annotation>
					<xs:documentation>A unique identifier in the display device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PaneLayout">
		<xs:annotation>
			<xs:documentation>A pane layout describes one Video window of a display. It links a pane configuration to a region of the screen.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Pane" type="tt:ReferenceToken">
				<xs:annotation>
					<xs:documentation>Reference to the configuration of the streaming and coding parameters.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Area" type="tt:Rectangle">
				<xs:annotation>
					<xs:documentation>Describes the location and size of the area on the monitor. The area coordinate values are espressed in normalized units [-1.0, 1.0].</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="Layout">
		<xs:annotation>
			<xs:documentation>A layout describes a set of Video windows that are displayed simultaniously on a display. </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PaneLayout" type="tt:PaneLayout" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of panes assembling the display layout.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:LayoutExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="LayoutExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="CodingCapabilities">
		<xs:annotation>
			<xs:documentation>This type contains the Audio and Video coding capabilities of a display service.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="AudioEncodingCapabilities" type="tt:AudioEncoderConfigurationOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device supports audio encoding this section describes the supported codecs and their configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioDecodingCapabilities" type="tt:AudioDecoderConfigurationOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the device supports audio decoding this section describes the supported codecs and their settings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoDecodingCapabilities" type="tt:VideoDecoderConfigurationOptions">
				<xs:annotation>
					<xs:documentation>This section describes the supported video codesc and their configuration.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="LayoutOptions">
		<xs:annotation>
			<xs:documentation>The options supported for a display layout.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="PaneLayoutOptions" type="tt:PaneLayoutOptions" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Lists the possible Pane Layouts of the Video Output</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:LayoutOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="LayoutOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PaneLayoutOptions">
		<xs:annotation>
			<xs:documentation>Description of a pane layout describing a complete display layout.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Area" type="tt:Rectangle" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of areas assembling a layout. Coordinate values are in the range [-1.0, 1.0].</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:PaneOptionExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PaneOptionExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<!--      End, Display Related Types  -->
	<!--===============================-->
	<!--===============================-->
	<!--   Receiver Types              -->
	<!--===============================-->
	<xs:complexType name="Receiver">
		<xs:annotation>
			<xs:documentation>
			Description of a receiver, including its token and configuration.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Token" type="tt:ReferenceToken">
				<xs:annotation>
					<xs:documentation>Unique identifier of the receiver.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Configuration" type="tt:ReceiverConfiguration">
				<xs:annotation>
					<xs:documentation>Describes the configuration of the receiver.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ReceiverConfiguration">
		<xs:annotation>
			<xs:documentation>
			Describes the configuration of a receiver.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Mode" type="tt:ReceiverMode">
				<xs:annotation>
					<xs:documentation>The following connection modes are defined:</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MediaUri" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>Details of the URI to which the receiver should connect.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StreamSetup" type="tt:StreamSetup">
				<xs:annotation>
					<xs:documentation>Stream connection parameters.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ReceiverMode">
		<xs:annotation>
			<xs:documentation>
			Specifies a receiver connection mode.
		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="AutoConnect">
				<xs:annotation>
					<xs:documentation>The receiver connects on demand, as required by consumers of the media streams.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AlwaysConnect">
				<xs:annotation>
					<xs:documentation>The receiver attempts to maintain a persistent connection to the configured endpoint.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="NeverConnect">
				<xs:annotation>
					<xs:documentation>The receiver does not attempt to connect.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Unknown">
				<xs:annotation>
					<xs:documentation>This case should never happen.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="ReceiverState">
		<xs:annotation>
			<xs:documentation>
			Specifies the current connection state of the receiver.
		</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="NotConnected">
				<xs:annotation>
					<xs:documentation>The receiver is not connected.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Connecting">
				<xs:annotation>
					<xs:documentation>The receiver is attempting to connect.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Connected">
				<xs:annotation>
					<xs:documentation>The receiver is connected.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Unknown">
				<xs:annotation>
					<xs:documentation>This case should never happen.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="ReceiverStateInformation">
		<xs:annotation>
			<xs:documentation>
			Contains information about a receiver's current state.
		</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="State" type="tt:ReceiverState">
				<xs:annotation>
					<xs:documentation>The connection state of the receiver may have one of the following states: </xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AutoCreated" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates whether or not the receiver was created automatically.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--   End, Receiver Types         					  -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--         Storage Types         -->
	<!--===============================-->
	<xs:simpleType name="ReceiverReference">
		<xs:restriction base="tt:ReferenceToken"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="RecordingReference">
		<xs:restriction base="tt:ReferenceToken"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="SourceReference">
		<xs:sequence>
			<xs:element name="Token" type="tt:ReferenceToken"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="Type" type="xs:anyURI" use="optional" default="http://www.onvif.org/ver10/schema/Receiver"/>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="TrackReference">
		<xs:restriction base="tt:ReferenceToken"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="Description">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="DateTimeRange">
		<xs:sequence>
			<xs:element name="From" type="xs:dateTime"/>
			<xs:element name="Until" type="xs:dateTime"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingSummary">
		<xs:sequence>
			<xs:element name="DataFrom" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The earliest point in time where there is recorded data on the device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DataUntil" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The most recent point in time where there is recorded data on the device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="NumberRecordings" type="xs:int">
				<xs:annotation>
					<xs:documentation>The device contains this many recordings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SearchScope">
		<xs:annotation>
			<xs:documentation>A structure for defining a limited scope when searching in recorded data.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="IncludedSources" type="tt:SourceReference" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A list of sources that are included in the scope. If this list is included, only data from one of these sources shall be searched.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="IncludedRecordings" type="tt:RecordingReference" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A list of recordings that are included in the scope. If this list is included, only data from one of these recordings shall be searched.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RecordingInformationFilter" type="tt:XPathExpression" minOccurs="0">
				<xs:annotation>
					<xs:documentation>An xpath expression used to specify what recordings to search. Only those recordings with an RecordingInformation structure that matches the filter shall be searched.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:SearchScopeExtension" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Extension point</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SearchScopeExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EventFilter">
		<xs:complexContent>
			<xs:extension base="wsnt:FilterType">
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="PTZPositionFilter">
		<xs:sequence>
			<xs:element name="MinPosition" type="tt:PTZVector">
				<xs:annotation>
					<xs:documentation>The lower boundary of the PTZ volume to look for.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaxPosition" type="tt:PTZVector">
				<xs:annotation>
					<xs:documentation>The upper boundary of the PTZ volume to look for.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EnterOrExit" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>If true, search for when entering the specified PTZ volume.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataFilter">
		<xs:sequence>
			<xs:element name="MetadataStreamFilter" type="tt:XPathExpression"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="XPathExpression">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="FindRecordingResultList">
		<xs:sequence>
			<xs:element name="SearchState" type="tt:SearchState">
				<xs:annotation>
					<xs:documentation>The state of the search when the result is returned. Indicates if there can be more results, or if the search is completed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RecordingInformation" type="tt:RecordingInformation" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A RecordingInformation structure for each found recording matching the search.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FindEventResultList">
		<xs:sequence>
			<xs:element name="SearchState" type="tt:SearchState">
				<xs:annotation>
					<xs:documentation>The state of the search when the result is returned. Indicates if there can be more results, or if the search is completed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Result" type="tt:FindEventResult" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A FindEventResult structure for each found event matching the search.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FindEventResult">
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference">
				<xs:annotation>
					<xs:documentation>The recording where this event was found. Empty string if no recording is associated with this event.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TrackToken" type="tt:TrackReference">
				<xs:annotation>
					<xs:documentation>A reference to the track where this event was found. Empty string if no track is associated with this event.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Time" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The time when the event occured.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Event" type="wsnt:NotificationMessageHolderType">
				<xs:annotation>
					<xs:documentation>The description of the event.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="StartStateEvent" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>If true, indicates that the event is a virtual event generated for this particular search session to give the state of a property at the start time of the search.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FindPTZPositionResultList">
		<xs:sequence>
			<xs:element name="SearchState" type="tt:SearchState">
				<xs:annotation>
					<xs:documentation>The state of the search when the result is returned. Indicates if there can be more results, or if the search is completed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Result" type="tt:FindPTZPositionResult" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A FindPTZPositionResult structure for each found PTZ position matching the search.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FindPTZPositionResult">
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference">
				<xs:annotation>
					<xs:documentation>A reference to the recording containing the PTZ position.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TrackToken" type="tt:TrackReference">
				<xs:annotation>
					<xs:documentation>A reference to the metadata track containing the PTZ position.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Time" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The time when the PTZ position was valid.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Position" type="tt:PTZVector">
				<xs:annotation>
					<xs:documentation>The PTZ position.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FindMetadataResultList">
		<xs:sequence>
			<xs:element name="SearchState" type="tt:SearchState">
				<xs:annotation>
					<xs:documentation>The state of the search when the result is returned. Indicates if there can be more results, or if the search is completed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Result" type="tt:FindMetadataResult" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A FindMetadataResult structure for each found set of Metadata matching the search.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="FindMetadataResult">
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference">
				<xs:annotation>
					<xs:documentation>A reference to the recording containing the metadata.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TrackToken" type="tt:TrackReference">
				<xs:annotation>
					<xs:documentation>A reference to the metadata track containing the matching metadata.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Time" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The point in time when the matching metadata occurs in the metadata track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="SearchState">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Queued">
				<xs:annotation>
					<xs:documentation>The search is queued and not yet started.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Searching">
				<xs:annotation>
					<xs:documentation>The search is underway and not yet completed.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Completed">
				<xs:annotation>
					<xs:documentation>The search has been completed and no new results will be found.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Unknown">
				<xs:annotation>
					<xs:documentation>The state of the search is unknown. (This is not a valid response from GetSearchState.)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:simpleType name="JobToken">
		<xs:restriction base="tt:ReferenceToken"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="RecordingInformation">
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference"/>
			<xs:element name="Source" type="tt:RecordingSourceInformation">
				<xs:annotation>
					<xs:documentation>
					Information about the source of the recording. This gives a description of where the data in the recording comes from. Since a single
					recording is intended to record related material, there is just one source. It is indicates the physical location or the
					major data source for the recording. Currently the recordingconfiguration cannot describe each individual data source.
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="EarliestRecording" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="LatestRecording" type="xs:dateTime" minOccurs="0"/>
			<xs:element name="Content" type="tt:Description"/>
			<xs:element name="Track" type="tt:TrackInformation" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Basic information about the track. Note that a track may represent a single contiguous time span or consist of multiple slices.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="RecordingStatus" type="tt:RecordingStatus"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingSourceInformation">
		<xs:annotation>
			<xs:documentation>
				A set of informative desciptions of a data source. The Search searvice allows a client to filter on recordings based on information in this structure.
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SourceId" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>
				Identifier for the source chosen by the client that creates the structure.
				This identifier is opaque to the device. Clients may use any type of URI for this field. A device shall support at least 128 characters.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Name" type="tt:Name">
				<xs:annotation>
					<xs:documentation>Informative user readable name of the source, e.g. "Camera23". A device shall support at least 20 characters.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Location" type="tt:Description">
				<xs:annotation>
					<xs:documentation>Informative description of the physical location of the source, e.g. the coordinates on a map.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Description" type="tt:Description">
				<xs:annotation>
					<xs:documentation>Informative description of the source.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Address" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>URI provided by the service supplying data to be recorded. A device shall support at least 128 characters.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="RecordingStatus">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Initiated"/>
			<xs:enumeration value="Recording"/>
			<xs:enumeration value="Stopped"/>
			<xs:enumeration value="Removing"/>
			<xs:enumeration value="Removed"/>
			<xs:enumeration value="Unknown">
				<xs:annotation>
					<xs:documentation>This case should never happen.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="TrackInformation">
		<xs:sequence>
			<xs:element name="TrackToken" type="tt:TrackReference"/>
			<xs:element name="TrackType" type="tt:TrackType">
				<xs:annotation>
					<xs:documentation>Type of the track: "Video", "Audio" or "Metadata".
					The track shall only be able to hold data of that type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Description" type="tt:Description">
				<xs:annotation>
					<xs:documentation>Informative description of the contents of the track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DataFrom" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The start date and time of the oldest recorded data in the track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DataTo" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The stop date and time of the newest recorded data in the track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="TrackType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Video"/>
			<xs:enumeration value="Audio"/>
			<xs:enumeration value="Metadata"/>
			<xs:enumeration value="Extended">
				<xs:annotation>
					<xs:documentation>Placeholder for future extension.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="MediaAttributes">
		<xs:annotation>
			<xs:documentation>A set of media attributes valid for a recording at a point in time or for a time interval.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference">
				<xs:annotation>
					<xs:documentation>A reference to the recording that has these attributes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TrackAttributes" type="tt:TrackAttributes" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>A set of attributes for each track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="From" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The attributes are valid from this point in time in the recording.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Until" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>The attributes are valid until this point in time in the recording. Can be equal to 'From' to indicate that the attributes are only known to be valid for this particular point in time.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="TrackAttributes">
		<xs:sequence>
			<xs:element name="TrackInformation" type="tt:TrackInformation">
				<xs:annotation>
					<xs:documentation>The basic information about the track. Note that a track may represent a single contiguous time span or consist of multiple slices.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="VideoAttributes" type="tt:VideoAttributes" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the track is a video track, exactly one of this structure shall be present and contain the video attributes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AudioAttributes" type="tt:AudioAttributes" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the track is an audio track, exactly one of this structure shall be present and contain the audio attributes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MetadataAttributes" type="tt:MetadataAttributes" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If the track is an metadata track, exactly one of this structure shall be present and contain the metadata attributes.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:TrackAttributesExtension" minOccurs="0">
				<xs:annotation>
					<xs:documentation/>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="TrackAttributesExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="VideoAttributes">
		<xs:sequence>
			<xs:element name="Bitrate" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Average bitrate in kbps.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Width" type="xs:int">
				<xs:annotation>
					<xs:documentation>The width of the video in pixels.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Height" type="xs:int">
				<xs:annotation>
					<xs:documentation>The height of the video in pixels.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Encoding" type="tt:VideoEncoding">
				<xs:annotation>
					<xs:documentation>Used video codec, either Jpeg, H.264 or Mpeg4</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Framerate" type="xs:float">
				<xs:annotation>
					<xs:documentation>Average framerate in frames per second.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AudioAttributes">
		<xs:sequence>
			<xs:element name="Bitrate" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The bitrate in kbps.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Encoding" type="tt:AudioEncoding">
				<xs:annotation>
					<xs:documentation>Audio codec used for encoding the audio (either G.711, G.726 or AAC)</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Samplerate" type="xs:int">
				<xs:annotation>
					<xs:documentation>The sample rate in kHz.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataAttributes">
		<xs:sequence>
			<xs:element name="CanContainPTZ" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates that there can be PTZ data in the metadata track in the specified time interval.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CanContainAnalytics" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates that there can be analytics data in the metadata track in the specified time interval.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="CanContainNotifications" type="xs:boolean">
				<xs:annotation>
					<xs:documentation>Indicates that there can be notifications in the metadata track in the specified time interval.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="PtzSpaces" type="tt:StringAttrList">
			<xs:annotation>
				<xs:documentation>List of all PTZ spaces active for recording. Note that events are only recorded on position changes and the actual point of recording may not necessarily contain an event of the specified type. </xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--         RecordingService Types         		  -->
	<!--===============================-->
	<!--===============================-->
	<xs:simpleType name="RecordingJobReference">
		<xs:restriction base="tt:ReferenceToken"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="RecordingConfiguration">
		<xs:sequence>
			<xs:element name="Source" type="tt:RecordingSourceInformation">
				<xs:annotation>
					<xs:documentation>Information about the source of the recording.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Content" type="tt:Description">
				<xs:annotation>
					<xs:documentation>Informative description of the source.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="MaximumRetentionTime" type="xs:duration">
				<xs:annotation>
					<xs:documentation>Sspecifies the maximum time that data in any track within the
				recording shall be stored. The device shall delete any data older than the maximum retention
				time. Such data shall not be accessible anymore. If the MaximumRetentionPeriod is set to 0,
				the device shall not limit the retention time of stored data, except by resource constraints.
				Whatever the value of MaximumRetentionTime, the device may automatically delete
				recordings to free up storage space for new recordings.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="TrackConfiguration">
		<xs:sequence>
			<xs:element name="TrackType" type="tt:TrackType">
				<xs:annotation>
					<xs:documentation>Type of the track. It shall be equal to the strings “Video”,
				“Audio” or “Metadata”. The track shall only be able to hold data of that type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Description" type="tt:Description">
				<xs:annotation>
					<xs:documentation>Informative description of the track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="GetRecordingsResponseItem">
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference">
				<xs:annotation>
					<xs:documentation>Token of the recording.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Configuration" type="tt:RecordingConfiguration">
				<xs:annotation>
					<xs:documentation>Configuration of the recording.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tracks" type="tt:GetTracksResponseList">
				<xs:annotation>
					<xs:documentation>List of tracks.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="GetTracksResponseList">
		<xs:sequence>
			<xs:element name="Track" type="tt:GetTracksResponseItem" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Configuration of a track.</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="GetTracksResponseItem">
		<xs:sequence>
			<xs:element name="TrackToken" type="tt:TrackReference">
				<xs:annotation>
					<xs:documentation>Token of the track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Configuration" type="tt:TrackConfiguration">
				<xs:annotation>
					<xs:documentation>Configuration of the track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobConfiguration">
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference">
				<xs:annotation>
					<xs:documentation>Identifies the recording to which this job shall store the received data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Mode" type="tt:RecordingJobMode">
				<xs:annotation>
					<xs:documentation>The mode of the job. If it is idle, nothing shall happen. If it is active, the device shall try
				to obtain data from the receivers. A client shall use GetRecordingJobState to determine if data transfer is really taking place.<br/>
				The only valid values for Mode shall be “Idle” and “Active”.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Priority" type="xs:int">
				<xs:annotation>
					<xs:documentation>This shall be a non-negative number. If there are multiple recording jobs that store data to
				the same track, the device will only store the data for the recording job with the highest
				priority. The priority is specified per recording job, but the device shall determine the priority
				of each track individually. If there are two recording jobs with the same priority, the device
				shall record the data corresponding to the recording job that was activated the latest.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Source" type="tt:RecordingJobSource" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Source of the recording.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RecordingJobConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="RecordingJobMode">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="RecordingJobConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobSource">
		<xs:sequence>
			<xs:element name="SourceToken" type="tt:SourceReference" minOccurs="0">
				<xs:annotation>
					<xs:documentation>This field shall be a reference to the source of the data. The type of the source
				is determined by the attribute Type in the SourceToken structure. If Type is
				http://www.onvif.org/ver10/schema/Receiver, the token is a ReceiverReference. In this case
				the device shall receive the data over the network. If Type is
				http://www.onvif.org/ver10/schema/Profile, the token identifies a media profile, instructing the
				device to obtain data from a profile that exists on the local device.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="AutoCreateReceiver" type="xs:boolean" minOccurs="0">
				<xs:annotation>
					<xs:documentation>If this field is TRUE, and if the SourceToken is omitted, the device
				shall create a receiver object (through the receiver service) and assign the
				ReceiverReference to the SourceToken field. When retrieving the RecordingJobConfiguration
				from the device, the AutoCreateReceiver field shall never be present.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tracks" type="tt:RecordingJobTrack" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of tracks associated with the recording.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RecordingJobSourceExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobSourceExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobTrack">
		<xs:sequence>
			<xs:element name="SourceTag" type="xs:string">
				<xs:annotation>
					<xs:documentation>If the received RTSP stream contains multiple tracks of the same type, the
						SourceTag differentiates between those Tracks. This field can be ignored in case of recording a local source.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Destination" type="tt:TrackReference">
				<xs:annotation>
					<xs:documentation>The destination is the tracktoken of the track to which the device shall store the
				received data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobStateInformation">
		<xs:sequence>
			<xs:element name="RecordingToken" type="tt:RecordingReference">
				<xs:annotation>
					<xs:documentation>Identification of the recording that the recording job records to.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="State" type="tt:RecordingJobState">
				<xs:annotation>
					<xs:documentation>Holds the aggregated state over the whole RecordingJobInformation structure.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Sources" type="tt:RecordingJobStateSource" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>Identifies the data source of the recording job.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:RecordingJobStateInformationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobStateInformationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="RecordingJobState">
		<xs:restriction base="xs:string"/>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="RecordingJobStateSource">
		<xs:sequence>
			<xs:element name="SourceToken" type="tt:SourceReference">
				<xs:annotation>
					<xs:documentation>Identifies the data source of the recording job.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="State" type="tt:RecordingJobState">
				<xs:annotation>
					<xs:documentation>Holds the aggregated state over all substructures of RecordingJobStateSource.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Tracks" type="tt:RecordingJobStateTracks">
				<xs:annotation>
					<xs:documentation>List of track items.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobStateTracks">
		<xs:sequence>
			<xs:element name="Track" type="tt:RecordingJobStateTrack" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="RecordingJobStateTrack">
		<xs:sequence>
			<xs:element name="SourceTag" type="xs:string">
				<xs:annotation>
					<xs:documentation>Identifies the track of the data source that provides the data.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Destination" type="tt:TrackReference">
				<xs:annotation>
					<xs:documentation>Indicates the destination track.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Error" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optionally holds an implementation defined string value that describes the error.
				The string should be in the English language.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="State" type="tt:RecordingJobState">
				<xs:annotation>
					<xs:documentation>Provides the job state of the track. The valid
				values of state shall be “Idle”, “Active” and “Error”. If state equals “Error”, the Error field may be filled in with an implementation defined value.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="GetRecordingJobsResponseItem">
		<xs:sequence>
			<xs:element name="JobToken" type="tt:RecordingJobReference"/>
			<xs:element name="JobConfiguration" type="tt:RecordingJobConfiguration"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--         End, RecordingService Types         		  -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--         Replay Types         		  -->
	<!--===============================-->
	<xs:complexType name="ReplayConfiguration">
		<xs:annotation>
			<xs:documentation>
      Configuration parameters for the replay service.
    </xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="SessionTimeout" type="xs:duration">
				<xs:annotation>
					<xs:documentation>The RTSP session timeout.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--         End, Replay Types         		  -->
	<!--===============================-->
	<!--===============================-->
	<!--===============================-->
	<!--         Analytics Device Types         -->
	<!--===============================-->
	<xs:complexType name="AnalyticsEngine">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="AnalyticsEngineConfiguration" type="tt:AnalyticsDeviceEngineConfiguration"/>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsDeviceEngineConfiguration">
		<xs:sequence>
			<xs:element name="EngineConfiguration" type="tt:EngineConfiguration" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:AnalyticsDeviceEngineConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsDeviceEngineConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="EngineConfiguration">
		<xs:sequence>
			<xs:element name="VideoAnalyticsConfiguration" type="tt:VideoAnalyticsConfiguration"/>
			<xs:element name="AnalyticsEngineInputInfo" type="tt:AnalyticsEngineInputInfo"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsEngineInputInfo">
		<xs:sequence>
			<xs:element name="InputInfo" type="tt:Config" minOccurs="0"/>
			<xs:element name="Extension" type="tt:AnalyticsEngineInputInfoExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsEngineInputInfoExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsEngineInput">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="SourceIdentification" type="tt:SourceIdentification"/>
					<xs:element name="VideoInput" type="tt:VideoEncoderConfiguration"/>
					<xs:element name="MetadataInput" type="tt:MetadataInput"/>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SourceIdentification">
		<xs:sequence>
			<xs:element name="Name" type="xs:string"/>
			<xs:element name="Token" type="tt:ReferenceToken" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:SourceIdentificationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="SourceIdentificationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataInput">
		<xs:sequence>
			<xs:element name="MetadataConfig" type="tt:Config" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="Extension" type="tt:MetadataInputExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MetadataInputExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsEngineControl">
		<xs:complexContent>
			<xs:extension base="tt:ConfigurationEntity">
				<xs:sequence>
					<xs:element name="EngineToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Token of the analytics engine (AnalyticsEngine) being controlled.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="EngineConfigToken" type="tt:ReferenceToken">
						<xs:annotation>
							<xs:documentation>Token of the analytics engine configuration (VideoAnalyticsConfiguration) in effect.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="InputToken" type="tt:ReferenceToken" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>Tokens of the input (AnalyticsEngineInput) configuration applied.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="ReceiverToken" type="tt:ReferenceToken" maxOccurs="unbounded">
						<xs:annotation>
							<xs:documentation>Tokens of the receiver providing media input data. The order of ReceiverToken shall exactly match the order of InputToken.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Multicast" type="tt:MulticastConfiguration" minOccurs="0"/>
					<xs:element name="Subscription" type="tt:Config"/>
					<xs:element name="Mode" type="tt:ModeOfOperation"/>
					<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="ModeOfOperation">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Idle"/>
			<xs:enumeration value="Active"/>
			<xs:enumeration value="Unknown">
				<xs:annotation>
					<xs:documentation>This case should never happen.</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="AnalyticsStateInformation">
		<xs:sequence>
			<xs:element name="AnalyticsEngineControlToken" type="tt:ReferenceToken">
				<xs:annotation>
					<xs:documentation>Token of the control object whose status is requested.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="State" type="tt:AnalyticsState"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="AnalyticsState">
		<xs:sequence>
			<xs:element name="Error" type="xs:string" minOccurs="0"/>
			<xs:element name="State" type="xs:string"/>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<!--         End, Analytics Device Types         -->
	<!--===============================-->
	<!--=========================================-->
	<!--  Action event payload Types   -->
	<!--=========================================-->
	<!--===============================-->
	<xs:complexType name="ActionEngineEventPayload">
		<xs:annotation>
			<xs:documentation>Action Engine Event Payload data structure contains the information about the ONVIF command invocations. Since this event could be generated by other or proprietary actions, the command invocation specific fields are defined as optional and additional extension mechanism is provided for future or additional action definitions.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="RequestInfo" type="soapenv:Envelope" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>Request Message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ResponseInfo" type="soapenv:Envelope" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>Response Message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Fault" type="soapenv:Fault" minOccurs="0" maxOccurs="1">
				<xs:annotation>
					<xs:documentation>Fault Message</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:ActionEngineEventPayloadExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ActionEngineEventPayloadExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, Action event payload Types   -->
	<!--=========================================-->
        <!--=========================================-->
	<!--  Begin, Audio event types               -->
	<!--=========================================-->
	<xs:simpleType name="AudioClassType">
		<xs:annotation>
		<xs:documentation>
		  AudioClassType acceptable values are;
		   gun_shot, scream, glass_breaking, tire_screech   
		</xs:documentation>
		</xs:annotation>
	<xs:restriction base="xs:string"/>
	</xs:simpleType>

	<xs:complexType name="AudioClassCandidate">
	<xs:sequence>
		<xs:element name="Type" type="tt:AudioClassType">
			<xs:annotation>
			<xs:documentation>Indicates audio class label</xs:documentation>
			</xs:annotation>
		</xs:element> 
 		<xs:element name="Likelihood" type="xs:float">
			<xs:annotation>
			<xs:documentation>A likelihood/probability that the corresponding audio event belongs to this class. The sum of the likelihoods shall NOT exceed 1</xs:documentation>
			</xs:annotation>
		</xs:element>  
		<xs:any namespace="##targetNamespace" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
	</xs:sequence>
	<xs:anyAttribute processContents="lax"/>
	</xs:complexType>

	<xs:complexType name="AudioClassDescriptor">
	<xs:sequence>
		<xs:element name="ClassCandidate" type="tt:AudioClassCandidate" minOccurs="0" maxOccurs="unbounded">
			<xs:annotation>
			<xs:documentation>Array of audio class label and class probability</xs:documentation>
			</xs:annotation>
		</xs:element>
		<xs:element name="Extension" type="tt:AudioClassDescriptorExtension" minOccurs="0"/>
	</xs:sequence>
	<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<xs:complexType name="AudioClassDescriptorExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, Audio event types                 -->
	<!--=========================================-->	
	<!--==================================-->
	<!--  Begin, OSD Device Types         -->
	<!--==================================-->
	<xs:complexType name="OSDReference">
		<xs:simpleContent>
			<xs:extension base="tt:ReferenceToken">
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:simpleContent>
	</xs:complexType>
	<!--===============================-->
	<xs:simpleType name="OSDType">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Text"/>
			<xs:enumeration value="Image"/>
			<xs:enumeration value="Extended"/>
		</xs:restriction>
	</xs:simpleType>
	<!--===============================-->
	<xs:complexType name="OSDPosConfiguration">
		<xs:sequence>
			<xs:element name="Type" type="xs:string">
				<xs:annotation>
					<xs:documentation>For OSD position type, following are the pre-defined: <ul><li>UpperLeft</li>
						<li>UpperRight</li>
						<li>LowerLeft</li>
						<li>LowerRight</li>
						<li>Custom</li></ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Pos" type="tt:Vector" minOccurs="0"/>
			<xs:element name="Extension" type="tt:OSDPosConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDPosConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDColor">
		<xs:annotation>
			<xs:documentation>The value range of "Transparent" could be defined by vendors only should follow this rule: the minimum value means non-transparent and the maximum value maens fully transparent.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Color" type="tt:Color"/>
		</xs:sequence>
		<xs:attribute name="Transparent" type="xs:int" use="optional"/>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextConfiguration">
		<xs:sequence>
			<xs:element name="Type" type="xs:string">
				<xs:annotation>
					<xs:documentation>
						The following OSD Text Type are defined:<ul>
							<li>Plain - The Plain type means the OSD is shown as a text string which defined in the "PlainText" item.</li>
							<li>Date - The Date type means the OSD is shown as a date, format of which should be present in the "DateFormat" item.</li>
							<li>Time - The Time type means the OSD is shown as a time, format of which should be present in the "TimeFormat" item.</li>
							<li>DateAndTime - The DateAndTime type means the OSD is shown as date and time, format of which should be present in the "DateFormat" and the "TimeFormat" item.</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateFormat" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						List of supported OSD date formats. This element shall be present when the value of Type field has Date or DateAndTime. The following DateFormat are defined:<ul>
							<li>M/d/yyyy - e.g. 3/6/2013</li>
							<li>MM/dd/yyyy - e.g. 03/06/2013</li>
							<li>dd/MM/yyyy - e.g. 06/03/2013</li>
							<li>yyyy/MM/dd - e.g. 2013/03/06</li>
							<li>yyyy-MM-dd - e.g. 2013-06-03</li>
							<li>dddd, MMMM dd, yyyy - e.g. Wednesday, March 06, 2013</li>
							<li>MMMM dd, yyyy - e.g. March 06, 2013</li>
							<li>dd MMMM, yyyy - e.g. 06 March, 2013</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeFormat" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>
						List of supported OSD time formats. This element shall be present when the value of Type field has Time or DateAndTime. The following TimeFormat are defined:<ul>
							<li>h:mm:ss tt - e.g. 2:14:21 PM</li>
							<li>hh:mm:ss tt - e.g. 02:14:21 PM</li>
							<li>H:mm:ss - e.g. 14:14:21</li>
							<li>HH:mm:ss - e.g. 14:14:21</li>
						</ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontSize" type="xs:int" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Font size of the text in pt.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontColor" type="tt:OSDColor" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Font color of the text.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BackgroundColor" type="tt:OSDColor" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Background color of the text.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PlainText" type="xs:string" minOccurs="0">
				<xs:annotation>
					<xs:documentation>The content of text to be displayed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDTextConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgConfiguration">
		<xs:sequence>
			<xs:element name="ImgPath" type="xs:anyURI">
				<xs:annotation>
					<xs:documentation>The URI of the image which to be displayed.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDImgConfigurationExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ColorspaceRange">
		<xs:sequence>
			<xs:element name="X" type="tt:FloatRange"/>
			<xs:element name="Y" type="tt:FloatRange"/>
			<xs:element name="Z" type="tt:FloatRange"/>
			<xs:element name="Colorspace" type="xs:anyURI"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="ColorOptions">
		<xs:annotation>
			<xs:documentation>Describe the option of the color supported. Either list each color or define the range of color value. The following values are acceptable for Colourspace attribute.<ul><li>http://www.onvif.org/ver10/colorspace/YCbCr - YCbCr colourspace</li>
				<li>http://www.onvif.org/ver10/colorspace/CIELUV - CIE LUV</li>
				<li>http://www.onvif.org/ver10/colorspace/CIELAB - CIE 1976 (L*a*b*)</li>
				<li>http://www.onvif.org/ver10/colorspace/HSV - HSV colourspace</li></ul>
			</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:choice>
				<xs:element name="ColorList" type="tt:Color" maxOccurs="unbounded">
					<xs:annotation>
						<xs:documentation>List the supported color.</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="ColorspaceRange" type="tt:ColorspaceRange" maxOccurs="unbounded">
					<xs:annotation>
						<xs:documentation>Define the rang of color supported.</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:choice>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDColorOptions">
		<xs:annotation>
			<xs:documentation>Describe the option of the color and its transparency.</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="Color" type="tt:ColorOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Optional list of supported colors.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Transparent" type="tt:IntRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Range of the transparent level. Larger means more tranparent.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDColorOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDColorOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextOptions">
		<xs:sequence>
			<xs:element name="Type" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported OSD text type. When a device indicates the supported number relating to Text type in MaximumNumberOfOSDs, the type shall be presented.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontSizeRange" type="tt:IntRange" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Range of the font size value.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="DateFormat" type="xs:string" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported date format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TimeFormat" type="xs:string" minOccurs="0" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of supported time format.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="FontColor" type="tt:OSDColorOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>List of supported font color.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="BackgroundColor" type="tt:OSDColorOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>List of supported background color.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDTextOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDTextOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgOptions">
		<xs:sequence>
			<xs:element name="ImagePath" type="xs:anyURI" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List of avaiable uris of image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDImgOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDImgOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfiguration">
		<xs:complexContent>
			<xs:extension base="tt:DeviceEntity">
				<xs:sequence>
					<xs:element name="VideoSourceConfigurationToken" type="tt:OSDReference">
						<xs:annotation>
							<xs:documentation>Reference to the video source configuration.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Type" type="tt:OSDType">
						<xs:annotation>
							<xs:documentation>Type of OSD.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Position" type="tt:OSDPosConfiguration">
						<xs:annotation>
							<xs:documentation>Position configuration of OSD.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="TextString" type="tt:OSDTextConfiguration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Text configuration of OSD. It shall be present when the value of Type field is Text.</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Image" type="tt:OSDImgConfiguration" minOccurs="0">
						<xs:annotation>
							<xs:documentation>Image configuration of OSD. It shall be present when the value of Type field is Image</xs:documentation>
						</xs:annotation>
					</xs:element>
					<xs:element name="Extension" type="tt:OSDConfigurationExtension" minOccurs="0"/>
				</xs:sequence>
				<xs:anyAttribute processContents="lax"/>
			</xs:extension>
		</xs:complexContent>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfigurationExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="MaximumNumberOfOSDs">
		<xs:attribute name="Total" type="xs:int" use="required"/>
		<xs:attribute name="Image" type="xs:int"/>
		<xs:attribute name="PlainText" type="xs:int"/>
		<xs:attribute name="Date" type="xs:int"/>
		<xs:attribute name="Time" type="xs:int"/>
		<xs:attribute name="DateAndTime" type="xs:int"/>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfigurationOptions">
		<xs:sequence>
			<xs:element name="MaximumNumberOfOSDs" type="tt:MaximumNumberOfOSDs">
				<xs:annotation>
					<xs:documentation>The maximum number of OSD configurations supported for the specificate video source configuration. If a device limits the number of instances by OSDType, it should indicate the supported number via the related attribute.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Type" type="tt:OSDType" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List supported type of OSD configuration. When a device indicates the supported number for each types in MaximumNumberOfOSDs, related type shall be presented. A device shall return Option element relating to listed type.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="PositionOption" type="xs:string" maxOccurs="unbounded">
				<xs:annotation>
					<xs:documentation>List available OSD position type. Following are the pre-defined:<ul><li>UpperLeft</li>
						<li>UpperRight</li>
						<li>LowerLeft</li>
						<li>LowerRight</li>
						<li>Custom</li></ul>
					</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="TextOption" type="tt:OSDTextOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Option of the OSD text configuration. This element shall be returned if the device is signaling the support for Text.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="ImageOption" type="tt:OSDImgOptions" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Option of the OSD image configuration. This element shall be returned if the device is signaling the support for Image.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="Extension" type="tt:OSDConfigurationOptionsExtension" minOccurs="0"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--===============================-->
	<xs:complexType name="OSDConfigurationOptionsExtension">
		<xs:sequence>
			<xs:any namespace="##any" processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:anyAttribute processContents="lax"/>
	</xs:complexType>
	<!--=========================================-->
	<!--  End, OSD Device Types                  -->
	<!--=========================================-->
</xs:schema>

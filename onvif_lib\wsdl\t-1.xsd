<?xml version="1.0" encoding="UTF-8"?>
<!-- 

OASIS takes no position regarding the validity or scope of any intellectual property or other rights that might be claimed to pertain to the implementation or use of the technology described in this document or the extent to which any license under such rights might or might not be available; neither does it represent that it has made any effort to identify any such rights. Information on OASIS's procedures with respect to rights in OASIS specifications can be found at the OASIS website. Copies of claims of rights made available for publication and any assurances of licenses to be made available, or the result of an attempt made to obtain a general license or permission for the use of such proprietary rights by implementors or users of this specification, can be obtained from the OASIS Executive Director.

OASIS invites any interested party to bring to its attention any copyrights, patents or patent applications, or other proprietary rights which may cover technology that may be required to implement this specification. Please address the information to the OASIS Executive Director.

Copyright (C) OASIS Open (2004-2006). All Rights Reserved.

This document and translations of it may be copied and furnished to others, and derivative works that comment on or otherwise explain it or assist in its implementation may be prepared, copied, published and distributed, in whole or in part, without restriction of any kind, provided that the above copyright notice and this paragraph are included on all such copies and derivative works. However, this document itself may not be modified in any way, such as by removing the copyright notice or references to OASIS, except as needed for the purpose of developing OASIS specifications, in which case the procedures for copyrights defined in the OASIS Intellectual Property Rights document must be followed, or as required to translate it into languages other than English. 

The limited permissions granted above are perpetual and will not be revoked by OASIS or its successors or assigns. 

This document and the information contained herein is provided on an "AS IS" basis and OASIS DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO ANY WARRANTY THAT THE USE OF THE INFORMATION HEREIN WILL NOT INFRINGE ANY RIGHTS OR ANY IMPLIED WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE.

-->


<xsd:schema 
  xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
  xmlns:wstop = "http://docs.oasis-open.org/wsn/t-1"
  targetNamespace = "http://docs.oasis-open.org/wsn/t-1"   
  elementFormDefault="qualified"  attributeFormDefault="unqualified">

<!-- =============== utility type definitions  ==================== -->
  <xsd:complexType name="Documentation" mixed="true">
    <xsd:sequence>
      <xsd:any processContents="lax" minOccurs="0" 
               maxOccurs="unbounded" namespace="##any"/>
    </xsd:sequence>
  </xsd:complexType>

  <xsd:complexType name="ExtensibleDocumented" abstract="true" 
                   mixed="false">
    <xsd:sequence>
      <xsd:element name="documentation" type="wstop:Documentation" 
                   minOccurs="0" />
    </xsd:sequence>
    <xsd:anyAttribute namespace="##other" processContents="lax" />
</xsd:complexType>

<xsd:complexType name="QueryExpressionType" mixed="true">
  <xsd:sequence>
    <xsd:any minOccurs="0" maxOccurs="1" processContents="lax" />
  </xsd:sequence>
  <xsd:attribute name="Dialect" type="xsd:anyURI" use="required"/>
</xsd:complexType>

<!-- ================== Topic-Namespace Related  ================ -->   
  <xsd:complexType name="TopicNamespaceType">
    <xsd:complexContent>
       <xsd:extension base="wstop:ExtensibleDocumented">
         <xsd:sequence>
           <xsd:element name="Topic" 
                        minOccurs="0" maxOccurs="unbounded">
              <xsd:complexType>
              	<xsd:complexContent>
              	  <xsd:extension base="wstop:TopicType">
              	    <xsd:attribute name="parent" type="wstop:ConcreteTopicExpression" />
              	  </xsd:extension>
              	</xsd:complexContent>
              </xsd:complexType>
           </xsd:element>   
           <xsd:any namespace="##other" 
                    minOccurs="0" maxOccurs="unbounded" 
                    processContents="lax"/>
         </xsd:sequence>
         <xsd:attribute name="name" type="xsd:NCName"/>
         <xsd:attribute name="targetNamespace" type="xsd:anyURI" 
                        use="required"/>
         <xsd:attribute name="final" type="xsd:boolean" 
                                     default="false"/>
       </xsd:extension>
     </xsd:complexContent> 
   </xsd:complexType>

  <xsd:element name="TopicNamespace" type="wstop:TopicNamespaceType">
    <xsd:unique name="rootTopicUniqueness">
      <xsd:selector xpath="wstop:Topic"/>
        <xsd:field xpath="@name"/>
    </xsd:unique>
  </xsd:element>
  
  <xsd:attribute name="topicNamespaceLocation" type="xsd:anyURI"/>



<!-- ===================== Topic Related  ========================= -->   

  <xsd:complexType name="TopicType">
    <xsd:complexContent>
      <xsd:extension base="wstop:ExtensibleDocumented">
        <xsd:sequence>
          <xsd:element name="MessagePattern" 
                       type="wstop:QueryExpressionType" 
                       minOccurs="0" maxOccurs="1" />
          <xsd:element name="Topic" type="wstop:TopicType" 
                       minOccurs="0" maxOccurs="unbounded">
            <xsd:unique name="childTopicUniqueness">
              <xsd:selector xpath="wstop:topic"/>
              <xsd:field xpath="@name"/>
            </xsd:unique>
          </xsd:element>
          <xsd:any namespace="##other" minOccurs="0" 
                                       maxOccurs="unbounded"/>
        </xsd:sequence>
        <xsd:attribute name="name" use="required" type="xsd:NCName"/>
        <xsd:attribute name="messageTypes">
          <xsd:simpleType>
            <xsd:list itemType="xsd:QName"/>
          </xsd:simpleType>
        </xsd:attribute>
        <xsd:attribute name="final" type="xsd:boolean" 
                                     default="false"/>
      </xsd:extension>
    </xsd:complexContent>  
  </xsd:complexType>

<!-- ================ Topic Set Related  =================== -->   
  
  <xsd:complexType name="TopicSetType">
    <xsd:complexContent>
       <xsd:extension base="wstop:ExtensibleDocumented">
         <xsd:sequence>
           <xsd:any namespace="##other" 
                    minOccurs="0" maxOccurs="unbounded" 
                    processContents="lax"/>
         </xsd:sequence>
       </xsd:extension>
     </xsd:complexContent> 
   </xsd:complexType>

  <xsd:element name="TopicSet" type="wstop:TopicSetType"/>
<xsd:attribute name="topic" type="xsd:boolean" default="false"/>

<!-- ================ Topic Expression Related  =================== -->   
  
  <xsd:simpleType name="FullTopicExpression">
    <xsd:restriction base="xsd:token">
      <xsd:annotation>
        <xsd:documentation>
        TopicPathExpression  ::=   TopicPath ( '|' TopicPath )*  
        TopicPath       ::=   RootTopic ChildTopicExpression* 
        RootTopic       ::=   NamespacePrefix? ('//')? (NCName | '*')  
        NamespacePrefix ::=   NCName ':'      
        ChildTopicExpression ::=   '/' '/'? (QName | NCName | '*'| '.')
                        
        </xsd:documentation>
      </xsd:annotation>
      <xsd:pattern value= 
         "([\i-[:]][\c-[:]]*:)?(//)?([\i-[:]][\c-[:]]*|\*)((/|//)(([\i-[:]][\c-[:]]*:)?[\i-[:]][\c-[:]]*|\*|[.]))*(\|([\i-[:]][\c-[:]]*:)?(//)?([\i-[:]][\c-[:]]*|\*)((/|//)(([\i-[:]][\c-[:]]*:)?[\i-[:]][\c-[:]]*|\*|[.]))*)*">
      </xsd:pattern>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="ConcreteTopicExpression">
    <xsd:restriction base="xsd:token">
      <xsd:annotation>
        <xsd:documentation>
  The pattern allows strings matching the following EBNF:
    ConcreteTopicPath    ::=   RootTopic ChildTopic*    
    RootTopic            ::=   QName  
    ChildTopic           ::=   '/' (QName | NCName) 
                        
        </xsd:documentation>
      </xsd:annotation>
      <xsd:pattern value=
"(([\i-[:]][\c-[:]]*:)?[\i-[:]][\c-[:]]*)(/([\i-[:]][\c-[:]]*:)?[\i-[:]][\c-[:]]*)*" >
      </xsd:pattern>
    </xsd:restriction>
  </xsd:simpleType>

  <xsd:simpleType name="SimpleTopicExpression">
    <xsd:restriction base="xsd:QName">
      <xsd:annotation>
        <xsd:documentation>
  The pattern allows strings matching the following EBNF:
    RootTopic            ::=   QName  
                        
        </xsd:documentation>
      </xsd:annotation>
    </xsd:restriction>
  </xsd:simpleType>

</xsd:schema>
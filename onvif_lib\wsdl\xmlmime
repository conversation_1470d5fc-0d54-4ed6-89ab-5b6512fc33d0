<?xml version="1.0" ?>
<!-- 
   W3C XML Schema defined in the Describing Media Content of Binary Data in XML
   specification
     http://www.w3.org/TR/xml-media-types

   Copyright © 2005 World Wide Web Consortium,
  
   (Massachusetts Institute of Technology, European Research Consortium for
   Informatics and Mathematics, Keio University). All Rights Reserved. This
   work is distributed under the W3C® Software License [1] in the hope that
   it will be useful, but WITHOUT ANY WARRANTY; without even the implied
   warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
  
   [1] http://www.w3.org/Consortium/Legal/2002/copyright-software-20021231

   $Id: xmlmime.xsd,v 1.1 2005/04/25 17:08:35 hugo Exp $
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:xmime="http://www.w3.org/2005/05/xmlmime"
           targetNamespace="http://www.w3.org/2005/05/xmlmime" >

  <xs:attribute name="contentType">
    <xs:simpleType>
      <xs:restriction base="xs:string" >
      <xs:minLength value="3" />
      </xs:restriction>
    </xs:simpleType>
  </xs:attribute>

  <xs:attribute name="expectedContentTypes" type="xs:string" />

  <xs:complexType name="base64Binary" >
    <xs:simpleContent>
        <xs:extension base="xs:base64Binary" >
            <xs:attribute ref="xmime:contentType" />
        </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

  <xs:complexType name="hexBinary" >
    <xs:simpleContent>
        <xs:extension base="xs:hexBinary" >
            <xs:attribute ref="xmime:contentType" />
        </xs:extension>
    </xs:simpleContent>
  </xs:complexType>

</xs:schema>
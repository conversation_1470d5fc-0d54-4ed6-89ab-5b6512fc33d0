#!/bin/bash

# Kiểm tra xem ffplay có sẵn trong PATH hay không
if ! command -v ffplay &> /dev/null
then
    echo "Lỗi: ffplay không được cài đặt hoặc không có trong PATH."
    exit 1
fi

# Kiểm tra tham số đầu vào STREAM_URL
if [ -z "$1" ]; then
    echo "Sử dụng: ./run_ffplay.sh <STREAM_URL>"
    echo "Ví dụ: ./run_ffplay.sh rtmp://implacable-ai.ai-vlab.com:1935/camera/50f71fd5-cfea-4882-9765-7eed5334fc0e"
    exit 1
fi

# Lấy STREAM_URL từ tham số đầu vào
STREAM_URL="$1"

# Chạy lệnh ffplay với các tham số
ffplay -loglevel verbose \
       -fast \
       -fflags +discardcorrupt+fastseek+nobuffer \
       -flags low_delay \
       -strict experimental \
       -noframedrop \
       -vf "setpts=N/1000/TB" \
       -af "asetpts=N/1000/TB" \
       -analyzeduration 100 \
       -i "$STREAM_URL"

# Kiểm tra lỗi cuối cùng nếu lệnh ffplay thất bại
if [ $? -ne 0 ]; then
    echo "Lỗi: ffplay đã gặp sự cố khi chạy stream."
    exit 1
else
    echo "Stream kết thúc thành công."
fi
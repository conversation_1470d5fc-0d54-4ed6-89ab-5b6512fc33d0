import copy
from dataclasses import dataclass, fields
import json
from PySide6.QtCore import QObject, Property, Signal,Slot,QByteArray, QEnum
from PySide6.QtQml import QJSValue
from datetime import datetime, timedelta
from src.common.qml.models.ruler_context import RulerContext
from src.common.model.camera_model import CameraModel,camera_model_manager,Camera
from src.common.controller.main_controller import main_controller
import enum
from src.common.model.main_tree_view_model import TreeType
from enum import IntEnum
import pickle
import time
import asyncio
import aiohttp
from PySide6.QtGui import QPixmap
from PySide6.QtCore import QThread,Qt
import threading
import logging
logger = logging.getLogger(__name__)

class ImageLoader(QObject):
    DEBUG = False
    finished = Signal(tuple)
    custom_finished_data = Signal(QPixmap, str)
    error = Signal(str)

    def __init__(self, url, width: int = 0, height: int = 0):
        super().__init__()
        self.width = width
        self.height = height
        self.url = url
        self.is_running = True
        self.max_retry = 10
        self.pixmap = None
        
    def stop(self):
        self.is_running = False
        self.pixmap = None
    @Slot()
    def load_image(self):
        asyncio.run(self._load_image_async())
        pass

    async def fetch_image(self, session: aiohttp.ClientSession, url):
        async with session.get(url) as response:
            if response.status == 200:
                return await response.read()
            else:
                raise aiohttp.ClientResponseError(response.request_info, response.history, status=response.status)
    
    async def _load_image_async(self, retry_count=0):
        logger.info(f'_load_image_async')
        if retry_count > self.max_retry:
            self.error.emit(f"Max retry count reached for loading image: {self.url}")
            return

        try:
            timeout = aiohttp.ClientTimeout(total=1)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                data = await self.fetch_image(session, self.url)
                pixmap = QPixmap()
                pixmap.loadFromData(data)
                
                pixmap_scale = self.caculate_pixmap_size(pixmap,width=self.width,height=self.height)

                if not self.is_running:
                    self.pixmap = None
                else:
                    logger.info(f'ImageLoader: {pixmap.size()} {self.is_running}')
                    self.finished.emit((pixmap,pixmap_scale))
        except Exception as e:
            await self._load_image_async(retry_count + 1)

    def caculate_pixmap_size(self, pixmap:QPixmap, width = 0, height = 0):
        origin_width = pixmap.size().width()
        origin_height = pixmap.size().height()
        if width !=0 and height !=0:
            origin_ratio = origin_width / origin_height
            new_ratio = width / height
            if origin_ratio < new_ratio:
                pixmap = pixmap.scaled(origin_ratio * height, height, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation) 
            else:
                pixmap = pixmap.scaled(width, width / origin_ratio, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation) 
        elif width !=0:
            pixmap = pixmap.scaledToWidth(width)
        elif height !=0:
            pixmap = pixmap.scaledToHeight(height)
        return pixmap
    
    def update_resize(self,width,height):
        self.width = width
        self.height = height
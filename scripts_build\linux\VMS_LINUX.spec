# -*- mode: python ; coding: utf-8 -*-


block_cipher = None


a = Analysis(['VMS.py'],
             pathex=[],
             binaries=[],
             datas=[('onvif_lib/wsdl', 'wsdl'),('src/assets/*', 'src/assets'),('src/assets/images/*', 'src/assets/images'),('src/assets/audio/*', 'src/assets/audio'),('src/assets/fonts/*', 'src/assets/fonts'),('src/assets/login_screen/*', 'src/assets/login_screen'),('src/assets/menu_icon/*', 'src/assets/menu_icon'),('src/assets/ptz_icon/*', 'src/assets/ptz_icon'),('src/assets/side_menu_icon/*', 'src/assets/side_menu_icon'),('src/assets/tab_icon/*', 'src/assets/tab_icon'),('src/assets/treeview_and_menu_treeview/*', 'src/assets/treeview_and_menu_treeview')],
             hiddenimports=[],
             hookspath=[],
             hooksconfig={},
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)

exe = EXE(pyz,
          a.scripts,
          [],
          exclude_binaries=True,
          name='VMS',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          console=True,
          disable_windowed_traceback=False,
          target_arch=None,
          codesign_identity=None,
          entitlements_file=None
          )
coll = COLLECT(exe,
               a.binaries,
               a.zipfiles,
               a.datas,
               strip=False,
               upx=True,
               upx_exclude=[],
               name='VMS')
app = BUNDLE(coll,
             name='VMS.app',
             icon='src/assets/images/icon_128.png',
             bundle_identifier=None)
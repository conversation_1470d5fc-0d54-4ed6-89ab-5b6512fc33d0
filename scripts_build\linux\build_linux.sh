#!/bin/sh
rm -rf VMS.build/
rm -rf VMS.dist/


# build
nuitka3 --include-data-dir=onvif_lib/wsdl=wsdl --standalone --disable-console --linux-icon=src/assets/images/icon_128.png --plugin-enable=pyside6 --include-qt-plugins=sensible,multimedia VMS.py

# copy onvif to the app bundle
# copy onvif_api/ver10 onvif_api/ver20 to VMS.app/Contents/MacOS/onvif_api
mkdir -p VMS.dist/wsdl
cp -r onvif_lib/wsdl VMS.dist/wsdl

# remove old .deb
rm -rf VMS.deb

# Create folders.
[ -e package ] && rm -r package
mkdir -p package/opt
mkdir -p package/usr/share/applications
mkdir -p package/usr/share/icons/hicolor/scalable/apps

# Copy files (change icon names, add lines for non-scaled icons)
cp -r VMS.dist/ package/opt/VMS
cp src/assets/images/icon_128.png package/usr/share/icons/hicolor/scalable/apps/icon_128.png
cp VMS.desktop package/usr/share/applications

# Change permissions
find package/opt/VMS -type f -exec chmod 644 -- {} +
find package/opt/VMS -type d -exec chmod 755 -- {} +
find package/usr/share -type f -exec chmod 644 -- {} +
chmod +x package/opt/VMS/*

fpm -C package -s dir -t deb -n "VMS" -v 1.0 -p VMS.deb

sudo dpkg -i VMS.deb
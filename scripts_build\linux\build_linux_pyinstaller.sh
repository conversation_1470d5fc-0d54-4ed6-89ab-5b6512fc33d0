#!/bin/sh
# Empty the dmg folder.
rm -rf build/
rm -rf dist/
rm -rf build_dist/
rm -rf VMS.app/
rm -rf VMS.dist/
rm -rf VMS.build/

# build
pyinstaller VMS_LINUX.spec

# remove old .deb
rm -rf VMS.deb

# Create folders.
[ -e package ] && rm -r package
mkdir -p package/opt
mkdir -p package/opt/VMS
mkdir -p package/usr/share/applications
mkdir -p package/usr/share/icons/hicolor/scalable/apps

# Copy files (change icon names, add lines for non-scaled icons)
cp -r dist/VMS/* package/opt/VMS/
cp src/assets/images/icon_128.png package/usr/share/icons/hicolor/scalable/apps/icon_128.png
cp VMS.desktop package/usr/share/applications

# Change permissions
find package/opt/VMS -type f -exec chmod 644 -- {} +
find package/opt/VMS -type d -exec chmod 755 -- {} +
find package/usr/share -type f -exec chmod 644 -- {} +
chmod +x package/opt/VMS/*

fpm -C package -s dir -t deb -n "VMS" -v 1.0 -p VMS.deb

sudo dpkg -i VMS.deb
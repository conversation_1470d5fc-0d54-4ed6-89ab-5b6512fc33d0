# -*- mode: python ; coding: utf-8 -*-

import os
from PyInstaller.utils.hooks import collect_data_files

block_cipher = None

a = Analysis(['../../VMS.py'],
             pathex=[],
             binaries=[],
             datas=[('../../pyjoystick', 'pyjoystick'),
                    ('../../lib_3rdparty/vlc/win/*', 'lib_3rdparty/vlc/win'),
                    ('../../lib_3rdparty/vlc/win/plugins/*', 'lib_3rdparty/vlc/win/plugins'),
                    ('../../src/assets/*', 'src/assets'),
                    ('../../src/assets/images/*', 'src/assets/images'),
                    ('../../src/assets/audio/*', 'src/assets/audio'),
                    ('../../src/assets/fonts/*', 'src/assets/fonts'),
                    ('../../src/assets/login_screen/*', 'src/assets/login_screen'),
                    ('../../src/assets/menu_icon/*', 'src/assets/menu_icon'),
                    ('../../src/assets/ptz_icon/*', 'src/assets/ptz_icon'),
                    ('../../src/assets/side_menu_icon/*', 'src/assets/side_menu_icon'),
                    ('../../src/assets/tab_icon/*', 'src/assets/tab_icon'),
                    ('../../src/assets/treeview_and_menu_treeview/*', 'src/assets/treeview_and_menu_treeview'),
                    ('../../src/common/qml/device_table/*', 'src/common/qml/device_table'),
                    ('../../version.json', 'version'),
                    ],
             hiddenimports=[],
             hookspath=[],
             hooksconfig={},
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(pyz,
          a.scripts,
          [],
          exclude_binaries=True,
          name='iVMS',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,  # This line ensures UPX is used to compress the executable
          console=False,
          disable_windowed_traceback=False,
          target_arch=None,
          codesign_identity=None,
          entitlements_file=None)

coll = COLLECT(exe,
               a.binaries,
               a.zipfiles,
               a.datas,
               strip=False,
               upx=True,  # This line ensures UPX is also applied to collected files
               upx_exclude=[],
               name='iVMS')

app = BUNDLE(coll,
             name='iVMS.app',
             icon='../../src/assets/images/icon_128.png',
             bundle_identifier='com.rosoboronexport.ivms',
             info_plist={'LSBackgroundOnly': False})
             

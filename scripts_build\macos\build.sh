#!/bin/bash

# Configuration
VERSION=$(jq -r '.version_string' version.json)
PRODUCT_NAME=$(jq -r '.product_name' version.json)
ARTIFACT_NAME="${PRODUCT_NAME}-${VERSION}"

# Clean PyInstaller cache directories
clean_pyinstaller_cache() {
    echo "Cleaning PyInstaller cache..."
    sudo rm -rf ~/Library/Application\ Support/pyinstaller/
    sudo rm -rf ~/.cache/pyinstaller/
    sudo rm -rf build/
    sudo rm -rf dist/
}

# Check and install Rosetta 2 if needed
check_rosetta() {
    if ! pgrep -q oahd; then
        echo "Rosetta 2 is not installed. Installing..."
        softwareupdate --install-rosetta --agree-to-license
    fi
}

# Function to get Python path for architecture
get_python_path() {
    local arch=$1
    if [ "${arch}" = "x86_64" ]; then
        echo "/usr/local/bin/python3.12"  # x86_64 Python path
    else
        echo "/opt/homebrew/bin/python3.12"  # ARM64 Python path
    fi
}

# Function to check Python version
check_python_version() {
    local arch=$1
    local python_path=$(get_python_path "${arch}")
    local required_version="3.12"
    
    echo "Checking Python version for ${arch}..."
    
    if [ ! -f "${python_path}" ]; then
        echo "Python not found at ${python_path}"
        if [ "${arch}" = "x86_64" ]; then
            echo "Installing x86_64 Python ${required_version}..."
            arch -x86_64 /usr/sbin/installer -pkg /Library/Frameworks/Python.framework/Versions/3.12/Python-3.12.2-macos11.pkg -target /
        else
            echo "Installing ARM64 Python ${required_version}..."
            brew install python@${required_version}
        fi
        return
    fi
    
    local current_version=$("${python_path}" -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
    echo "Current Python version: ${current_version}"
    
    if [ "${current_version}" != "${required_version}" ]; then
        echo "Error: Python version mismatch. Required: ${required_version}, Found: ${current_version}"
        echo "Please install Python ${required_version} for ${arch} architecture"
        exit 1
    fi
}

# Function to setup Python environment for specific architecture
setup_python_env() {
    local arch=$1
    local env_dir="venv_${arch}"
    local python_path=$(get_python_path "${arch}")
    
    echo "Setting up Python environment for ${arch}..."
    
    # Check Python version before proceeding
    check_python_version "${arch}"
    
    # Deactivate any existing virtual environment
    if [ -n "${VIRTUAL_ENV}" ]; then
        echo "Deactivating existing virtual environment: ${VIRTUAL_ENV}"
        deactivate
    fi
    
    # Remove existing virtual environment directory if it exists
    if [ -d "${env_dir}" ]; then
        echo "Removing existing virtual environment: ${env_dir}"
        rm -rf "${env_dir}"
    fi
    
    # Create architecture-specific virtual environment
    if [ "${arch}" = "x86_64" ]; then
        check_rosetta
        
        # Check if x86_64 Python is installed
        if [ ! -f "/usr/local/bin/python3.12" ]; then
            echo "Installing x86_64 Python..."
            arch -x86_64 /usr/sbin/installer -pkg /Library/Frameworks/Python.framework/Versions/3.12/Python-3.12.2-macos11.pkg -target /
        fi
        
        # Create virtual environment using x86_64 Python
        /usr/local/bin/python3.12 -m venv "${env_dir}"
        # Activate the virtual environment before pip install
        source "${env_dir}/bin/activate"
        arch -x86_64 python3 -m pip install pyinstaller
        arch -x86_64 python3 -m pip install -r scripts_build/macos/requirements_macos.txt
    else
        # Create virtual environment using ARM64 Python
        /opt/homebrew/bin/python3.12 -m venv "${env_dir}"
        # Activate the virtual environment before pip install
        source "${env_dir}/bin/activate"
        python3 -m pip install pyinstaller
        python3 -m pip install -r scripts_build/macos/requirements_macos.txt
    fi
    
    # Verify PyInstaller is installed
    if [ ! -f "${env_dir}/bin/pyinstaller" ]; then
        echo "PyInstaller not found in ${env_dir}, reinstalling..."
        if [ "${arch}" = "x86_64" ]; then
            arch -x86_64 python3 -m pip install --force-reinstall pyinstaller
        else
            python3 -m pip install --force-reinstall pyinstaller
        fi
    fi
}

# Ensure Homebrew binaries are in PATH
export PATH="/opt/homebrew/bin:/usr/local/bin:${PATH}"

# Clean all build and cache directories
clean_pyinstaller_cache
rm -rf build_dist/
rm -rf VMS.app/
rm -rf VMS.dist/
rm -rf VMS.build/

# Ensure Python environment is set up correctly
export PYTHONPATH="$(pwd):${PYTHONPATH}"

# Add this function at the beginning of the script
ensure_icon_exists() {
    if [ ! -f "src/assets/images/icon_128.icns" ]; then
        echo "Converting icon to icns format..."
        mkdir -p icon.iconset
        sips -z 16 16     src/assets/images/icon_128.png --out icon.iconset/icon_16x16.png
        sips -z 32 32     src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
        sips -z 32 32     src/assets/images/icon_128.png --out icon.iconset/icon_32x32.png
        sips -z 64 64     src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
        sips -z 128 128   src/assets/images/icon_128.png --out icon.iconset/icon_128x128.png
        sips -z 256 256   src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
        sips -z 256 256   src/assets/images/icon_128.png --out icon.iconset/icon_256x256.png
        sips -z 512 512   src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
        sips -z 512 512   src/assets/images/icon_128.png --out icon.iconset/icon_512x512.png
        sips -z 1024 1024 src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
        iconutil -c icns icon.iconset -o src/assets/images/icon_128.icns
        rm -rf icon.iconset
    fi
}

# Ensure icon exists before building
ensure_icon_exists

# Copy icon.icns to the root directory for PyInstaller
cp src/assets/images/icon_128.icns ./icon.icns

# Install UPX if not already installed
if ! command -v upx &> /dev/null; then
    brew install upx
fi

# Clean Python cache files before building
find . -type f -name "*.pyc" -delete
find . -type d -name "__pycache__" -exec rm -r {} +

# Function to verify PyInstaller installation
verify_pyinstaller() {
    local env_dir=$1
    local arch=$2
    
    echo "Verifying PyInstaller installation in ${env_dir}..."
    
    # Check if PyInstaller is properly installed
    if ! "${env_dir}/bin/python3" -c "import PyInstaller" 2>/dev/null; then
        echo "PyInstaller not properly installed in ${env_dir}, reinstalling..."
        if [ "${arch}" = "x86_64" ]; then
            arch -x86_64 "${env_dir}/bin/python3" -m pip install --force-reinstall pyinstaller
        else
            "${env_dir}/bin/python3" -m pip install --force-reinstall pyinstaller
        fi
    else
        echo "PyInstaller is properly installed"
    fi
}

# Function to verify virtual environment
verify_venv() {
    if [ -z "${VIRTUAL_ENV}" ]; then
        echo "Error: Virtual environment is not activated"
        exit 1
    fi
    echo "Virtual environment is active: ${VIRTUAL_ENV}"
}

# Function to build for specific architecture
build_for_arch() {
    local arch=$1
    local spec_file="scripts_build/macos/VMS_MACOS_${arch}.spec"
    
    echo "Building for ${arch}..."
    
    # Setup and activate appropriate Python environment
    setup_python_env "${arch}"
    
    # Verify virtual environment is active
    verify_venv
    
    # Verify PyInstaller installation
    verify_pyinstaller "${VIRTUAL_ENV}" "${arch}"
    
    # Clean PyInstaller cache before each build
    clean_pyinstaller_cache
    
    if [ "${arch}" = "x86_64" ]; then
        # Use Rosetta 2 for x86_64 build
        echo "Using Rosetta 2 for x86_64 build..."
        arch -x86_64 "${VIRTUAL_ENV}/bin/python3" -m PyInstaller "${spec_file}" \
            --noconfirm \
            --upx-dir=$(which upx) \
            --clean
    else
        # Native ARM64 build
        "${VIRTUAL_ENV}/bin/python3" -m PyInstaller "${spec_file}" \
            --noconfirm \
            --upx-dir=$(which upx) \
            --clean
    fi

    # Check if build was successful
    if [ ! -d "dist/iVMS_${arch}.app" ]; then
        echo "Error: Build failed for ${arch}"
        echo "Contents of dist directory:"
        ls -la dist/
        exit 1
    fi

    # Deactivate virtual environment
    deactivate

    # After building, apply additional UPX compression to large files
    if [ -d "dist/iVMS_${arch}.app" ]; then
        find "dist/iVMS_${arch}.app" -type f -size +1M -exec upx -9 {} \;
    else
        echo "Warning: dist/iVMS_${arch}.app directory not found"
        exit 1
    fi

    # Create a folder for DMG
    mkdir -p "build_dist/dmg_${arch}"

    # Copy the app bundle to the dmg folder
    cp -r "dist/iVMS_${arch}.app" "build_dist/dmg_${arch}/"

    # Create DMG
    echo "Creating DMG for ${arch}..."
    create-dmg \
        --volname "${PRODUCT_NAME}-${arch}" \
        --volicon "src/assets/images/icon_128.icns" \
        --window-pos 200 120 \
        --window-size 600 300 \
        --icon-size 100 \
        --icon "iVMS_${arch}.app" 175 120 \
        --hide-extension "iVMS_${arch}.app" \
        --app-drop-link 425 120 \
        "build_dist/${ARTIFACT_NAME}-${arch}.dmg" \
        "build_dist/dmg_${arch}/"
}

# Add this function after the build_for_arch function
create_universal_binary() {
    local version=$1
    echo "Creating universal binary..."
    
    # Create directories for universal app
    mkdir -p "build_dist/dmg_universal/iVMS.app"
    
    # Copy the ARM64 app as the base
    cp -R "build_dist/dmg_arm64/iVMS_arm64.app/" "build_dist/dmg_universal/iVMS.app/"
    
    # Find all binary files in both apps and combine them
    find "build_dist/dmg_arm64/iVMS_arm64.app" -type f -exec file {} \; | grep 'Mach-O.*arm64' | while read -r line; do
        # Get the binary path
        binary_path=${line%:*}
        # Get relative path from the app bundle
        rel_path=${binary_path#build_dist/dmg_arm64/iVMS_arm64.app/}
        # Get corresponding x86_64 binary path
        x86_path="build_dist/dmg_x86_64/iVMS_x86_64.app/${rel_path}"
        # Get universal binary path
        universal_path="build_dist/dmg_universal/iVMS.app/${rel_path}"
        
        # Check if both binaries exist
        if [ -f "$x86_path" ]; then
            echo "Creating universal binary for: ${rel_path}"
            # Create universal binary
            lipo -create -output "$universal_path" "$binary_path" "$x86_path"
        fi
    done
    
    # Create DMG for universal binary
    echo "Creating Universal DMG..."
    create-dmg \
        --volname "iVMS" \
        --volicon "src/assets/images/icon_128.icns" \
        --window-pos 200 120 \
        --window-size 600 300 \
        --icon-size 100 \
        --icon "iVMS.app" 175 120 \
        --hide-extension "iVMS.app" \
        --app-drop-link 425 120 \
        "build_dist/iVMS-${version}-universal.dmg" \
        "build_dist/dmg_universal/"
}

show_menu() {
    clear
    echo "iVMS Build System"
    echo "================="
    echo
    echo "Options:"
    echo "1. Full Build (all architectures)"
    echo "2. Build x86_64 Only"
    echo "3. Build ARM64 Only"
    echo "4. Clean Build Environment"
    echo "5. Exit"
    echo
}

process_choice() {
    local choice=$1
    case $choice in
        1)
            echo "Starting full build..."
            # Build for x86_64
            echo "Starting x86_64 build..."
            build_for_arch "x86_64"
            
            # Clean up between builds
            clean_pyinstaller_cache
            
            # Build for ARM64
            echo "Starting ARM64 build..."
            build_for_arch "arm64"
            
            # Create universal binary
            create_universal_binary "${VERSION}"
            ;;
        2)
            echo "Starting x86_64 build..."
            build_for_arch "x86_64"
            ;;
        3)
            echo "Starting ARM64 build..."
            build_for_arch "arm64"
            ;;
        4)
            echo "Cleaning build environment..."
            clean_pyinstaller_cache
            rm -rf build_dist/
            rm -rf VMS.app/
            rm -rf VMS.dist/
            rm -rf VMS.build/
            rm -rf venv_x86_64/
            rm -rf venv_arm64/
            echo "Clean completed."
            ;;
        5)
            echo "Exiting..."
            exit 0
            ;;
        *)
            echo "Invalid option"
            return 1
            ;;
    esac
}

# Main loop
while true; do
    show_menu
    read -p "Enter your choice (1-5): " choice
    echo
    
    process_choice $choice
    
    if [ $? -eq 0 ] && [ "$choice" != "5" ]; then
        echo
        read -p "Do you want to perform another operation? (y/N) " another
        if [[ ! $another =~ ^[Yy]$ ]]; then
            break
        fi
    fi
done

echo "Build completed successfully!"
echo "x86_64 DMG: build_dist/${ARTIFACT_NAME}-x86_64.dmg"
echo "ARM64 DMG: build_dist/${ARTIFACT_NAME}-arm64.dmg"
echo "Universal DMG: build_dist/${ARTIFACT_NAME}-universal.dmg"
#!/bin/sh

# Empty the dmg folder.
rm -rf build_dist/
rm -rf VMS.app/
rm -rf VMS.dist/
rm -rf VMS.build/

python -m nuitka --macos-create-app-bundle \
        --plugin-enable=pyside6 \
        --include-data-dir=pyjoystick/sdl2_mac_arm=pyjoystick/sdl2_mac_arm \
        --include-data-dir=lib_3rdparty/vlc/mac/arm64=lib_3rdparty/vlc/mac/arm64 \
        --include-data-dir=lib_3rdparty/vlc/mac/arm64/plugins=lib_3rdparty/vlc/mac/arm64/plugins \
        --include-qt-plugins=sensible,multimedia,qml,geoservices \
        --macos-app-icon=icon.icns \
        --macos-app-name="VMS" \
        --macos-app-version="1.2.0" \
        VMS.py

# Copy library files to the app bundle
mkdir -p VMS.app/Contents/MacOS/pyjoystick/sdl2_mac_arm
mkdir -p VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64
mkdir -p VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64/plugins

# Copy files maintaining directory structure
cp -r pyjoystick/sdl2_mac_arm/* VMS.app/Contents/MacOS/pyjoystick/sdl2_mac_arm/
cp -r lib_3rdparty/vlc/mac/arm64/* VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64/
cp -r lib_3rdparty/vlc/mac/arm64/plugins/* VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64/plugins/

# Create a folder (named dmg) to prepare our DMG in (if it doesn't already exist).
mkdir -p build_dist/dmg

# Copy the app bundle to the dmg folder.
cp -r "VMS.app" build_dist/dmg

# If the DMG already exists, delete it.
test -f "build_dist/VMS.dmg" && rm "build_dist/VMS.dmg"
create-dmg \
  --volname "VMS" \
  --volicon "icon.icns" \
  --window-pos 200 120 \
  --window-size 600 300 \
  --icon-size 100 \
  --icon "VMS.app" 175 120 \
  --hide-extension "VMS.app" \
  --app-drop-link 425 120 \
  "build_dist/VMS.dmg" \
  "build_dist/dmg/"
#!/bin/sh

# Configuration
VERSION=$(jq -r '.version_string' version.json)
PRODUCT_NAME=$(jq -r '.product_name' version.json)
COMPANY_NAME=$(jq -r '.company.name' version.json)
COMPANY_LEGAL_NAME=$(jq -r '.company.legal_name' version.json)
COMPANY_ADDRESS=$(jq -r '.company.address' version.json)
COMPANY_PHONE=$(jq -r '.company.phone' version.json)
COMPANY_EMAIL=$(jq -r '.company.email' version.json)
COMPANY_WEBSITE=$(jq -r '.company.website' version.json)
COPYRIGHT=$(jq -r '.copyright' version.json)
ARTIFACT_NAME="${PRODUCT_NAME}-${VERSION}.pkg"

# Get the absolute path of the workspace directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
WORKSPACE_DIR="$(dirname "$(dirname "$SCRIPT_DIR")")"

# WORKSPACE_DIR echo
echo "WORKSPACE_DIR: ${WORKSPACE_DIR}"

# # Empty the build folders
rm -rf build/
rm -rf dist/
rm -rf build_dist/
rm -rf VMS.app/
rm -rf VMS.dist/
rm -rf VMS.build/

# Create build_dist directory
mkdir -p build_dist

# Build the app bundle first
python -m nuitka --macos-create-app-bundle \
        --plugin-enable=pyside6 \
        --include-data-dir=pyjoystick/sdl2_mac_arm=pyjoystick/sdl2_mac_arm \
        --include-data-dir=lib_3rdparty/vlc/mac/arm64=lib_3rdparty/vlc/mac/arm64 \
        --include-data-dir=lib_3rdparty/vlc/mac/arm64/plugins=lib_3rdparty/vlc/mac/arm64/plugins \
        --include-qt-plugins=sensible,multimedia,qml,geoservices \
        --macos-app-icon=icon.icns \
        --macos-app-name="VMS" \
        --macos-app-version="${VERSION}" \
        VMS.py

# Copy library files to the app bundle
sudo mkdir -p VMS.app/Contents/MacOS/pyjoystick/sdl2_mac_arm
sudo mkdir -p VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64
sudo mkdir -p VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64/plugins

# Copy files maintaining directory structure
sudo cp -r pyjoystick/sdl2_mac_arm/* VMS.app/Contents/MacOS/pyjoystick/sdl2_mac_arm/
sudo cp -r lib_3rdparty/vlc/mac/arm64/* VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64/
sudo cp -r lib_3rdparty/vlc/mac/arm64/plugins/* VMS.app/Contents/MacOS/lib_3rdparty/vlc/mac/arm64/plugins/

# Verify VMS.app exists
if [ ! -d "${WORKSPACE_DIR}/VMS.app" ]; then
    echo "Error: VMS.app not found at ${WORKSPACE_DIR}/VMS.app"
    exit 1
fi

# Create welcome file
cat > welcome.md << EOF
# Chao mung den voi ${PRODUCT_NAME}

He Thong Quan Ly Video Thong Minh

## Gioi thieu ve ${PRODUCT_NAME}

${PRODUCT_NAME} la he thong quan ly video thong minh duoc thiet ke cho cac ung dung giam sat va an ninh chuyen nghiep. Duoc xay dung voi cong nghe hien dai va tap trung vao trai nghiem nguoi dung, ${PRODUCT_NAME} cung cap kha nang giam sat va phan tich video toan dien.

## Tinh nang chinh

- Phat video thoi gian thuc voi ho tro nhieu camera
- Phan tich video thong minh
- Tuong thich da nen tang
- Giao dien nguoi dung hien dai va tuy chinh
- Ho tro da ngon ngu
- Ho tro tang toc phan cung

## Yeu cau he thong

- macOS 10.15 tro len
- RAM toi thieu 4GB (khuyen nghi 8GB)
- Dung luong o dia trong 1GB
- Ket noi internet de cap nhat va truy cap tu xa

## Cai dat

Bo cai dat se huong dan ban qua qua trinh cai dat:

1. Xem giay phep: Vui long doc va chap nhan thoa thuan giay phep
2. Chon vi tri: Chon vi tri cai dat (mac dinh: /Applications)
3. Cai dat: Nhap vao Cai dat de bat dau qua trinh cai dat
4. Hoan thanh: Khoi chay ${PRODUCT_NAME} tu thu muc Applications

## Khoi chay lan dau

Sau khi cai dat:
1. Khoi chay ${PRODUCT_NAME} tu thu muc Applications
2. Lam theo huong dan thiet lap ban dau
3. Cau hinh camera va cac cai dat
4. Bat dau giam sat!

## Ho tro

De duoc ho tro ky thuat hoac giai dap thac mac:
- Email: ${COMPANY_EMAIL}
- Dien thoai: ${COMPANY_PHONE}
- Website: ${COMPANY_WEBSITE}

---

Cam on ban da chon ${PRODUCT_NAME}
EOF

# Create readme file
cat > readme.md << EOF
# ${PRODUCT_NAME} ${VERSION}

## Gioi thieu

${PRODUCT_NAME} la he thong quan ly video toan dien duoc thiet ke cho cac ung dung giam sat va an ninh.

## Tinh nang

- Giam sat video thoi gian thuc
- Ho tro nhieu camera
- Phat hien chuyen dong
- Ghi su kien
- Quan ly nguoi dung
- Truy cap tu xa

## Cai dat

Bo cai dat se tu dong:
1. Cai dat ${PRODUCT_NAME} vao thu muc Applications
2. Thiet lap quyen truy cap
3. Cau hinh he thong

## Khoi chay lan dau

Sau khi cai dat:
1. Khoi chay ${PRODUCT_NAME} tu thu muc Applications
2. Lam theo huong dan thiet lap ban dau
3. Cau hinh camera va cac cai dat

## Ho tro

De duoc ho tro ky thuat hoac giai dap thac mac:
- Email: ${COMPANY_EMAIL}
- Dien thoai: ${COMPANY_PHONE}
- Website: ${COMPANY_WEBSITE}
EOF

# Create license file
cat > license.md << EOF
# Thoa Thuan Giay Phep Phan Mem

## Dieu Khoan va Dieu Kien

1. Cap phep
   - Phan mem nay duoc cap phep boi ${COMPANY_LEGAL_NAME}
   - Giay phep cho mot nguoi dung, mot lan cai dat
   - Khong duoc chuyen nhuong

2. Quyen su dung
   - Cai dat va su dung tren mot may tinh
   - Sao luu ban sao
   - Su dung cho muc dich kinh doanh

3. Han che
   - Khong duoc dich nguoc
   - Khong duoc phan phoi lai
   - Khong duoc sua doi
   - Khong duoc ban lai

4. Bao hanh
   - Phan mem duoc cung cap "nguyen trang"
   - Khong co bao hanh
   - Khong dam bao phu hop voi muc dich su dung

5. Trach nhiem
   - Gioi han o gia mua
   - Khong chiu trach nhiem ve thiet hai gian tiep
   - Khong chiu trach nhiem ve mat du lieu

6. Cham dut
   - Giay phep cham dut khi vi pham
   - Phai huy tat ca ban sao
   - Khong hoan tien khi cham dut

Bang viec cai dat phan mem nay, ban dong y voi cac dieu khoan tren.

${COPYRIGHT}
${COMPANY_LEGAL_NAME}
${COMPANY_ADDRESS}
EOF

# Create postinstall script to handle Gatekeeper
cat > postinstall << 'EOF'
#!/bin/sh

# Enable logging
exec 1> /tmp/vms_install.log 2>&1
echo "Starting VMS installation process at $(date)"

# Check if the app exists
if [ -d "/Applications/VMS.app" ]; then
    echo "VMS.app exists at /Applications/VMS.app"
    ls -la "/Applications/VMS.app"
else
    echo "VMS.app not found at /Applications/VMS.app"
fi

# Remove quarantine attribute from the app
echo "Removing quarantine attribute from VMS.app"
sudo xattr -cr "/Applications/VMS.app"
if [ $? -eq 0 ]; then
    echo "Successfully removed quarantine attribute"
else
    echo "Failed to remove quarantine attribute"
fi

# Final verification
echo "Final verification at $(date)"
if [ -d "/Applications/VMS.app" ]; then
    echo "VMS.app is present at /Applications/VMS.app"
    ls -la "/Applications/VMS.app"
else
    echo "ERROR: VMS.app is not present at /Applications/VMS.app"
fi

echo "Installation process completed at $(date)"
exit 0
EOF

# Make postinstall executable
chmod +x postinstall

# echo start build pkg
echo "Start build pkg"

# Create Python script to build the package
cat > build_pkg.py << EOF
from macos_pkg_builder import Packages

# Set environment variables for language and font
import os
os.environ['LANG'] = 'vi_VN.UTF-8'
os.environ['LC_ALL'] = 'vi_VN.UTF-8'

# Function to read file with UTF-8 encoding
def read_utf8_file(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return f.read()

pkg = Packages(
    pkg_output="build_dist/${ARTIFACT_NAME}",
    pkg_bundle_id="ru.rosoboronexport.vms",
    pkg_version="${VERSION}",
    pkg_title="${PRODUCT_NAME}",
    pkg_file_structure={
        "${WORKSPACE_DIR}/VMS.app": "/Applications/VMS.app"
    },
    pkg_postinstall_script="postinstall",
    pkg_as_distribution=True,
    pkg_install_location="/",
    pkg_allow_relocation=False,
    pkg_welcome=read_utf8_file("welcome.md"),
    pkg_readme=read_utf8_file("readme.md"),
    pkg_license=read_utf8_file("license.md"),
    pkg_background="src/assets/installer/installer_background.png"
)

success = pkg.build()
if not success:
    exit(1)
EOF

# Install macos-pkg-builder if not already installed
pip install macos-pkg-builder

# Run the Python script to build the package
python build_pkg.py

if [ $? -eq 0 ]; then
    echo "Successfully created package: build_dist/${ARTIFACT_NAME}"
else
    echo "Failed to create package"
    exit 1
fi

# Clean up
rm postinstall
rm build_pkg.py
rm welcome.md
rm readme.md
rm license.md

echo "Package created: build_dist/${ARTIFACT_NAME}"
echo "To install, run: sudo installer -pkg build_dist/${ARTIFACT_NAME} -target /"
echo "To view installation logs, check: /tmp/vms_install.log after installation" 
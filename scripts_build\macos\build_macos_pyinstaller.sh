#!/bin/sh

# Configuration
VERSION=$(jq -r '.version_string' version.json)
PRODUCT_NAME=$(jq -r '.product_name' version.json)
ARTIFACT_NAME="${PRODUCT_NAME}-${VERSION}.dmg"

# Ensure Homebrew binaries are in PATH
export PATH="/opt/homebrew/bin:${PATH}"

# Empty the dmg folder.
rm -rf build/
rm -rf dist/
rm -rf build_dist/
rm -rf VMS.app/
rm -rf VMS.dist/
rm -rf VMS.build/

# Ensure Python environment is set up correctly
export PYTHONPATH="$(pwd):${PYTHONPATH}"

# Convert icon if needed (only needs to be done once)
if [ ! -f "src/assets/images/icon_128.icns" ]; then
    echo "Converting icon to icns format..."
    mkdir -p icon.iconset
    sips -z 16 16     src/assets/images/icon_128.png --out icon.iconset/icon_16x16.png
    sips -z 32 32     src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
    sips -z 32 32     src/assets/images/icon_128.png --out icon.iconset/icon_32x32.png
    sips -z 64 64     src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
    sips -z 128 128   src/assets/images/icon_128.png --out icon.iconset/icon_128x128.png
    sips -z 256 256   src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
    sips -z 256 256   src/assets/images/icon_128.png --out icon.iconset/icon_256x256.png
    sips -z 512 512   src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
    sips -z 512 512   src/assets/images/icon_128.png --out icon.iconset/icon_512x512.png
    sips -z 1024 1024 src/assets/images/icon_128.png --out icon.iconset/<EMAIL>
    iconutil -c icns icon.iconset
    mv icon.icns src/assets/images/icon_128.icns
    rm -rf icon.iconset
fi

# Copy icon.icns to the root directory for PyInstaller
cp src/assets/images/icon_128.icns ./icon.icns

# build
echo "Building with PyInstaller..."
pyinstaller scripts_build/macos/VMS_MACOS.spec

# Create a folder (named dmg) to prepare our DMG in
mkdir -p build_dist/dmg

# Copy the app bundle to the dmg folder
cp -r "dist/iVMS.app" build_dist/dmg/

# Set language to English to avoid localization issues
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

# Create DMG using a more reliable approach
echo "Creating DMG..."
create-dmg \
  --volname "${PRODUCT_NAME}" \
  --volicon "src/assets/images/icon_128.icns" \
  --window-pos 200 120 \
  --window-size 600 300 \
  --icon-size 100 \
  --icon "${PRODUCT_NAME}.app" 175 120 \
  --hide-extension "${PRODUCT_NAME}.app" \
  --app-drop-link 425 120 \
  "build_dist/${PRODUCT_NAME}.dmg" \
  "build_dist/dmg/"

# Rename the DMG with version
mv "build_dist/${PRODUCT_NAME}.dmg" "build_dist/${ARTIFACT_NAME}"

# Upload to MinIO
echo "Uploading ${ARTIFACT_NAME} to MinIO..."
mc alias set minio ${MINIO_HOST} ${MINIO_ACCESS_KEY} ${MINIO_SECRET_KEY}
mc cp "build_dist/${ARTIFACT_NAME}" "minio/videos/${ARTIFACT_NAME}"

# Get the public URL and save it for the deploy stage
DOWNLOAD_URL="${MINIO_DOMAIN}/videos/${ARTIFACT_NAME}"
echo "DOWNLOAD_URL=${DOWNLOAD_URL}" >> build.env
echo "ARTIFACT_NAME=${ARTIFACT_NAME}" >> build.env
echo "VERSION=${VERSION}" >> build.env

echo "Build and upload completed successfully!"
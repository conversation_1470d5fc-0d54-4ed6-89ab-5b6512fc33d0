#!/bin/bash

# Install Command Line Tools if needed
if ! xcode-select -p &> /dev/null; then
    echo "Installing Command Line Tools..."
    sudo rm -rf /Library/Developer/CommandLineTools
    xcode-select --install
    
    # Wait for installation to complete
    echo "Waiting for Command Line Tools installation to complete..."
    until xcode-select -p &> /dev/null; do
        sleep 5
    done
fi

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    echo "Installing Homebrew..."
    NONINTERACTIVE=1 /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    
    # Add Homebrew to PATH
    if [[ $(uname -m) == "arm64" ]]; then
        echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zshrc
        eval "$(/opt/homebrew/bin/brew shellenv)"
        export PATH="/opt/homebrew/bin:$PATH"
    else
        echo 'eval "$(/usr/local/bin/brew shellenv)"' >> ~/.zshrc
        eval "$(/usr/local/bin/brew shellenv)"
        export PATH="/usr/local/bin:$PATH"
    fi
else
    # Add Homebrew to PATH even if already installed
    if [[ $(uname -m) == "arm64" ]]; then
        eval "$(/opt/homebrew/bin/brew shellenv)"
        export PATH="/opt/homebrew/bin:$PATH"
    else
        eval "$(/usr/local/bin/brew shellenv)"
        export PATH="/usr/local/bin:$PATH"
    fi
    echo "Homebrew already installed"
fi

# Update Homebrew
echo "Updating Homebrew..."
brew update

# Install dependencies
echo "Installing dependencies..."
brew install jq
brew install create-dmg
brew install python@3.12

# Install MinIO client
echo "Installing MinIO client..."
brew install minio/stable/mc

# Create and activate virtual environment
echo "Setting up virtual environment..."
python3.12 -m venv venv
source venv/bin/activate

# Upgrade pip in the virtual environment
echo "Upgrading pip..."
python -m pip install --upgrade pip

# Install pip dependencies
echo "Installing PyInstaller and Pillow..."
pip install pyinstaller
pip install Pillow

# Verify installations
echo "Verifying installations..."
python --version
pip --version
pyinstaller --version
create-dmg --version
jq --version
mc --version

# Create build environment file with valid variable names and include Homebrew paths
echo "VENV_PATH=$(pwd)/venv" > build.env
if [[ $(uname -m) == "arm64" ]]; then
    echo "PATH_BREW=/opt/homebrew/bin:/usr/local/bin" >> build.env
else
    echo "PATH_BREW=/usr/local/bin" >> build.env
fi
echo "SYSTEM_PATH=/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin" >> build.env
echo "PATH_UPDATE=\${VENV_PATH}/bin:\${PATH_BREW}:\${SYSTEM_PATH}" >> build.env

echo "All dependencies installed successfully!" 
#!/usr/bin/env python3
import os
import sys
import ctypes
import platform

def verify_vlc():
    # Determine architecture
    arch = 'arm64' if platform.machine() == 'arm64' else 'intel'
    
    # Get project root
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    vlc_path = os.path.join(project_root, 'lib_3rdparty', 'vlc', 'mac', arch)
    
    print(f"Checking VLC in: {vlc_path}")
    
    if not os.path.exists(vlc_path):
        print(f"ERROR: VLC path does not exist: {vlc_path}")
        return False
        
    lib_path = os.path.join(vlc_path, 'libvlc.dylib')
    vlccore_path = os.path.join(vlc_path, 'libvlccore.dylib')
    plugins_path = os.path.join(vlc_path, 'plugins')
    
    # Check paths
    print(f"\nChecking paths:")
    print(f"libvlc.dylib: {'EXISTS' if os.path.exists(lib_path) else 'MISSING'}")
    print(f"libvlccore.dylib: {'EXISTS' if os.path.exists(vlccore_path) else 'MISSING'}")
    print(f"plugins directory: {'EXISTS' if os.path.exists(plugins_path) else 'MISSING'}")
    
    # Check library dependencies
    print("\nChecking library dependencies:")
    os.system(f"otool -L '{lib_path}'")
    print("\nChecking core library dependencies:")
    os.system(f"otool -L '{vlccore_path}'")
    
    # Try loading libraries
    print("\nAttempting to load libraries:")
    try:
        print("Loading libvlccore.dylib...")
        core = ctypes.CDLL(vlccore_path, mode=ctypes.RTLD_GLOBAL)
        print("Loading libvlc.dylib...")
        lib = ctypes.CDLL(lib_path, mode=ctypes.RTLD_GLOBAL)
        print("SUCCESS: Libraries loaded successfully!")
        return True
    except Exception as e:
        print(f"ERROR: Failed to load libraries: {e}")
        return False

if __name__ == "__main__":
    if verify_vlc():
        sys.exit(0)
    else:
        sys.exit(1) 
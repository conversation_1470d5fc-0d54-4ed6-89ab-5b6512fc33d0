#!/bin/bash

# Script to automatically update CHANGELOG.md when new tags are created
# This script should be run in CI/CD pipeline after a new tag is created

# Configuration
CHANGELOG_FILE="CHANGELOG.md"
TEMP_FILE="temp_changelog.md"
REPO_URL="https://gitlab.ai-vlab.com/gpstech/vms/iVMS"

# Get current tag from CI environment
CURRENT_TAG=$CI_COMMIT_TAG
if [ -z "$CURRENT_TAG" ]; then
    echo "Error: No tag found. This script should only run on tag creation."
    exit 1
fi

# Get previous tag
PREVIOUS_TAG=$(git describe --tags --abbrev=0 $CURRENT_TAG^ 2>/dev/null)

# Function to generate changelog entry for a tag
generate_changelog_entry() {
    local tag=$1
    local prev_tag=$2
    
    # Get tag date
    local tag_date=$(git log -1 --format=%ai $tag)
    
    # Generate changelog entry
    echo "## [$tag]($REPO_URL/tags/$tag) - $tag_date"
    echo
    if [ ! -z "$prev_tag" ]; then
        echo "<small>[Compare with $prev_tag]($REPO_URL/compare/$prev_tag...$tag)</small>"
        echo
    fi
    
    # Get commits between tags
    if [ ! -z "$prev_tag" ]; then
        git log --pretty=format:"* %s" $prev_tag..$tag 2>/dev/null
    else
        git log --pretty=format:"* %s" $tag 2>/dev/null
    fi
    echo
}

# Main script
echo "Updating changelog for tag: $CURRENT_TAG"

# Create temporary file with header
echo "# Changelog" > $TEMP_FILE
echo "" >> $TEMP_FILE
echo "<!-- insertion marker -->" >> $TEMP_FILE
echo "" >> $TEMP_FILE

# Generate changelog entry for current tag
generate_changelog_entry "$CURRENT_TAG" "$PREVIOUS_TAG" >> $TEMP_FILE

# Get existing changelog content after the insertion marker
if [ -f "$CHANGELOG_FILE" ]; then
    sed -n '/<!-- insertion marker -->/,$p' "$CHANGELOG_FILE" | sed '1d' >> $TEMP_FILE
fi

# Replace the old changelog with the new one
mv $TEMP_FILE $CHANGELOG_FILE

echo "Changelog updated successfully for tag: $CURRENT_TAG" 
@echo off
setlocal enabledelayedexpansion

:main_menu
cls
echo iVMS Build System
echo ================
echo.
echo Select Build Engine:
echo 0. Nuitka
echo 1. PyInstaller
echo 2. Exit
echo.

:engine_choice
set /p engine="Enter your choice (0-2): "

if "%engine%"=="2" (
    exit /b
) else if "%engine%"=="0" (
    set BUILD_ENGINE=Nuitka
    set AIP_FILE=..\iVMS_nuitka.aip
) else if "%engine%"=="1" (
    set BUILD_ENGINE=PyInstaller
    set AIP_FILE=..\iVMS.aip
) else (
    echo Invalid choice. Please try again.
    goto engine_choice
)

:project_menu
cls
echo iVMS Build System - %BUILD_ENGINE%
echo ================================
echo.
echo Select Build Project Type:
echo 0. Full build and Deploy
echo 1. Only build
echo 2. Only Deploy
echo 3. Back to Engine Selection
echo 4. Exit
echo.

:project_choice
set /p project="Enter your choice (0-4): "

if "%project%"=="4" (
    exit /b
) else if "%project%"=="3" (
    goto main_menu
) else if "%project%"=="0" (
    if "%BUILD_ENGINE%"=="Nuitka" (
        powershell -NoProfile -ExecutionPolicy Bypass -File "%~dp0\build.ps1" -All -Nuitka -AipFile "%~dp0\%AIP_FILE%"
    ) else (
        powershell -NoProfile -ExecutionPolicy Bypass -File "%~dp0\build.ps1" -All -AipFile "%~dp0\%AIP_FILE%"
    )
) else if "%project%"=="1" (
    if "%BUILD_ENGINE%"=="Nuitka" (
        bash "%~dp0\nuitka_build.sh"
    ) else (
        powershell -NoProfile -ExecutionPolicy Bypass -File "%~dp0\build.ps1" -PyInstallerOnly
    )
) else if "%project%"=="2" (
    if "%BUILD_ENGINE%"=="Nuitka" (
        powershell -NoProfile -ExecutionPolicy Bypass -File "%~dp0\build.ps1" -DeployOnly -Nuitka -AipFile "%~dp0\%AIP_FILE%"
    ) else (
        powershell -NoProfile -ExecutionPolicy Bypass -File "%~dp0\build.ps1" -DeployOnly -AipFile "%~dp0\%AIP_FILE%"
    )
) else (
    echo Invalid choice. Please try again.
    goto project_choice
)

echo.
choice /c YN /n /m "Do you want to perform another operation? (Y/N) "
if errorlevel 2 goto end
goto main_menu

:end
exit /b
param (
    [switch]$Clean,
    [switch]$InstallDeps,
    [switch]$BuildOnly,
    [switch]$PyInstallerOnly,
    [switch]$DeployOnly,
    [switch]$All,
    [switch]$Nuitka,
    [string]$AipFile
)

$ErrorActionPreference = "Stop"
$projectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
$versionFile = Join-Path $projectRoot "version.json"

# Add error handling for version file
if (-not (Test-Path $versionFile)) {
    throw "Version file not found at: $versionFile. Please ensure version.json exists in the project root directory."
}

try {
    $versionConfig = Get-Content $versionFile | ConvertFrom-Json
} catch {
    throw "Failed to parse version.json: $_"
}

# Configuration
$config = @{
    ProjectName = $versionConfig.product_name
    Version = $versionConfig.version_string
    RequiredTools = @{
        Python = "3.12"
        AdvancedInstaller = "21.0.1"
    }
}

# Helper functions
function Write-Header($message) { Write-Host "`n=== $message ===" -ForegroundColor Cyan }
function Write-Step($message) { Write-Host "`n>> $message" -ForegroundColor Yellow }
function Confirm-Step($message) {
    if ($All) { return $true }
    $response = Read-Host "`nDo you want to $message (Y/N)?"
    return $response -eq "Y" -or $response -eq "y"
}

function Install-Chocolatey {
    if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Step "Installing Chocolatey..."
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine")
    }
}

function Get-Python312Path {
    # Check common installation paths
    $possiblePaths = @(
        "C:\Program Files\Python312\python.exe",    # Chocolatey default
        "C:\Program Files (x86)\Python312\python.exe", # 32-bit install
        "C:\Python312\python.exe",                  # Standard installer default
        (Get-Command python -ErrorAction SilentlyContinue).Source  # PATH python
    )
    
    Write-Host "Searching for Python 3.12 installation..." -ForegroundColor Yellow
    foreach ($path in $possiblePaths) {
        Write-Host "Checking path: $path"
        if (Test-Path $path) {
            # Verify it's Python 3.12
            try {
                $version = & $path -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')" 2>$null
                Write-Host "Found Python version: $version at $path"
                if ($version -eq "3.12") {
                    return $path
                }
            } catch {
                Write-Host "Failed to check version at $path"
                continue
            }
        }
    }
    
    # Try to find Python in PATH
    try {
        $pythonInPath = where.exe python 2>$null
        if ($pythonInPath) {
            foreach ($path in $pythonInPath) {
                Write-Host "Checking PATH Python: $path"
                $version = & $path -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')" 2>$null
                Write-Host "Found Python version: $version at $path"
                if ($version -eq "3.12") {
                    return $path
                }
            }
        }
    } catch {
        Write-Host "No Python found in PATH"
    }
    
    return $null
}

function Install-Python {
    $python312Path = Get-Python312Path
    
    if ($python312Path) {
        Write-Host "Python 3.12 found at: $python312Path" -ForegroundColor Green
        return
    }
    
    Write-Step "Python 3.12 not found. Installing..."
    
    # Ensure Chocolatey is installed first
    if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Host "Chocolatey not found. Installing Chocolatey first..." -ForegroundColor Yellow
        Install-Chocolatey
    }
    
    try {
        Write-Host "Installing Python 3.12 via Chocolatey..." -ForegroundColor Yellow
        choco install python312 -y
        
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path","Machine") + ";" + 
                   [System.Environment]::GetEnvironmentVariable("Path","User")
        
        # Wait a moment for installation to complete
        Start-Sleep -Seconds 2
        
        # Verify installation
        $python312Path = Get-Python312Path
        if (-not $python312Path) {
            throw "Python 3.12 installation was not found after install"
        }
        
        Write-Host "Python 3.12 installed successfully at: $python312Path" -ForegroundColor Green
        
        # Verify pip is working
        Write-Host "Verifying pip installation..." -ForegroundColor Yellow
        & $python312Path -m pip --version
        if ($LASTEXITCODE -ne 0) {
            & $python312Path -m ensurepip --upgrade
        }
        
        return $python312Path
    } catch {
        throw "Failed to install Python 3.12: $_`nPlease install Python 3.12 manually from https://www.python.org/downloads/"
    }
}

function Verify-PythonVersion {
    Write-Step "Verifying Python version..."
    
    $python312Path = Get-Python312Path
    if (-not $python312Path) {
        throw "Python $($config.RequiredTools.Python) is required but was not found"
    }
    
    try {
        $pythonVersion = & $python312Path -c "import sys; print('.'.join(map(str, sys.version_info[:2])))"
        if ($pythonVersion -ne $config.RequiredTools.Python) {
            throw "Python $($config.RequiredTools.Python) is required but version $pythonVersion was found"
        }
        Write-Host "Python $pythonVersion detected at: $python312Path" -ForegroundColor Green
    } catch {
        throw "Failed to verify Python version: $_"
    }
}

function Install-AdvancedInstaller {
    Write-Step "Checking Advanced Installer..."
    $advInstPath = "C:\Program Files (x86)\Caphyon\Advanced Installer $($config.RequiredTools.AdvancedInstaller)"
    $advInstZip = Join-Path $PSScriptRoot "Caphyon.zip"
    
    if (-not (Test-Path $advInstPath)) {
        Write-Step "Installing Advanced Installer $($config.RequiredTools.AdvancedInstaller)..."
        
        # Check if zip file exists
        if (-not (Test-Path $advInstZip)) {
            throw "Advanced Installer zip file not found at: $advInstZip"
        }
        
        try {
            # Create temporary extraction directory
            $tempExtractPath = Join-Path $env:TEMP "AdvancedInstallerTemp"
            if (Test-Path $tempExtractPath) {
                Remove-Item -Recurse -Force $tempExtractPath
            }
            New-Item -ItemType Directory -Path $tempExtractPath -Force | Out-Null
            
            # Extract with progress using Shell.Application
            Write-Host "Extracting Advanced Installer to temp location..."
            $shell = New-Object -ComObject Shell.Application
            $zip = $shell.NameSpace($advInstZip)
            $destination = $shell.NameSpace($tempExtractPath)
            
            $totalItems = $zip.Items().Count
            $currentItem = 0
            
            foreach ($item in $zip.Items()) {
                $currentItem++
                $percentComplete = [math]::Round(($currentItem / $totalItems) * 100)
                Write-Progress -Activity "Extracting Files" -Status "$percentComplete% Complete" `
                             -PercentComplete $percentComplete -CurrentOperation $item.Name
                
                $destination.CopyHere($item, 0x14)
            }
            
            Write-Progress -Activity "Extracting Files" -Completed
            
            # Create Caphyon directory if it doesn't exist
            $caphyonPath = "C:\Program Files (x86)\Caphyon"
            if (-not (Test-Path $caphyonPath)) {
                New-Item -ItemType Directory -Path $caphyonPath -Force | Out-Null
            }
            
            # Find the correct source directory
            Write-Host "Locating Advanced Installer files..."
            $possiblePaths = @(
                (Join-Path $tempExtractPath "Advanced Installer $($config.RequiredTools.AdvancedInstaller)"),
                (Join-Path $tempExtractPath "Advanced Installer $($config.RequiredTools.AdvancedInstaller)\Advanced Installer $($config.RequiredTools.AdvancedInstaller)"),
                (Get-ChildItem -Path $tempExtractPath -Directory | Select-Object -First 1 -ExpandProperty FullName)
            )
            
            $advInstTempPath = $null
            foreach ($path in $possiblePaths) {
                if (Test-Path $path) {
                    $advInstTempPath = $path
                    break
                }
            }
            
            if (-not $advInstTempPath) {
                throw "Could not find Advanced Installer directory in extracted files"
            }
            
            # Check if the path contains a duplicate folder structure
            $binPath = Join-Path $advInstTempPath "bin"
            if (-not (Test-Path $binPath)) {
                # If no bin directory found, check one level deeper
                $subDirs = Get-ChildItem -Path $advInstTempPath -Directory
                if ($subDirs.Count -eq 1 -and (Test-Path (Join-Path $subDirs[0].FullName "bin"))) {
                    $advInstTempPath = $subDirs[0].FullName
                }
            }
            
            # Use robocopy to move files with progress
            Write-Host "Moving files to Program Files..."
            $totalSize = (Get-ChildItem $advInstTempPath -Recurse | Measure-Object -Property Length -Sum).Sum
            $progress = 0
            
            # Monitor robocopy progress using log file
            $logFile = Join-Path $env:TEMP "robocopy_progress.log"
            $robocopyJob = Start-Job -ScriptBlock {
                param($src, $dst, $log)
                robocopy $src $dst /E /NFL /NDL /NJH /NJS /NC /NS /NP /LOG:$log
            } -ArgumentList $advInstTempPath, $advInstPath, $logFile
            
            while ($robocopyJob.State -eq 'Running') {
                if (Test-Path $logFile) {
                    $copiedSize = (Get-ChildItem $advInstPath -Recurse | Measure-Object -Property Length -Sum).Sum
                    if ($copiedSize -gt 0) {
                        $progress = [math]::Min(($copiedSize / $totalSize) * 100, 99)
                        Write-Progress -Activity "Moving Files" -Status "$([math]::Round($progress))% Complete" -PercentComplete $progress
                    }
                }
                Start-Sleep -Milliseconds 100
            }
            
            Write-Progress -Activity "Moving Files" -Completed
            Remove-Item $logFile -ErrorAction SilentlyContinue
            
            # Clean up temp directory
            Remove-Item -Recurse -Force $tempExtractPath -ErrorAction SilentlyContinue
            
            # Verify installation
            if (-not (Test-Path (Join-Path $advInstPath "bin\x86\AdvancedInstaller.com"))) {
                throw "Failed to verify Advanced Installer installation"
            }
            
            Write-Host "Advanced Installer installed successfully." -ForegroundColor Green
            
        } catch {
            Write-Warning "Failed to install Advanced Installer: $_"
            throw
        }
    } else {
        Write-Host "Advanced Installer $($config.RequiredTools.AdvancedInstaller) is already installed." -ForegroundColor Green
    }
}

function Setup-FFmpeg {
    Write-Step "Setting up FFmpeg"
    $zipFile = Join-Path $projectRoot "ffmpeg\ffmpeg.zip"
    $extractPath = "C:\ffmpeg"
    
    if (-not (Test-Path $zipFile)) { throw "FFmpeg zip file not found at: $zipFile" }
    if (Test-Path $extractPath) { Remove-Item -Recurse -Force $extractPath }
    
    Copy-Item $zipFile "C:\"
    New-Item -ItemType Directory -Force $extractPath
    
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    [System.IO.Compression.ZipFile]::ExtractToDirectory("C:\ffmpeg.zip", $extractPath)
    
    [Environment]::SetEnvironmentVariable("LIB", "C:\ffmpeg\lib", "Machine")
    [Environment]::SetEnvironmentVariable("INCLUDE", "C:\ffmpeg\include", "Machine")
    [Environment]::SetEnvironmentVariable("PYAV_LIBRARY", "C:\ffmpeg\bin", "Machine")
    
    $path = [Environment]::GetEnvironmentVariable("Path", "Machine")
    if (-not $path.Contains("C:\ffmpeg\bin")) {
        [Environment]::SetEnvironmentVariable("Path", "$path;C:\ffmpeg\bin", "Machine")
    }
}

function Clean-Environment {
    Write-Step "Cleaning build environment..."
    
    # Deactivate virtual environment if active
    if (Test-Path "venv") {
        Write-Host "Deactivating virtual environment..."
        if (Test-Path "venv\Scripts\deactivate.bat") {
            & "venv\Scripts\deactivate.bat"
        }
    }
    
    # Kill any Python processes that might be locking files
    Get-Process | Where-Object {$_.Name -like "python*" -or $_.Name -like "pip*"} | ForEach-Object {
        try {
            $_ | Stop-Process -Force -ErrorAction SilentlyContinue
            Write-Host "Stopped process: $($_.Name)"
        } catch {
            Write-Warning "Could not stop process $($_.Name): $_"
        }
    }
    
    # Wait a moment for processes to fully stop
    Start-Sleep -Seconds 2
    
    # Function to remove directory with retries
    function Remove-DirectoryWithRetry {
        param (
            [string]$path,
            [int]$retries = 3,
            [int]$waitSeconds = 2
        )
        
        for ($i = 1; $i -le $retries; $i++) {
            try {
                if (Test-Path $path) {
                    # Try using cmd.exe rd command first
                    cmd /c "rd /s /q `"$path`"" 2>$null
                    if (Test-Path $path) {
                        # If rd fails, try using robocopy to empty directory first
                        $tempEmpty = New-Item -ItemType Directory -Path "$env:TEMP\empty" -Force
                        robocopy "$env:TEMP\empty" "$path" /MIR /NFL /NDL /NJH /NJS /NC /NS /NP
                        Remove-Item -Path "$env:TEMP\empty" -Force
                        Remove-Item -Recurse -Force -Path $path -ErrorAction Stop
                    }
                }
                return $true
            }
            catch {
                Write-Warning "Attempt $i of $retries to remove $path failed: $_"
                if ($i -lt $retries) {
                    Write-Host "Waiting $waitSeconds seconds before retry..."
                    Start-Sleep -Seconds $waitSeconds
                }
            }
        }
        return $false
    }

    # Directories to clean
    $dirsToRemove = @("build", "dist", "venv", "env")
    
    foreach ($dir in $dirsToRemove) {
        if (Test-Path $dir) {
            Write-Host "Removing $dir..."
            if (-not (Remove-DirectoryWithRetry -path $dir)) {
                Write-Warning "Failed to remove $dir after multiple attempts. Please close any applications using these files and try again."
            }
        }
    }

    # Clean Python cache files
    Write-Host "Cleaning Python cache files..."
    try {
        if (Test-Path "resources_rc.py") { 
            Remove-Item -Force "resources_rc.py" -ErrorAction SilentlyContinue 
        }
        Get-ChildItem -Recurse -Filter "*.pyc" | Remove-Item -Force -ErrorAction SilentlyContinue
        Get-ChildItem -Recurse -Filter "__pycache__" | ForEach-Object {
            Remove-DirectoryWithRetry -path $_.FullName -retries 2 -waitSeconds 1
        }
    }
    catch {
        Write-Warning "Some cache files could not be removed: $_"
    }
}

function Read-AndExecute-Translations {
    Write-Step "Reading translation commands from generate_resources_rc.sh..."
    
    $translationScript = Join-Path $projectRoot "generate_resources_rc.sh"
    if (-not (Test-Path $translationScript)) {
        throw "Translation script not found at: $translationScript"
    }

    # Read the content of the shell script
    $scriptContent = Get-Content $translationScript -Raw

    # Extract the pyside6-lupdate commands
    $lupdateCommands = $scriptContent -split "`n" | Where-Object { $_ -match "pyside6-lupdate" }
    
    # Extract the pyside6-lrelease commands
    $lreleaseCommands = $scriptContent -split "`n" | Where-Object { $_ -match "pyside6-lrelease" }

    # Execute each lupdate command
    foreach ($cmd in $lupdateCommands) {
        Write-Host "Executing: $cmd" -ForegroundColor Yellow
        $cmd = $cmd.Trim()
        if ($cmd) {
            Invoke-Expression $cmd
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Failed to execute: $cmd"
            }
        }
    }

    # Execute each lrelease command
    foreach ($cmd in $lreleaseCommands) {
        Write-Host "Executing: $cmd" -ForegroundColor Yellow
        $cmd = $cmd.Trim()
        if ($cmd) {
            Invoke-Expression $cmd
            if ($LASTEXITCODE -ne 0) {
                Write-Warning "Failed to execute: $cmd"
            }
        }
    }

    # Execute the final pyside6-rcc command
    $rccCommand = $scriptContent -split "`n" | Where-Object { $_ -match "pyside6-rcc" } | Select-Object -First 1
    if ($rccCommand) {
        Write-Host "Executing: $rccCommand" -ForegroundColor Yellow
        Invoke-Expression $rccCommand.Trim()
        if ($LASTEXITCODE -ne 0) {
            Write-Warning "Failed to execute: $rccCommand"
        }
    }
}

function Get-TranslationFiles {
    param (
        [string]$scriptContent
    )
    
    # Extract the first pyside6-lupdate command to get the file list
    $lupdateCommand = $scriptContent -split "`n" | Where-Object { $_ -match "pyside6-lupdate" } | Select-Object -First 1
    
    if (-not $lupdateCommand) {
        throw "No pyside6-lupdate command found in the script"
    }
    
    # Extract the file list from the command
    # The format is: pyside6-lupdate file1 file2 file3 -ts output.ts
    $fileList = $lupdateCommand -replace "pyside6-lupdate\s+", "" -replace "\s+-ts\s+.*$", ""
    
    # Split the file list into an array
    return $fileList -split "\s+"
}

function Generate-Resources {
    Write-Step "Generating translations and resources..."
    
    # First try to use the shell script if it exists
    $translationScript = Join-Path $projectRoot "generate_resources_rc.sh"
    if (Test-Path $translationScript) {
        Write-Host "Found generate_resources_rc.sh, using it for translations..." -ForegroundColor Green
        Read-AndExecute-Translations
        return
    }

    # Fallback to reading files from the script if it exists but couldn't be executed
    if (Test-Path $translationScript) {
        Write-Host "Using file list from generate_resources_rc.sh..." -ForegroundColor Yellow
        $scriptContent = Get-Content $translationScript -Raw
        $translationFiles = Get-TranslationFiles -scriptContent $scriptContent
        
        $languages = @("en_US", "vi_VN", "ru_RU")
        foreach ($lang in $languages) {
            & pyside6-lupdate $translationFiles -ts "src/languages/qt_$lang.ts"
            & pyside6-lrelease "src/languages/qt_$lang.ts"
        }
        
        & pyside6-rcc resource.qrc -o resources_rc.py
        return
    }

    throw "Neither generate_resources_rc.sh nor translation files list found"
}

function Build-PyInstaller {
    Write-Step "Building with PyInstaller..."
    & pyinstaller --noconfirm "$PSScriptRoot\pyinstaller_build.spec"
}

function Get-CompanyInfo {
    $company = $versionConfig.company
    return @"
$($company.legal_name)
$($company.address)
Phone: $($company.phone)
Fax: $($company.fax)
Email: $($company.email)
Website: $($company.website)
"@
}

function Create-Installer {
    param(
        [string]$AipFile = $(if ($Nuitka) { "..\iVMS_nuitka.aip" } else { "..\iVMS.aip" })
    )
    
    Write-Step "Creating installer..."
    
    # Ensure Advanced Installer is installed
    Install-AdvancedInstaller
    
    $advInstPath = "C:\Program Files (x86)\Caphyon\Advanced Installer $($config.RequiredTools.AdvancedInstaller)\bin\x86\AdvancedInstaller.com"
    if (-not (Test-Path $advInstPath)) {
        throw "Advanced Installer executable not found at: $advInstPath"
    }
    
    $commandFile = [System.IO.Path]::GetTempFileName()
    
    # Convert paths to proper Windows format and ensure they exist
    if ($Nuitka) {
        $distPath = Join-Path $projectRoot "VMS.dist"
        $exePath = Join-Path $distPath "VMS.exe"
        if (-not (Test-Path $exePath)) {
            throw "Nuitka build output not found at: $exePath"
        }
    } else {
        $distPath = Join-Path $projectRoot "dist\iVMS"
        $exePath = Join-Path $distPath "iVMS.exe"
        $internalPath = Join-Path $distPath "_internal"
        if (-not (Test-Path $exePath)) {
            throw "PyInstaller build output not found at: $exePath"
        }
    }
    
    $iconPath = Join-Path $projectRoot "icon.ico"
    
    # Verify icon exists
    if (-not (Test-Path $iconPath)) {
        Write-Warning "Icon file not found at: $iconPath"
        $iconPath = $exePath  # Fallback to exe icon if ico file not found
    }
    
    # Create Advanced Installer commands with proper paths
    $commands = @"
;aic
SetVersion $($config.Version)
SetPackageType x64
SetProperty "ProductName=$($versionConfig.product_name)"
SetProperty "Manufacturer=$($versionConfig.company.legal_name)"
SetProperty "Author=$($versionConfig.company.legal_name)"
SetProperty "Copyright=$($versionConfig.copyright)"
SetOutputLocation -buildname "DefaultBuild" -path "$([System.IO.Path]::GetFullPath($projectRoot))\dist"

"@

    if ($Nuitka) {
        $commands += @"
DelFolder APPDIR\\VMS.dist
AddFolder APPDIR "$([System.IO.Path]::GetFullPath($distPath))"
SetIcon -icon "$([System.IO.Path]::GetFullPath($iconPath))"

; Remove existing shortcuts first
DelShortcut -name "$($versionConfig.product_name)" -dir DesktopFolder
DelShortcut -name "$($versionConfig.product_name)" -dir StartMenuFolder

; Create shortcuts
NewShortcut -name "$($versionConfig.product_name)" -dir DesktopFolder -target "APPDIR\\VMS.dist\\VMS.exe" -wkdir "APPDIR" -desc "$($versionConfig.product_name) $($config.Version)" -icon "$([System.IO.Path]::GetFullPath($iconPath))"
NewShortcut -name "$($versionConfig.product_name)" -dir StartMenuFolder -target "APPDIR\\VMS.dist\\VMS.exe" -wkdir "APPDIR" -desc "$($versionConfig.product_name) $($config.Version)" -icon "$([System.IO.Path]::GetFullPath($iconPath))"

"@
    } else {
        $commands += @"
DelFolder APPDIR\\_internal
AddFolder APPDIR "$([System.IO.Path]::GetFullPath($internalPath))"
SetIcon -icon "$([System.IO.Path]::GetFullPath($iconPath))"

; Remove existing shortcuts first
DelShortcut -name "$($versionConfig.product_name)" -dir DesktopFolder
DelShortcut -name "$($versionConfig.product_name)" -dir StartMenuFolder

; Create shortcuts
NewShortcut -name "$($versionConfig.product_name)" -dir DesktopFolder -target "APPDIR\\iVMS.exe" -wkdir "APPDIR" -desc "$($versionConfig.product_name) $($config.Version)" -icon "$([System.IO.Path]::GetFullPath($iconPath))"
NewShortcut -name "$($versionConfig.product_name)" -dir StartMenuFolder -target "APPDIR\\iVMS.exe" -wkdir "APPDIR" -desc "$($versionConfig.product_name) $($config.Version)" -icon "$([System.IO.Path]::GetFullPath($iconPath))"

"@
    }

    $commands += @"
Save
Build -buildslist "DefaultBuild"
"@

    # Write commands to file with UTF-8 encoding and BOM
    [System.IO.File]::WriteAllText($commandFile, $commands, [System.Text.UTF8Encoding]::new($true))
    
    & $advInstPath /execute $AipFile $commandFile
    Remove-Item $commandFile -Force
}

function Build-Project {
    Write-Header "Building Project"
    
    if (-not $DeployOnly) {
        # Create virtual environment and install requirements
        Write-Step "Setting up Python environment"
        if (Test-Path "venv") { Remove-Item -Recurse -Force "venv" }
        
        # Get Python 3.12 path
        $python312Path = Get-Python312Path
        if (-not $python312Path) {
            throw "Python 3.12 is required but was not found"
        }
        
        # Create virtual environment using the verified Python 3.12
        & $python312Path -m venv venv
        & "venv\Scripts\activate"
        
        # Upgrade pip and install requirements
        python -m pip install --upgrade pip
        pip install -r "$PSScriptRoot\requirements.txt"
        
        # Install build tools based on selection
        if ($Nuitka) {
            Write-Step "Installing Nuitka..."
            pip install nuitka==2.6.9
        } else {
            Write-Step "Installing PyInstaller..."
            pip install pyinstaller
        }
        
        # Generate translations and resources
        Write-Step "Generating translations and resources"
        Generate-Resources
        
        # Build with either Nuitka or PyInstaller
        if ($Nuitka) {
            Write-Step "Building with Nuitka"
            bash "$PSScriptRoot\nuitka_build.sh"
        } else {
            Write-Step "Building with PyInstaller"
            Build-PyInstaller
        }
    }
    
    # Create installer only if not PyInstallerOnly
    if ((-not $PyInstallerOnly) -and (Confirm-Step "create installer")) {
        Write-Step "Creating installer"
        Create-Installer -AipFile $AipFile
    }
}

function Initialize-VersionFile {
    if (-not (Test-Path $versionFile)) {
        throw "Version file not found at: $versionFile. Please ensure version.json exists in the project root directory."
    }
    
    try {
        $versionConfig = Get-Content $versionFile | ConvertFrom-Json
        Write-Host "Successfully loaded version configuration from: $versionFile" -ForegroundColor Green
        return $versionConfig
    } catch {
        throw "Failed to parse version.json: $_"
    }
}

# Main execution
try {
    Write-Header "iVMS Build System"
    Push-Location $projectRoot
    
    # Initialize version file
    $versionConfig = Initialize-VersionFile
    
    if ($Clean -or $All) { Clean-Environment }
    
    if ($InstallDeps -or $All) {
        Write-Header "Installing Dependencies"
        if (Confirm-Step "install required tools") {
            Install-Chocolatey
            Install-Python
            Install-AdvancedInstaller
        }
        if (Confirm-Step "setup FFmpeg") { Setup-FFmpeg }
    }
    
    if ($BuildOnly -or $PyInstallerOnly -or $DeployOnly -or $All) {
        Build-Project
    }
    
    Write-Host "`nBuild process completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "`nError: $_" -ForegroundColor Red
    exit 1
}
finally {
    Pop-Location
} 
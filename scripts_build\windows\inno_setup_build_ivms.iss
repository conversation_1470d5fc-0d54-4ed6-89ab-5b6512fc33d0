; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "iVMS"
#define MyAppVersion "1.1.8"
#define MyAppPublisher "GPS Viet Nam"
#define MyAppURL "GPS Viet Nam"
#define MyAppExeName "iVMS.exe"
#define MyAppAssocName MyAppName + " File"
#define MyAppAssocExt ".myp"
#define MyAppAssocKey StringChange(MyAppAssocName, " ", "") + MyAppAssocExt

[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{77116EFA-651D-4530-A3C0-9AAF90EEDEB0}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
;AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#MyAppName}
; "ArchitecturesAllowed=x64compatible" specifies that Setup cannot run
; on anything but x64 and Windows 11 on Arm.
ArchitecturesAllowed=x64compatible
; "ArchitecturesInstallIn64BitMode=x64compatible" requests that the
; install be done in "64-bit mode" on x64 or Windows 11 on Arm,
; meaning it should use the native 64-bit Program Files directory and
; the 64-bit view of the registry.
ArchitecturesInstallIn64BitMode=x64compatible
ChangesAssociations=yes
DisableProgramGroupPage=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir=E:\WORK\GPS\iVMS
OutputBaseFilename=iVMS_Setup
SetupIconFile=E:\WORK\GPS\iVMS\icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "russian"; MessagesFile: "compiler:Languages\Russian.isl"
Name: "vietnamese"; MessagesFile: "compiler:Languages\Vietnamese.isl"


[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "E:\WORK\GPS\iVMS\dist\iVMS\{#MyAppExeName}"; DestDir: "{app}"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\*"; DestDir: "{app}\_internal"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_asyncio.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_bz2.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_ctypes.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_decimal.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_elementtree.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_hashlib.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_lzma.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_multiprocessing.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_overlapped.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_queue.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_socket.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_sqlite3.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_ssl.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_uuid.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\_wmi.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-console-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-datetime-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-debug-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-errorhandling-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-fibers-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-file-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-file-l1-2-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-file-l2-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-handle-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-heap-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-interlocked-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-libraryloader-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-localization-l1-2-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-memory-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-namedpipe-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-processenvironment-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-processthreads-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-processthreads-l1-1-1.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-profile-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-rtlsupport-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-string-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-synch-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-synch-l1-2-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-sysinfo-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-timezone-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-core-util-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-conio-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-convert-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-environment-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-filesystem-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-heap-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-locale-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-math-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-multibyte-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-private-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-process-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-runtime-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-stdio-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-string-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-time-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\api-ms-win-crt-utility-l1-1-0.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\avcodec-60.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\avdevice-60.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\avfilter-9.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\avformat-60.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\avutil-58.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\base_library.zip"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\libcrypto-3.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\libcrypto-3-x64.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\libffi-8.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\libssl-3.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\libssl-3-x64.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\postproc-57.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\pyexpat.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\python3.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\python312.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\select.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\sqlite3.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\swresample-4.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\swscale-7.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\ucrtbase.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\unicodedata.pyd"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\VCRUNTIME140.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
Source: "E:\WORK\GPS\iVMS\dist\iVMS\_internal\VCRUNTIME140_1.dll"; DestDir: "{app}\_internal"; Flags: ignoreversion
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Registry]
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocExt}\OpenWithProgids"; ValueType: string; ValueName: "{#MyAppAssocKey}"; ValueData: ""; Flags: uninsdeletevalue
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}"; ValueType: string; ValueName: ""; ValueData: "{#MyAppAssocName}"; Flags: uninsdeletekey
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"
Root: HKA; Subkey: "Software\Classes\{#MyAppAssocKey}\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""
Root: HKA; Subkey: "Software\Classes\Applications\{#MyAppExeName}\SupportedTypes"; ValueType: string; ValueName: ".myp"; ValueData: ""

[Icons]
Name: "{autoprograms}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent


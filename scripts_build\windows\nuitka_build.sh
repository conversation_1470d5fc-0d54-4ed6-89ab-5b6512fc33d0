#!/bin/bash

rm -rf VMS.dist
rm -rf VMS.build

# Run Nuitka to compile the application
python -m nuitka --standalone \
        --plugin-enable=pyside6 \
        --include-qt-plugins=sensible,qml,geoservices \
        VMS.py

# Create necessary directories if they don't exist
mkdir -p VMS.dist/pyjoystick/sdl2_win32
mkdir -p VMS.dist/pyjoystick/sdl2_win64
mkdir -p VMS.dist/lib_3rdparty/vlc/win/plugins

# Copy files maintaining directory structure
cp -r pyjoystick/sdl2_win32/* VMS.dist/pyjoystick/sdl2_win32/
cp -r pyjoystick/sdl2_win64/* VMS.dist/pyjoystick/sdl2_win64/
cp -r lib_3rdparty/vlc/win/* VMS.dist/lib_3rdparty/vlc/win/
cp -r lib_3rdparty/vlc/win/plugins/* VMS.dist/lib_3rdparty/vlc/win/plugins/

# remove reduce size of dist
#qt6webenginecore.dll "E:\WORK\iVMS\VMS.dist\qt6webenginecore.dll"
rm VMS.dist/qt6webenginecore.dll

echo "Build completed successfully."
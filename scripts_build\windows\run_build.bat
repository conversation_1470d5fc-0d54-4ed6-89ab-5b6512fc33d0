@echo off
echo Starting iVMS build process with administrator privileges...

:: Check for admin rights and elevate if needed
>nul 2>&1 "%SYSTEMROOT%\system32\cacls.exe" "%SYSTEMROOT%\system32\config\system"
if '%errorlevel%' NEQ '0' (
    echo Requesting administrative privileges...
    powershell -Command "Start-Process powershell -ArgumentList '-NoProfile -ExecutionPolicy Bypass -Command cd \"%~dp0\"; .\build.bat' -Verb RunAs"
    exit /b
)

:: If we get here, we already have admin rights
cd /d "%~dp0"
call build.bat
@echo off

set ZIP_FILE=C:\ffmpeg.zip
set EXTRACT_PATH=C:\ffmpeg

rem Check if C:\ffmpeg directory exists, and remove it if it does
if exist "%EXTRACT_PATH%" rmdir /S /Q "%EXTRACT_PATH%"

rem Create the extraction directory
mkdir "%EXTRACT_PATH%"

rem Extract ffmpeg.zip to C:\ffmpeg
powershell -command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('%ZIP_FILE%', '%EXTRACT_PATH%')}"

rem Setup system environment variables
setx LIB "C:\ffmpeg\lib" /M
setx INCLUDE "C:\ffmpeg\include" /M
setx PYAV_LIBRARY "C:\ffmpeg\bin" /M

echo System environment variables set successfully.

rem Display a message asking the user to restart the command prompt or any open applications.
echo Please restart your command prompt or any open applications to apply the changes.

import traceback
import numpy as np
from PySide6 import QtCore, QtGui
import urllib.request
import cv2

EXTEND_BOX = 50


def download_image(url, bounding_box):
    try:
        response = urllib.request.urlopen(url)
        if response.getcode() == 200:
            image = np.asarray(bytearray(response.read()), dtype="uint8")
            image = cv2.imdecode(image, cv2.IMREAD_COLOR)

            # NguyenNH: crop theo bounding box và extend thêm 1 khoảng EXTEND_BOX
            image = image[bounding_box[1] - EXTEND_BOX:bounding_box[3] + EXTEND_BOX,
                    bounding_box[0] - EXTEND_BOX:bounding_box[2] + EXTEND_BOX]

            image = np.ascontiguousarray(image)
            height, width, channel = image.shape
            bytes_per_line = 3 * width

            image = QtGui.QImage(image.data, width, height, bytes_per_line,
                                 QtGui.QImage.Format_RGB888).rgbSwapped()
            pix = QtGui.QPixmap.fromImage(image)
            return pix
        else:
            return None
    except Exception as e:
        traceback.print_exc()
        return None


class ImageDownloadThread(QtCore.QThread):
    on_image_downloaded = QtCore.Signal(QtGui.QPixmap)

    def __init__(self, parent=None, callback=None, args=()):
        super(ImageDownloadThread, self).__init__(parent)
        self.daemon = True
        self.callback = callback
        self._target = download_image
        self._args = args
        self.on_image_downloaded.connect(self.callback)

    def run(self):
        try:
            if self._target:
                response = self._target(*self._args)
                self.on_image_downloaded.emit(response)
        except Exception as e:
            traceback.print_exc()
            self.on_image_downloaded.emit(None)
        finally:
            # Avoid a refcycle if the thread is running a function with
            # an argument that has a member that points to the thread.
            del self._target, self._args

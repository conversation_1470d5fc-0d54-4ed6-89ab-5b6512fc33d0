# public class ErrorType {
#     public static final String UNKNOWN = "UNKNOWN";
#     public static final String INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR";
#     public static final String CAMERA_NOT_FOUND = "CAMERA_NOT_FOUND";
#     public static final String CAMERA_INVALID = "CAMERA_INVALID";
#     public static final String CAMERA_NAME_NULL = "CAMERA_NAME_NULL";
#     public static final String CAMERA_NAME_EXISTS = "CAMERA_NAME_EXISTS";
#     public static final String CAMERA_URL_NULL = "CAMERA_URL_NULL";
#     public static final String CAMERA_URL_EXISTS = "CAMERA_URL_EXISTS";
#     public static final String CAMERA_URL_MAINSTREAM_INVALID = "CAMERA_URL_MAINSTREAM_INVALID";
#     public static final String CAMERA_URL_SUBSTREAM_INVALID = "CAMERA_URL_SUBSTREAM_INVALID";
#     public static final String CAMERA_SUPPORTED_MAIN_RESOLUTION_INVALID = "CAMERA_SUPPORTED_MAIN_RESOLUTION_INVALID";
#     public static final String CAMERA_SUPPORTED_MAIN_FPS_INVALID = "CAMERA_SUPPORTED_MAIN_FPS_INVALID";
#     public static final String CAMERA_SUPPORTED_SUB_RESOLUTION_INVALID = "CAMERA_SUPPORTED_SUB_RESOLUTION_INVALID";
#     public static final String CAMERA_SUPPORTED_SUB_FPS_INVALID = "CAMERA_SUPPORTED_SUB_FPS_INVALID";
#     public static final String CAMERA_STATUS_NULL = "CAMERA_STATUS_NULL";
#     public static final String CAMERA_STATE_NULL = "CAMERA_STATE_NULL";
#     public static final String CAMERA_GROUP_NOT_FOUND = "CAMERA_GROUP_NOT_FOUND";
#     public static final String CAMERA_GROUP_INVALID = "CAMERA_GROUP_INVALID";
#     public static final String CAMERA_GROUP_INVALID_PARENT_CHILD = "CAMERA_GROUP_INVALID_PARENT_CHILD";
#     public static final String CAMERA_GROUP_NAME_NULL = "CAMERA_GROUP_NAME_NULL";
#     public static final String CAMERA_GROUP_NAME_EXISTS = "CAMERA_GROUP_NAME_EXISTS";
#     public static final String CAMERA_GROUP_PARENT_ID_NOT_EXISTS = "CAMERA_GROUP_PARENT_ID_NOT_EXISTS";
#     public static final String CAMERA_GROUP_CHILD_ID_NOT_EXISTS = "CAMERA_GROUP_CHILD_ID_NOT_EXISTS";
#     public static final String EVENT_NOT_FOUND = "EVENT_NOT_FOUND";
#     public static final String EVENT_INVALID = "EVENT_INVALID";
#     public static final String EVENT_PROFILE_ID_NULL = "EVENT_PROFILE_ID_NULL";
#     public static final String EVENT_CREATE_AT_NULL = "EVENT_CREATE_AT_NULL";
#     public static final String EVENT_IMAGE_URL_NULL = "EVENT_IMAGE_URL_NULL";
#     public static final String EVENT_VIDEO_URL_NULL = "EVENT_VIDEO_URL_NULL";
#     public static final String META_DATA_NULL = "META_DATA_NULL";
#     public static final String META_DATA_INVALID = "META_DATA_INVALID";
#     public static final String PROFILE_NOT_FOUND = "PROFILE_NOT_FOUND";
#     public static final String PROFILE_NAME_NULL = "PROFILE_NAME_NULL";
#     public static final String PROFILE_INVALID = "PROFILE_INVALID";
#     public static final String PROFILE_NAME_EXISTS = "PROFILE_NAME_EXISTS";
#     public static final String PROFILE_UUID_EXISTS = "PROFILE_UUID_EXISTS";
#     public static final String AI_FLOW_NOT_FOUND = "AI_FLOW_NOT_FOUND";
#     public static final String AI_FLOW_NAME_NULL = "AI_FLOW_NAME_NULL";
#     public static final String AI_FLOW_TYPE_NULL = "AI_FLOW_TYPE_NULL";
#     public static final String AI_FLOW_APPLY_NULL = "AI_FLOW_APPLY_NULL";
#     public static final String AI_FLOW_INVALID = "AI_FLOW_INVALID";
#     public static final String FORMAT_DATE_INVALID = "FORMAT_DATE_INVALID";
#     public static final String USER_USERNAME_NULL = "USER_USERNAME_NULL";
#     public static final String USER_USERNAME_EXISTS = "USER_USERNAME_EXISTS";
#     public static final String USER_PASSWORD_NULL = "USER_PASSWORD_NULL";
#     public static final String USER_INVALID = "USER_INVALID";
#     public static final String INVALID_TOKEN = "INVALID_TOKEN";
#     public static final String USER_NOT_FOUND = "USER_NOT_FOUND";
#     public static final String INVALID_PASSWORD = "INVALID_PASSWORD";
#     public static final String API_KEY_NOT_FOUND = "API_KEY_NOT_FOUND";
#     public static final String CAMERA_ID_NULL = "CAMERA_ID_NULL";
#     public static final String CAMERA_GROUP_ID_NULL = "CAMERA_GROUP_ID_NULL";
# }

import enum 

class ErrorType(enum.Enum):
    UNKNOWN = "UNKNOWN"
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    CAMERA_NOT_FOUND = "CAMERA_NOT_FOUND"
    CAMERA_INVALID = "CAMERA_INVALID"
    CAMERA_NAME_NULL = "CAMERA_NAME_NULL"
    CAMERA_NAME_EXISTS = "CAMERA_NAME_EXISTS"
    CAMERA_URL_NULL = "CAMERA_URL_NULL"
    CAMERA_URL_EXISTS = "CAMERA_URL_EXISTS"
    CAMERA_URL_MAINSTREAM_INVALID = "CAMERA_URL_MAINSTREAM_INVALID"
    CAMERA_URL_SUBSTREAM_INVALID = "CAMERA_URL_SUBSTREAM_INVALID"
    CAMERA_SUPPORTED_MAIN_RESOLUTION_INVALID = "CAMERA_SUPPORTED_MAIN_RESOLUTION_INVALID"
    CAMERA_SUPPORTED_MAIN_FPS_INVALID = "CAMERA_SUPPORTED_MAIN_FPS_INVALID"
    CAMERA_SUPPORTED_SUB_RESOLUTION_INVALID = "CAMERA_SUPPORTED_SUB_RESOLUTION_INVALID"
    CAMERA_SUPPORTED_SUB_FPS_INVALID = "CAMERA_SUPPORTED_SUB_FPS_INVALID"
    CAMERA_STATUS_NULL = "CAMERA_STATUS_NULL"
    CAMERA_STATE_NULL = "CAMERA_STATE_NULL"
    CAMERA_GROUP_NOT_FOUND = "CAMERA_GROUP_NOT_FOUND"
    CAMERA_GROUP_INVALID = "CAMERA_GROUP_INVALID"
    CAMERA_GROUP_INVALID_PARENT_CHILD = "CAMERA_GROUP_INVALID_PARENT_CHILD"
    CAMERA_GROUP_NAME_NULL = "CAMERA_GROUP_NAME_NULL"
    CAMERA_GROUP_NAME_EXISTS = "CAMERA_GROUP_NAME_EXISTS"
    CAMERA_GROUP_PARENT_ID_NOT_EXISTS = "CAMERA_GROUP_PARENT_ID_NOT_EXISTS"
    CAMERA_GROUP_CHILD_ID_NOT_EXISTS = "CAMERA_GROUP_CHILD_ID_NOT_EXISTS"
    EVENT_NOT_FOUND = "EVENT_NOT_FOUND"
    EVENT_INVALID = "EVENT_INVALID"
    EVENT_PROFILE_ID_NULL = "EVENT_PROFILE_ID_NULL"
    EVENT_CREATE_AT_NULL = "EVENT_CREATE_AT_NULL"
    EVENT_IMAGE_URL_NULL = "EVENT_IMAGE_URL_NULL"
    EVENT_VIDEO_URL_NULL = "EVENT_VIDEO_URL_NULL"
    META_DATA_NULL = "META_DATA_NULL"
    META_DATA_INVALID = "META_DATA_INVALID"
    PROFILE_NOT_FOUND = "PROFILE_NOT_FOUND"
    PROFILE_NAME_NULL = "PROFILE_NAME_NULL"
    PROFILE_INVALID = "PROFILE_INVALID"
    PROFILE_NAME_EXISTS = "PROFILE_NAME_EXISTS"
    PROFILE_UUID_EXISTS = "PROFILE_UUID_EXISTS"
    AI_FLOW_NOT_FOUND = "AI_FLOW_NOT_FOUND"
    AI_FLOW_NAME_NULL = "AI_FLOW_NAME_NULL"
    AI_FLOW_TYPE_NULL = "AI_FLOW_TYPE_NULL"
    AI_FLOW_APPLY_NULL = "AI_FLOW_APPLY_NULL"
    AI_FLOW_INVALID = "AI_FLOW_INVALID"
    FORMAT_DATE_INVALID = "FORMAT_DATE_INVALID"
    USER_USERNAME_NULL = "USER_USERNAME_NULL"
    USER_USERNAME_EXISTS = "USER_USERNAME_EXISTS"
    USER_PASSWORD_NULL = "USER_PASSWORD_NULL"
    USER_INVALID = "USER_INVALID"

    # input array string
    # output string error message
    @staticmethod
    def get_error_message(array_error):
        error_message = ""
        if len(array_error) == 0:
            return None
        else:
            # show all error message
            for error in array_error:
                # remove "_" and replace with " " and capitalize only first letter
                error = error.replace("_", " ").capitalize()
                # if only one item, no need new line
                if len(array_error) == 1:
                    error_message += error
                else:
                    error_message += "\n" + error
            return error_message

    
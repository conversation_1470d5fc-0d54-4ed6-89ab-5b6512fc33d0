from PySide6.QtCore import QRunnable

class GenericRunnable(QRunnable):
    def __init__(self, target, callback=None, *args, **kwargs):
        """
        A generic worker that can execute any API function and call the given callback function.

        :param api_function: The API function to be executed.
        :param callback: A function to call after completion.
        :param args: Positional arguments for the API function.
        :param kwargs: Keyword arguments for the API function.
        """
        super().__init__()
        self.target = target
        self.callback = callback
        self.args = args
        self.kwargs = kwargs

    def run(self):
        # Call the API function with its arguments
        result = self.target(*self.args, **self.kwargs)
        # Call the callback function with the result, if a callback is provided
        if self.callback:
            self.callback(result)

<svg width="61" height="75" viewBox="0 0 61 75" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1117_14619)">
<g filter="url(#filter1_d_1117_14619)">
<path d="M50.2727 26.3333C50.2727 41.8888 30.2727 55.2221 30.2727 55.2221C30.2727 55.2221 10.2727 41.8888 10.2727 26.3333C10.2727 21.0289 12.3799 15.9418 16.1306 12.1911C19.8813 8.44039 24.9684 6.33325 30.2727 6.33325C35.5771 6.33325 40.6641 8.44039 44.4149 12.1911C48.1656 15.9418 50.2727 21.0289 50.2727 26.3333Z" fill="#FD7B38"/>
</g>
<path d="M30.1338 40.9631C38.2903 40.9631 44.9023 34.351 44.9023 26.1945C44.9023 18.0381 38.2903 11.426 30.1338 11.426C21.9774 11.426 15.3653 18.0381 15.3653 26.1945C15.3653 34.351 21.9774 40.9631 30.1338 40.9631Z" fill="white" stroke="#DFE0E0" stroke-width="0.986111" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<path d="M39.5746 22.5641C33.6346 22.5549 27.6747 22.558 21.695 22.5733C21.2214 22.5733 20.8043 22.5137 20.4438 22.3945" stroke="#92A5AF" stroke-width="0.55"/>
<path d="M39.4096 23.3663L20.5675 23.3159" stroke="#92A5AF" stroke-width="0.55"/>
<path d="M37.8146 26.6387L22.1625 26.6478" stroke="#8C7CBE" stroke-width="0.55"/>
<path d="M33.7858 33.9308C33.7095 33.4113 33.6728 32.5008 33.6758 31.1991C33.6804 29.1458 31.8425 27.4041 29.7433 27.6058C27.1446 27.8579 26.2829 29.7599 26.3379 32.1341C26.3532 32.7391 26.3486 33.3579 26.3242 33.9904" stroke="#553099" stroke-width="0.55"/>
<path d="M31.8104 31.222C31.8104 30.7419 31.6197 30.2814 31.2801 29.9419C30.9406 29.6024 30.4801 29.4116 30 29.4116C29.5198 29.4116 29.0594 29.6024 28.7198 29.9419C28.3803 30.2814 28.1896 30.7419 28.1896 31.222C28.1896 31.7022 28.3803 32.1627 28.7198 32.5022C29.0594 32.8417 29.5198 33.0325 30 33.0325C30.4801 33.0325 30.9406 32.8417 31.2801 32.5022C31.6197 32.1627 31.8104 31.7022 31.8104 31.222Z" stroke="#7B57BD" stroke-width="0.55"/>
<path d="M39.5746 22.5642C33.6346 22.555 27.6747 22.5581 21.695 22.5733C21.2214 22.5733 20.8043 22.5138 20.4437 22.3946C20.3368 21.9668 20.3399 21.6231 20.4529 21.3633C20.4827 21.2925 20.5326 21.232 20.5962 21.1896C20.6598 21.1472 20.7344 21.1247 20.8104 21.125H39.2721C39.4249 21.125 39.5165 21.1983 39.5471 21.345C39.6296 21.7483 39.6387 22.1547 39.5746 22.5642Z" fill="#B0BEC5"/>
<path d="M20.4438 22.3945C20.8043 22.5137 21.2214 22.5733 21.695 22.5733C27.6747 22.558 33.6346 22.5549 39.5746 22.5641C39.6693 23.0286 39.6143 23.2959 39.4096 23.3662L20.5675 23.3158C20.3658 23.0713 20.3246 22.7643 20.4438 22.3945Z" fill="#748C99"/>
<path d="M20.5675 23.3159L39.4096 23.3663C39.324 25.768 38.7924 26.8588 37.8146 26.6388L22.1625 26.648C21.2061 26.8374 20.6744 25.7268 20.5675 23.3159Z" fill="#B0BEC5"/>
<path d="M22.1625 26.6478L37.8146 26.6387C37.8024 29.8409 36.4595 32.2716 33.7858 33.9308C33.7095 33.4113 33.6728 32.5008 33.6758 31.1991C33.6804 29.1458 31.8425 27.4041 29.7433 27.6058C27.1446 27.8578 26.2829 29.7599 26.3379 32.1341C26.3532 32.7391 26.3486 33.3578 26.3242 33.9903C23.5986 32.3526 22.2114 29.9051 22.1625 26.6478Z" fill="#673AB7"/>
<path d="M33.7858 33.9308C31.32 35.263 28.8328 35.2829 26.3242 33.9904C26.3486 33.3579 26.3532 32.7391 26.3379 32.1341C26.2829 29.7599 27.1446 27.8579 29.7433 27.6058C31.8425 27.4041 33.6804 29.1458 33.6758 31.1991C33.6728 32.5008 33.7095 33.4113 33.7858 33.9308ZM31.8104 31.222C31.8104 30.7419 31.6197 30.2814 31.2802 29.9419C30.9406 29.6023 30.4802 29.4116 30 29.4116C29.5199 29.4116 29.0594 29.6023 28.7198 29.9419C28.3803 30.2814 28.1896 30.7419 28.1896 31.222C28.1896 31.7022 28.3803 32.1627 28.7198 32.5022C29.0594 32.8417 29.5199 33.0324 30 33.0324C30.4802 33.0324 30.9406 32.8417 31.2802 32.5022C31.6197 32.1627 31.8104 31.7022 31.8104 31.222Z" fill="#42257A"/>
<path d="M30 33.0325C30.9999 33.0325 31.8104 32.2219 31.8104 31.222C31.8104 30.2222 30.9999 29.4116 30 29.4116C29.0001 29.4116 28.1896 30.2222 28.1896 31.222C28.1896 32.2219 29.0001 33.0325 30 33.0325Z" fill="#B489FF"/>
<defs>
<filter id="filter0_d_1117_14619" x="0.411629" y="0.416588" width="59.7222" height="68.6111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.94444"/>
<feGaussianBlur stdDeviation="4.93055"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.992157 0 0 0 0 0.482353 0 0 0 0 0.219608 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1117_14619"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1117_14619" result="shape"/>
</filter>
<filter id="filter1_d_1117_14619" x="0.411629" y="6.33325" width="59.7222" height="68.6111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9.86111"/>
<feGaussianBlur stdDeviation="4.93055"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.992157 0 0 0 0 0.482353 0 0 0 0 0.219608 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1117_14619"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1117_14619" result="shape"/>
</filter>
</defs>
</svg>

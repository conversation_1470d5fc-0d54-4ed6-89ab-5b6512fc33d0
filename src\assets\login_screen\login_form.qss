
/*-----QWidget-----*/
QWidget
{
	color: #000000;
}


/*-----QLabel-----*/
QLabel
{
	background-color: transparent;
	color: #8D9DB1;
	font-size: 14px;
	font-weight: 200;

}


/*-----QPushButton-----*/
QPushButton
{
	background-color: #B5122E;
	color: #fff;
	font-size: 14px;
	font-weight: bold;
	border: none;
	border-radius: 4px;
	padding: 5px;

}


QPushButton::disabled
{
	background-color: #5c5c5c;

}


QPushButton::pressed
{
	background-color: #8C0D23;

}


/*-----QCheckBox-----*/
QCheckBox
{
	background-color: transparent;
	color: #fff;
	font-size: 10px;
	font-weight: bold;
	border: none;
	border-radius: 5px;

}


/*-----QCheckBox-----*/
QCheckBox::indicator
{
    color: #b1b1b1;
    background-color: #323232;
    border: 1px solid darkgray;
    width: 12px;
    height: 12px;

}


QCheckBox::indicator:checked
{
    image:url("./ressources/check.png");
	background-color: #ff3333;
    border: 1px solid #ff3333;

}


QCheckBox::indicator:unchecked:hover
{
    border: 1px solid #ff3333;

}


QCheckBox::disabled
{
	color: #656565;

}


QCheckBox::indicator:disabled
{
	background-color: #656565;
	color: #656565;
    border: 1px solid #656565;

}


/*-----QLineEdit-----*/

QLineEdit
{
    background-color: transparent;
    border-radius: 4px;
    color: #ffffff;
	font-weight: bold;
    border: 1px solid #5C687F;
    padding: 5px 15px;
}

QLineEdit::focus
{
    border: 2px solid #5C687F;
    color: #5C687F
}

QLineEdit:!focus
{
    color: #5C687F;
}

QLineEdit::placeholder
{
    color: #5C687F;
}
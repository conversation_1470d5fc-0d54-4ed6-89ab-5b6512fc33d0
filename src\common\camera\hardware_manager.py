"""
Hardware Acceleration Manager - Lớp singleton để quản lý phát hiện và cấu hình tăng tốc phần cứng.
"""

import av
import cv2
import platform
import subprocess
import re
import threading
import logging
import concurrent.futures
from av.codec.hwaccel import hwdevices_available
import logging

logger = logging.getLogger(__name__)

class HardwareAccelerationManager:
    """
    Lớp singleton để quản lý phát hiện và cấu hình tăng tốc phần cứng.
    Lớp này đảm bảo tăng tốc phần cứng chỉ được phát hiện một lần và cung cấp
    một vị trí trung tâm để truy cập thông tin tăng tốc phần cứng.
    """
    _instance = None
    _lock = threading.Lock()
    _thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)
    _initialization_started = False
    _initialization_completed = False
    
    def __new__(cls):
        """<PERSON><PERSON><PERSON> bảo chỉ tồn tại một instance của lớp này."""
        with cls._lock:
            if cls._instance is None:
                cls._instance = super(HardwareAccelerationManager, cls).__new__(cls)
                # Khởi tạo các thuộc tính cốt lõi ở đây để đảm bảo chúng tồn tại trước __init__
                cls._instance.initialized = False
                cls._instance.ffmpeg_available = None
                cls._instance.ffmpeg_version = None
                cls._instance.hw_types = []
                cls._instance.hw_accel = {}
                cls._instance.supported_hw = {}
                cls._instance.platform_info = {}
                cls._instance.pending_checks = {}
                cls._instance.initialization_future = None
        return cls._instance
    
    def __init__(self):
        """Khởi tạo các thuộc tính nếu đây là instance mới."""
        # Không cần kiểm tra hasattr vì chúng ta đã khởi tạo trong __new__
        pass

    def run_subprocess_in_thread(self, cmd, capture_output=True, text=True, timeout=5, callback=None):
        """
        Chạy lệnh subprocess trong một thread riêng biệt để tránh blocking.
        
        Args:
            cmd: Lệnh cần chạy (list hoặc string)
            capture_output: Có bắt đầu output hay không
            text: Có chuyển output thành text hay không
            timeout: Thời gian tối đa chờ kết quả (giây)
            callback: Hàm callback để xử lý kết quả khi hoàn thành
            
        Returns:
            concurrent.futures.Future nếu có callback, ngược lại trả về kết quả trực tiếp
        """
        def _run_subprocess():
            try:
                result = subprocess.run(
                    cmd, 
                    capture_output=capture_output, 
                    text=text,
                    timeout=timeout
                )
                if callback:
                    callback(result)
                return result
            except subprocess.TimeoutExpired:
                logger.warning("Lệnh %s đã hết thời gian chờ sau %d giây", cmd, timeout)
                if callback:
                    callback(None)
                return None
            except Exception as e:
                logger.error("Lỗi khi chạy lệnh %s: %s", cmd, e)
                if callback:
                    callback(None)
                return None
        
        future = self._thread_pool.submit(_run_subprocess)
        
        # Nếu không có callback, đợi kết quả
        if callback is None:
            try:
                return future.result(timeout=timeout)
            except concurrent.futures.TimeoutError:
                logger.warning("Lệnh %s đã hết thời gian chờ sau %d giây", cmd, timeout)
                return None
            except Exception as e:
                logger.error("Lỗi khi chạy lệnh %s: %s", cmd, e)
                return None
        
        # Nếu có callback, trả về future để người gọi có thể theo dõi
        return future

    def check_library_availability(self, library_name, callback=None):
        """
        Kiểm tra xem một thư viện có khả dụng trên hệ thống hay không.
        Hoạt động trên nhiều nền tảng khác nhau (Windows, macOS, Linux).
        
        Args:
            library_name: Tên thư viện cần kiểm tra
            callback: Hàm callback để xử lý kết quả khi hoàn thành
            
        Returns:
            bool hoặc Future tùy thuộc vào việc có callback hay không
        """
        def _check_library(result):
            if result is None:
                if callback:
                    callback(False)
                return False
            
            is_available = result.returncode == 0
            if callback:
                callback(is_available)
            return is_available
        
        try:
            if platform.system() == "Windows":
                # Trên Windows, thử sử dụng lệnh where
                return self.run_subprocess_in_thread(['where', library_name], callback=_check_library)
            else:
                # Trên các hệ thống giống Unix (macOS, Linux), sử dụng lệnh which
                return self.run_subprocess_in_thread(['which', library_name], callback=_check_library)
        except Exception as e:
            logger.error("Lỗi kiểm tra thư viện %s: %s", library_name, e)
            if callback:
                callback(False)
            return False

    def check_ffmpeg_availability(self, callback=None):
        """
        Kiểm tra xem FFmpeg có khả dụng và trả về phiên bản của nó.
        Hoạt động trên nhiều nền tảng khác nhau.
        
        Args:
            callback: Hàm callback để xử lý kết quả khi hoàn thành
            
        Returns:
            tuple hoặc Future tùy thuộc vào việc có callback hay không
        """
        # Trả về kết quả đã cache nếu có
        if self.ffmpeg_available is not None:
            if callback:
                callback(self.ffmpeg_available, self.ffmpeg_version)
            return self.ffmpeg_available, self.ffmpeg_version

        logger.debug("Đang kiểm tra tính khả dụng của FFmpeg")
        
        def _check_ffmpeg_version(result):
            if result is None:
                self.ffmpeg_available = False
                self.ffmpeg_version = ""
                if callback:
                    callback(False, "")
                return False, ""
                
            if result.returncode == 0:
                # Trích xuất phiên bản từ dòng đầu tiên
                version_line = result.stdout.split('\n')[0]
                version_match = re.search(r'ffmpeg version (\S+)', version_line)
                if version_match:
                    self.ffmpeg_version = version_match.group(1)
                    self.ffmpeg_available = True
                    logger.debug("Phiên bản FFmpeg: %s", self.ffmpeg_version)
                    if callback:
                        callback(True, self.ffmpeg_version)
                    return True, self.ffmpeg_version
            
            self.ffmpeg_available = False
            self.ffmpeg_version = ""
            if callback:
                callback(False, "")
            return False, ""
        
        def _check_ffmpeg_library(is_available):
            if not is_available:
                self.ffmpeg_available = False
                self.ffmpeg_version = ""
                if callback:
                    callback(False, "")
                return
            
            # Lấy phiên bản FFmpeg
            self.run_subprocess_in_thread(['ffmpeg', '-version'], callback=_check_ffmpeg_version)
        
        # Kiểm tra xem ffmpeg có khả dụng không
        return self.check_library_availability('ffmpeg', callback=_check_ffmpeg_library)

    def check_hardware_acceleration(self, callback=None):
        """
        Kiểm tra các phương pháp tăng tốc phần cứng khả dụng trên các nền tảng.
        
        Args:
            callback: Hàm callback để xử lý kết quả khi hoàn thành
            
        Returns:
            dict hoặc Future tùy thuộc vào việc có callback hay không
        """
        hw_accel = {}
        
        def _process_hwaccels(result):
            if result is not None and result.returncode == 0:
                # Phân tích các thiết bị tăng tốc phần cứng
                for line in result.stdout.split('\n'):
                    if line.strip() and not line.startswith('Hardware'):
                        hw_type = line.strip()
                        hw_accel[hw_type] = True
                        logger.debug("Đã phát hiện tăng tốc phần cứng: %s", hw_type)
            
            # Kiểm tra các loại tăng tốc phần cứng cụ thể dựa trên nền tảng
            if platform.system() == "Windows":
                # Kiểm tra NVIDIA CUDA
                self.check_library_availability('nvcc', 
                    lambda is_available: _check_cuda(is_available, hw_accel, callback))
            elif platform.system() == "Darwin":  # macOS
                # Kiểm tra VideoToolbox
                if 'videotoolbox' in hw_accel:
                    logger.debug("Đã phát hiện VideoToolbox")
                if callback:
                    callback(hw_accel)
            else:  # Linux
                # Kiểm tra VAAPI và CUDA
                self.check_library_availability('vainfo', 
                    lambda is_available: _check_vaapi(is_available, hw_accel, callback))
        
        def _check_cuda(is_available, hw_accel, callback):
            if is_available:
                hw_accel['cuda'] = True
                logger.debug("Đã phát hiện NVIDIA CUDA")
            if callback:
                callback(hw_accel)
        
        def _check_vaapi(is_available, hw_accel, callback):
            if is_available:
                hw_accel['vaapi'] = True
                logger.debug("Đã phát hiện VAAPI")
            
            # Kiểm tra NVIDIA CUDA
            self.check_library_availability('nvcc', 
                lambda is_available: _check_cuda(is_available, hw_accel, callback))
        
        def _check_ffmpeg(ffmpeg_available, _):
            if not ffmpeg_available:
                logger.debug("FFmpeg không khả dụng, phát hiện tăng tốc phần cứng bị giới hạn")
                if callback:
                    callback(hw_accel)
                return
            
            # Lấy các thiết bị tăng tốc phần cứng từ FFmpeg
            self.run_subprocess_in_thread(['ffmpeg', '-hide_banner', '-hwaccels'], 
                                        callback=_process_hwaccels)
        
        try:
            # Kiểm tra xem FFmpeg có khả dụng không
            return self.check_ffmpeg_availability(callback=_check_ffmpeg)
        except Exception as e:
            logger.error("Lỗi kiểm tra tăng tốc phần cứng: %s", e)
            if callback:
                callback(hw_accel)
            return hw_accel

    def get_available_hwaccel(self):
        """Lấy các thiết bị tăng tốc phần cứng khả dụng."""
        # Đầu tiên kiểm tra tăng tốc phần cứng bằng FFmpeg
        hw_accel = self.check_hardware_acceleration()
        
        # Đảm bảo hw_accel là một dictionary
        if not isinstance(hw_accel, dict):
            logger.warning("hw_accel không phải là từ điển, chuyển đổi thành từ điển trống")
            hw_accel = {}
        
        # Sau đó lấy các thiết bị phần cứng từ PyAV
        hw_types = list(hwdevices_available())
        logger.debug("Các thiết bị phần cứng ban đầu từ PyAV: %s", hw_types)
        
        # Thêm các loại tăng tốc phần cứng từ FFmpeg có thể không có trong PyAV
        for hw_type in hw_accel:
            if hw_type not in hw_types:
                hw_types.append(hw_type)
                logger.debug("Đã thêm loại tăng tốc phần cứng từ FFmpeg: %s", hw_type)
        
        logger.debug("Các thiết bị tăng tốc phần cứng cuối cùng: %s", hw_types)
        return hw_types

    def get_hw_config(self, hw_types, codec_name, preferred_device=None):
        """Lấy cấu hình tăng tốc phần cứng dựa trên các thiết bị khả dụng và codec."""
        # Bảng ánh xạ cấu hình cho các phương pháp tăng tốc phần cứng khác nhau
        hw_configs = []
        
        # Thêm cấu hình MF cho Windows
        if platform.system() == "Windows":
            hw_configs.append({
                'device': 'mf',  # Media Foundation
                'codec_map': {
                    'h264': 'h264_mf',
                    'hevc': 'hevc_mf',
                    'mpeg2': 'mpeg2_mf',
                    'vp9': 'vp9_mf',
                    'av1': 'av1_mf'
                }
            })
        
        # Thêm các cấu hình khác
        hw_configs.extend([
            {
                'device': 'cuda',
                'codec_map': {
                    'h264': 'h264_cuvid',
                    'hevc': 'hevc_cuvid',
                    'mjpeg': 'mjpeg_cuvid',
                    'mpeg1': 'mpeg1_cuvid',
                    'mpeg2': 'mpeg2_cuvid',
                    'mpeg4': 'mpeg4_cuvid',
                    'vc1': 'vc1_cuvid',
                    'vp8': 'vp8_cuvid',
                    'vp9': 'vp9_cuvid',
                    'av1': 'av1_cuvid'
                }
            },
            {
                'device': 'd3d11va',
                'codec_map': {
                    'h264': 'h264',
                    'hevc': 'hevc',
                    'vp9': 'vp9',
                    'av1': 'av1'
                }
            },
            {
                'device': 'dxva2',
                'codec_map': {
                    'h264': 'h264',
                    'hevc': 'hevc',
                    'mpeg2': 'mpeg2',
                    'vc1': 'vc1',
                    'wmv3': 'wmv3'
                }
            },
            {
                'device': 'vaapi',
                'codec_map': {
                    'h264': 'h264',
                    'hevc': 'hevc',
                    'mjpeg': 'mjpeg',
                    'mpeg2': 'mpeg2',
                    'vp8': 'vp8',
                    'vp9': 'vp9',
                    'av1': 'av1'
                }
            }
        ])
        
        if preferred_device:
            # Thử thiết bị ưa thích trước
            for config in hw_configs:
                if config['device'] == preferred_device and codec_name in config['codec_map']:
                    return {
                        'device_type': config['device'],
                        'decoder_name': config['codec_map'][codec_name]
                    }
            logger.debug("Thiết bị ưa thích %s không tương thích với codec %s", 
                          preferred_device, codec_name)
        
        # Thử từng cấu hình phần cứng theo thứ tự
        for config in hw_configs:
            if config['device'] in hw_types and codec_name in config['codec_map']:
                return {
                    'device_type': config['device'],
                    'decoder_name': config['codec_map'][codec_name]
                }
        
        return None

    def initialize(self, callback=None):
        """
        Khởi tạo thông tin tăng tốc phần cứng.
        Phương thức này an toàn với luồng và sẽ chỉ chạy một lần.
        
        Args:
            callback: Hàm callback để gọi khi khởi tạo hoàn tất
        """
        # Kiểm tra xem đã bắt đầu khởi tạo chưa
        if HardwareAccelerationManager._initialization_started:
            # Nếu đã bắt đầu khởi tạo, đợi cho đến khi hoàn thành
            if HardwareAccelerationManager._initialization_completed:
                if callback:
                    callback(True)
                return
            
            # Nếu đang khởi tạo, đợi cho đến khi hoàn thành
            if self.initialization_future:
                try:
                    self.initialization_future.result(timeout=30)  # Đợi tối đa 30 giây
                    if callback:
                        callback(True)
                except (concurrent.futures.TimeoutError, Exception) as e:
                    logger.error("Lỗi khi đợi khởi tạo: %s", e)
                    if callback:
                        callback(False)
            return
        
        # Đánh dấu đã bắt đầu khởi tạo
        HardwareAccelerationManager._initialization_started = True
        
        # Bắt đầu khởi tạo trong một thread riêng
        def _initialize_in_thread():
            try:
                with self._lock:
                    if not self.initialized:
                        logger.debug("Đang khởi tạo tăng tốc phần cứng...")
                        
                        # Lấy các thiết bị tăng tốc phần cứng
                        hw_accel_future = self.check_hardware_acceleration()
                        
                        # Đợi kết quả từ Future nếu cần
                        if isinstance(hw_accel_future, concurrent.futures.Future):
                            try:
                                self.hw_accel = hw_accel_future.result(timeout=30)
                            except (concurrent.futures.TimeoutError, Exception) as e:
                                logger.error("Lỗi khi đợi kết quả kiểm tra tăng tốc phần cứng: %s", e)
                                self.hw_accel = {}
                        else:
                            self.hw_accel = hw_accel_future
                        
                        # Lấy các thiết bị phần cứng khả dụng từ PyAV
                        self.hw_types = list(hwdevices_available())
                        logger.debug("Các thiết bị phần cứng ban đầu từ PyAV: %s", self.hw_types)
                        
                        # Thêm các loại tăng tốc phần cứng từ FFmpeg có thể không có trong PyAV
                        if isinstance(self.hw_accel, dict):
                            for hw_type in self.hw_accel:
                                if hw_type not in self.hw_types:
                                    self.hw_types.append(hw_type)
                                    logger.debug("Đã thêm loại tăng tốc phần cứng từ FFmpeg: %s", hw_type)
                        else:
                            logger.warning("hw_accel không phải là từ điển, không thể thêm các loại tăng tốc phần cứng từ FFmpeg")
                        
                        logger.debug("Các thiết bị tăng tốc phần cứng cuối cùng: %s", self.hw_types)
                        
                        # Lấy thông tin tăng tốc phần cứng được hỗ trợ
                        self.supported_hw = self.list_supported_hardware(self.hw_types)
                        
                        # Thu thập thông tin nền tảng
                        self.platform_info = {
                            'platform': platform.platform(),
                            'python_version': platform.python_version(),
                            'pyav_version': av.__version__,
                            'opencv_version': cv2.__version__,
                            'system': platform.system(),
                            'processor': platform.processor(),
                            'machine': platform.machine(),
                            'ffmpeg_version': self.ffmpeg_version
                        }
                        
                        # Ghi log thông tin nền tảng
                        logger.debug("Nền tảng: %s, Python: %s, PyAV: %s, OpenCV: %s, FFmpeg: %s", 
                                self.platform_info['platform'], 
                                self.platform_info['python_version'],
                                self.platform_info['pyav_version'],
                                self.platform_info['opencv_version'],
                                self.platform_info['ffmpeg_version'])
                        
                        self.initialized = True
                        logger.debug("Hoàn thành khởi tạo tăng tốc phần cứng")
            except Exception as e:
                logger.error("Lỗi khi khởi tạo tăng tốc phần cứng: %s", e)
            finally:
                # Đánh dấu đã hoàn thành khởi tạo
                HardwareAccelerationManager._initialization_completed = True
                # Gọi callback nếu có
                if callback:
                    callback(self.initialized)
        
        # Chạy khởi tạo trong một thread riêng
        self.initialization_future = self._thread_pool.submit(_initialize_in_thread)
        
        # Nếu không có callback, đợi kết quả
        if callback is None:
            try:
                self.initialization_future.result(timeout=30)  # Đợi tối đa 30 giây
            except (concurrent.futures.TimeoutError, Exception) as e:
                logger.error("Lỗi khi đợi khởi tạo: %s", e)
    
    def get_hw_types(self, callback=None):
        """
        Lấy các loại tăng tốc phần cứng khả dụng.
        
        Args:
            callback: Hàm callback để gọi khi có kết quả
            
        Returns:
            list hoặc None nếu có callback
        """
        def _get_hw_types_after_init(initialized):
            if callback:
                callback(self.hw_types if initialized else [])
            return self.hw_types if initialized else []
            
        if not self.initialized:
            self.initialize(callback=_get_hw_types_after_init)
            return None if callback else []
            
        if callback:
            callback(self.hw_types)
            return None
            
        return self.hw_types
        
    def get_supported_hw(self, callback=None):
        """
        Lấy các loại tăng tốc phần cứng và codec được hỗ trợ.
        
        Args:
            callback: Hàm callback để gọi khi có kết quả
            
        Returns:
            dict hoặc None nếu có callback
        """
        def _get_supported_hw_after_init(initialized):
            if callback:
                callback(self.supported_hw if initialized else {})
            return self.supported_hw if initialized else {}
            
        if not self.initialized:
            self.initialize(callback=_get_supported_hw_after_init)
            return None if callback else {}
            
        if callback:
            callback(self.supported_hw)
            return None
            
        return self.supported_hw
        
    def get_platform_info(self, callback=None):
        """
        Lấy thông tin nền tảng.
        
        Args:
            callback: Hàm callback để gọi khi có kết quả
            
        Returns:
            dict hoặc None nếu có callback
        """
        def _get_platform_info_after_init(initialized):
            if callback:
                callback(self.platform_info if initialized else {})
            return self.platform_info if initialized else {}
            
        if not self.initialized:
            self.initialize(callback=_get_platform_info_after_init)
            return None if callback else {}
            
        if callback:
            callback(self.platform_info)
            return None
            
        return self.platform_info
        
    def is_hw_accel_available(self, hw_type=None, callback=None):
        """
        Kiểm tra xem tăng tốc phần cứng có khả dụng hay không.
        
        Args:
            hw_type: Tùy chọn loại tăng tốc phần cứng cụ thể cần kiểm tra
            callback: Hàm callback để gọi khi có kết quả
            
        Returns:
            bool hoặc None nếu có callback
        """
        def _is_hw_accel_available_after_init(initialized):
            if not initialized:
                if callback:
                    callback(False)
                return False
                
            result = hw_type in self.hw_types if hw_type else len(self.hw_types) > 0
            if callback:
                callback(result)
            return result
            
        if not self.initialized:
            self.initialize(callback=_is_hw_accel_available_after_init)
            return None if callback else False
            
        result = hw_type in self.hw_types if hw_type else len(self.hw_types) > 0
        if callback:
            callback(result)
            return None
            
        return result
        
    def get_best_hw_accel_for_codec(self, codec_name, callback=None):
        """
        Lấy loại tăng tốc phần cứng tốt nhất cho một codec cụ thể.
        
        Args:
            codec_name: Tên codec (ví dụ: 'h264', 'hevc')
            callback: Hàm callback để gọi khi có kết quả
            
        Returns:
            tuple hoặc None nếu có callback
        """
        def _get_best_hw_accel_after_init(initialized):
            if not initialized:
                if callback:
                    callback(None, None)
                return None, None
                
            # Định nghĩa thứ tự ưu tiên cho các thiết bị tăng tốc phần cứng
            priority_order = [
                'cuda',      # Tăng tốc GPU NVIDIA (ưu tiên cao nhất)
                'd3d11va',   # Direct3D 11 Video Acceleration
                'dxva2',     # DirectX Video Acceleration 2
                'vaapi',     # Video Acceleration API
                'mf',        # Media Foundation
                'videotoolbox' # VideoToolbox MacOS
            ]
            
            # Lọc các loại phần cứng khả dụng dựa trên ưu tiên
            available_prioritized = [hw for hw in priority_order if hw in self.hw_types]
            logger.debug("Các loại phần cứng ưu tiên khả dụng: %s", available_prioritized)
            if not available_prioritized:
                if callback:
                    callback(None, None)
                return None, None
            
            # Thử từng loại phần cứng theo thứ tự ưu tiên
            for device_type in available_prioritized:
                if device_type in self.supported_hw and codec_name in self.supported_hw[device_type]:
                    result = (device_type, self.supported_hw[device_type][codec_name])
                    if callback:
                        callback(*result)
                    return result
                    
            logger.debug("Không tìm thấy tăng tốc phần cứng phù hợp")
            if callback:
                callback(None, None)
            return None, None
            
        if not self.initialized:
            self.initialize(callback=_get_best_hw_accel_after_init)
            return None if callback else (None, None)
            
        # Định nghĩa thứ tự ưu tiên cho các thiết bị tăng tốc phần cứng
        priority_order = [
            'cuda',      # Tăng tốc GPU NVIDIA (ưu tiên cao nhất)
            'd3d11va',   # Direct3D 11 Video Acceleration
            'dxva2',     # DirectX Video Acceleration 2
            'vaapi',     # Video Acceleration API
            'mf',        # Media Foundation
            'videotoolbox' # VideoToolbox MacOS
        ]
        
        # Lọc các loại phần cứng khả dụng dựa trên ưu tiên
        available_prioritized = [hw for hw in priority_order if hw in self.hw_types]
        logger.debug("Các loại phần cứng ưu tiên khả dụng: %s", available_prioritized)
        if not available_prioritized:
            if callback:
                callback(None, None)
            return None, None
        
        # Thử từng loại phần cứng theo thứ tự ưu tiên
        for device_type in available_prioritized:
            if device_type in self.supported_hw and codec_name in self.supported_hw[device_type]:
                result = (device_type, self.supported_hw[device_type][codec_name])
                if callback:
                    callback(*result)
                return result
                
        logger.debug("Không tìm thấy tăng tốc phần cứng phù hợp")
        if callback:
            callback(None, None)
        return None, None
        
    def list_supported_hardware(self, hw_types):
        """
        Liệt kê tất cả các loại tăng tốc phần cứng được hỗ trợ và các codec tương ứng.
        
        Args:
            hw_types: Danh sách các loại tăng tốc phần cứng khả dụng
            
        Returns:
            dict: Từ điển ánh xạ các loại phần cứng với các codec được hỗ trợ
        """
        supported_hw = {}
        
        # Các codec phổ biến cần kiểm tra cho mỗi loại phần cứng
        common_codecs = ['h264', 'hevc', 'mjpeg', 'mpeg2', 'vp8', 'vp9', 'av1']
        
        # Kiểm tra hỗ trợ Windows Media Foundation
        if platform.system() == "Windows" and 'mf' in hw_types:
            supported_hw['mf'] = {
                'h264': 'h264_mf',
                'hevc': 'hevc_mf',
                'mpeg2': 'mpeg2_mf',
                'vp9': 'vp9_mf',
                'av1': 'av1_mf'
            }
        
        # Kiểm tra hỗ trợ CUDA
        if 'cuda' in hw_types:
            supported_hw['cuda'] = {
                'h264': 'h264_cuvid',
                'hevc': 'hevc_cuvid',
                'mjpeg': 'mjpeg_cuvid',
                'mpeg1': 'mpeg1_cuvid',
                'mpeg2': 'mpeg2_cuvid',
                'mpeg4': 'mpeg4_cuvid',
                'vc1': 'vc1_cuvid',
                'vp8': 'vp8_cuvid',
                'vp9': 'vp9_cuvid',
                'av1': 'av1_cuvid'
            }
        
        # Kiểm tra hỗ trợ D3D11VA
        if 'd3d11va' in hw_types:
            supported_hw['d3d11va'] = {
                'h264': 'h264',
                'hevc': 'hevc',
                'vp9': 'vp9',
                'av1': 'av1'
            }
        
        # Kiểm tra hỗ trợ DXVA2
        if 'dxva2' in hw_types:
            supported_hw['dxva2'] = {
                'h264': 'h264',
                'hevc': 'hevc',
                'mpeg2': 'mpeg2',
                'vc1': 'vc1',
                'wmv3': 'wmv3'
            }
        
        # Kiểm tra hỗ trợ VAAPI
        if 'vaapi' in hw_types:
            supported_hw['vaapi'] = {
                'h264': 'h264',
                'hevc': 'hevc',
                'mjpeg': 'mjpeg',
                'mpeg2': 'mpeg2',
                'vp8': 'vp8',
                'vp9': 'vp9',
                'av1': 'av1'
            }
        
        # Kiểm tra hỗ trợ VideoToolbox (macOS)
        if 'videotoolbox' in hw_types:
            supported_hw['videotoolbox'] = {
                'h264': 'h264_videotoolbox',
                'hevc': 'hevc_videotoolbox',
            }
            
        return supported_hw

    def print_supported_hardware(self):
        """In thông tin tăng tốc phần cứng được hỗ trợ."""
        self.initialize()
        if not self.supported_hw:
            logger.error("Không có tăng tốc phần cứng được hỗ trợ trên hệ thống này")
            return
            
        logger.debug("=== TĂNG TỐC PHẦN CỨNG ĐƯỢC HỖ TRỢ ===")
        for hw_type, codecs in self.supported_hw.items():
            logger.debug(f"{hw_type.upper()}:")
            for codec, decoder in codecs.items():
                logger.debug(f"  - {codec} ({decoder})")
        logger.debug("=====================================") 
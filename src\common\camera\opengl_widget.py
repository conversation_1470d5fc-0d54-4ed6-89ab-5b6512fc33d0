import numpy as np
from PySide6.QtOpenGLWidgets import QOpenGLWidget
import OpenGL.GL as gl
from OpenGL.GL import shaders
import logging
import ctypes

logger = logging.getLogger(__name__)

class OpenGLWidget(QOpenGLWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.texture_y = None
        self.texture_uv = None
        self.program = None
        self.texture_width = 0
        self.texture_height = 0
        
        # Vertex shader for rendering a textured quad
        self.vertex_shader = """
        #version 330
        layout(location = 0) in vec2 position;
        layout(location = 1) in vec2 texcoord;
        out vec2 v_texcoord;
        void main() {
            gl_Position = vec4(position, 0.0, 1.0);
            // Flip texture coordinates vertically (1.0 - texcoord.y)
            v_texcoord = vec2(texcoord.x, 1.0 - texcoord.y);
        }
        """

        # Fragment shader for NV12 to RGB conversion
        self.fragment_shader = """
        #version 330
        in vec2 v_texcoord;
        out vec4 fragColor;
        uniform sampler2D tex_y;
        uniform sampler2D tex_uv;
        
        void main() {
            float y = texture(tex_y, v_texcoord).r;
            vec2 uv = texture(tex_uv, v_texcoord).rg - vec2(0.5, 0.5);
            
            // YUV to RGB conversion (BT.709)
            float r = y + 1.402 * uv.y;
            float g = y - 0.344 * uv.x - 0.714 * uv.y;
            float b = y + 1.772 * uv.x;
            
            // Clamp values to [0, 1] range
            r = clamp(r, 0.0, 1.0);
            g = clamp(g, 0.0, 1.0);
            b = clamp(b, 0.0, 1.0);
            
            fragColor = vec4(r, g, b, 1.0);
        }
        """

    def initializeGL(self):
        # Create and compile shaders
        vertex_shader = shaders.compileShader(self.vertex_shader, gl.GL_VERTEX_SHADER)
        fragment_shader = shaders.compileShader(self.fragment_shader, gl.GL_FRAGMENT_SHADER)
        self.program = shaders.compileProgram(vertex_shader, fragment_shader)
        
        # Create vertex buffer for a quad
        vertices = np.array([
            -1.0, -1.0,  0.0, 0.0,
             1.0, -1.0,  1.0, 0.0,
             1.0,  1.0,  1.0, 1.0,
            -1.0,  1.0,  0.0, 1.0
        ], dtype=np.float32)
        
        self.vbo = gl.glGenBuffers(1)
        gl.glBindBuffer(gl.GL_ARRAY_BUFFER, self.vbo)
        gl.glBufferData(gl.GL_ARRAY_BUFFER, vertices.nbytes, vertices, gl.GL_STATIC_DRAW)
        
        # Create and bind VAO
        self.vao = gl.glGenVertexArrays(1)
        gl.glBindVertexArray(self.vao)
        
        # Position attribute
        gl.glVertexAttribPointer(0, 2, gl.GL_FLOAT, gl.GL_FALSE, 16, None)
        gl.glEnableVertexAttribArray(0)
        
        # Texture coordinate attribute
        gl.glVertexAttribPointer(1, 2, gl.GL_FLOAT, gl.GL_FALSE, 16, ctypes.c_void_p(8))
        gl.glEnableVertexAttribArray(1)
        
        gl.glClearColor(0.0, 0.0, 0.0, 1.0)

    def resizeGL(self, width, height):
        gl.glViewport(0, 0, width, height)

    def paintGL(self):
        gl.glClear(gl.GL_COLOR_BUFFER_BIT)
        
        if self.texture_y is not None and self.texture_uv is not None:
            gl.glUseProgram(self.program)
            
            # Bind Y texture
            gl.glActiveTexture(gl.GL_TEXTURE0)
            gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture_y)
            gl.glUniform1i(gl.glGetUniformLocation(self.program, "tex_y"), 0)
            
            # Bind UV texture
            gl.glActiveTexture(gl.GL_TEXTURE1)
            gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture_uv)
            gl.glUniform1i(gl.glGetUniformLocation(self.program, "tex_uv"), 1)
            
            # Draw quad
            gl.glBindVertexArray(self.vao)
            gl.glDrawArrays(gl.GL_TRIANGLE_FAN, 0, 4)

    def set_frame(self, frame):
        """Update the frame data. Frame should be in NV12 format with shape (height*3/2, width)"""
        try:
            self.makeCurrent()
            
            height, width = frame.shape
            y_height = height * 2 // 3
            
            # Split NV12 into Y and UV planes
            y_plane = frame[:y_height, :]
            uv_plane = frame[y_height:, :]
            
            # Create or update Y texture
            if self.texture_y is None or width != self.texture_width or y_height != self.texture_height:
                if self.texture_y is not None:
                    gl.glDeleteTextures([self.texture_y])
                self.texture_y = gl.glGenTextures(1)
                gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture_y)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MIN_FILTER, gl.GL_LINEAR)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MAG_FILTER, gl.GL_LINEAR)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_WRAP_S, gl.GL_CLAMP_TO_EDGE)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_WRAP_T, gl.GL_CLAMP_TO_EDGE)
                gl.glTexImage2D(gl.GL_TEXTURE_2D, 0, gl.GL_R8, width, y_height, 0, gl.GL_RED, gl.GL_UNSIGNED_BYTE, None)
                self.texture_width = width
                self.texture_height = y_height
            
            # Update Y texture data
            gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture_y)
            gl.glTexSubImage2D(gl.GL_TEXTURE_2D, 0, 0, 0, width, y_height, gl.GL_RED, gl.GL_UNSIGNED_BYTE, y_plane)
            
            # Create or update UV texture
            if self.texture_uv is None or width != self.texture_width or y_height != self.texture_height:
                if self.texture_uv is not None:
                    gl.glDeleteTextures([self.texture_uv])
                self.texture_uv = gl.glGenTextures(1)
                gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture_uv)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MIN_FILTER, gl.GL_LINEAR)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MAG_FILTER, gl.GL_LINEAR)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_WRAP_S, gl.GL_CLAMP_TO_EDGE)
                gl.glTexParameteri(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_WRAP_T, gl.GL_CLAMP_TO_EDGE)
                gl.glTexImage2D(gl.GL_TEXTURE_2D, 0, gl.GL_RG8, width//2, y_height//2, 0, gl.GL_RG, gl.GL_UNSIGNED_BYTE, None)
            
            # Update UV texture data
            gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture_uv)
            gl.glTexSubImage2D(gl.GL_TEXTURE_2D, 0, 0, 0, width//2, y_height//2, gl.GL_RG, gl.GL_UNSIGNED_BYTE, uv_plane)
            
            self.update()
            
        except Exception as e:
            logger.error(f"Error in set_frame: {e}")
        finally:
            self.doneCurrent()

    def cleanup(self):
        if self.texture_y is not None:
            gl.glDeleteTextures([self.texture_y])
            self.texture_y = None
        if self.texture_uv is not None:
            gl.glDeleteTextures([self.texture_uv])
            self.texture_uv = None
        if self.program is not None:
            gl.glDeleteProgram(self.program)
            self.program = None
        if hasattr(self, 'vbo'):
            gl.glDeleteBuffers(1, [self.vbo])
        if hasattr(self, 'vao'):
            gl.glDeleteVertexArrays(1, [self.vao]) 
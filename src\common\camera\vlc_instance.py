import os
from src.utils.vlc_utils import init_vlc_environment
import logging

logger = logging.getLogger(__name__)

class VLCInstance:
    """
    Singleton class để quản lý VLC instance
    """
    _instance = None
    _vlc_instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(VLCInstance, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._initialize_vlc()
            VLCInstance._initialized = True

    def _initialize_vlc(self):
        """Khởi tạo VLC instance với các tùy chọn tối ưu"""
        try:
            # Khởi tạo môi trường VLC dựa trên nền tảng
            vlc_path, lib_path, plugins_path = init_vlc_environment()
            
            # Import vlc sau khi môi trường đã được thiết lập
            import vlc
            
            print(f'Bắt đầu khởi tạo VLC Instance')
            
            VLCInstance._vlc_instance = vlc.Instance(
                '--avcodec-fast',              # Bật giải mã nhanh
                '--avcodec-threads=1',         # Sử dụng 1 luồng để giải mã
                '--network-caching=1000',      # Bộ đệm mạng tính bằng ms
                '--live-caching=1000',         # Bộ đệm cho luồng trực tiếp tính bằng ms
                '--sout-mux-caching=1000',     # Bộ đệm đầu ra luồng tính bằng ms
                '--quiet',                     # Giảm thông báo
                '--log-verbose=-1',            # Tắt ghi log
                '--file-caching=1000',         # Giá trị bộ đệm tệp
                '--no-snapshot-preview',       # Tắt xem trước ảnh chụp
                '--no-stats',                  # Tắt thống kê
                '--no-sub-autodetect-file',    # Tắt tự động phát hiện phụ đề
                '--no-video-title-show',       # Không hiển thị tiêu đề video
                '--no-osd',                    # Không hiển thị trên màn hình
                '--prefetch-buffer-size=1024'  # Kích thước bộ đệm tải trước tính bằng KB
            )
            
            print(f'KHỞI TẠO VLC INSTANCE THÀNH CÔNG')
            
            # In thông tin biến môi trường
            try:
                print(f'PYTHON_VLC_MODULE_PATH: {os.environ.get("PYTHON_VLC_MODULE_PATH", "Not set")}')
                print(f'PYTHON_VLC_LIB_PATH: {os.environ.get("PYTHON_VLC_LIB_PATH", "Not set")}')
                print(f'VLC_PLUGIN_PATH: {os.environ.get("VLC_PLUGIN_PATH", "Not set")}')
            except Exception as env_error:
                print(f'Cảnh báo: Không thể in biến môi trường: {env_error}')
                
        except Exception as e:
            print(f'Lỗi: KHỞI TẠO VLC INSTANCE thất bại: {e}')
            logger.error(f'Lỗi khởi tạo VLC: {e}')
            VLCInstance._vlc_instance = None

    @classmethod
    def get_instance(cls):
        """
        Trả về VLC instance singleton
        
        Returns:
            vlc.Instance: VLC instance hoặc None nếu khởi tạo thất bại
        """
        if cls._vlc_instance is None:
            # Tạo instance của VLCInstance để khởi tạo VLC
            VLCInstance()
        return cls._vlc_instance

    @classmethod
    def is_available(cls):
        """
        Kiểm tra xem VLC instance có sẵn sàng không
        
        Returns:
            bool: True nếu VLC instance đã được khởi tạo thành công
        """
        return cls._vlc_instance is not None

    @classmethod
    def reset_instance(cls):
        """
        Reset VLC instance (dùng cho testing hoặc cleanup)
        """
        cls._vlc_instance = None
        cls._initialized = False
        cls._instance = None


# Hàm tiện ích để lấy VLC instance
def get_vlc_instance():
    """
    Hàm tiện ích để lấy VLC instance
    
    Returns:
        vlc.Instance: VLC instance hoặc None nếu không khả dụng
    """
    logger.debug(f'get_vlc_instance')
    return VLCInstance.get_instance()

def get_vlc_module():
    """
    Hàm tiện ích để lấy VLC module sau khi đã được khởi tạo
    
    Returns:
        module: VLC module hoặc None nếu không khả dụng
    """
    if VLCInstance.is_available():
        import vlc
        return vlc
    return None 
from queue import Queue
from typing import Callable, List
import threading
import logging
from PySide6.QtCore import QObject
from PySide6.QtGui import QPixmap
from src.common.onvif_api.worker_thread import start_threads,wait_for
import av
from threading import RLock
import requests
class ThreadImageManager:
    def __init__(self):
        super().__init__()
        self.url_queue = Queue()
    def start(self):
        self.url_queue = Queue()
        self.image_threads = start_threads(20,self.get_image,self.url_queue)
        # for camera in camera_list:
        #     self.screenshot_queue.put(camera)

        # wait_for(self.screenshot_queue,screenshot_threads)
        # # logger.debug(f'End health_check_cameras')
        return True
    
    def set_url(self,url):
        # print(f'url = {url}')
        self.url_queue.put(url)

    def stop(self):
        wait_for(self.url_queue,self.image_threads)

    def get_image(self,url_queue:Queue)-> None:
        while True:
            # print(f'url = 6666')
            url = url_queue.get()
            # print(f'url = {url}')
            if url is None:
                print(f'get_image = {url}')
                break
            else:
                try:
                    response = requests.get(url)
                    if response.status_code == requests.codes.ok:
                        data = response.content
                        pixmap = QPixmap()
                        pixmap.loadFromData(data)
                        print(f'done url = {url}')
                        # return pixmap
                    
                except requests.RequestException as e:
                    print(f"Error loading image: {str(e)}")
            url_queue.task_done()

from PySide6.QtWidgets import QGraphicsRectItem,QGraphicsProxyWidget,QGraphicsSceneMouseEvent,QGraphicsProxyWidget, QGraphicsItemAnimation
from PySide6.QtCore import QRectF,QSize,QPoint, QTimeLine, QPointF, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QColor,QPen, QTransform
from src.common.graphics.focus_grid_item import FocusItem
from src.presentation.camera_screen.map.map_widget_item import Map2DWidgetItem
from src.common.controller.main_controller import main_controller
from src.common.model.main_tree_view_model import TreeType
from src.common.model.camera_model import camera_model_manager
from src.common.graphics.base_proxy_widget import BaseProxyWidget
from src.common.graphics.number_widget import NumberWidget
from src.common.model.tab_model import TabModel,GridItem,ItemType
from src.common.model.event_data_model import event_manager
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.model.group_model import group_model_manager
from src.styles.style import Style
import pickle
import logging
logger = logging.getLogger(__name__)

class WidgetType:
    Invalid = "Invalid"
    NormalWidget = "NormalWidget"
    VideoWidget = "VideoWidget"

class BaseGridItem(QGraphicsRectItem):
    # drop_changed = Signal(tuple)
    def __init__(self, rect,row,col,tab_model = None):
        super().__init__(rect)
        self.initial_width = self.rect().width()
        self.initial_height = self.rect().height()
        self.row = row
        self.col = col
        self.is_fullscreen = False
        self.padding = 1
        self.tab_model = tab_model
        self.setAcceptDrops(True)
        # self.drop_changed.connect(self.connect_drop)
        self.drag = None
        
        self.setBrush(QColor(Style.PrimaryColor.on_background))  # Đặt màu nền
        # self.set_border(enable=False)
        self.graphics_widget = None
        self.video_item = None
        self.mouse_press_pos = None
        # sử dụng biến is_grid_selected,length_w,length_h để quản lý viec resize grid item này sử dụng để hiển thị item trong custom grid
        self.is_grid_selected = False
        self.length_w = 1
        self.length_h = 1
        self.resgistered = {'type': WidgetType.Invalid,'data':{'widget': None}}
        self.number_widget = None
        self.create_number_widget()
        
    
    def setRectSize(self, scale):
        new_width = self.initial_width * scale
        new_height = self.initial_height * scale
        self.setRect(-new_width / 2, -new_height / 2, new_width, new_height)

    def create_number_widget(self):
        self.number_widget = NumberWidget(font_size='40px')
        self.graphics_widget = QGraphicsProxyWidget()
        self.graphics_widget.setWidget(self.number_widget)

    def set_border(self,enable = True):

        if enable:
            self.scene().focus_item.setRect(self.rect())
            border_color = QColor(181, 18, 46)  # Màu đỏ (RGB: 255, 0, 0)
            border_width = 1
            new_pen = QPen(border_color, border_width)
            self.scene().focus_item.setPen(new_pen)
        else:
            border_color = QColor(Style.PrimaryColor.on_background)  # Màu đỏ (RGB: 255, 0, 0)
            border_width = 1
            new_pen = QPen(border_color, border_width)
            self.scene().focus_item.setPen(new_pen)

    def register_widget(self,type = WidgetType.Invalid, widget = None):
        self.resgistered['type'] = type
        self.resgistered['data']['widget'] = widget
        if hasattr(widget, 'resgister_grid_item'):
            widget.resgister_grid_item(base_grid_item = self)

    def unregister_widget(self):
        self.resgistered['type'] = WidgetType.Invalid
        widget = self.resgistered['data'].get('widget',None)
        if widget is not None:
            if hasattr(widget, 'grid_item') and hasattr(widget, 'unresgister_grid_item'):
                widget.base_grid_item = None
                widget.unresgister_grid_item()
        self.resgistered['data']['widget'] = None

    def match_gird_item(self,json:dict = None):
        type = None
        widget = None
        type = json.get('type',None)
        # rect = self.rect()
        data = json.get('data',None)
        if data is not None:
            widget = data.get("widget",None)
        self.register_widget(type=type,widget=widget)

    def dragMoveEvent(self, event):
        scene = self.scene()
        # logger.debug(f'dragMoveEvent BaseGridItem')
        widget = scene.widget_focused
        if widget is not None:
            widget.dragMoveEvent(event)

    def dragEnterEvent(self, event):
        # logger.debug(f'dragEnterEvent BaseGridItem')
        # widget = self.scene().widget_focused
        # # Mỗi khi người dùng kéo di chuyển item đến Item khác thì focus luôn BaseGraphicsWidget vào BaseGridItem đó luôn.
        # # Thêm case này nhằm mục đích gỡ kịch bản khi người dùng di chuyển Item ra ngoài Scene.
        # if widget is not None:
        #     widget.set_base_grid_item(self)
        #     self.scene().focus_item.item = self
        self.setBrush(QColor(Style.PrimaryColor.on_hover_secondary))
        event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        # logger.debug(f'dragLeaveEvent BaseGridItem')
        self.setBrush(QColor(Style.PrimaryColor.on_background))
        event.acceptProposedAction()

    def mouseReleaseEvent(self, event: QGraphicsSceneMouseEvent):
        pass
        # logger.debug(f'mouseReleaseEvent BaseGridItem')
        

    def mouseMoveEvent(self, event: QGraphicsSceneMouseEvent):
        pass
        # print(f'mouseMoveEvent')
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().mouseMoveEvent(event)
                return
        


    def mousePressEvent(self, event):
        # print(f"mousePressEvent BaseGridItem = {event.pos()}")
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().mousePressEvent(event)
                return
        self.grid_item_clicked()
        # self.scene().focus_item_changed.emit((self))
        # if event.button() == Qt.RightButton:
        #     self.create_video()

    def mouseDoubleClickEvent(self, event):
        # print(f"mouseDoubleClickEvent! = {self.row,self.col}")
        pass

    def dropEvent(self,event):
        # logger.debug(f"dropEvent BaseGridItem = {self.row,self.col}")
        scene = self.scene()
        mime_data = event.mimeData()
        # print(f"dropEvent BaseGridItem = {mime_data.objectName()}")
        if mime_data.hasText() and mime_data.objectName() == TreeType.Camera:
            text = mime_data.text()
            event.acceptProposedAction()
            
            camera_list = camera_model_manager.get_camera_list()
            model = None
            for camera_model in camera_list:
                if text == camera_model.data.name:
                    model = camera_model
                    break
            if model is not None:
                tab_model:TabModel = scene.tab_model
                if tab_model is not None:
                    key = (self.row,self.col)
                    grid_item = tab_model.list_grid_item.get(key,None)
                    if grid_item is None:
                        grid_item = GridItem(index=key, type=ItemType.Camera, row=self.row,col=self.col,model = model)
                        tab_model.add_grid_item(item = grid_item)
                        tab_model.add_grid_item_signal.emit((key))
                    else:
                        if grid_item.model == model:
                            return
                        else:
                            if grid_item.type == ItemType.Floor and isinstance(grid_item.widget, BaseProxyWidget):
                                map_item_widget = grid_item.widget.widget()
                                if map_item_widget.editable:
                                    map_item_widget.scene.dropEvent(event)
                            else:
                                tab_model.remove_grid_item_signal.emit(key)
                                tab_model.remove_grid_item(grid_item)
                                tab_model.update_qsetting_signal.emit()
                                grid_item = GridItem(index=key, type=ItemType.Camera, row=self.row,col=self.col,model = model)
                                tab_model.add_grid_item(item = grid_item)
                                tab_model.add_grid_item_signal.emit((key))
            self.setBrush(QColor(Style.PrimaryColor.on_background))
        elif mime_data.hasText() and mime_data.objectName() == TreeType.Group:
            text = mime_data.text()
            event.acceptProposedAction()
            list_id = []
            group_list = group_model_manager.get_group_list()
            camera_list = camera_model_manager.get_camera_list()
            tab_model:TabModel = scene.tab_model
            for index, item in enumerate(group_list):
                if text == item.data.name:
                    for id in item.data.cameraIds:
                        list_id.append(id)
                    # list_camera = main_controller.get_all_camera_in_group(
                    #     item.id)
                    list_camera= []
                    if len(list_id) > 0:
                        for camera in camera_list:
                            if camera.data.id in list_id:
                                list_camera.append(camera)
                        tab_model.add_group_signal.emit(list_camera)
            self.setBrush(QColor(Style.PrimaryColor.on_background))
        elif mime_data.hasText() and mime_data.objectName() == "Multi_selection_item":
            tab_model: TabModel = scene.tab_model
            mime_text = mime_data.text()
            event.acceptProposedAction()
            retrieved_byte_array = mime_data.data("application/multidata")
            retrieved_data_dict = pickle.loads(retrieved_byte_array)
            list_camera_name = []
            list_group_name = []
            for item, tree_type in retrieved_data_dict.items():
                if tree_type == TreeType.Camera:
                    list_camera_name.append(item)
                    pass
                elif tree_type == TreeType.Group:
                    list_group_name.append(item)
            group_list = group_model_manager.get_group_list()
            camera_list = camera_model_manager.get_camera_list()
            list_camera = []
            list_camera_id = []
            for index, group_item in enumerate(group_list):
                for group_name in list_group_name:
                    if group_name == group_item.data.name:
                        for camera_id in group_item.data.cameraIds:
                            list_camera_id.append(camera_id)
                        if len(list_camera_id) > 0:
                            for camera in camera_list:
                                if camera.data.id in list_camera_id:
                                    list_camera.append(camera)
            for idx, camera_item in enumerate(camera_list):
                for camera_name in list_camera_name:
                    if camera_name == camera_item.data.name and camera_item not in list_camera:
                        list_camera.append(camera_item)
            tab_model.add_group_signal.emit(list_camera)
            self.setBrush(QColor(Style.PrimaryColor.on_background))
        elif mime_data.hasText() and mime_data.objectName() == 'event':
            text = mime_data.text()
            event.acceptProposedAction()
            model = event_manager.get_event(text)

            if model is not None:
                tab_model:TabModel = scene.tab_model
                # grid_item = tab_model.list_grid_item.get(self.index,None)
                if tab_model is not None:
                    key = (self.row,self.col)
                    grid_item = tab_model.list_grid_item.get(key,None)
                    if grid_item is None:
                        grid_item = GridItem(index=key, type=ItemType.Event, row=self.row,col=self.col,model = model)
                        tab_model.add_grid_item(item = grid_item)
                        tab_model.add_grid_item_signal.emit((key))
                    else:
                        if grid_item.model == model:
                            return
                        else:
                            tab_model.remove_grid_item_signal.emit(key)
                            tab_model.remove_grid_item(grid_item)
                            tab_model.update_qsetting_signal.emit()
                            grid_item = GridItem(index=key, type=ItemType.Event, row=self.row,col=self.col,model = model)
                            tab_model.add_grid_item(item = grid_item)
                            tab_model.add_grid_item_signal.emit((key))
            self.setBrush(QColor(Style.PrimaryColor.on_background))
        elif mime_data.hasText() and mime_data.objectName() == 'Map_Item':
            text = mime_data.text()
            event.acceptProposedAction()
            model = None
            for map in main_controller.list_item_map_model:
                if map.name == text:
                    model = map
                    break
            if model is not None:
                tab_model:TabModel = scene.tab_model
                # grid_item = tab_model.list_grid_item.get(self.index,None)
                if tab_model is not None:
                    key = (self.row,self.col)
                    grid_item = tab_model.list_grid_item.get(key,None)
                    if grid_item is None:
                        grid_item = GridItem(index=key, type=ItemType.Floor, row=self.row,col=self.col,model = model)
                        tab_model.add_grid_item(item = grid_item)
                        tab_model.add_grid_item_signal.emit((key))
                    else:
                        if grid_item.model == model:
                            return
                        else:
                            tab_model.remove_grid_item_signal.emit(key)
                            tab_model.remove_grid_item(grid_item)
                            tab_model.update_qsetting_signal.emit()
                            grid_item = GridItem(index=key, type=ItemType.Floor, row=self.row,col=self.col,model = model)
                            tab_model.add_grid_item(item = grid_item)
                            tab_model.add_grid_item_signal.emit((key))
            self.setBrush(QColor(Style.PrimaryColor.on_background))
        elif mime_data.objectName() == "swap_item":
            event.acceptProposedAction()
            data = mime_data.data("application/position")
            retrieved_data_list = pickle.loads(data)
            retrieved_row, retrieved_col = retrieved_data_list
            if self.row != retrieved_row or self.col != retrieved_col:
                scene = self.scene()
                # items = scene.items()
                tab_model = scene.tab_model
                tab_model.swap_grid_item_signal.emit(((self.row,self.col),(retrieved_row,retrieved_col)))
            else:
                self.update_widget_position()
            self.setBrush(QColor(Style.PrimaryColor.on_background))

        elif event.mimeData().hasUrls():
            urls = [url.toLocalFile() for url in event.mimeData().urls()]
            # tạm thời chỉ xử lý url đầu tiên trong danh sách urls
            if len(urls) > 0:
                url = urls[0]
                # logger.debug(f"dropEvent GridItem1 = {model}")
                tab_model:TabModel = scene.tab_model
                if tab_model is not None:
                    # logger.debug(f"dropEvent GridItem2 = {model}")
                    key = (self.row,self.col)
                    grid_item = tab_model.list_grid_item.get(key,None)
                    if grid_item is None:
                        grid_item = GridItem(index=key, type=ItemType.VideoLocal, row=self.row,col=self.col,model = url)
                        tab_model.add_grid_item(item = grid_item)
                        tab_model.add_grid_item_signal.emit((key))

            logger.debug(f"ahihi = {urls}")
            self.setBrush(QColor(Style.PrimaryColor.on_background))
            # self.label.setText("\n".join(urls))
        # logger.debug(f"dropEvent BaseGridItem1 = {self.row,self.col}")
        scene.widget_focused = None
        self.grid_item_clicked()  

    def update_widget_position(self):
        type = self.resgistered.get("type", None)
        if type is not None:
            if type == WidgetType.NormalWidget:
                data = self.resgistered.get("data",None)
                graphics_widget = data.get('widget',None)
                graphics_widget.setGeometry(self.rect().x() + self.padding, self.rect().y() + self.padding, self.rect().width() -2*self.padding, self.rect().height() - 2*self.padding)
                graphics_widget.update_resize(self.rect().width()-2, self.rect().height() - 2)
                graphics_widget.opacity_effect.setOpacity(1)
                return graphics_widget
            elif type == WidgetType.VideoWidget:
                data = self.resgistered.get("data",None)
                video_proxy_widget = data.get('widget',None)
                video_proxy_widget.video_item.setSize(QSize(self.rect().width()-2*self.padding, self.rect().height()-2*self.padding))  # Thiết lập kích thước hiển thị của video 2
                video_proxy_widget.video_item.setOffset(QPoint(self.rect().x()+self.padding,self.rect().y()+self.padding))  
                video_proxy_widget.setGeometry(self.rect().x()+self.padding, self.rect().y() + self.padding, self.rect().width() -2*self.padding, self.rect().height() - 2*self.padding)
                video_proxy_widget.update_resize(self.rect().width() -2*self.padding, self.rect().height() - 2*self.padding)
                video_proxy_widget.video_item.opacity_effect.setOpacity(1)
                return video_proxy_widget
        return None
    
    def update_focus_item_position(self):
        self.scene().focus_item.setRect(self.rect().x(),self.rect().y(),self.rect().width(),self.rect().height())

    def update_position(self):
        scene = self.scene()
        if not self.is_grid_selected:
            rect = QRectF(self.col *scene.grid_width_size+ self.col*scene.spacing + scene.padding, self.row * scene.grid_height_size+ self.row *scene.spacing + scene.padding, scene.grid_width_size, scene.grid_height_size)
            self.setRect(rect)
            # scene.focus_item.setRect(rect.x(),rect.y(),rect.width(),rect.height())
            type = self.resgistered.get("type", None)
            if type is not None:
                if type == WidgetType.NormalWidget:
                    data = self.resgistered.get("data",None)
                    graphics_widget = data.get('widget',None)
                    graphics_widget.setGeometry(rect.x() + self.padding, rect.y() + self.padding, scene.grid_width_size -2*self.padding, scene.grid_height_size - 2*self.padding)
                    graphics_widget.update_resize(scene.grid_width_size -2*self.padding, scene.grid_height_size - 2*self.padding)
                    graphics_widget.setZValue(0)
                elif type == WidgetType.VideoWidget:
                    data = self.resgistered.get("data",None)
                    video_proxy_widget = data.get('widget',None)
                    video_proxy_widget.video_item.setSize(QSize(rect.width()-2*self.padding, rect.height()-2*self.padding))  # Thiết lập kích thước hiển thị của video 2
                    video_proxy_widget.video_item.setOffset(QPoint(rect.x()+self.padding,rect.y()+self.padding))  
                    video_proxy_widget.widget().setGeometry(rect.x() + self.padding, rect.y() + self.padding, scene.grid_width_size -2*self.padding, scene.grid_height_size - 2*self.padding)
                    video_proxy_widget.update_resize(scene.grid_width_size -2*self.padding, scene.grid_height_size - 2*self.padding)
                    video_proxy_widget.setZValue(0)
        else:
            rect = QRectF(self.col *scene.grid_width_size + self.col *scene.spacing + scene.padding, self.row  * scene.grid_height_size+ self.row *scene.spacing + scene.padding, scene.grid_width_size * self.length_w + (self.length_w -self.padding)*scene.spacing, scene.grid_height_size * self.length_h + (self.length_h -self.padding)*scene.spacing)
            self.setRect(rect)
            # scene.focus_item.setRect(rect.x(),rect.y(),rect.width(),rect.height())
            type = self.resgistered.get("type", None)
            if type is not None:
                if type == WidgetType.NormalWidget:
                    data = self.resgistered.get("data",None)
                    graphics_widget = data.get('widget',None)
                    graphics_widget.setGeometry(rect.x() + self.padding, rect.y() + self.padding, rect.width() - 2*self.padding, rect.height() - 2*self.padding)
                    graphics_widget.update_resize(rect.width() -2*self.padding, rect.height() - 2*self.padding)
                    graphics_widget.setZValue(0)
                elif type == WidgetType.VideoWidget:
                    data = self.resgistered.get("data",None)
                    video_proxy_widget = data.get('widget',None)
                    video_proxy_widget.video_item.setSize(QSize(rect.width()-2*self.padding, rect.height()-2*self.padding))  # Thiết lập kích thước hiển thị của video 2
                    video_proxy_widget.video_item.setOffset(QPoint(rect.x()+self.padding,rect.y()+self.padding))  
                    video_proxy_widget.widget().setGeometry(rect.x() + self.padding, rect.y() + self.padding, scene.grid_width_size -2*self.padding, scene.grid_height_size - 2*self.padding)
                    video_proxy_widget.widget().update_resize(scene.grid_width_size -2*self.padding, 48)
                    video_proxy_widget.setZValue(0)
    def get_target_rect(self):
        scene = self.scene()
        if not self.is_grid_selected:
            rect = QRectF(self.col *scene.grid_width_size+ self.col*scene.spacing + scene.padding, self.row * scene.grid_height_size+ self.row *scene.spacing + scene.padding, scene.grid_width_size, scene.grid_height_size)
            # self.setRect(rect)
        else:
            rect = QRectF(self.col *scene.grid_width_size + self.col *scene.spacing + scene.padding, self.row  * scene.grid_height_size+ self.row *scene.spacing + scene.padding, scene.grid_width_size * self.length_w + (self.length_w -self.padding)*scene.spacing, scene.grid_height_size * self.length_h + (self.length_h -self.padding)*scene.spacing)
            # self.setRect(rect)
        return rect
    def fullscreen(self,enable):
        scene = self.scene()
        type = self.resgistered['type']
        # graphics_widget = self.resgistered['data']['widget']
        if not enable:
            print(f'FULL to NORMAL {self.row,self.col}')
            # exit fullscreen
            self.is_fullscreen = False
            focus_item: FocusItem = self.scene().focus_item
            # target_rect = QRectF(self.col * scene.grid_width_size + (self.col + 1) * scene.spacing + scene.padding,
            #                  self.row * scene.grid_height_size + (self.row + 1) * scene.spacing + scene.padding,
            #                  scene.grid_width_size, scene.grid_height_size)
            # print(f'NORMAL {target_rect}')
            target_rect = self.get_target_rect()
            def animation_finished():
                graphics_widget = self.update_widget_position()
                graphics_widget.setZValue(0)
                focus_item.setRect(self.rect().x(),self.rect().y(),self.rect().width(),self.rect().height())
                self.setZValue(0)
            self.animate_rect_change(target_item=self, start_rect=self.rect(), end_rect=target_rect, animation_finished=animation_finished, is_expanding=False)
            
        else:
            print(f'NORMAL to FULL {self.row,self.col}')
            self.is_fullscreen = True
            self.setZValue(1)
            focus_item: FocusItem = self.scene().focus_item
            # rect = QRectF(scene.sceneRect().x() * scene.spacing + scene.padding + 1,
            #           scene.sceneRect().y() * scene.spacing + scene.padding + 1,
            #           scene.sceneRect().width() * scene.spacing + scene.padding - 5,
            #           scene.sceneRect().height() * scene.spacing + scene.padding - 5)
            rect = QRectF(scene.sceneRect().x() + scene.padding,
                      scene.sceneRect().y() + scene.padding,
                      scene.sceneRect().width() - 2*scene.padding,
                      scene.sceneRect().height() - 2*scene.padding)

            def animation_finished():
                graphics_widget = self.update_widget_position()
                focus_item.setRect(self.rect().x(),self.rect().y(),self.rect().width(),self.rect().height())
                graphics_widget.setZValue(1)
            self.animate_rect_change(target_item=self, start_rect=self.rect(), end_rect=rect, animation_finished=animation_finished, is_expanding=True)

    
    def animate_rect_change(self, target_item, start_rect, end_rect, duration=250, animation_finished=None, is_expanding=False):
        # print(f'Animating from start_rect = {start_rect} to end_rect = {end_rect}')
        # Create the QTimeLine object for animation
        # Configure QTimeLine for 60 FPS
        frame_count = 480 * (duration / 1000.0)  # Number of frames for the given duration
        timer = QTimeLine(duration)
        timer.setFrameRange(0, frame_count)
        timer.setUpdateInterval(1000 / 480)  # Set update interval for approximately 60 FPS

        # Create the QGraphicsItemAnimation object
        animation = QGraphicsItemAnimation()
        animation.setItem(target_item)
        animation.setTimeLine(timer)
        
        # Update the position over time
        for i in range(int(frame_count)+1):  # Include 101 to cover the end value
            progress = i / float(frame_count)
            x = start_rect.x() + (end_rect.x() - start_rect.x()) * progress
            y = start_rect.y() + (end_rect.y() - start_rect.y()) * progress
            width = start_rect.width() + (end_rect.width() - start_rect.width()) * progress
            height = start_rect.height() + (end_rect.height() - start_rect.height()) * progress
            
            # Set position and size at the current progress
            animation.setPosAt(progress, QPointF(x, y))
            animation.setScaleAt(progress, width / start_rect.width(), height / start_rect.height())

        # Optional: Connect the frameChanged signal for additional updates
        def update_size(frame):
            progress = frame / float(frame_count)
            x = start_rect.x() + (end_rect.x() - start_rect.x()) * progress
            y = start_rect.y() + (end_rect.y() - start_rect.y()) * progress
            width = start_rect.width() + (end_rect.width() - start_rect.width()) * progress
            height = start_rect.height() + (end_rect.height() - start_rect.height()) * progress
            
            # Update position and size in real-time
            target_item.setRect(QRectF(x, y, width, height))
            # print(f'target_item: {type(target_item)} Frame = {frame}, x = {x}, y = {y}, width = {width}, height = {height}')
            # Update widget position and size
            graphics_widget = self.update_widget_position()
            self.scene().focus_item.setRect(QRectF(x, y, width, height))
            graphics_widget.setZValue(1)


        # Connect the frameChanged signal to the update_size function
        timer.frameChanged.connect(update_size)
        # animation_finished
        if animation_finished is not None:
            timer.finished.connect(animation_finished)

        # Set easing curve for smoother transitions
        timer.setEasingCurve(QEasingCurve.InOutQuad)
        timer.start()


    def grid_item_clicked(self):
        # logger.debug(f'grid_item_selected = {grid_item_selected.data}')
        focus_item = self.scene().focus_item
        scene = grid_item_selected.data['scene']
        grid_item = grid_item_selected.data['grid_item']
        if scene is None:
            # Case chưa focus vào đâu
            # logger.debug(f'grid_item_selected1')
            focus_item.item = self
            focus_item.show_border(True)
            grid_item_selected.set_data(screen=self.scene().screen_name,scene = self.scene(),grid_item=self)
            widget = self.widget()
            if widget is not None and hasattr(widget, 'grid_item_clicked'):  
                widget.grid_item_clicked()
        else:
            if scene == self.scene():
                # Case đã focus vào item nào đó trong cùng 1 tab
                # Cần bỏ focus ở item cũ -> truy cập đến widget cũ để xử lý
                # logger.debug(f'grid_item_selected2')
                if grid_item == self:
                    focus_item.show_border(True)
                    # trùng item
                    # logger.debug(f'grid_item_clicked trùng item')
                else:
                    grid_item.grid_item_unclicked()
                    focus_item.item = self
                    focus_item.show_border(True)
                    grid_item_selected.set_data(screen=self.scene().screen_name, scene= self.scene(),grid_item=self)
                    widget = self.widget()
                    if widget is not None and hasattr(widget, 'grid_item_clicked'):  
                        widget.grid_item_clicked()
            else:
                # Case đã focus vào item nào đó ở tab khác
                # Cần bỏ focus ở item cũ -> truy cập đến widget cũ để xử lý
                logger.debug(f'grid_item_selected3')
                grid_item.grid_item_unclicked()
                focus_item.item = self
                focus_item.show_border(True)
                grid_item_selected.set_data(screen=self.scene().screen_name,scene = self.scene(),grid_item=self)
                widget = self.widget()
                if widget is not None and hasattr(widget, 'grid_item_clicked'):  
                    widget.grid_item_clicked()
        self.scene().grid_item_clicked_signal.emit((self,True))

    def grid_item_unclicked(self):
        scene = grid_item_selected.data['scene']
        grid_item = grid_item_selected.data['grid_item']
        if grid_item is not None:
            widget = grid_item.widget()
            if widget is not None and hasattr(widget, 'grid_item_unclicked'):
                widget.grid_item_unclicked()
            self.scene().grid_item_clicked_signal.emit((self,False))
        scene.focus_item.show_border(False)
        

    def widget(self):
        widget = self.resgistered['data']['widget']
        if widget is not None:
            return widget.widget()
        return None
    
    def set_drag_move_event(self,enable):
        widget = self.resgistered['data']['widget']
        if widget is not None:
            widget.is_move = enable

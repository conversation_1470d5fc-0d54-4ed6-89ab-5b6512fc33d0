
from PySide6.QtWidgets import QGraphicsItem,QGraphicsOpacityEffect,QGraphicsProxyWidget, QGraphicsSceneMouseEvent
from PySide6.QtCore import Qt, QMimeData
from PySide6.QtGui import QDrag
import pickle
import logging

from src.common.widget.camera_widget import CameraWidget
from src.presentation.camera_screen.map.map_widget_item import Map2DWidgetItem
logger = logging.getLogger(__name__)

class BaseProxyWidget(QGraphicsProxyWidget):
    def __init__(self, parent=None,row = 0,col = 0,base_grid_item = None):
        super().__init__(parent)
        self.row = row
        self.col = col
        self.base_grid_item = base_grid_item
        self.grid_item = None
        self.drag = None
        self.is_move = True
        self.setAcceptDrops(True)
        self.setAcceptHoverEvents(True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsMovable, True)
        self.mouse_press_pos = None
        self.opacity_effect = QGraphicsOpacityEffect(self)
        self.setGraphicsEffect(self.opacity_effect)
        self.opacity_effect.setOpacity(1)

    def set_grid_item(self,grid_item = None):
        self.grid_item = grid_item

    def set_base_grid_item(self,base_grid_item = None):
        self.base_grid_item = base_grid_item

    def mouseReleaseEvent(self, event: QGraphicsSceneMouseEvent) -> None:
        # logger.debug(f"mouseReleaseEvent BaseProxyWidget")
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().mouseReleaseEvent(event)
                return
        # self.setZValue(0)
        scene = self.scene()
        scene.widget_focused = None
        return super().mouseReleaseEvent(event)
    
    def mouseMoveEvent(self, event: QGraphicsSceneMouseEvent) -> None:
        # logger.debug(f"mouseMoveEvent BaseProxyWidget")
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().mouseMoveEvent(event)
                return
        if not self.is_move:
            self.drag = None
            
        if self.drag is not None:
            # check self.drag not deleted
            self.drag.exec()
        return super().mouseMoveEvent(event)
    
    def mousePressEvent(self, event):
        print(f'mousePressEvent BaseProxyWidget {self.widget().btn_close.is_hover}')
        if self.widget().btn_close.is_hover:
            self.widget().btn_close_clicked()
            return False
        if self.widget().btn_full_screen.is_hover:
            return super().mousePressEvent(event)

        if isinstance(self.widget(), CameraWidget):
            # check self.widget has attribute btn_crop_frame
            if hasattr(self.widget(), 'btn_crop_frame') and self.widget().btn_crop_frame.is_hover:
                return super().mousePressEvent(event)
            if hasattr(self.widget(), 'btn_ptz') and self.widget().btn_ptz.is_hover:
                return super().mousePressEvent(event)
            if hasattr(self.widget(), 'btn_3_d_locate') and self.widget().btn_3_d_locate.is_hover:
                return super().mousePressEvent(event)
            if hasattr(self.widget(), 'btn_ptz_arrow') and self.widget().btn_ptz_arrow.is_hover:
                return super().mousePressEvent(event)

        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().mousePressEvent(event)
                return
        # self.setZValue(1)
        scene = self.scene()
        scene.widget_focused = self
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().mousePressEvent(event)
                return

        # self.setZValue(scene.itemsBoundingRect().topLeft().y() - 1)
        if event.button() == Qt.LeftButton:
            self.mouse_press_pos = event.pos()
            position = (self.row, self.col)
            data_bytes = pickle.dumps(position)
            self.drag = QDrag(self.scene().views()[0])
            mime_data = QMimeData()
            mime_data.setObjectName("swap_item")
            mime_data.setData("application/position", data_bytes)
            self.drag.setMimeData(mime_data)
        #     self.drag.exec(Qt.MoveAction)
        return super().mousePressEvent(event)
    
    def dragEnterEvent(self, event):
        # print(f'dragEnterEvent BaseProxyWidget {self.widget()}')
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().dragEnterEvent(event)
                return
        event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        # print(f'dragLeaveEvent BaseProxyWidget {self.widget()}')
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().dragLeaveEvent(event)
                return
        event.accept()

    def dragMoveEvent(self, event):
        # print(f'dragMoveEvent BaseProxyWidget {self.widget()}')
        if isinstance(self.widget(), Map2DWidgetItem):
            if self.widget().editable:
                self.widget().dragMoveEvent(event)
                return
        if self.is_move:
            self.setZValue(1)
            pos = event.scenePos()
            delta = pos-self.mouse_press_pos
            self.setPos(delta)
            if self.scene().focus_item.item == self.base_grid_item:
                self.scene().focus_item.setRect(delta.x()-1,delta.y()-1,self.base_grid_item.rect().width(),self.base_grid_item.rect().height())
        event.accept()

    def resgister_grid_item(self,base_grid_item = None):
        # logger.debug(f'resgister_grid_item = {grid_item.row,grid_item.col}')
        self.base_grid_item = base_grid_item
        widget = self.widget()
        if hasattr(widget, 'set_grid_item'):
            widget.set_grid_item(base_grid_item = base_grid_item)

    def unresgister_grid_item(self):
        self.grid_item = None
        widget = self.widget()
        if hasattr(widget, 'grid_item'):
            widget.grid_item = None   

    # def resizeEvent(self, event):
    #     # logger.debug(f'resizeEvent')
    #     size = event.newSize()
    #     widget = self.widget()
    #     if widget is not None:
    #         logger.debug(f'resizeEvent BaseProxyWidget = {size.width(),size.height()}')
    #         if hasattr(widget, 'update_resize'):
    #             widget.update_resize(size.width(),size.height())
    #     super().resizeEvent(event)

    def update_resize(self,width,height):
        # logger.debug('update_resize BaseProxyWidget')
        widget = self.widget()
        if widget is not None:
            widget.update_resize(width,height)
from PySide6.QtWidgets import QApplication, QGraphicsScene, QGraphicsView, QGraphicsRectItem
from PySide6.QtGui import QColor,QPen
class FocusItem(QGraphicsRectItem):
    # drop_changed = Signal(tuple)
    def __init__(self, rect):
        super().__init__(rect)
        self.item = None
        self.initial_width = self.rect().width()
        self.initial_height = self.rect().height()

    def setRectSize(self, scale):
        new_width = self.initial_width * scale
        new_height = self.initial_height * scale
        self.setRect(-new_width / 2, -new_height / 2, new_width, new_height)

    def show_border(self,enable = True):
        if enable:
            self.setRect(self.item.rect())
            self.setVisible(True)
            border_color = QColor(181, 18, 46)  # Màu đỏ (RGB: 255, 0, 0)
            border_width = 1
            new_pen = QPen(border_color, border_width)
            self.setPen(new_pen)
        else:
            self.setVisible(False)
            self.item = None
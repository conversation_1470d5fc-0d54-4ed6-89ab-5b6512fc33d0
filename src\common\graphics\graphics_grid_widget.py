import sys
from PySide6.QtWidgets import QGraphicsScene, QGraphicsView, QW<PERSON>t,QVBoxLayout,QGraphicsRectItem,QGraphicsProxyWidget
from PySide6.QtCore import QRectF,QSize,QPoint,Signal,QTimer,Qt, QEvent
from PySide6.QtGui import QColor, QEnterEvent, QMouseEvent, QPainter
from src.common.graphics.focus_grid_item import FocusItem
from src.common.graphics.base_grid_item import BaseGridItem,WidgetType
from src.common.graphics.base_proxy_widget import BaseProxyWidget
from src.common.graphics.video_proxy_widget import VideoProxyWidget
from src.common.model.item_grid_model import ItemGridModel
from src.common.widget.button_state import ButtonState
from src.common.model.tab_model import TabModel,GridItem
from src.common.graphics.number_widget import NumberWidget
from src.common.key_board.key_board_manager import key_board_manager
from src.styles.style import Style
import pickle
# from PySide6.QtOpenGLWidgets import QOpenGLWidget
import logging
logger = logging.getLogger(__name__)

# class WidgetType:
#     Invalid = "Invalid"
#     NormalWidget = "NormalWidget"
#     CameraWidget = "CameraWidget"

class BaseScene(QGraphicsScene):
    # widget_focused = None
    grid_item_clicked_signal = Signal(tuple)
    def __init__(self, parent=None,num_grid = 1):
        super().__init__(parent)
        self.spacing = 1
        self.padding = 1
        self.grid_width_size = 20  # Kích thước ô lưới
        self.grid_height_size = 20
        self.num_columns = 3  # Số cột
        self.num_rows = 3  # Số hàng
        self.num_grid = num_grid
        self.list_items = []
        self.setSceneRect(0, 0, self.num_grid * self.grid_width_size, self.num_grid * self.grid_height_size)
        self.drawGrid()
        self.focus_item = self.create_focus_item()
        self.widget_focused = None
        self.tab_model = None
        self.screen_name = 'Main'
        self.num_screen = None
        self.graphics_widget = None
        self.create_number_screen()
        # self.removeItem()

    def dragEnterEvent(self, event):
        # logger.debug(f'dragEnterEvent BaseScene')
        # widget = self.widget_focused
        # if widget is not None:
        #     widget.drag = None
        #     # widget.base_grid_item.update_widget_position()
        #     widget.setAcceptDrops(True)
        event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        # logger.debug(f'dragLeaveEvent BaseScene = {self.widget_focused}')
        # Khi người dùng kéo item ra ngoài scene thì cập nhật luôn vị trí cũ cho nó
        widget = self.widget_focused
        if widget is not None:
            # widget.drag = None
            widget.base_grid_item.update_widget_position()
            widget.base_grid_item.update_focus_item_position()
            # widget.setAcceptDrops(False)
            # widget.base_grid_item.setBrush(QColor(Style.PrimaryColor.on_background))
            self.widget_focused = None
        for base_grid_item in self.list_items:
            if isinstance(base_grid_item,BaseGridItem) and base_grid_item.isVisible():
                base_grid_item.setBrush(QColor(Style.PrimaryColor.on_background))

        event.accept()



    def create_focus_item(self):
        rect = QRectF(self.spacing,self.spacing, self.grid_width_size, self.grid_height_size)
        item = FocusItem(rect)
        self.addItem(item)
        item.setVisible(False)
        item.setZValue(1)
        return item
    def set_spacing(self,num = 1):
        self.spacing = num

    def remove_item(self,widget = None):
        # logger.debug(f"gggggggggg {widget}")
        widget.base_grid_item.unregister_widget()
        self.removeItem(widget)

    def drawGrid(self):
        for row in range(self.num_grid):
            for col in range(self.num_grid):
                rect = QRectF(col * self.grid_width_size + col*self.spacing +self.padding, row * self.grid_height_size + (row +1) *self.spacing+self.padding, self.grid_width_size, self.grid_height_size)
                item = BaseGridItem(rect,row,col)
                self.addItem(item)
                self.addItem(item.graphics_widget)
                item.graphics_widget.setVisible(False)
                self.list_items.append(item)
        

    def create_number_screen(self):
        self.num_screen = NumberWidget(font_size='60px')
        self.graphics_widget = QGraphicsProxyWidget()
        self.graphics_widget.setWidget(self.num_screen)
        self.addItem(self.graphics_widget)
        self.graphics_widget.setVisible(False)

    def update_resize(self,width,height):
        items = self.items()
        for item in items:
            if isinstance(item,BaseGridItem) and item.isVisible():
                # rect = item.rect()
                if not item.is_grid_selected:
                    # trường hợp đang fullscreen mà resize thì cần tính toán lại rect của item fullscreen
                    if item.is_fullscreen:
                        rect = QRectF(self.sceneRect().x() + self.padding,self.sceneRect().y() + self.padding,self.sceneRect().width()  -2*self.padding,self.sceneRect().height() -2*self.padding)
                        item.setRect(rect)
                    else:
                        rect = QRectF(item.col *self.grid_width_size+ item.col*self.spacing+self.padding, item.row * self.grid_height_size+ item.row *self.spacing+self.padding, self.grid_width_size, self.grid_height_size)
                        item.setRect(rect)
                        # logger.debug(f'aaaaaaaaaaaa = {item.row,item.col,item.rect().width(),item.rect().height()}')
                    item.update_widget_position()
                    item.graphics_widget.setGeometry(item.rect().width()/2 + item.rect().x()-35, item.rect().height()/2 + item.rect().y() - 35, 70, 70)
                else:
                    # trường hợp đang fullscreen mà resize thì cần tính toán lại rect của item fullscreen
                    if item.is_fullscreen:
                        logger.debug(f'is_fullscreen = ')
                        rect = QRectF(self.sceneRect().x() + self.padding,self.sceneRect().y() + self.padding,self.sceneRect().width()  -2*self.padding,self.sceneRect().height() -2*self.padding)
                        item.setRect(rect)
                    else:
                        rect = QRectF(item.col *self.grid_width_size + item.col *self.spacing+self.padding, item.row  * self.grid_height_size+ item.row *self.spacing+self.padding, self.grid_width_size * item.length_w + (item.length_w -1)*self.spacing, self.grid_height_size * item.length_h + (item.length_h -1)*self.spacing)
                        item.setRect(rect)
                    item.update_widget_position()
                    item.graphics_widget.setGeometry(item.rect().width()/2 + item.rect().x()-35, item.rect().height()/2 + item.rect().y() - 35, 70, 70)
                    
        self.graphics_widget.setGeometry(self.sceneRect().width()/2 - 75, 0, 150, 150)
        if self.focus_item.item is not None:
            self.focus_item.setRect(self.focus_item.item.rect())

    def find_index(self, number):
        count = 0
        items = self.list_items
        for item in items:
            if isinstance(item,BaseGridItem) and item.isVisible():
                if count == number:
                    item.grid_item_clicked()
                    item.graphics_widget.widget().is_selected(enable = True)
                count += 1

    def show_index(self,index):
        count = 0
        items = self.list_items
        for item in items:
            if isinstance(item,BaseGridItem) and item.isVisible():
                item.graphics_widget.setZValue(1)
                item.graphics_widget.setVisible(True) 
                item.graphics_widget.widget().setText(str(count)) 
                count += 1   
        self.graphics_widget.setZValue(1)        
        self.graphics_widget.setVisible(True)   
        self.graphics_widget.widget().setText(str(index)) 
        timer = QTimer(self)
        timer.setInterval(5000)  
        timer.setSingleShot(True)
        timer.timeout.connect(self.hide_index)
        timer.start()
        key_board_manager.timer_list.append(timer)

    def hide_index(self):
        # logger.debug(f'hide_index')
        for item in self.items():
            if isinstance(item,BaseGridItem): 
                # logger.debug(f'hide_index1')
                item.graphics_widget.setVisible(False) 
                item.graphics_widget.widget().is_selected(enable = False)
        self.graphics_widget.setVisible(False) 
        self.graphics_widget.widget().is_selected(enable = False)
        if str(Qt.Key.Key_Alt) in key_board_manager.keys:
            key_board_manager.keys = {}

class BaseGraphicsView(QGraphicsView):
    # grid_changed_signal = Signal(str)
    def __init__(self,tab_model = None):
        super().__init__()
        self.current_grid = -1
        self.tab_model = tab_model
        # self.grid_changed_signal.connect(self.grid_changed)

    def set_tab_model(self,tab_model = None):
        self.tab_model:TabModel = tab_model
        scene = self.scene()
        scene.tab_model = tab_model
        # tab_model.scene = scene

    def set_screen_name(self,screen_name = 'Main'):
        scene = self.scene()
        scene.screen_name = screen_name

    def grid_changed(self,num_grid):
        
        self.change_grid(grid=num_grid)

    def find_base_grid_item(self,base_grid_item:BaseGridItem,item_grid_model:ItemGridModel,action):
        base_grid_item_selected = None
        if action == 2 or action == 8:
            list_items = []
            for col_item in range(item_grid_model.column):
                for row_item in range(item_grid_model.column):
                    item = self.is_visible(row_item,col_item,item_grid_model)
                    if item is not None:
                        list_items.append(item)

        elif action == 4 or action == 6:
            list_items = []
            for item in self.scene().list_items:
                if isinstance(item, BaseGridItem) and item.isVisible():
                    list_items.append(item)
        temp = []
        next_base_grid_item = False
        for item in list_items:
            if base_grid_item == item:
                if action == 4 or action == 8:
                    if len(temp) > 0:
                        base_grid_item_selected = temp[-1]
                        break
                elif action == 6 or action == 2:
                    next_base_grid_item = True
            else:
                if next_base_grid_item:
                    base_grid_item_selected = item
                    break
            temp.append(item)
        return base_grid_item_selected
    
    def is_visible(self,row,col,item_grid_model:ItemGridModel):
        count = item_grid_model.column*item_grid_model.column
        for idx,item in enumerate(self.scene().list_items):
            if idx < count and isinstance(item,BaseGridItem) and item.isVisible():
                if item.row == row and item.col == col:
                    return item
        return None
    
    def get_base_grid_item(self,index):
        row,col = index
        for item in self.scene().list_items:
            if isinstance(item,BaseGridItem) and item.isVisible():
                if item.row == row and item.col == col:
                    return item
        return None        
    def change_custom_grid(self,total_grid_count = 1,num_grid = 1,list_row_col = []):
        scene:BaseScene = self.scene()
        count = 0
        list_item = []
        items = scene.list_items
        for item in items:
            if isinstance(item,BaseGridItem):
                if count < total_grid_count:
                    list_item.append(item)
                    item.setVisible(True)
                    type = item.resgistered.get('type',None)
                    if type is not None:
                        # print(f'row-col = {item.row,item.col}')
                        if type == WidgetType.NormalWidget:
                            data = item.resgistered.get('data',None)
                            if data is not None:
                                graphics_widget = data.get('widget',None)
                                graphics_widget.setVisible(True)
                        elif type == WidgetType.VideoWidget:
                            data = item.resgistered.get('data',None)
                            if data is not None:
                                video_item = data.get("widget",None)
                                video_item.setVisible(True)
                else:
                    item.setVisible(False)
                    type = item.resgistered.get('type',None)
                    if type is not None:
                        if type == WidgetType.NormalWidget:
                            data = item.resgistered.get('data',None)
                            if data is not None:
                                graphics_widget = data.get('widget',None)
                                graphics_widget.setVisible(False)
                        elif type == WidgetType.VideoWidget:
                            data = item.resgistered.get('data',None)
                            if data is not None:
                                video_item = data.get("widget",None)
                                video_item.setVisible(False)
                count += 1
        scene.num_grid = num_grid  
        scene.grid_width_size = (self.sceneRect().width() - 2*scene.padding- (scene.num_grid - 1) *scene.spacing) /scene.num_grid 
        scene.grid_height_size = (self.sceneRect().height() -2*scene.padding -(scene.num_grid - 1) *scene.spacing) /scene.num_grid 
        list_data_checked = []
        for row in range(scene.num_grid):
            for col in range(scene.num_grid):
                check = False
                data_checked = None
                for key in list_row_col:
                    for row_col in key:
                        r,c = row_col
                        if row == r and col == c:
                            check = True
                            data_checked = key
                if not check:
                    item = list_item[0]
                    item.row = row
                    item.col = col
                    item.is_grid_selected = False
                    # item.setRect(col * scene.grid_width_size, row * scene.grid_height_size, scene.grid_width_size, scene.grid_height_size)  
                    rect = QRectF(col *scene.grid_width_size+ col*scene.spacing+scene.padding, row * scene.grid_height_size+ row *scene.spacing+scene.padding, scene.grid_width_size, scene.grid_height_size)
                    item.setRect(rect)
                    item.graphics_widget.setGeometry(item.rect().width()/2 + item.rect().x()-35, item.rect().height()/2 + item.rect().y() - 35, 70, 70)
                    graphics_widget = item.update_widget_position()
                    if isinstance(graphics_widget,BaseProxyWidget):
                        graphics_widget.row = row
                        graphics_widget.col = col
                    elif isinstance(graphics_widget,VideoProxyWidget):
                        graphics_widget.video_item.row = row
                        graphics_widget.video_item.col = col
                    # có thể item đang fullscreen mà người dùng change_grid thì cần update lại trạng thái is_fullscreen off
                    if item.is_fullscreen:
                        item.is_fullscreen = False
                    list_item.remove(list_item[0]) 

                    if scene.focus_item.item == item:
                        scene.focus_item.show_border(True)
                else:
                    pass
                    # tim data phu hop
                    if data_checked not in list_data_checked:
                        x = 0
                        y = 0
                        width = 0
                        height = 0
                        row_min = None
                        col_min = None
                        row_max = 0
                        col_max = 0
                        for row_col in data_checked:
                            # print(f'row_col = {row_col}')
                            r,c = row_col
                            if row_min is None or r < row_min:
                                row_min = r
                            if row_max is None or r > row_max:
                                row_max = r
                            if col_min is None or c < col_min:
                                col_min = c
                            if col_max is None or c > col_max:
                                col_max = c
                        item = list_item[0]
                        item.row = row_min
                        item.col = col_min
                        item.is_grid_selected = True
                        x = col_min *scene.grid_width_size+ col_min*scene.spacing+scene.padding
                        y = row_min * scene.grid_height_size+ row_min *scene.spacing+scene.padding
                        width = scene.grid_width_size * (col_max - col_min + 1) + (col_max - col_min) *scene.spacing
                        height = scene.grid_height_size * (row_max - row_min + 1) + (row_max - row_min) *scene.spacing
                        item.length_w = col_max - col_min + 1
                        item.length_h = row_max - row_min + 1
                        # rect = QRectF(col *scene.grid_width_size+ (col + 1)*scene.spacing, row * scene.grid_height_size+ (row + 1) *scene.spacing, scene.grid_width_size, scene.grid_height_size)
                        rect = QRectF(x,y,width,height)
                        logger.debug(f'ahihi = {rect}')
                        item.setRect(rect) 
                        item.graphics_widget.setGeometry(item.rect().width()/2 + item.rect().x()-35, item.rect().height()/2 + item.rect().y() - 35, 70, 70)
                        graphics_widget = item.update_widget_position()
                        if isinstance(graphics_widget,BaseProxyWidget):
                            graphics_widget.row = row
                            graphics_widget.col = col
                        elif isinstance(graphics_widget,VideoProxyWidget):
                            graphics_widget.video_item.row = row
                            graphics_widget.video_item.col = col 
                        # có thể item đang fullscreen mà người dùng change_grid thì cần update lại trạng thái is_fullscreen off
                        if item.is_fullscreen:
                            item.is_fullscreen = False
                        list_item.remove(list_item[0])  
                        list_data_checked.append(data_checked)
                        if scene.focus_item.item == item:
                            scene.focus_item.show_border(True)
        # if scene.focus_item.item is not None:
        #     scene.focus_item.show_border(True)

    def get_type(self,num_grid = '1x1'):
        if num_grid == '1x1':
            return 1
        elif num_grid == '2x2':
            return 2
        elif num_grid == '3x3':
            return 3
        elif num_grid == '4x4':
            return 4
        elif num_grid == '5x5':
            return 5
        elif num_grid == '6x6':
            return 6
        elif num_grid == '8x8':
            return 8
        # elif num_grid == 'custom_6x':
        #     return 64

    def change_grid(self,data):
        # logger.debug(f'change_grid = {data}')
        # self.tab_model.list_grid_item = {}
        scene:BaseScene = self.scene()
        if scene.focus_item.item is not None:
            scene.focus_item.setVisible(False)

        item_grid_model:ItemGridModel = data
        # logger.debug(f'change_grid = {item_grid_model.grid_type,item_grid_model}')
        if item_grid_model.divisions_type == ButtonState.DivisionType.CUSTOM_DIVISIONS:
            self.change_custom_grid(total_grid_count=item_grid_model.divisions,num_grid=item_grid_model.row,list_row_col=item_grid_model.data)
        else:
            # logger.debug(f'change_grid = {item_grid_model.grid_type}')
            num_grid = self.get_type(num_grid = item_grid_model.grid_type)
            self.current_grid = num_grid
            # scene:BaseScene = self.scene()
            count = 0
            list_item = []
            items = scene.list_items
            for item in items:
                if isinstance(item,BaseGridItem):
                    item.is_grid_selected = False
                    if count < num_grid * num_grid:
                        list_item.append(item)
                        item.setVisible(True)
                        type = item.resgistered.get('type',None)
                        if type is not None:
                            # print(f'row-col = {item.row,item.col}')
                            if type == WidgetType.NormalWidget:
                                data = item.resgistered.get('data',None)
                                if data is not None:
                                    graphics_widget = data.get('widget',None)
                                    graphics_widget.setVisible(True)
                            elif type == WidgetType.VideoWidget:
                                data = item.resgistered.get('data',None)
                                if data is not None:
                                    video_item = data.get("widget",None)
                                    video_item.setVisible(True)
                    else:
                        item.setVisible(False)
                        type = item.resgistered.get('type',None)
                        if type is not None:
                            if type == WidgetType.NormalWidget:
                                data = item.resgistered.get('data',None)
                                if data is not None:
                                    graphics_widget = data.get('widget',None)
                                    graphics_widget.setVisible(False)
                            elif type == WidgetType.VideoWidget:
                                data = item.resgistered.get('data',None)
                                if data is not None:
                                    video_item = data.get("widget",None)
                                    video_item.setVisible(False)
                    count += 1
            scene.num_grid = num_grid  
            scene.grid_width_size = (self.sceneRect().width()-2*scene.padding - (scene.num_grid - 1) *scene.spacing) /scene.num_grid 
            scene.grid_height_size = (self.sceneRect().height() -2*scene.padding- (scene.num_grid - 1) *scene.spacing) /scene.num_grid 
            for row in range(scene.num_grid):
                for col in range(scene.num_grid):
                    item = list_item[0]
                    item.row = row
                    item.col = col
                    # item.is_grid_selected = False
                    type = item.resgistered.get('type',None)
                    rect = QRectF(col *scene.grid_width_size+ col*scene.spacing+scene.padding, row * scene.grid_height_size+ row *scene.spacing+scene.padding, scene.grid_width_size, scene.grid_height_size)
                    # item.setRect(col * scene.grid_width_size, row * scene.grid_height_size, scene.grid_width_size, scene.grid_height_size)
                    item.setRect(rect)
                    item.graphics_widget.setGeometry(item.rect().width()/2 + item.rect().x()-35, item.rect().height()/2 + item.rect().y() - 35, 70, 70)
                    # logger.debug(f'rect = {rect}')
                    graphics_widget = item.update_widget_position()
                    if isinstance(graphics_widget,BaseProxyWidget):
                        graphics_widget.row = row
                        graphics_widget.col = col
                    elif isinstance(graphics_widget,VideoProxyWidget):
                        graphics_widget.video_item.row = row
                        graphics_widget.video_item.col = col
                    # có thể item đang fullscreen mà người dùng change_grid thì cần update lại trạng thái is_fullscreen off
                    if item.is_fullscreen:
                        item.is_fullscreen = False
                    list_item.remove(list_item[0])

                    # update focusitem
                    if scene.focus_item.item == item:
                        scene.focus_item.show_border(True)
            # if scene.focus_item.item is not None:
            #     scene.focus_item.show_border(True)

    def resizeEvent(self, event):
        # logger.debug(f'resizeEvent = {event}')
        scene = self.scene()
        size = event.size()
        scene.grid_width_size = (size.width()-2*scene.padding - (scene.num_grid - 1) *scene.spacing) /scene.num_grid 
        scene.grid_height_size = (size.height() -2*scene.padding - (scene.num_grid - 1) *scene.spacing) /scene.num_grid 
        # scene.setSceneRect(0, 0, scene.num_grid * scene.grid_width_size, scene.num_grid * scene.grid_height_size)
        # scene.setSceneRect(scene.padding, scene.padding, size.width() - 2*scene.padding, size.height() - 2*scene.padding)
        scene.setSceneRect(0, 0, size.width(), size.height())
        if scene is not None:
            scene.update_resize(size.width(),size.height())
        super().resizeEvent(event)


# class OpenGLViewport(QOpenGLWidget):
#     def __init__(self, parent=None):
#         super(OpenGLViewport, self).__init__(parent)
#         self.setMouseTracking(True)
#         self.setFocusPolicy(Qt.StrongFocus)
        
class GraphicsGridWidget(QWidget):
    grid_changed_signal = Signal(tuple)
    def __init__(self, parent=None,max_num_grid = 10):
        super().__init__(parent)
        self.max_num_grid = max_num_grid
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0,0,0,0)
        self.scene = BaseScene(num_grid=self.max_num_grid)
        self.graphicsview = BaseGraphicsView()
        self.grid_changed_signal.connect(self.graphicsview.change_grid)
        self.graphicsview.setScene(self.scene)
        # self.opengl_viewport = OpenGLViewport(self.graphicsview)
        # self.graphicsview.setViewport(self.opengl_viewport)
        self.graphicsview.setCacheMode(QGraphicsView.CacheModeFlag.CacheBackground)
        self.graphicsview.setRenderHint(QPainter.RenderHint.Antialiasing)
        self.graphicsview.setBackgroundBrush(QColor(Style.PrimaryColor.on_background))
        self.graphicsview.setMouseTracking(True)
        self.graphicsview.setAcceptDrops(True)
        # self.graphicsview.setStyleSheet("border:1px solid #FFFFFF")
        # self.scene.setBackgroundBrush(QColor("#CCCCCC"))
        main_layout.addWidget(self.graphicsview)

    def set_spacing(self,num = 1):
        self.scene.set_spacing(num= num)

    def set_tab_model(self,tab_model = None):
        self.graphicsview.set_tab_model(tab_model=tab_model)

    def set_screen_name(self,screen_name = None):
        self.graphicsview.set_screen_name(screen_name=screen_name)

    def create_grid(self,num_grid = 1):
            # scene:CustomScene = self.scene()
            # items = scene.items()
            count = 0
            list_item = []
            items = self.scene.list_items
            for item in items:
                if isinstance(item,BaseGridItem):
                    if count < num_grid * num_grid:
                        list_item.append(item)
                        item.setVisible(True)
                    else:
                        item.setVisible(False)
                    count += 1
            self.scene.num_grid = num_grid  
            self.scene.grid_width_size = self.width()/self.scene.num_grid
            self.scene.grid_height_size = self.height()/self.scene.num_grid
            for row in range(self.scene.num_grid):
                for col in range(self.scene.num_grid):
                    item = list_item[0]
                    item.row = row
                    item.col = col
                    item.setRect(col * self.scene.grid_width_size, row * self.scene.grid_height_size, self.scene.grid_width_size, self.scene.grid_height_size)
                    list_item.remove(list_item[0])

    def get_scene(self):
        return self.scene
    
    def add_widget(self,widget = None, row = 0,col = 0):
        pass
        # grid_item = 

# if __name__ == "__main__":
#     app = QApplication(sys.argv)
#     example = GraphicsGridWidget()
#     example.create_grid(num_grid=8)
#     example.show()

#     sys.exit(app.exec())


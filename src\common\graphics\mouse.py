from PySide6.QtWidgets import <PERSON>Widget, Q<PERSON>abel
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>, QCursor, QPixmap, QMovie
from PySide6.QtCore import Qt, QPoint, QTimer
from src.styles.style import Style


class CustomCursorOverlay(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent, Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Define cursor images (.cur files)
        self.cursor_pixmaps = {
            'arrow': QCursor(QPixmap(Style.PrimaryImage.cursor)),
            'horizontal_resize': QCursor(QPixmap(Style.PrimaryImage.cursor_horizontal_resize)),
            'vertical_resize': QCursor(QPixmap(Style.PrimaryImage.cursor_vertical_resize)),
            'link': QCursor(QPixmap(Style.PrimaryImage.cursor_link)),
            'help': QCursor(QPixmap(Style.PrimaryImage.cursor_help)),
            'move': QCursor(QPixmap(Style.PrimaryImage.cursor_move)),
            'top_right': QCursor(QPixmap(Style.PrimaryImage.cursor_top_right_corner)),  # Bottom-left to top-right
            'top_left': QCursor(QPixmap(Style.PrimaryImage.cursor_top_left_corner)),  # Top-left to bottom-right
        }

        # Animated cursor
        # Animated cursor
        self.cursor_animation = QLabel(self)
        self.cursor_animation.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.cursor_animation.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        self.cursor_animation.setVisible(False)

        self.movie = QMovie(Style.PrimaryImage.cursor_busy)
        # self.movie.setFileName(Style.PrimaryImage.cursor_busy)  # Set animated GIF for busy state
        self.movie.setScaledSize(self.movie.currentPixmap().size())
        self.cursor_animation.setMovie(self.movie)
        self.cursor_animation.setGeometry(0, 0, self.movie.currentPixmap().size().width(), self.movie.currentPixmap().size().height())
        self.movie.start()

        self.current_cursor = self.cursor_pixmaps.get('arrow', QCursor())  # Default cursor
        self.setFixedSize(self.current_cursor.pixmap().size())

        # Set up a timer to periodically update the cursor position
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_cursor_position)
        self.timer.start(10)  # Update every 10 milliseconds

        # Set the widget to be transparent to mouse events
        self.setAttribute(Qt.WA_TransparentForMouseEvents)

        self.last_cursor_shape = None

    def update_cursor_position(self):
        """
        Update the position of the custom cursor overlay widget to follow the system cursor.
        """
        cursor_pos = QCursor.pos()
        self.move(cursor_pos - QPoint(self.width() // 2, self.height() // 2))
        self.update()  # Request a redraw
        
        """
        Check and respond to changes in the system cursor state.
        """
        # This is a dummy example; you would use your logic to detect cursor changes
        current_cursor_shape = QCursor().shape()
        # print(f"Current cursor shape: {current_cursor_shape}")
        if current_cursor_shape != self.last_cursor_shape:
            # Cursor state has changed
            self.last_cursor_shape = current_cursor_shape
            print(f"Cursor shape changed to: {current_cursor_shape}")

            # Update your custom cursor based on the detected state
            # Example: You can map system cursor shapes to your custom cursors
            self.update_custom_cursor()

    def update_custom_cursor(self):
        """
        Update the custom cursor based on detected state changes.
        """
        # Example: Change custom cursor based on detected system cursor state
        # You can use self.last_cursor_shape to map to a custom cursor state
        print(f"Updating custom cursor for shape: {self.last_cursor_shape}")
        if self.last_cursor_shape == Qt.CursorShape.SizeAllCursor:
            self.set_cursor_state('move')
        elif self.last_cursor_shape == Qt.CursorShape.PointingHandCursor:
            self.set_cursor_state('link')
        elif self.last_cursor_shape == Qt.CursorShape.SizeHorCursor:
            self.set_cursor_state('horizontal_resize')
        elif self.last_cursor_shape == Qt.CursorShape.SizeVerCursor:
            self.set_cursor_state('vertical_resize')
        elif self.last_cursor_shape == Qt.CursorShape.SizeBDiagCursor:
            self.set_cursor_state('top_right')
        elif self.last_cursor_shape == Qt.CursorShape.SizeFDiagCursor:
            self.set_cursor_state('top_left')
        elif self.last_cursor_shape == Qt.CursorShape.WaitCursor or self.last_cursor_shape == Qt.CursorShape.BusyCursor:
            self.set_cursor_state('busy')
        else:
            self.set_cursor_state('arrow')

    def paintEvent(self, event):
        """
        Paint the current cursor pixmap onto the widget.
        """
        painter = QPainter(self)
        painter.drawPixmap(0, 0, self.current_cursor.pixmap())

    def set_cursor_state(self, state):
        """
        Set the cursor state based on the given state key.

        :param state: The key for the desired cursor state (e.g., 'arrow', 'busy').
        """
        print(f"Setting cursor state to: {state}")
        if state in self.cursor_pixmaps:
            self.current_cursor = self.cursor_pixmaps[state]
            self.setFixedSize(self.current_cursor.pixmap().size())
            self.update()  # Request a redraw
            self.cursor_animation.setVisible(False)  # Hide animation if showing static cursor
        elif state == 'busy':
            # Show animation for busy state
            self.cursor_animation.setVisible(True)  # Show animation
        else:
            self.cursor_animation.setVisible(False)  # Hide animation if invalid state


    # def mouseMoveEvent(self, event):
    #     """
    #     Handle mouse movement events to change cursor state.
    #     """
    #     # get current state
    #     print(f"Mouse position: state {event}")
    #     if event.pos().x() < self.parent().width() // 2:
    #         self.set_cursor_state('arrow')
    #     elif event.pos().y() < self.parent().height() // 2:
    #         self.set_cursor_state('help')
    #     else:
    #         self.set_cursor_state('move')

    # def keyPressEvent(self, event):
    #     """
    #     Handle key press events to change cursor state.
    #     """
    #     if event.key() == Qt.Key_H:
    #         self.set_cursor_state('help')
    #     elif event.key() == Qt.Key_B:
    #         self.set_cursor_state('busy')
    #     elif event.key() == Qt.Key_M:
    #         self.set_cursor_state('move')


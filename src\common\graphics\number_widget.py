from PySide6.QtWidgets import QLabel
from PySide6.QtCore import Qt
from src.styles.style import Style
class NumberWidget(QLabel):
    def __init__(self, parent=None,font_size='40px'):
        super().__init__(parent)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.font_size = font_size
        self.is_selected(enable=False)
        self.setText('0')

    def is_selected(self,enable = False):
        if enable:
            self.setStyleSheet(f'''border: 2px solid #FFFFFF; background-color: #808080; color: #B5122E;font-size: {self.font_size};font: bold;''')
        else:
            self.setStyleSheet(f'''border: 2px solid #FFFFFF; background-color: #808080; color: #FFFFFF;font-size: {self.font_size};font: bold;''')

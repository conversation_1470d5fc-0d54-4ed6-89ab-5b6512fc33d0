import sys
from PySide6.QtWidgets import QGraphicsOpacityEffect,QGraphicsProxyWidget,QPushButton,QGraphicsSceneMouseEvent,QGraphicsItem
from PySide6.QtCore import Qt,QUrl,QPoint,QMimeData
from PySide6.QtMultimedia import QMediaPlayer
from PySide6.QtMultimediaWidgets import QGraphicsVideoItem
from PySide6.QtGui import QDrag
from src.presentation.camera_screen.stream_object_base_widget import StreamObjectBaseWidget
import pickle
import logging
logger = logging.getLogger(__name__)

class BaseVideoItem(QGraphicsVideoItem):     
    def __init__(self, parent=None,row = 0,col = 0):
        super().__init__(parent)
        self.row = row
        self.col = col
        self.drag = None
        self.mouse_press_pos = None
        self.opacity_effect = QGraphicsOpacityEffect(self)
        self.setGraphicsEffect(self.opacity_effect)
        self.opacity_effect.setOpacity(1)

    def mousePressEvent(self, event):
        logger.debug(f"mousePressEvent  BaseVideoItem= {event.pos(),event.scenePos(),self.offset()}")
        scene = self.scene()
        scene.widget_focused = self
        if event.button() == Qt.LeftButton:
            self.mouse_press_pos = event.scenePos() - self.offset()
            position = (self.row, self.col)
            data_bytes = pickle.dumps(position)
            # if isinstance(item_grid, QWidget):
                # Start the drag operation
            self.drag = QDrag(self.scene().views()[0])
            mime_data = QMimeData()
            mime_data.setObjectName("swap_item")
            # mime_data.setText(self.camera_name)
            mime_data.setData("application/position", data_bytes)
            self.drag.setMimeData(mime_data)
            self.drag.exec(Qt.MoveAction)
    # def mouseMoveEvent(self, event):
    #     logger.debug(f"mouseMoveEvent BaseVideoItem")
    #     if self.drag is not None:
    #         self.drag.exec()

    def dragEnterEvent(self, event):
        # print(f'dragEnterEvent DraggableVideoItem')
        event.acceptProposedAction()

    def dragLeaveEvent(self, event):
        print(f'dragLeaveEvent DraggableVideoItem')
        event.accept()

    def dragMoveEvent(self, event):
        pos = event.scenePos()
        delta = pos - self.mouse_press_pos
        self.setOffset(QPoint(delta.x()+1,delta.y()+1))  
        self.opacity_effect.setOpacity(0.5)
        event.accept()


    # def mouseReleaseEvent(self, event: QGraphicsSceneMouseEvent) -> None:
    #     scene = self.scene()
    #     scene.widget_focused = self
    #     return super().mouseReleaseEvent(event)
    
    # def mouseMoveEvent(self, event: QGraphicsSceneMouseEvent) -> None:
    #     # logger.debug(f"mouseMoveEvent BaseProxyWidget")
    #     if self.drag is not None:
    #         self.drag.exec()

    # def mousePressEvent(self, event):
    #     logger.debug(f"mousePressEvent BaseProxyWidget")
    #     scene = self.scene()
    #     scene.widget_focused = self
    #     # self.setZValue(scene.itemsBoundingRect().topLeft().y() - 1)
    #     if event.button() == Qt.LeftButton:
    #         self.mouse_press_pos = event.pos()

    #         position = (self.row, self.col)
    #         data_bytes = pickle.dumps(position)
    #         self.drag = QDrag(self.scene().views()[0])
    #         mime_data = QMimeData()
    #         mime_data.setObjectName("swap_item")
    #         mime_data.setData("application/position", data_bytes)
    #         self.drag.setMimeData(mime_data)
    #     #     self.drag.exec(Qt.MoveAction)
    #     return super().mousePressEvent(event)
    
    # def dragEnterEvent(self, event):
    #     event.acceptProposedAction()

    # def dragLeaveEvent(self, event):
    #     print(f'dragLeaveEvent DraggableProxyWidget')
    #     event.accept()

    # def dragMoveEvent(self, event):
    #     # print(f'dragMoveEvent DraggableProxyWidget')
    #     pos = event.scenePos()
    #     self.setPos(pos-self.mouse_press_pos)

    #     # self.opacity_effect.setOpacity(0.5)
    #     event.accept() 

class VideoProxyWidget(QGraphicsProxyWidget):
    def __init__(self, parent=None,row = 0,col = 0,base_grid_item = None,url = None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.setAcceptHoverEvents(True)
        self.setFlag(QGraphicsItem.GraphicsItemFlag.ItemIsMovable, True)
        self.row = row
        self.col = col
        self.base_grid_item = base_grid_item
        self.grid_item = None
        self.drag = None
        self.player = None
        self.url = url
        # button = QPushButton('ahihi')
        self.video_item = BaseVideoItem(row=self.row,col=self.col)
        # self.setWidget(button)
        self.setGraphicsItem(self.video_item)
        self.mouse_press_pos = None
        self.opacity_effect = QGraphicsOpacityEffect(self)
        self.setGraphicsEffect(self.opacity_effect)
        self.opacity_effect.setOpacity(1)
        # self.create()

    def set_grid_item(self,grid_item = None):
        self.grid_item = grid_item

    def resgister_grid_item(self,base_grid_item = None):
        # logger.debug(f'resgister_grid_item = {grid_item.row,grid_item.col}')
        self.base_grid_item = base_grid_item
        widget = self.widget()
        if hasattr(widget, 'set_grid_item'):
            widget.set_grid_item(base_grid_item = base_grid_item)
            # widget.grid_item = grid_item
            # logger.debug(f'resgister_grid_item = { widget.grid_item.row, widget.grid_item.col}')

    def unresgister_grid_item(self):
        self.grid_item = None
        widget = self.widget()
        if hasattr(widget, 'grid_item'):
            widget.grid_item = None   

    def set_source(self,url = None):
        self.url = url
        self.player = QMediaPlayer()
        if self.url is None:
            self.url = "d:/source_code/DAS-SN4225-SL_2024_05_22_5AM_25_57.mkv"
        self.player.setSource(QUrl(self.url))
        self.player.setVideoOutput(self.video_item)
        self.player.play()

    def update_resize(self,width,height):
        logger.debug('update_resize VideoProxyWidget')
        widget = self.widget()
        if widget is not None:
            # logger.debug(f'update_resize = {width,height}')
            widget.update_resize(width,height)

    # def mouseReleaseEvent(self, event: QGraphicsSceneMouseEvent) -> None:
    #     scene = self.scene()
    #     scene.widget_focused = self
    #     return super().mouseReleaseEvent(event)
    
    # def mouseMoveEvent(self, event: QGraphicsSceneMouseEvent) -> None:
    #     # logger.debug(f"mouseMoveEvent VideoProxyWidget")
    #     if self.drag is not None:
    #         self.drag.exec()

    # def mousePressEvent(self, event):
    #     logger.debug(f"mousePressEvent VideoProxyWidget")
    #     scene = self.scene()
    #     scene.widget_focused = self
    #     # self.setZValue(scene.itemsBoundingRect().topLeft().y() - 1)
    #     if event.button() == Qt.LeftButton:
    #         self.mouse_press_pos = event.pos()

    #         position = (self.row, self.col)
    #         data_bytes = pickle.dumps(position)
    #         self.drag = QDrag(self.scene().views()[0])
    #         mime_data = QMimeData()
    #         mime_data.setObjectName("swap_item")
    #         mime_data.setData("application/position", data_bytes)
    #         self.drag.setMimeData(mime_data)
    #     #     self.drag.exec(Qt.MoveAction)
    #     return super().mousePressEvent(event)
    
    # def dragEnterEvent(self, event):
    #     event.acceptProposedAction()

    # def dragLeaveEvent(self, event):
    #     print(f'dragLeaveEvent VideoProxyWidget')
    #     event.accept()

    # def dragMoveEvent(self, event):
    #     # print(f'dragMoveEvent DraggableProxyWidget')
    #     pos = event.scenePos()
    #     self.setPos(pos-self.mouse_press_pos)

    #     # self.opacity_effect.setOpacity(0.5)
    #     event.accept() 
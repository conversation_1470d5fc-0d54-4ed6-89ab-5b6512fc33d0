import logging
import pyjoystick
from pyjoystick.sdl2 import Key, Joystick, run_event_loop
from PySide6.QtCore import Signal,QObject
from src.common.joystick.profiles import KEYBOARD_1000, DCZ

logger = logging.getLogger(__name__)
class WidgetType:
    Invalid = "Invalid"
    CameraScreen = 'CameraScreen'
    DeviceScreen = 'DeviceScreen'
    PlayBackScreen = 'PlayBackScreen'
    SettingScreen = 'SettingScreen'
    UserPermissionsScreen = 'UserPermissionsScreen'
    ServerScreen = 'ServerScreen'
class JoystickProfile:
    def __init__(self,joystick = None):
        self.joystick = joystick
        self.button_list = {}
    
    def get_key_event(self,key):
        if self.joystick is not None:
            if self.joystick.name == 'DCZ':
                key_event = DCZ.get(key.number,None)
                if key_event is not None:
                    return key_event[1]
                return None
            elif self.joystick.name == 'KEYBOARD 1000':
                key_event = KEYBOARD_1000.get(key.number,None)
                if key_event is not None:
                    return key_event[1]
                return None
            else:
                key_event = KEYBOARD_1000.get(key.number,None)
                if key_event is not None:
                    return key_event[1]
                return None
        return None    
class JoystickManager(QObject):
    add_device_signal = Signal(object)
    exit_device_signal = Signal(object)
    key_received_signal = Signal(object)
    __instance = None
    def __init__(self):
        super().__init__()
        self.action_handler = {}
        self.joystick_list = {}
        self.widget_type = WidgetType.CameraScreen
        # self.worker_thread = None
        self.devices = Joystick.get_joysticks()
        self.repeater = pyjoystick.HatRepeater(first_repeat_timeout=0.5, repeat_timeout=0.03, check_timeout=0.01)
        self.create_event_thread()
        self.key_received_signal.connect(self.key_received_slot)

    @staticmethod
    def get_instance():
        if JoystickManager.__instance is None:
            JoystickManager.__instance = JoystickManager()
        return JoystickManager.__instance
    

    def add_device(self,joy):
        # logger.debug(f'add_device = {joy}')
        self.create_joystick(joystick=joy)
        self.add_device_signal.emit(joy)

    def exit_device(self,joy):
        # logger.debug(f'exit_device = {joy}')
        self.remove_joystick(name=joy.name)
        self.exit_device_signal.emit(joy)

    def key_received(self,key):
        # logger.debug(f'key_received = {key.value}')
        self.key_received_signal.emit(key)
        # for widget_type, func in self.action_handler.items():
        #     if func is not None:
        #         func(key)

    def key_received_slot(self,key):
        for widget_type, func in self.action_handler.items():
            if func is not None:
                func(key)

    def register(self,widget_type,func = None):
        if func is not None:
            self.action_handler[widget_type] = func
            return func
        return None
    
    def unregister(self, widget_type):
        try:
            del self.action_handler[widget_type]
        except (KeyError, TypeError, Exception):
            pass

    def create_event_thread(self):
        self.worker_thread = pyjoystick.ThreadEventManager(event_loop=run_event_loop,add_joystick = self.add_device,remove_joystick = self.exit_device,
                                            handle_key_event=self.key_received,
                                            button_repeater=self.repeater)
        self.worker_thread.start()

    def create_joystick(self,joystick = None):
        self.joystick_list[joystick.name] = JoystickProfile(joystick=joystick)
        # logger.debug(f'create_joystick = {self.joystick_list}')

    def remove_joystick(self,name = None):
        if name in self.joystick_list:
            del self.joystick_list[name]
    
    def get_key_event(self,key):
        joystick_profile:JoystickProfile = self.joystick_list[key.joystick.name]
        return joystick_profile.get_key_event(key)
    
    def stop(self):
        self.worker_thread.stop()
        JoystickManager.__instance = None
        self.action_handler = {}
        self.joystick_list = {}

joystick_manager = JoystickManager.get_instance()

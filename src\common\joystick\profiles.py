from PySide6.QtCore import Signal,QObject,Qt,QEvent
# 0: ['1',Qt.Key.Key_0],
# 0: là key nhận đư<PERSON><PERSON> từ Joystick tool
# '1': là số vật lý trên thiết bị Joystick
# Qt.Key.Key_0 : là định nghĩa keyEvent tương úng bàn phím sử dụng cho iVMS

KEYBOARD_1000 = {
    0: ['1',Qt.Key.Key_1],
    1: ['2',Qt.Key.Key_2],
    2: ['3',Qt.Key.Key_3],
    3: ['4',Qt.Key.Key_4],
    4: ['5',Qt.Key.Key_5],
    5: ['6',Qt.Key.Key_6],
    6: ['7',Qt.Key.Key_7],
    7: ['8',Qt.Key.Key_8],
    8: ['9',Qt.Key.Key_9],
    9: ['0',Qt.<PERSON>.Key_0],
    10: ['ESC',None],
    11: ['SETUP',None],
    12: ['SHIFT',Qt.Key.Key_Shift],
    13: ['ENTER',Qt.Key.Key_Return],
    14: ['FN',Qt.Key.Key_Slash],
    15: ['ID',None],
    16: ['CAM',None],
    17: ['MULT',None],
    18: ['AUX',Qt.Key.Key_Asterisk],
    19: ['PTZ',None],
    20: ['PLAY',Qt.Key.Key_Alt],
    21: ['IRIS-',None],
    22: ['IRIS+',None],
    23: ['FOCUS-',None],
    24: ['FOCUS+',None],
    25: ['ZOOM-',Qt.Key.Key_Minus],
    26: ['ZOOM+',Qt.Key.Key_Equal],
    27: ['OTHER',None],
    28: ['PRESET',None],
    29: ['SCAN',None],
    30: ['PAN',None],
    31: ['TOUR',None],
    32: ['PATTERN',None],
}

DCZ = {
    0: ['1 Prev',Qt.Key.Key_1],
    1: ['2 Next',Qt.Key.Key_2],
    2: ['3 Open',Qt.Key.Key_3],
    3: ['4 Layout',Qt.Key.Key_4],
    4: ['5',Qt.Key.Key_5],
    5: ['6 Close',Qt.Key.Key_6],
    6: ['7',Qt.Key.Key_7],
    7: ['8',Qt.Key.Key_8],
    8: ['9',Qt.Key.Key_9],
    9: ['Cancel',None],
    10: ['0',Qt.Key.Key_0],
    11: ['Enter',Qt.Key.Key_Return],
    12: ['Preset',None],
    13: ['Pattern',None],
    14: ['Start',Qt.Key.Key_Alt],
    15: ['Stop',None],
    16: ['Open',None],
    17: ['Close',None],
    18: ['Near',None],
    19: ['Far',None],
    20: ['Pause',None],
    21: ['Previous',None],
    22: ['Next',None],
    23: ['ZoomIn',Qt.Key.Key_Equal],
    24: ['Audio',None],
    25: ['Record Image',None],
    26: ['Output',None],
    27: ['Ack',Qt.Key.Key_Slash],
    28: ['Maximize',Qt.Key.Key_Asterisk],
    29: ['Close',None],
    30: ['Select',None],
    31: ['Camera',None],
    32: ['Nut Xoay tron nho',None],
    33: ['Nut Xoay tron lon',None],
    34: ['Live',None],
    35: ['Record',None],
    36: ['Cycle',None],
    37: ['FullScreen',None],
    38: ['Ptz',None],
    39: ['ZoomOut',Qt.Key.Key_Minus],
}

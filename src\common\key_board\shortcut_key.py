
from typing import List
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject,Signal
@dataclass
class ShortCutKey:
    id: str = None
    name: str = None
    startKey: int = None
    shortcutId: int = None
    treeType: str = None
    keyList: List[int] = None
    func: str = None
    direction: dict = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        # filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        # return cls(**filtered_dict)
        filtered_dict = {}
        for key, value in data_dict.items():
            if key in field_names:
                if key == 'direction':
                    result = eval(value) if value is not None else {}
                    filtered_dict[key] = result
                else:
                    filtered_dict[key] = value
        return cls(**filtered_dict)
    
    def to_dict(self):
        # only return the fields that are not None
        # return {k: v for k, v in self.__dict__.items() if v is not None}
        dist = {}
        for k, v in self.__dict__.items():
            if k == 'direction':
                if v is not None:
                    dist[k] = str(v)
            else:
                if v is not None:
                    dist[k] = v   
        return dist
    
class ShortCutKeyModel(QObject):
    def __init__(self,shortcut_key:ShortCutKey = None):
        super().__init__()
        self.data = shortcut_key


class ShortCutKeyModelManager(QObject):
    add_shortcut_key_signal = Signal(int)
    add_shortcut_key_list_signal = Signal(str)
    notification_signal = Signal(str)
    __instance = None
    def __init__(self):
        super().__init__()
        self.shortcut_key_list = {}

    @staticmethod
    def get_instance():
        if ShortCutKeyModelManager.__instance is None:
            ShortCutKeyModelManager.__instance = ShortCutKeyModelManager()
        return ShortCutKeyModelManager.__instance
    
    def add_shortcut_key(self,shortcut_key:ShortCutKeyModel = None):
        if shortcut_key.data.startKey not in self.shortcut_key_list:
            self.shortcut_key_list[shortcut_key.data.startKey] = {'data':{shortcut_key.data.shortcutId:shortcut_key}}
        else:
            data = self.shortcut_key_list[shortcut_key.data.startKey].get('data',None)
            if data is not None:
                data[shortcut_key.data.shortcutId] = shortcut_key
                self.shortcut_key_list[shortcut_key.data.startKey][shortcut_key.data.shortcutId] = shortcut_key  
            else:
                self.shortcut_key_list[shortcut_key.data.startKey]['data'] = {shortcut_key.data.shortcutId:shortcut_key}
        self.add_shortcut_key_signal.emit(shortcut_key.data.startKey)
        
    def add_shortcut_key_list(self,shortcut_key_list = []):
        for shortcut_key in shortcut_key_list:
            if shortcut_key.data.startKey not in self.shortcut_key_list:
                self.shortcut_key_list[shortcut_key.data.startKey] = {'data':{shortcut_key.data.shortcutId:shortcut_key}}
            else:
                data = self.shortcut_key_list[shortcut_key.data.startKey].get('data',None)
                if data is not None:
                    data[shortcut_key.data.shortcutId] = shortcut_key
                else:
                    self.shortcut_key_list[shortcut_key.data.startKey]['data'] = {shortcut_key.data.shortcutId:shortcut_key}
        self.add_shortcut_key_list_signal.emit('add_shortcut_key_list_signal')
    def get_shortcut_id(self,start_key = None, name = None,tree_type = None):
        if start_key in self.shortcut_key_list:
            data = self.shortcut_key_list[start_key].get('data',None)
            if data is not None:
                for id, shortcut_key in data.items():
                    if name == shortcut_key.data.name:
                        return id
        return None

    def find_shortcut_id(self,start_key = None, id = None,name = None):
        if start_key in self.shortcut_key_list:
            data = self.shortcut_key_list[start_key].get('data',None)
            if data is not None:
                if id in data:
                    return id 

        return None
    
    def get_shortcutkey_model(self,start_key = None,shortcutId = None,name =None,id = None):
        if shortcutId is not None:
            if start_key in self.shortcut_key_list:
                data = self.shortcut_key_list[start_key].get('data',None)
                if data is not None:
                    if shortcutId in data:
                        return data[shortcutId]
        if name is not None:
            if start_key in self.shortcut_key_list:
                data = self.shortcut_key_list[start_key].get('data',None)
                for shortcutId, shortcutkey_model in data.items():
                    if name == shortcutkey_model.data.name:
                        return shortcutkey_model
        if id is not None:
            if start_key in self.shortcut_key_list:
                data = self.shortcut_key_list[start_key].get('data',None)
                if data is not None:
                    for shortcutId, shortcutkey_model in data.items():
                        if id == shortcutkey_model.data.id:
                            return shortcutkey_model          
        return None
    
    def delete_shortcutkey_model(self,shortcutkey_model = None, model_id = None):
        if shortcutkey_model is not None:
            del self.shortcut_key_list[shortcutkey_model.data.startKey]['data'][shortcutkey_model.data.shortcutId]
            return None
        if model_id is not None:
            temp = None
            for startKey, data in self.shortcut_key_list.items():
                for key, model in data['data'].items():
                    if model.data.name == model_id:
                        print(f'delete_shortcutkey_model key = {key}')
                        temp = model.data.id
                        del data['data'][key]
                        return temp
                    
    def run_func(self,start_key = None,id = None):
        func = self.shortcut_key_list[start_key].get('func',None)
        shortcutkey_model = self.get_shortcutkey_model(start_key = start_key,shortcutId = id)
        if shortcutkey_model is not None:
            func(id = shortcutkey_model.data.name,tree_type= shortcutkey_model.data.treeType)
            return True
        return False

shortcut_key_model_manager = ShortCutKeyModelManager.get_instance()
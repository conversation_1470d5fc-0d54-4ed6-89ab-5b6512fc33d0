from abc import ABC
from dataclasses import dataclass
import logging
logger = logging.getLogger(__name__)
from typing import get_type_hints


# @dataclass
class BaseModel(ABC):
    @classmethod
    def from_dict(cls, data: dict):
        type_hints_dict = get_type_hints(cls)
        filtered_data = {}
        for k, v in data.items():
            if k in type_hints_dict:
                filtered_data[k] = v
        return cls(**filtered_data)

    @classmethod
    def to_dict(cls, obj):
        return {k: v for k, v in obj.__dict__.items() if v is not None}
from PySide6.QtCore import QObject, QCoreApplication,Signal
from enum import Enum
class CameraType(QObject):
    check_box = "Check_box"
    # id = "ID"
    # image = "Image"
    camera_name = "Camera Name"
    camera_branch = "Manufacturer"
    ip_address = "IP Address"
    type = "Type"
    model = "Model"
    camera_group = "Camera Group"
    status = "Status"
    ai_flows = "AI Flows"
    action = "Action"
    btn_edit = "btn_edit"
    btn_trash = "btn_trash"
    all = "All"
    camera_id = "Camera Id"
    urlMainstream = "urlMainstream"
    camera_model = "Camera Model"
    row_page = "row page"
    filter = "filter"

class StatusType:
    All = 'All'
    Online = 'Online'
    Offline = 'Offline'

class AiType:
    All = 'All'
    Face = 'Face'
    Vehicle = 'Vehicle'
    Crowd = 'Crowd'
    Access_Control = 'Access Control'
    Off = 'Off'

class FilterType:
    SearchCameraName = 'SearchCameraName'
    SearchCameraGroup = 'SearchCameraGroup'
    SearchCameraAI = 'SearchCameraAI'
    SearchCameraStatus = 'SearchCameraStatus'

class TabType:
    Invalid = 'Invalid'
    VirtualWindow = 'VirtualWindow'
    SavedView = 'SavedView'
    MapView = 'MapWidget'
    BuildingView = 'BuildingView'
    FloorView = 'FloorView'
       
class GroupType(QObject):
    check_box = 'Check_box'
    # id = 'ID'
    group_name = "Group Name"
    sub_group = "Sub Group"
    number_cameras = "Number of Cameras"
    description = "Description"
    ai_flows = "AI Flows"
    action = "Action"
    btn_edit = "btn_edit"
    btn_trash = "btn_trash"
    all = "All"
    group_id = "Group Id"
    group_model = "Group Model"
    row_page = "row page"
    filter = "filter"
class Columns(QObject):
    check_box = 'Check_box'
    id = 'ID'
    image = "Image"
    camera_name = "Camera Name"
    camera_group = "Camera Group"
    status = "Status"
    ai_flows = "AI Flows"
    action = "Action"

class CameraParameters(QObject):
    record_quality = [['HIGH', 'High (Default)'], ['MEDIUM', 'Medium'], ['LOW', 'Low'],
                      {'HIGH': 0, 'MEDIUM': 1, 'LOW': 2}]
    record_resolution = [['1080p', '1080p (Default)'], ['720', '720'], ['480', '480'],
                         {'1080p': 0, '720': 1, '480': 2}]
    timelapse_speed = [['X1', 'x1 (Default)'], ['X2', 'x2'], ['X4', 'x4'], ['X8', 'x8'],
                       {'X1': 0, 'X2': 1, 'X4': 2, 'X8': 3}]
    record_segment = [[15, '15 minutes (Default)'], [20, '20 minutes'], [25, '25 minutes'],
                      {15: 0, 20: 1, 25: 2}]
    main_stream_resolution = [['1080p', '1080p (Default)'], ['720', '720'], ['480', '480'],
                              {'1080p': 0, '720': 1, '480': 2}]
    main_stream_fps = [[15, '15 (Default)'], [20, '20'], [25, '25'], {15: 0, 20: 1, 25: 2}]
    sub_stream_resolution = [['1080p', '1080p (Default)'], ['720', '720'], ['480', '480'],
                             {'1080p': 0, '720': 1, '480': 2}]
    sub_stream_fps = [[15, '15 (Default)'], [20, '20'], [25, '25'], {15: 0, 20: 1, 25: 2}]
    image_diagonal = [[56, '56 (Default)'], [57, '57'], [58, '58'], {56: 0, 57: 1, 58: 2}]
    similarity_threshold = [[0.5, '0.5 (Default)'], [0.6, '0.6'], [0.7, '0.7'],
                            {0.5: 0, 0.6: 1, 0.7: 2}]
    input_confident = [[0.48, '0.48 (Default)'], [0.5, '0.5'], [0.52, '0.52'],
                       {0.48: 0, 0.5: 1, 0.52: 2}]
    input_image_quality = [[21, '21 (Default)'], [22, '22'], [23, '23'], {21: 0, 22: 1, 23: 2}]
    similarity_score = [[0.48, '0.48 (Default)'], [0.5, '0.5'], [0.52, '0.52'],
                        {0.48: 0, 0.5: 1, 0.52: 2}]
    maximium_similarity = [[0.48, '0.48 (Default)'], [0.5, '0.5'], [0.52, '0.52'],
                           {0.48: 0, 0.5: 1, 0.52: 2}]
    image_quality = [[5, '5 (Default)'], [6, '6'], [7, '7'], {5: 0, 6: 1, 7: 2}]
    color_brand_type_recognition = [[0.5, '0.5 (Default)'], [0.6, '0.6'], [0.7, '0.7'],
                                    {0.5: 0, 0.6: 1, 0.7: 2}]
    crowd_detection = [[15, '15 (Default)'], [20, '20'], [25, '25'], {15: 0, 20: 1, 25: 2}]
    lpr_confident = [[0.48, '0.48 (Default)'], [0.5, '0.5'], [0.52, '0.52'],
                     {0.48: 0, 0.5: 1, 0.52: 2}]
    warning_level = [[10, '10 Object Quantity'], [20, '20 Object Quantity'], [30, '30 Object Quantity'],[40, '40 Object Quantity'],[50, '50 Object Quantity'],[60, '60 Object Quantity'],[70, '70 Object Quantity'],[80, '80 Object Quantity'],[90, '90 Object Quantity'],[100, '100 Object Quantity'],[200, '200 Object Quantity'],[300, '300 Object Quantity'],[400, '400 Object Quantity'],[500, '500 Object Quantity'],
                     {10: 0, 20: 1, 30: 2, 40: 3, 50: 4, 60: 5, 70: 6, 80: 7, 90: 8, 100: 9, 200: 10, 300: 11, 400: 12, 500: 13}]

    vehicle_recognition = [[0.5, '0.5 (Default)'], [0.6, '0.6'], [0.7, '0.7'],
                           {0.5: 0, 0.6: 1, 0.7: 2}]
    camera_type = [[0,'Please select'],[1,'Hikvision'],[2,'Dahua'],[3,'Axis'],[4,'Avigilon'],[5,'Panasonic'],[6,'Bosch'],[7,'Canon'],[8,'Hanwha Techwin'],[9,'Samsung'],[10,'Sony'],[11,'Milestone'],[12,'Ezviz'],[13,'Vivotek'],[14,'Vantech'],[15,'KB Vision'],[16,'Nichietsu'],[17,'Imou'],[18,'Pelco'],{1:0,2:1}]


class CameraDataModel(QObject):
    change_ai_signal = Signal(int)
    change_status_signal = Signal(bool)
    change_camera_name_signal = Signal(str)
    change_camera_group_signal = Signal(str)
    def __init__(self):
        super().__init__()
        self.dist = {}
    
    def add_key(self,key,data):
        self.dist[key] = data
    
    def get_key(self,key):
        return self.dist[key]
    
class GroupDataModel(QObject):
    change_ai_signal = Signal(int)
    def __init__(self):
        super().__init__()
        self.dist = {}
    
    def add_key(self,key,data):
        self.dist[key] = data
    
    def get_key(self,key):
        return self.dist[key]
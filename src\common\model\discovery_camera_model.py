from typing import List
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal
# object Camera Model dùng để lưu trữ thông tin của Camera đã khớp với dữ liệu trả về từ API
# và dùng để chuyển đổi thành dữ liệu để hiển thị trên UI
# Không được thay đổi các thuộc tính của object này trừ khi Backend API thay đổi
# @dataclass
# class Parent:
#     @classmethod
#     def from_dict(cls, data: dict):
#         filtered_data = {key: value for key,
#                             value in data.items() if hasattr(cls, key)}
#         return cls(**filtered_data)
    
@dataclass
class CameraInfo:
    id: str = None
    name: str = None
    urlMainstream: str = None
    username: str = None
    password: str = None
    ipAddress: str = None
    port: int = None
    added: bool = None
    cameraGroupIds: List[str] = None
    cameraGroupDTOList: List[str] = None


    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
@dataclass
class DiscoveryCameraModel:
    id: str = None
    discoveredCameraIds: List[str] = None
    discoveredCameraDTOs: bool = None
    cameraGroupId: bool = None
    cameraGroupDTO: bool = None
    
    # @classmethod
    # def from_dict(cls, data_dict):
    #     field_names = {field.name for field in fields(cls)}
    #     filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
    #     return cls(**filtered_dict)
    
    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        # filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        filtered_dict = {}
        for key, value in data_dict.items():
            if key in field_names:
                if key == 'discoveredCameraDTOs':
                    result = []
                    for item in value:
                        result.append(CameraInfo.from_dict(item) )
                    filtered_dict[key] = result
                else:
                    filtered_dict[key] = value
        return cls(**filtered_dict)
    
    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
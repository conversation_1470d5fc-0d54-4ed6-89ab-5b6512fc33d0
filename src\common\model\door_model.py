from typing import List
#from src.common.model.camera_model import Camera
# object Camera Model dùng để lưu trữ thông tin của Camera đã khớp với dữ liệu trả về từ API
# và dùng để chuyển đổi thành dữ liệu để hiển thị trên UI
# Không được thay đổi các thuộc tính của object này trừ khi Backend API thay đổi
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal

@dataclass
class Door:
    id: str = None
    server_ip: str = None
    name: str = None
    doorType: bool = None
    brandName: bool = None
    username: str = None
    password: bool = None
    ipAddress: str = None
    port: int = None
    activate: bool = None
    polygonIds: List[int] = None
    polygonDTOs: List[dict] = None
    userGroupIdAltList: List[str] = None
    userGroupDTOs: List[dict] = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)
    
    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
class DoorModel(QObject):
    change_model = Signal(tuple)
    def __init__(self,door: Door = None):
        super().__init__()
        self.data = door

    def set_name(self,name):
        self.data.name = name
        self.change_model.emit(('name',name,self))

    def diff_door_model(self, door:Door = None):
        dict1 = asdict(self.data)
        dict2 = asdict(door)
        # print(f'ahihi {self.data.server_ip}-----{self.data.aiFlowIds} -- {camera.aiFlowIds}')
        diff = []
        for field,value in dict2.items():
            if dict1[field] != value:
                if field == 'name':
                    diff.append(field)
                    self.data.name = value
                    self.change_model.emit(('name',value,self))
                elif field == 'doorType':
                    diff.append(field)
                    self.data.doorType = value
                    self.change_model.emit(('doorType',value,self))
                elif field == 'brandName':
                    diff.append(field)
                    self.data.brandName = value
                    self.change_model.emit(('brandName',value,self))
                elif field == 'username':
                    diff.append(field)
                    self.data.username = value
                    self.change_model.emit(('username',value,self))
                elif field == 'password':
                    diff.append(field)
                    self.data.password = value
                    self.change_model.emit(('password',value,self))
                elif field == 'ipAddress':
                    diff.append(field)
                    self.data.ipAddress = value
                    self.change_model.emit(('ipAddress',value,self))
                elif field == 'port':
                    diff.append(field)
                    self.data.port = value
                    self.change_model.emit(('port',value,self))
                else:
                    diff.append(field)
                    setattr(self.data, field, value)
        # print(f'diff_camera_model = {diff}')
        # print(f'diff_camera_model = {diff}')
        return diff
                
        
class DoorModelManager(QObject):
    add_doors_signal = Signal(tuple)
    delete_camera_model_signal = Signal(list)
    add_door_signal = Signal(QObject)
    __instance = None
    def __init__(self):
        super().__init__()
        self.doors = {}

    @staticmethod
    def get_instance():
        if DoorModelManager.__instance is None:
            DoorModelManager.__instance = DoorModelManager()
        return DoorModelManager.__instance
    
    def add_doors(self,doors:List[DoorModel] = [],controller = None):
        output = {}
        for door_model in doors:
            door_model.data.server_ip = controller.server.data.server_ip
            output[door_model.data.id] = door_model
        # print(f'add_camera_list = {camera_list}')
        self.doors[controller.server.data.server_ip] = output
        self.add_doors_signal.emit((controller,doors))

    def add_door(self,controller = None,door:DoorModel = None):
        door.data.server_ip = controller.server.data.server_ip
        door_result = self.get_door_model(id=door.data.id)
        if door_result is None:
            self.doors[door.data.server_ip][door.data.id] = door
            self.add_door_signal.emit(door)

    def get_door_model(self,id = None):
        if id is not None:
            for idx,doors in self.doors.items():
                return doors.get(id,None)
        return None
    
    def get_doors(self,server_ip = None):
        output = {}
        if server_ip in self.doors:
            output = self.doors[server_ip]
            return output
        else:
            return {}
        
    def delete_server(self,server_ip = None):
        if server_ip in self.doors:
            del self.doors[server_ip]

door_model_manager = DoorModelManager.get_instance()
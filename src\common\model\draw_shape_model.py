from typing import List
from enum import Enum

# (255, 0, 0) red
# (0, 255, 0) green
# (0, 0, 255) blue
# (255, 255, 0) yellow
# (0, 255, 255) cyan
# (255, 128, 0) orange
# (255, 0, 255) pink
# (127, 0, 255) purple


class ShapeType(Enum):
    ZONE_FLOW_IN = "ZONE_FLOW_IN"
    ZONE_FLOW_OUT = "ZONE_FLOW_OUT" 
    ZONE_DETECT = "ZONE_DETECT" 
    ZONE_ACCESS_CONTROL_IN = "ZONE_ACCESS_CONTROL_IN"
    ZONE_ACCESS_CONTROL_OUT = "ZONE_ACCESS_CONTROL_OUT"
    ZONE_INTRUSION = "ZONE_INTRUSION" 
    ZONE_PROTECT_IN = "ZONE_PROTECT_IN"
    ZONE_PROTECT_OUT = "ZONE_PROTECT_OUT"
    ZONE_PROTECT = "ZONE_PROTECT"

class DrawShapeModel:
    rect: List = None
    line: List = None
    arrow: '<PERSON>Model' = None
    color_line = None
    shape_type: 'ShapeType' = None
    color_point = None
    shape_name = None

    def __init__(self, rect=None, line=None, arrow=None, color=None, shape_type=None, color_point=None, shape_name=None):
        self.rect = rect
        self.line = line
        self.arrow = arrow
        self.color_line = color
        self.shape_type = shape_type
        self.color_point = color_point
        self.shape_name = shape_name

    def update_data(self, rect=None, line=None, arrow=None, color=None, shape_type=None, color_point=None, shape_name=None):
        if rect is not None:
            self.rect = rect
        if line is not None:
            self.line = line
        if arrow is not None:
            self.arrow = arrow
        if color is not None:
            self.color_line = color
        if shape_type is not None:
            self.shape_type = shape_type
        if color_point is not None:
            self.color_point = color_point
        if shape_name is not None:
            self.shape_name = shape_name

    def to_dict(self):
        return {
            'rect': self.rect,
            'line': self.line,
            'arrow': self.arrow.__dict__ if self.arrow else None,
            'color': self.color_line,
            'shape_type': self.shape_type.value if self.shape_type else None,
            'color_point': self.color_point,
            'shape_name': self.shape_name
        }


class ArrowModel:
    midpoint: tuple = None
    arrow_x: int = None
    arrow_y: int = None
    color: str = None
    edge_index: int = None

    def __init__(self, midpoint=None, arrow_x=None, arrow_y=None, color=None, edge_index=None):
        self.midpoint = midpoint
        self.arrow_x = arrow_x
        self.arrow_y = arrow_y
        self.color = color
        self.edge_index = edge_index

    def update_data(self, midpoint=None, arrow_x=None, arrow_y=None, color=None, edge_index=None):
        if midpoint:
            self.midpoint = midpoint
        if arrow_x:
            self.arrow_x = arrow_x
        if arrow_y:
            self.arrow_y = arrow_y
        if color:
            self.color = color
        if edge_index:
            self.edge_index = edge_index

    def to_dict(self):
        return {
            'midpoint': self.midpoint,
            'arrow_x': self.arrow_x,
            'arrow_y': self.arrow_y,
            'color': self.color,
            'edge_index': self.edge_index
        }

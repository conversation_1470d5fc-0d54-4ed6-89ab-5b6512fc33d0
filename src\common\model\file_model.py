from dataclasses import dataclass


@dataclass
class FileModel:
    fileName: str = None
    url: str = None

    @classmethod
    def from_dict(cls, data_dict):
        return cls(
            fileName=data_dict.get('fileName'),
            url=data_dict.get('url')
        )

    def to_dict(self):
        # Convert instance attributes back to a dictionary
        return {
            'fileName': self.fileName,
            'url': self.url
        }


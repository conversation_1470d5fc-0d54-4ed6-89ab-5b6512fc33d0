from dataclasses import dataclass, field
from typing import List

from PySide6.QtCore import QObject, Signal


@dataclass
class FunctionMenu:
    children: List['FunctionMenu'] = field(default_factory=list)
    clientId: int = None
    createdTime: str = None
    icon: str = None
    id: int = None
    modifiedTime: str = None
    name: str = None
    orderNum: int = None
    parentId: int = None
    path: str = None
    perms: str = None
    status: int = None
    type: int = None
    server_ip: str = None

    @classmethod
    def from_dict(cls, data_dict):
        # Convert 'children' into a list of Menu objects if provided
        children_data = data_dict.get('children') or []  # This will set it to [] if None
        children = [cls.from_dict(child) if isinstance(child, dict) else child for child in children_data]

        return cls(
            id=data_dict.get('id'),
            name=data_dict.get('name'),
            clientId=data_dict.get('clientId'),
            createdTime=data_dict.get('createdTime'),
            icon=data_dict.get('icon'),
            modifiedTime=data_dict.get('modifiedTime'),
            orderNum=data_dict.get('orderNum'),
            parentId=data_dict.get('parentId'),
            path=data_dict.get('path'),
            perms=data_dict.get('perms'),
            status=data_dict.get('status'),
            type=data_dict.get('type'),
            children=children,
            server_ip=data_dict.get('server_ip')
        )

    def to_dict(self):
        # Convert instance attributes back to a dictionary
        return {
            'id': self.id,
            'name': self.name,
            'clientId': self.clientId,
            'createdTime': self.createdTime,
            'icon': self.icon,
            'modifiedTime': self.modifiedTime,
            'orderNum': self.orderNum,
            'parentId': self.parentId,
            'path': self.path,
            'perms': self.perms,
            'status': self.status,
            'type': self.type,
            'children': [child.to_dict() if isinstance(child, FunctionMenu) else child for child in self.children],
            'server_ip': self.server_ip
        }
class FunctionMenuModel(QObject):
    change_name_signal = Signal(str)

    def __init__(self, menu: FunctionMenu = None):
        super().__init__()
        self.data = menu

    def to_dict(self):
        return {
            "data": self.data.to_dict()
        }

    @classmethod
    def from_dict(cls, data_dict):
        menu_data = data_dict.get("data", {})
        menu = FunctionMenu.from_dict(menu_data)
        return cls(menu=menu)

class FunctionMenuModelManager(QObject):
    __instance = None

    def __init__(self):
        super().__init__()
        self.function_menu_list = {}

    @staticmethod
    def get_instance():
        if FunctionMenuModelManager.__instance is None:
            FunctionMenuModelManager.__instance = FunctionMenuModelManager()
        return FunctionMenuModelManager.__instance

    # def get_user_model(self, user_id=None, name=None):
    #     if user_id is not None:
    #         for idx, users_list in self.user_list.items():
    #             user_model = users_list.get(user_id, None)
    #             if user_model is not None:
    #                 return user_model
    #     elif name is not None:
    #         for idx, users_list in self.user_list.items():
    #             for key, user_model in users_list.items():
    #                 if name == user_model.data.name:
    #                     return user_model
    #     return None
    def add_function_menu_list(self, controller=None, menu_list: List[FunctionMenuModel] = [], server_ip=None):
        output = {}
        for menu_model in menu_list:
            menu_model.data.server_ip = controller.server.data.server_ip
            output[menu_model.data.id] = menu_model
        self.function_menu_list[controller.server.data.server_ip] = output
        # self.add_user_list_signal.emit((controller, user_list))

    def get_function_menu_list(self, server_ip=None):
        if server_ip in self.function_menu_list:
            return self.function_menu_list[server_ip]
        else:
            return {}

    def get_function_menu_model(self, function_menu_id=None):
        if function_menu_id is not None:
            for idx, item_function in self.function_menu_list.items():
                function_menu_model = item_function.get(function_menu_id, None)
                if function_menu_model is not None:
                    return function_menu_model
        return None

    def delete_server(self, server_ip=None):
        if server_ip in self.function_menu_list:
            del self.function_menu_list[server_ip]

    def clear(self):
        self.function_menu_list.clear()


function_menu_model_manager = FunctionMenuModelManager.get_instance()

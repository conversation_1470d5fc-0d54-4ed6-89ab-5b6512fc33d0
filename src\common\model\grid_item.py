
from dataclasses import dataclass
from dataclasses import dataclass, asdict, fields
from PySide6.QtWidgets import QWidget
@dataclass
class GridItem:
    index: tuple = None
    type: str = None
    is_fullscreen: bool = False
    is_virtual_fullscreen: bool = False
    row: int = None
    col: int = None
    width: int = None
    height: int = None
    virtual_width:int = None
    virtual_height:int = None
    model:dict = None
    widget:QWidget = None
    virtual_widget:QWidget = None
    # base_grid_item: BaseGridItem = None
    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    # def to_dict(self):
    #     return asdict(self)
    def to_dict(self):
        # only return the fields that are not None
        # return {k: v for k, v in self.__dict__.items() if v is not None}
        dist = {}
        for k, v in self.__dict__.items():
            if k != 'index':
                dist[k] = v
            else:
                dist[k] = str(v)
        return dist
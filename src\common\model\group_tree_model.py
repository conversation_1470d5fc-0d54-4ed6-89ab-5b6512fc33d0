from dataclasses import dataclass, field
from typing import List
from src.common.model.camera_model import Camera
from src.common.model.group_model import Group
from src.common.model.aiflows_model import AiFlow

# Không được thay đổi các thuộc tính của object này trừ khi Backend API thay đổi


@dataclass
class GroupTreeModel:
    id: int = None  # not null
    name: str = None
    description: str = None
    cameras: List['Camera'] = None
    childGroups: List['GroupTreeModel'] = None
    aiFlows: AiFlow = None
    checked: bool = False

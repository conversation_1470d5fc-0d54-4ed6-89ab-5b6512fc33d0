from typing import List, Tuple, Set

from src.common.model.base_model import BaseModel
from src.styles.style import Style
class ItemGridModel(BaseModel):
    name_grid: str = None
    data: List[Set[Tuple[int, int]]] = None
    total_grid_count: int = None
    row: int = None
    column: int = None
    image_url: str = None
    grid_type: str = None
    divisions: int = None
    divisions_type: str = None
    grid_path: str = None

    def __init__(self, name_grid=None, data=None, total_grid_count=None, row=None, column=None, image_url=None, grid_type=None, divisions=None, divisions_type=None, grid_path=None):
        self.name_grid = name_grid
        self.data = data
        self.total_grid_count = total_grid_count
        self.row = row
        self.column = column
        self.image_url = image_url
        self.grid_type = grid_type
        self.divisions = divisions
        self.divisions_type = divisions_type
        self.grid_path = grid_path

    def update_data(self, name_grid=None, data: List = None, total_grid_count=None, row=None, column=None,
                    image_url=None, grid_type=None, divisions=None, divisions_type=None,
                    grid_path=None):
        if name_grid:
            self.name_grid = name_grid
        if data:
            self.data = data
        if total_grid_count:
            self.total_grid_count = total_grid_count
        if row:
            self.row = row
        if column:
            self.column = column
        if image_url:
            self.image_url = image_url
        if grid_type:
            self.grid_type = grid_type
        if divisions:
            self.divisions = divisions
        if divisions_type:
            self.divisions_type = divisions_type
        if grid_path:
            self.grid_path = grid_path

    def to_dict(self):
        serialized_data = [list(entry) for entry in self.data]
        return {
            "name_grid": self.name_grid,
            "data": serialized_data,
            "total_grid_count": self.total_grid_count,
            "row": self.row,
            "column": self.column,
            "image_url": self.image_url,
            "grid_type": self.grid_type,
            "divisions": self.divisions,
            "divisions_type": self.divisions_type,
            "grid_path": self.grid_path
        }

    @classmethod
    def from_dict(cls, model_dict):
        return cls(
            name_grid=model_dict.get("name_grid"),
            data=model_dict.get("data"),
            total_grid_count=model_dict.get("total_grid_count"),
            row=model_dict.get("row"),
            column=model_dict.get("column"),
            image_url=model_dict.get("image_url"),
            grid_type=model_dict.get("grid_type"),
            divisions=model_dict.get("divisions"),
            divisions_type=model_dict.get("divisions_type"),
            grid_path = model_dict.get("grid_path")
        )


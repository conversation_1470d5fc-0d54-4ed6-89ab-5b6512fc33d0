from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QStandardItemModel
from enum import Enum
from typing import List
from dataclasses import dataclass
from src.common.model.camera_model import Camera,CameraModel
from src.common.model.group_model import Group,GroupModel
from src.common.model.group_tree_model import GroupTreeModel
from src.common.model.device_models import TabType
from src.common.controller.main_controller import main_controller
from queue import Queue

import logging
logger = logging.getLogger(__name__)

class Status:
    Invalid = 'Invalid'
    Change_Server = 'Change_Server'
    Change_Filter_Mode = 'Change_Filter_Mode'
    Change_Text = 'Change_Text'
    
class TreeType:
    Invalid = 'Invalid'
    Server = 'Server'
    List_Camera = 'List_Camera'
    Group = 'Group'
    Camera = 'Camera'
    List_Virtual_Window = 'List_Virtual_Window'
    List_Saved_View = 'List_Saved_View'
    List_Map = 'List_Map'
    Virtual_Window_Item = 'Virtual_Window_Item'
    Saved_View_Item = 'Saved_View_Item'
    BuildingItem = 'BuildingItem'
    # CameraOnMap = 'CameraOnMap'
    FloorItem = 'FloorItem'
    # Camera_On_Floor_Item = 'Camera_On_Floor_Item'
    Camera_Virtual_Window_Item = 'Camera_Virtual_Window_Item'
    Camera_Saved_View_Item = 'Camera_Saved_View_Item'
    Camera_Map_Item = 'Camera_Map_Item'
    Multi_Select_Item = 'Multi_Select_Item'

@dataclass
class BaseItem: # virtual window, savedview, map item
    type: str = None
    name: str = None
    model: str = None # GridModel
    # list_cameras: List[CameraModel] = None

@dataclass
class ListItem: # ListVirtualWindow, ListSavedView, ListMap item
    type: str = None
    name: str = None
    child_item: List[BaseItem] = None

@dataclass
class ListCamera:
    type: str = None
    name: str = None
    list_groups: dict = None
    list_aibox_non_group: dict = None
    list_camera_non_group:List[CameraModel] = None # danh sach camera khong thuoc group
    list_cameras: List[Camera] = None # danh sach tat ca camera tren Server

@dataclass
class ListMap:
    type: str = None
    name: str = None
    list_buildings: dict = None
    list_cameras: dict = None # danh sach tat ca camera tren Server

@dataclass
class ListDynamicCamera:
    type: str = None
    name: str = None
    list_groups: dict = None
    list_camera_non_group:List[CameraModel] = None 

@dataclass
class Server:
    name: str = None
    list_cameras: ListCamera = None
    list_dynamic_cameras: List[ListDynamicCamera] = None
    list_virtual_windows: ListItem = None
    list_saved_views: ListItem = None
    list_maps: ListMap = None

@dataclass
class MainTreeviewData:
    servers: List[Server] = None


@dataclass   
class CameraGroupFilterMode:
    server: Server = None

class MainTreeViewModel:
    def __init__(self):
        # super().__init__()
        self.filter_mode_status = TreeType.Server
        self.search_text_status = False
        self.tree_data = MainTreeviewData(servers=[])
        self.tree_filter_mode = self.tree_data
        self.tree_search_mode = self.tree_data
        

    def add_servers(self,list_server:List[Server] = []):   
        self.tree_data = list_server    

    def add_server(self,server: Server= None):
        self.tree_data.servers.append(server)

    def get_server(self,server_ip = None):
        for server in self.tree_data.servers:
            if server_ip == server.name:
                return server
        return None
    
    def add_base_items(self,list_base_items = [],server: Server = None,tree_type:TreeType = TreeType.List_Virtual_Window):
        
        if tree_type == TreeType.List_Virtual_Window:
            if server.list_virtual_windows:
                server.list_virtual_windows.child_item = list_base_items
            else:
                temp = ListItem(name=tree_type,child_item=list_base_items)
                server.list_virtual_windows = temp
        elif tree_type == TreeType.List_Saved_View:
            if server.list_saved_views:
                server.list_saved_views.child_item = list_base_items
            else:
                temp = ListItem(name=tree_type,child_item=list_base_items)
                server.list_saved_views = temp
        # elif tree_type == TreeType.List_Map:
        #     if server.list_maps:
        #         server.list_maps.child_item = list_base_items
        #     else:
        #         temp = ListItem(name=tree_type,child_item=list_base_items)
        #         server.list_maps = temp

    def add_groups(self,list_groups:dict = {},server: Server = None):
        if server.list_cameras:
            if server.list_cameras.list_groups:
                server.list_cameras.list_groups.update(list_groups)
            else:
                server.list_cameras.list_groups = list_groups
        else:
            list_camera = ListCamera(name='List Camera',list_groups=list_groups,list_camera_non_group=[])
            server.list_cameras = list_camera

    def add_buildings(self,list_buildings:dict = {},server: Server = None):
        if server.list_maps:
            if server.list_maps.list_buildings:
                server.list_maps.list_buildings.update(list_buildings)
            else:
                server.list_maps.list_buildings = list_buildings
        else:
            list_map = ListMap(name='List Map',list_buildings=list_buildings,list_cameras=[])
            server.list_maps = list_map

    def add_camCfgs(self,list_camCfgs:dict = {},server: Server = None):
        if server.list_maps:
            if server.list_maps.list_cameras:
                server.list_maps.list_cameras.update(list_camCfgs)
            else:
                server.list_maps.list_cameras = list_camCfgs
        # else:
        #     list_map = ListMap(name='List Map',list_buildings=list_buildings,list_cameras=[])
        #     server.list_maps = list_map

    def add_camOnFloor(self, list_camOnFloor:dict = {},server: Server = None):
        if server.list_maps:
            if server.list_maps.list_buildings:
                for building in server.list_maps.list_buildings.values():
                    for floor in building._floorIds:
                        pass
                    

    def add_dynamic_groups(self,list_dynamic_camera:ListDynamicCamera = None,server: Server = None):
        if server.list_dynamic_cameras:
            server.list_dynamic_cameras.append(list_dynamic_camera)
        else:
            server.list_dynamic_cameras = [list_dynamic_camera]
        # if server.list_cameras:
        #     if server.list_cameras.list_groups:
        #         server.list_cameras.list_groups = list_groups
        #     else:
        #         server.list_cameras.list_groups = list_groups
        # else:
        #     list_camera = ListCamera(name='List Camera',list_groups=list_groups,list_camera_non_group=[])
        #     server.list_cameras = list_camera  

    def add_cameras(self,list_camera_non_group:List[CameraModel] = [],server: Server = None):
        server.list_cameras.list_camera_non_group = list_camera_non_group

    def add_aiboxs(self,list_aibox_non_group:List[GroupModel] = [],server: Server = None):
        server.list_cameras.list_aibox_non_group = list_aibox_non_group

    def add_camera(self,camera_model:CameraModel = None,server: Server = None):
        if camera_model not in server.list_cameras.list_camera_non_group:
            server.list_cameras.list_camera_non_group.append(camera_model)

    def delete_camera(self,camera_model:CameraModel = None,server: Server = None):
        if camera_model in server.list_cameras.list_camera_non_group:
            server.list_cameras.list_camera_non_group.remove(camera_model)

    def remove_data(self,id = None,name = None,tab_type:TreeType = TreeType.Server, server: Server = None):
        if tab_type == TreeType.Virtual_Window_Item:
            if server.list_virtual_windows.child_item:
                for base_item in server.list_virtual_windows.child_item:
                    if base_item.model is not None and id == base_item.model.get_property('id'):
                        # logger.debug(f'camera_item.name = {camera_item.name}')
                        server.list_virtual_windows.child_item.remove(base_item)
                        break
        elif tab_type == TreeType.Saved_View_Item:
            if server.list_saved_views.child_item:
                for base_item in server.list_saved_views.child_item:
                    if base_item.model is not None and id == base_item.model.get_property('id'):
                        server.list_saved_views.child_item.remove(base_item)
                        break 
        elif tab_type == TreeType.BuildingItem:
            for key,item in server.list_maps.list_buildings.items():
                if id == key:
                    del server.list_maps.list_buildings[id]
                    break


    def add_data(self,name = None,tab_type:TreeType = TreeType.Server, server: Server = None,model = None):
        if tab_type == TreeType.Virtual_Window_Item:
            if server.list_virtual_windows.child_item:
                server.list_virtual_windows.child_item.append(BaseItem(name=name,type=TreeType.Virtual_Window_Item,model = model))
            else:
                server.list_virtual_windows.child_item = [BaseItem(name=name,type=TreeType.Virtual_Window_Item,model = model)]
        elif tab_type == TreeType.Saved_View_Item:
            if server.list_saved_views.child_item:
                server.list_saved_views.child_item.append(BaseItem(name=name,type=TreeType.Saved_View_Item,model = model))
            else:
                server.list_saved_views.child_item = [BaseItem(name=name,type=TreeType.Saved_View_Item,model = model)]

    def edit_data(self,new_name = None,old_name = None,tab_type:TreeType = TreeType.Server, server: Server = None):
        if tab_type == TreeType.Virtual_Window_Item:
            if server.list_virtual_windows.child_item:
                for camera_item in server.list_virtual_windows.child_item:
                    if old_name == camera_item.name:
                        # logger.debug(f'camera_item.name = {camera_item.name}')
                        if new_name is not None:
                            camera_item.name = new_name
                        break
        elif tab_type == TreeType.Saved_View_Item:
            if server.list_saved_views.child_item:
                for camera_item in server.list_saved_views.child_item:
                    if old_name == camera_item.name:
                        if new_name is not None:
                            camera_item.name = new_name
                        break 

    def get_group_list(self,server_ip = None):
        for server in self.tree_data.servers:
            if server_ip == server.name:
                return server.list_cameras.list_groups
        return {}



import json
from typing import List
from src.common.model.map_config_camera_model import MapConfigCameraModel
from src.common.model.camera_model import CameraModel, Camera
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal
# object Camera Model dùng để lưu trữ thông tin của Camera đã khớp với dữ liệu trả về từ API
# và dùng để chuyển đổi thành dữ liệu để hiển thị trên UI
# Không được thay đổi các thuộc tính của object này trừ khi Backend API thay đổi


@dataclass
class Floor:
    id: str = None
    server_ip: str = None
    name: str = None
    image: str = None
    listCameras: List[MapConfigCameraModel] = None
    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {}
        for key, value in data_dict.items():
            if key in field_names:
                if key == 'listCameras':
                    temp = []
                    for camera in value:
                        temp.append(MapConfigCameraModel.from_dict(camera))
                        print(f'temp = {camera}')
                    filtered_dict[key] = temp
                    
                else:
                    filtered_dict[key] = value
        return cls(**filtered_dict)
    
    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for k, v in self.__dict__.items():
            if k == 'id' or k == 'name':
                if v is not None:
                   dist[k] = v
            elif k == 'listCameras':
                if v is not None:
                    temp = []
                    for item in v:
                        temp.append(item)
                    dist[k] = temp
            else:
                if v is not None:
                    dist[k] = v
        return dist 
    
class FloorModel(QObject):
    def __init__(self,floor: Floor = None):
        super().__init__()
        self.data = floor

class FloorManager(QObject):
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}


    @staticmethod
    def get_instance():
        if FloorManager.__instance is None:
            FloorManager.__instance = FloorManager()
        return FloorManager.__instance
    
    def add_floor(self, floor_model: FloorModel):
        if floor_model.data.server_ip in self.data:
            self.data[floor_model.data.server_ip][floor_model.data.id] = floor_model
        else:
            self.data[floor_model.data.server_ip] = {}
            self.data[floor_model.data.server_ip][floor_model.data.id] = floor_model

    def remove_floor(self, floor_model: FloorModel):
        if floor_model.data.server_ip in self.data:
            if floor_model.data.id in self.data[floor_model.data.server_ip]:
                del self.data[floor_model.data.server_ip][floor_model.data.id]

    def get_floor(self,id):
        for server_ip,item in self.data.items():
            if id in item:
                return item[id]

    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for server_ip, list_floor in self.data.items():
            temp = {}
            for id, floor_model in list_floor.items():
                temp[floor_model.data.id] = floor_model.data.to_dict()
            dist[server_ip] = temp
        return dist
    
floor_manager = FloorManager.get_instance()  
@dataclass
class Building:
    id: str = None
    server_ip: str = None
    name: str = None
    listFloor: List[FloorModel] = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {}
        for key, value in data_dict.items():
            if key in field_names:
                if key == 'id':
                    filtered_dict[key] = value
                elif key == 'name':
                    filtered_dict[key] = value
                elif key == 'listFloor':
                    temp = []
                    if value is not None:
                        for id in value:
                            temp.append(id)
                    filtered_dict[key] = temp

                else:
                    filtered_dict[key] = value
        return cls(**filtered_dict)
    
    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for k, v in self.__dict__.items():
            if k == 'listFloor':
                temp = []
                for item in v:
                    temp.append(item.data.id)
                dist[k] = temp
            else:
                if v is not None:
                    dist[k] = v
        return dist 

class BuildingModel(QObject):
    def __init__(self,building: Building = None):
        super().__init__()
        self.data = building

    def add_floor(self, floor_model: FloorModel):
        self.data.listFloor.append(floor_model)
        floor_manager.add_floor(floor_model)

    def remove_floor(self, floor_model: FloorModel = None):
        if floor_model is None:
            # đây là case xóa toàn bộ Floor trong BuildingModel này
            for item in self.data.listFloor:
                floor_manager.remove_floor(floor_model=item)
            self.data.listFloor = []    
        if floor_model in self.data.listFloor:
            self.data.listFloor.remove(floor_model)
            floor_manager.remove_floor(floor_model=floor_model)

class BuildingManager(QObject):
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}


    @staticmethod
    def get_instance():
        if BuildingManager.__instance is None:
            BuildingManager.__instance = BuildingManager()
        return BuildingManager.__instance
    
    def add_building(self, building_model: BuildingModel):
        if building_model.data.server_ip in self.data:
            self.data[building_model.data.server_ip][building_model.data.id] = building_model
        else:
            self.data[building_model.data.server_ip] = {}
            self.data[building_model.data.server_ip][building_model.data.id] = building_model

    def remove_building(self, building_model: BuildingModel):
        if building_model.data.server_ip in self.data:
            if building_model.data.id in self.data[building_model.data.server_ip]:
                building_model.remove_floor()
                del self.data[building_model.data.server_ip][building_model.data.id]

    def remove_floor(self, floor_model: FloorModel):
        for server_ip, list_building in self.data.items():
            for id, building_model in list_building.items():
                if floor_model in building_model.data.listFloor:
                    building_model.remove_floor(floor_model)
                    return

    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for server_ip, list_building in self.data.items():
            temp = {}
            for id, building_model in list_building.items():
                print(f'ttttttttttt = {building_model.data.listFloor}')
                temp[building_model.data.id] = building_model.data.to_dict()
            dist[server_ip] = temp
        return dist 
    
    def get_building_list(self,server_ip = None):
        if server_ip in self.data:
            return self.data[server_ip]
        else:
            return {}
        
building_manager = BuildingManager.get_instance() 

@dataclass
class Map:
    id: str = None
    server_ip: str = None
    listBuildings: List[BuildingModel] = None
    listCameras: List[CameraModel] = None
    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        # filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        filtered_dict = {}
        for key, value in data_dict.items():
            if key in field_names:
                if key == 'id':
                    filtered_dict[key] = value
                elif key == 'server_ip':
                    filtered_dict[key] = value
                elif key == 'listBuildings':
                    temp = []
                    for id in value:
                        temp.append(id)
                    filtered_dict[key] = temp
                else:
                    filtered_dict[key] = value
        return cls(**filtered_dict)
    
    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for k, v in self.__dict__.items():
            if v is not None:
                if k == 'listBuildings' or k == 'listCameras':
                    # dist[k] = {}
                    temp = []
                    for item in v:
                        temp.append(v.id)
                    dist[k] = temp
                else:
                    if v is not None:
                        dist[k] = v
        return dist 
    
class MapModel(QObject):
    def __init__(self,map: Map = None):
        super().__init__()
        self.data = map

class MapManager(QObject):
    __instance = None
    def __init__(self):
        super().__init__()
        self.data = {}


    @staticmethod
    def get_instance():
        if MapManager.__instance is None:
            MapManager.__instance = MapManager()
        return MapManager.__instance
    
    def add_map(self, map_model: MapModel):
        self.data[map_model.data.server_ip] = map_model

    def add_building(self, building_model: BuildingModel):
        if building_model.data.server_ip in self.data:
            map_model:MapModel = self.data[building_model.data.server_ip]
            map_model.data.listBuildings.append(building_model)
            building_manager.add_building(building_model)

    def remove_building(self, building_model: BuildingModel):
        if building_model.data.server_ip in self.data:
            map_model:MapModel = self.data[building_model.data.server_ip]
            if building_model in map_model.data.listBuildings:
                map_model.data.listBuildings.remove(building_model)
                building_manager.remove_building(building_model)

    def to_dict(self):
        # only return the fields that are not None
        dist = {}
        for k, v in self.data.items():
            dist[k] = v.data.to_dict()
        return dist 
    
map_manager = MapManager.get_instance()

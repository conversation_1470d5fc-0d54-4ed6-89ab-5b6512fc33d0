from dataclasses import dataclass
from typing import List

from src.common.model.base_model import BaseModel
@dataclass
class Vehicle(BaseModel):
    id: int = None
    number_plate: str = None
    color: str = None
    brand: str = None
    body_style: str = None

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
@dataclass
class User(BaseModel):
    id: int = None
    auxCmts: List[str] = None
    vehicle: Vehicle = None
    soCmt: str = None
    hoVaTen: str = None
    namSinh: str = None
    queQuan: str = None
    noiTru: str = None
    dacDiemNhanDang: str = None
    ngayCap: str = None
    noiCap: str = None
    quocTich: str = None
    ngayHetHan: str = None
    gioiTinh: str = None
    status: str = None
    image: str = None

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
@dataclass
class Profile(BaseModel):
    id: int = None
    users: List[User] = None
    vehicles: List[Vehicle] = None
    name: str = None
    description: str = None
    warning: bool = False

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}


from typing import List
from dataclasses import dataclass, asdict, field
from PySide6.QtCore import QObject, Signal

@dataclass
class ProfileGroup:
    children: List['ProfileGroup'] = field(default_factory=list)
    createdBy: str = None
    createdTime: str = None
    description: str = None
    id: int = None
    modifiedBy: str = None
    modifiedTime: str = None
    name: str = None
    parentId: int = None
    warningLevel: int = None
    server_ip: str = None

    @classmethod
    def from_dict(cls, data_dict):
        children_data = data_dict.get('children') or []  # This will set it to [] if None
        children = [cls.from_dict(child) if isinstance(child, dict) else child for child in children_data]
        return cls(
            children=children,
            createdBy=data_dict.get('createdBy'),
            createdTime=data_dict.get('createdTime'),
            description=data_dict.get('description'),
            id=data_dict.get('id'),
            modifiedBy=data_dict.get('modifiedBy'),
            modifiedTime=data_dict.get('modifiedTime'),
            name=data_dict.get('name'),
            parentId=data_dict.get('parentId'),
            warningLevel=data_dict.get('warningLevel', 0),
            server_ip=data_dict.get('server_ip')
        )

    def to_dict(self):
        """Convert the instance back to a dictionary."""
        return {
            'children': [child.to_dict() if isinstance(child, ProfileGroup) else child for child in self.children],
            'createdBy': self.createdBy,
            'createdTime': self.createdTime,
            'description': self.description,
            'id': self.id,
            'modifiedBy': self.modifiedBy,
            'modifiedTime': self.modifiedTime,
            'name': self.name,
            'parentId': self.parentId,
            'warningLevel': self.warningLevel,
            'server_ip': self.server_ip
        }

class ProfileGroupModel(QObject):

    def __init__(self, profile_group: ProfileGroup = None):
        super().__init__()
        self.data = profile_group

    def to_dict(self):
        return {
            "data": self.data.to_dict()
        }

    @classmethod
    def from_dict(cls, data_dict):
        profile_group_data = data_dict.get("data", {})
        profile_group = ProfileGroup.from_dict(profile_group_data)
        return cls(profile_group=profile_group)


class ProfileGroupModelManager(QObject):
    __instance = None

    def __init__(self):
        super().__init__()
        self.profile_group_list = {}

    @staticmethod
    def get_instance():
        if ProfileGroupModelManager.__instance is None:
            ProfileGroupModelManager.__instance = ProfileGroupModelManager()
        return ProfileGroupModelManager.__instance

    def add_profile_group_list(self, controller=None, profile_list: List[ProfileGroupModel] = [], server_ip=None):
        output = {}
        for profile_group_model in profile_list:
            profile_group_model.data.server_ip = controller.server.data.server_ip
            output[profile_group_model.data.id] = profile_group_model
        self.profile_group_list[controller.server.data.server_ip] = output
        # self.add_user_list_signal.emit((controller, user_list))

    def get_profile_group_list(self, server_ip=None):
        if server_ip in self.profile_group_list:
            return self.profile_group_list[server_ip]
        else:
            return {}

    def delete_server(self, server_ip=None):
        if server_ip in self.profile_group_list:
            del self.profile_group_list[server_ip]

    def clear(self):
        self.profile_group_list.clear()


profile_group_model_manager = ProfileGroupModelManager.get_instance()

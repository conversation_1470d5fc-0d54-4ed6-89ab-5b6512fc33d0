from typing import List
from src.common.model.group_model import Group
from src.common.model.aiflows_model import AiFlow
from src.common.model.user_model import Users
from src.common.onvif_api.camera_model import Resolution
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Property, Signal
from src.styles.style import Style
import enum
import logging
logger = logging.getLogger(__name__)

class ScreenModel(QObject):
    def __init__(self,data: dict = None):
        super().__init__()
        self.data = data
        
    def get_property(self, key, default=None):
        """Lấy giá trị từ self.data, nếu không có thì trả về default."""
        return self.data.get(key, default)

    def set_property(self, key, value):
        """Cập nhật giá trị trong self.data."""
        self.data[key] = value

class ScreenModelManager(QObject):

    __instance = None
    def __init__(self):
        super().__init__()
        self.isTracking = False
        self.data = {}

    @staticmethod
    def get_instance():
        if ScreenModelManager.__instance is None:
            ScreenModelManager.__instance = ScreenModelManager()
        return ScreenModelManager.__instance
    
    def addScreen(self, screen: ScreenModel):
        self.data[screen.get_property('index',None)] = screen

    def getScreenIndex(self):
        for idx, screen in self.data.items():
            if screen.get_property('state',None):
                return idx
        return None
    
    def to_dict(self):
        output = {}
        for idx, screen in self.data.items():
            output[idx] = screen.data
        return output
    
screenModelManager = ScreenModelManager.get_instance()


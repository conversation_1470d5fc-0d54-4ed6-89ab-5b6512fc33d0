from typing import List, <PERSON><PERSON>

from PySide6.QtCore import Signal, QObject

from src.common.model.camera_model import CameraModel
from src.common.model.group_model import GroupModel

class ConnectNodeModel:
    node_id: str = None
    # node_name: str = None
    # current_value: str = None
    # # input_list: List[Tuple[str, int]] = None
    input_list: List = None  # List[CameraModel]
    output_list: List = None  # List[CameraModel]
    camera_model: CameraModel = None

    def __init__(self, node_id=None, node_name=None, input_list=None, output_list=None, current_value=None, camera_model=None):
        self.node_id = node_id
        # self.node_name = node_name
        self.input_list = input_list
        self.output_list = output_list
        # self.current_value = current_value
        self.camera_model = camera_model

    def update_data(self, node_id=None, node_name=None, input_list=None, output_list=None, current_value=None, camera_model=None):
        if node_id:
            self.node_id = node_id
        # if node_name:
        #     self.node_name = node_name
        if input_list:
            self.input_list = input_list
        if output_list:
            self.output_list = output_list
        # if current_value:
        #     self.current_value = current_value
        if camera_model:
            self.camera_model = camera_model

    def to_dict(self):
        return {
            "node_id": self.node_id,
            # "node_name": self.node_name,
            "input_list": self.input_list,
            "output_list": self.output_list,
            # "current_value": self.current_value,
            "camera_model": self.camera_model.to_dict() if self.camera_model else None
        }

    @classmethod
    def from_dict(cls, model_dict):
        camera_model_dict = model_dict.get("camera_model")
        camera_model = CameraModel.from_dict(camera_model_dict) if camera_model_dict else None
        return cls(
            node_id=model_dict.get("node_id"),
            # node_name=model_dict.get("node_name"),
            input_list=model_dict.get("input_list"),
            output_list=model_dict.get("output_list"),
            # current_value=model_dict.get("current_value"),
            camera_model=camera_model
        )


class TrackingDialogModel:
    server_ip: str = None
    data_node_cameras: List[ConnectNodeModel] = None
    tracking_group: GroupModel = None
    screen_index_to_show: int = None

    def __init__(self, data_node_cameras=None, tracking_group=None, screen_index_to_show=None, server_ip=None):
        self.data_node_cameras = data_node_cameras
        self.tracking_group = tracking_group
        self.screen_index_to_show = screen_index_to_show
        self.server_ip = server_ip

    def to_dict(self):
        return {
            'data_node_cameras': [node.to_dict() for node in self.data_node_cameras],
            'tracking_group': self.tracking_group.to_dict() if self.tracking_group else None,
            'screen_index_to_show': self.screen_index_to_show,
            'server_ip': self.server_ip
        }

    @classmethod
    def from_dict(cls, data_dict):
        data_node_cameras = [ConnectNodeModel.from_dict(node) for node in data_dict.get('data_node_cameras', [])]
        tracking_group = GroupModel.from_dict(data_dict['tracking_group']) if data_dict.get('tracking_group') else None
        return cls(data_node_cameras=data_node_cameras, tracking_group=tracking_group,
                   screen_index_to_show=data_dict.get('screen_index_to_show'), server_ip=data_dict.get('server_ip'))


class TrackingModelManager(QObject):
    add_tracking_group_signal = Signal(object)
    signal_update_tracking_model = Signal()
    add_list_tracking_group_signal = Signal(str)
    update_tracking_group_signal = Signal(object)
    __instance = None

    def __init__(self):
        super().__init__()
        self.tracking_model_list = {}

    @staticmethod
    def get_instance():
        if TrackingModelManager.__instance is None:
            TrackingModelManager.__instance = TrackingModelManager()
        return TrackingModelManager.__instance

    # TRACKING GROUP
    def add_list_tracking_group(self, server_ip=None, group_list: List[TrackingDialogModel] = []):
        self.tracking_model_list[server_ip] = group_list
        self.add_list_tracking_group_signal.emit('add_list_tracking_group_signal')

    def add_tracking_group(self, tracking_model: TrackingDialogModel = None, server_ip=None):
        self.tracking_model_list[server_ip].append(tracking_model)
        tracking_qsetting.save_tracking_dialog_models(key=server_ip, list_tracking_data=self.tracking_model_list[server_ip])
        self.add_tracking_group_signal.emit(tracking_model)

    def update_tracking_group_list(self, tracking_model_list: List[TrackingDialogModel] = None, server_ip=None):
        for tracking_model in tracking_model_list:
            for tracking_model_item in self.tracking_model_list[server_ip]:
                if tracking_model.tracking_group.data.id == tracking_model_item.tracking_group.data.id:
                    tracking_model_item.tracking_group.diff_group_model(group=tracking_model.tracking_group.data)
                    tracking_model_item.data_node_cameras = tracking_model.data_node_cameras
                    break
        tracking_qsetting.save_tracking_dialog_models(key=server_ip, list_tracking_data=self.tracking_model_list[server_ip])
        self.add_list_tracking_group_signal.emit('add_list_tracking_group_signal')
        # self.update_tracking_group_signal.emit(self._list_tracking_model)

    def get_tracking_group(self, server_ip):
        return self.tracking_model_list.get(server_ip, [])
    
    def delete_tracking_group(self, tracking_model: TrackingDialogModel = None, server_ip=None):
        for item in self.tracking_model_list[server_ip]:
            if item == tracking_model:
                self.tracking_model_list[server_ip].remove(item)
                tracking_qsetting.save_tracking_dialog_models(key=server_ip, list_tracking_data=self.tracking_model_list[server_ip])
                self.add_list_tracking_group_signal.emit('add_list_tracking_group_signal')
                break


tracking_model_manager = TrackingModelManager.get_instance()

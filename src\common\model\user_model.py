from typing import List
from dataclasses import dataclass, asdict, fields

from PySide6.QtCore import QObject, Signal
from src.common.model.function_permission_model import FunctionMenu

@dataclass
class UserRole:
    id: int = None
    roleId: int = None
    userId: str = None

    @classmethod
    def from_dict(cls, data_dict):
        return cls(
            id=data_dict.get('id'),
            roleId=data_dict.get('roleId'),
            userId=data_dict.get('userId'),
        )

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}


@dataclass
class Users:
    id: str = None
    username: str = None
    password: str = None
    avatar: str = None
    birthday: str = None
    clientId: int = None
    createdBy: str = None
    createdTime: str = None
    description: str = ""
    email: str = ""
    fullName: str = None
    gender: int = None
    menus: List[FunctionMenu] = None
    modifiedBy: str = None
    modifiedTime: str = None
    permissions: List[str] = None
    phone: str = ""
    position: str = ""
    rolesName: str = ""
    status: int = 0
    userRoles: List[UserRole] = None
    server_ip: str = None

    @classmethod
    def from_dict(cls, data_dict):
        # Convert MenuItem and UserRole lists from dicts
        # Check if 'menus' and 'userRoles' are in data_dict and not None
        menus = [FunctionMenu.from_dict(menu) for menu in data_dict.get('menus', [])] if data_dict.get('menus') else []
        user_roles = [UserRole.from_dict(role) for role in data_dict.get('userRoles', [])] if data_dict.get(
            'userRoles') else []

        return cls(
            id=data_dict.get('id'),
            username=data_dict.get('username'),
            password=data_dict.get('password'),
            avatar=data_dict.get('avatar'),
            birthday=data_dict.get('birthday'),
            clientId=data_dict.get('clientId'),
            createdBy=data_dict.get('createdBy'),
            createdTime=data_dict.get('createdTime'),
            description=data_dict.get('description'),
            email=data_dict.get('email'),
            fullName=data_dict.get('fullName'),
            gender=data_dict.get('gender'),
            menus=menus,
            modifiedBy=data_dict.get('modifiedBy'),
            modifiedTime=data_dict.get('modifiedTime'),
            permissions=data_dict.get('permissions', []),
            phone=data_dict.get('phone'),
            position=data_dict.get('position'),
            rolesName=data_dict.get('rolesName'),
            status=data_dict.get('status'),
            userRoles=user_roles,
            server_ip=data_dict.get('server_ip')
        )

    def to_dict(self):
        # Convert nested MenuItem and UserRole lists to dicts
        return {
            'id': self.id,
            'username': self.username,
            'password': self.password,
            'avatar': self.avatar,
            'birthday': self.birthday,
            'clientId': self.clientId,
            'createdBy': self.createdBy,
            'createdTime': self.createdTime,
            'description': self.description,
            'email': self.email,
            'fullName': self.fullName,
            'gender': self.gender,
            'menus': [menu.to_dict() for menu in self.menus] if self.menus else [],
            'modifiedBy': self.modifiedBy,
            'modifiedTime': self.modifiedTime,
            'permissions': self.permissions,
            'phone': self.phone,
            'position': self.position,
            'rolesName': self.rolesName,
            'status': self.status,
            'userRoles': [role.to_dict() for role in self.userRoles] if self.userRoles else [],
            'server_ip': self.server_ip
        }

class UserModel(QObject):
    change_name_signal = Signal(str)
    change_user_model_signal = Signal(tuple)
    delete_user_signal = Signal(QObject)

    def __init__(self, user: Users = None):
        super().__init__()
        self.data = user

    def to_dict(self):
        return {
            "data": self.data.to_dict()
        }

    @classmethod
    def from_dict(cls, data_dict):
        user_data = data_dict.get("data", {})
        user = Users.from_dict(user_data)
        return cls(user=user)

    def set_full_name(self, fullName):
        self.data.fullName = fullName
        self.change_user_model_signal.emit(('fullName', fullName, self))

    def set_state(self, state):
        self.data.status = state
        self.change_user_model_signal.emit(('status', state, self))

    def set_user_email(self, email):
        self.data.email = email
        self.change_user_model_signal.emit(('email', email, self))

    def set_user_avatar_path(self, avatar_path):
        self.data.avatar = avatar_path
        self.change_user_model_signal.emit(('avatar', avatar_path, self))

    def set_user_birthday(self, birthday):
        self.data.birthday = birthday
        self.change_user_model_signal.emit(('birthday', birthday, self))

    def set_user_permissions(self, permissions: List = []):
        self.data.permissions = permissions
        self.change_user_model_signal.emit(('permissions', permissions, self))

    def set_user_phone(self, phone):
        self.data.phone = phone
        self.change_user_model_signal.emit(('phone', phone, self))

    def set_user_position(self, position):
        self.data.position = position
        self.change_user_model_signal.emit(('position', position, self))

    def set_user_roleName(self, rolesName):
        self.data.rolesName = rolesName
        self.change_user_model_signal.emit(('rolesName', rolesName, self))

    def set_userRoles(self, userRoles: List = []):
        self.data.userRoles = userRoles
        self.change_user_model_signal.emit(('userRoles', userRoles, self))

    def set_menus(self, menus: List = []):
        self.data.menus = menus
        self.change_user_model_signal.emit(('menus', menus, self))

    def diff_user_model(self, user: Users = None):
        dict1 = asdict(self.data)
        dict2 = asdict(user)
        diff = []
        for field, value in dict2.items():
            if dict1[field] != value:
                if field == 'fullName':
                    diff.append(field)
                    self.set_full_name(value)
                elif field == 'status':
                    diff.append(field)
                    self.set_state(value)
                elif field == 'email':
                    diff.append(field)
                    self.set_user_email(value)
                elif field == 'avatar':
                    diff.append(field)
                    self.set_user_avatar_path(value)
                elif field == 'birthday':
                    diff.append(field)
                    self.set_user_birthday(value)
                elif field == 'permissions':
                    diff.append(field)
                    self.set_user_permissions(value)
                elif field == 'phone':
                    diff.append(field)
                    self.set_user_phone(value)
                elif field == 'position':
                    diff.append(field)
                    self.set_user_position(value)
                elif field == 'rolesName':
                    diff.append(field)
                    self.set_user_roleName(value)
                elif field == 'userRoles':
                    diff.append(field)
                    self.set_userRoles(value)
                elif field == 'menus':
                    diff.append(field)
                    self.set_menus(value)
                else:
                    diff.append(field)
                    # if field != 'active' and field != 'check_box' and field != 'btn_edit' and field != 'btn_trash' and field != 'server_ip':
                    #     setattr(self.data, field, value)
        return diff

class UserModelManager(QObject):
    add_user_list_signal = Signal(tuple)
    delete_user_model_signal = Signal(list)
    add_user_signal = Signal(QObject)
    add_users_signal = Signal(list)
    __instance = None

    def __init__(self):
        super().__init__()
        self.user_list = {}

    @staticmethod
    def get_instance():
        if UserModelManager.__instance is None:
            UserModelManager.__instance = UserModelManager()
        return UserModelManager.__instance

    def add_user_list(self, controller=None, user_list: List[UserModel] = [], server_ip=None):
        output = {}
        for user_model in user_list:
            user_model.data.server_ip = controller.server.data.server_ip
            output[user_model.data.id] = user_model
        self.user_list[controller.server.data.server_ip] = output
        self.add_user_list_signal.emit((controller, user_list))

    def add_user(self, user: UserModel = None, is_insert_group=None):
        user_result = self.get_user_model(user_id=user.data.id)
        if user_result is None:
            server_ip = user.data.server_ip
            user_id = user.data.id
            # Add the new user at the start of the dictionary
            if server_ip not in self.user_list:
                self.user_list[server_ip] = {}
            # Reorder the dictionary to have the new user at the start
            self.user_list[server_ip] = {user_id: user, **self.user_list[server_ip]}
            # Emit the signal
            self.add_user_signal.emit(user)

    def update_user_list(self, controller=None, user_list: List[UserModel] = []):
        for user in user_list:
            for user_model in self.user_list[controller.server.data.server_ip].values():
                if user.data.id == user_model.data.id:
                    user_model.diff_user_model(user=user.data)
                    break

    def find_user_deleted(self, controller=None, user_list: List[UserModel] = []):
        temp = []
        for user_model in self.user_list[controller.server.data.server_ip].values():
            check = False
            for user in user_list:
                if user_model.data.id == user.data.id:
                    check = True
            if not check:
                temp.append(user_model)

        for user_model in temp:
            user_model.delete_user_signal.emit(user_model)
            del self.user_list[user_model.data.server_ip][user_model.data.id]
        self.delete_user_model_signal.emit(temp)

    def find_user_added(self, user_list: List[UserModel] = []):
        temp = []
        for user_model in user_list:
            check = False
            for user in self.user_list:
                if user.data.id == user_model.data.id:
                    check = True

            if not check:
                temp.append(user_model)
        for user_model in temp:
            self.user_list.append(user_model)
        self.add_users_signal.emit(temp)
        return temp

    def get_user_model(self, user_id=None, name=None):
        if user_id is not None:
            for idx, users_list in self.user_list.items():
                user_model = users_list.get(user_id, None)
                if user_model is not None:
                    return user_model
        elif name is not None:
            for idx, users_list in self.user_list.items():
                for key, user_model in users_list.items():
                    if name == user_model.data.name:
                        return user_model
        return None

    def get_user_list(self, server_ip=None):
        if server_ip in self.user_list:
            return self.user_list[server_ip]
        else:
            return {}

    def delete_user(self, user_model: UserModel = None):
        self.user_list.remove(user_model)

    def delete_server(self, server_ip=None):
        if server_ip in self.user_list:
            del self.user_list[server_ip]

    def clear(self):
        self.user_list.clear()


user_model_manager = UserModelManager.get_instance()

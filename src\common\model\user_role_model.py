from dataclasses import dataclass, asdict
from typing import List

from PySide6.QtCore import QObject, Signal

from src.common.model.user_model import UserRole

class ChildrenRoleType:
    ROLE_CAMERAS = 0
    ROLE_GROUP = 1
    ROLE_MENUS = 2


@dataclass
class RoleCamera:
    cameraId: str = None
    dieuKhien: int = None
    groupCamId: str = None
    id: int = None
    roleId: int = None
    xem: int = None

    @classmethod
    def from_dict(cls, data_dict):
        return cls(
            cameraId=data_dict.get('cameraId'),
            dieuKhien=data_dict.get('dieuKhien'),
            groupCamId=data_dict.get('groupCamId'),
            id=data_dict.get('id'),
            roleId=data_dict.get('roleId'),
            xem=data_dict.get('xem')
        )

    def to_dict(self):
        return {k: v for k, v in self.__dict__.items() if v is not None}


@dataclass
class RoleGroup:
    groupId: int = None
    id: int = None
    roleId: int = None

    @classmethod
    def from_dict(cls, data_dict):
        return cls(
            groupId=data_dict.get('groupId'),
            id=data_dict.get('id'),
            roleId=data_dict.get('roleId')
        )

    def to_dict(self):
        return {k: v for k, v in self.__dict__.items() if v is not None}


@dataclass
class RoleMenu:
    id: int = None
    menuId: int = None
    roleId: int = None

    @classmethod
    def from_dict(cls, data_dict):
        return cls(
            id=data_dict.get('id'),
            menuId=data_dict.get('menuId'),
            roleId=data_dict.get('roleId')
        )

    def to_dict(self):
        return {k: v for k, v in self.__dict__.items() if v is not None}

@dataclass
class Role:
    createdTime: str = None
    description: str = None
    id: int = None
    modifiedTime: str = None
    name: str = None
    roleCameras: List[RoleCamera] = None
    roleGroups: List[RoleGroup] = None
    roleMenus: List[RoleMenu] = None
    status: int = None
    userRoles: List[UserRole] = None
    server_ip: str = None

    @classmethod
    def from_dict(cls, data_dict):
        role_cameras = [RoleCamera.from_dict(cam) for cam in data_dict.get('roleCameras', [])] if data_dict.get(
            'roleCameras') else []
        role_groups = [RoleGroup.from_dict(group) for group in data_dict.get('roleGroups', [])] if data_dict.get(
            'roleGroups') else []
        role_menus = [RoleMenu.from_dict(menu) for menu in data_dict.get('roleMenus', [])] if data_dict.get(
            'roleMenus') else []
        user_roles = [UserRole.from_dict(user_role) for user_role in data_dict.get('userRoles', [])] if data_dict.get(
            'userRoles') else []

        return cls(
            createdTime=data_dict.get('createdTime'),
            description=data_dict.get('description'),
            id=data_dict.get('id'),
            modifiedTime=data_dict.get('modifiedTime'),
            name=data_dict.get('name'),
            roleCameras=role_cameras,
            roleGroups=role_groups,
            roleMenus=role_menus,
            status=data_dict.get('status'),
            userRoles=user_roles,
            server_ip=data_dict.get('server_ip')
        )

    def to_dict(self):
        return {
            'createdTime': self.createdTime,
            'description': self.description,
            'id': self.id,
            'modifiedTime': self.modifiedTime,
            'name': self.name,
            'roleCameras': [cam.to_dict() for cam in self.roleCameras] if self.roleCameras else [],
            'roleGroups': [group.to_dict() for group in self.roleGroups] if self.roleGroups else [],
            'roleMenus': [menu.to_dict() for menu in self.roleMenus] if self.roleMenus else [],
            'status': self.status,
            'userRoles': [user_role.to_dict() for user_role in self.userRoles] if self.userRoles else [],
            'server_ip': self.server_ip
        }

class RoleModel(QObject):
    change_name_signal = Signal(str)
    change_role_model_signal = Signal(tuple)
    delete_role_signal = Signal(QObject)

    def __init__(self, role: Role = None):
        super().__init__()
        self.data = role

    def to_dict(self):
        return {
            "data": self.data.to_dict()
        }

    @classmethod
    def from_dict(cls, data_dict):
        role_data = data_dict.get("data", {})
        role = Role.from_dict(role_data)
        return cls(role=role)

    def set_role_name(self, name):
        self.data.name = name
        self.change_role_model_signal.emit(('name', name, self))

    def set_description(self, description):
        self.data.description = description
        self.change_role_model_signal.emit(('description', description, self))

    def set_status(self, status):
        self.data.status = status
        self.change_role_model_signal.emit(('status', status, self))

    def set_roleCameras(self, roleCameras: List = []):
        self.data.roleCameras = [RoleCamera.from_dict(cam) for cam in roleCameras] if roleCameras else []
        self.change_role_model_signal.emit(('roleCameras', roleCameras, self))

    def set_roleGroups(self, roleGroups: List = []):
        self.data.roleGroups = [RoleGroup.from_dict(group) for group in roleGroups] if roleGroups else []
        self.change_role_model_signal.emit(('roleGroups', roleGroups, self))

    def set_userRoles(self, userRoles: List = []):
        self.data.userRoles = [UserRole.from_dict(user_role) for user_role in userRoles] if userRoles else []
        self.change_role_model_signal.emit(('userRoles', userRoles, self))

    def set_roleMenus(self, roleMenus: List = []):
        self.data.roleMenus = [RoleMenu.from_dict(menu) for menu in roleMenus] if roleMenus else []
        self.change_role_model_signal.emit(('roleMenus', roleMenus, self))

    def diff_role_model(self, role: Role = None):
        dict1 = asdict(self.data)
        dict2 = asdict(role)
        diff = []
        for field, value in dict2.items():
            if dict1[field] != value:
                if field == 'name':
                    diff.append(field)
                    self.set_role_name(value)
                elif field == 'description':
                    diff.append(field)
                    self.set_description(value)
                elif field == 'status':
                    diff.append(field)
                    self.set_status(value)
                elif field == 'roleCameras':
                    diff.append(field)
                    self.set_roleCameras(value)
                elif field == 'roleGroups':
                    diff.append(field)
                    self.set_roleGroups(value)
                elif field == 'userRoles':
                    diff.append(field)
                    self.set_userRoles(value)
                elif field == 'roleMenus':
                    diff.append(field)
                    self.set_roleMenus(value)
                else:
                    diff.append(field)
        return diff

class RoleModelManager(QObject):
    add_role_list_signal = Signal(tuple)
    delete_role_model_signal = Signal(list)
    add_role_signal = Signal(QObject)
    add_roles_signal = Signal(list)
    __instance = None

    def __init__(self):
        super().__init__()
        self.role_list = {}

    @staticmethod
    def get_instance():
        if RoleModelManager.__instance is None:
            RoleModelManager.__instance = RoleModelManager()
        return RoleModelManager.__instance

    def add_role_list(self, controller=None, role_list: List[RoleModel] = [], server_ip=None):
        output = {}
        for role_model in role_list:
            role_model.data.server_ip = controller.server.data.server_ip
            output[role_model.data.id] = role_model
        self.role_list[controller.server.data.server_ip] = output
        self.add_role_list_signal.emit((controller, role_list))

    def add_role(self, role: RoleModel = None, is_insert_group=None):
        role_result = self.get_role_model(role_id=role.data.id)
        if role_result is None:
            server_ip = role.data.server_ip
            role_id = role.data.id
            # Add the new user at the start of the dictionary
            if server_ip not in self.role_list:
                self.role_list[server_ip] = {}
            # Reorder the dictionary to have the new user at the start
            self.role_list[server_ip] = {role_id: role, **self.role_list[server_ip]}
            # Emit the signal
            self.add_role_signal.emit(role)
            # self.role_list[role.data.server_ip][role.data.id] = role
            # self.add_role_signal.emit(role)

    def update_role_list(self, controller=None, role_list: List[RoleModel] = []):
        for role in role_list:
            for role_model in self.role_list[controller.server.data.server_ip].values():
                if role.data.id == role_model.data.id:
                    role_model.diff_role_model(role=role.data)
                    break

    def find_role_deleted(self, controller=None, role_list: List[RoleModel] = []):
        temp = []
        for role_model in self.role_list[controller.server.data.server_ip].values():
            check = False
            for role in role_list:
                if role_model.data.id == role.data.id:
                    check = True
            if not check:
                temp.append(role_model)

        for role_model in temp:
            role_model.delete_role_signal.emit(role_model)
            del self.role_list[role_model.data.server_ip][role_model.data.id]
        self.delete_role_model_signal.emit(temp)

    def find_role_added(self, role_list: List[RoleModel] = []):
        temp = []
        for role_model in role_list:
            check = False
            for role in self.role_list:
                if role.data.id == role_model.data.id:
                    check = True

            if not check:
                temp.append(role_model)
        for role_model in temp:
            self.role_list.append(role_model)
        self.add_roles_signal.emit(temp)
        return temp

    def get_role_model(self, role_id=None, name=None):
        if role_id is not None:
            for role_lists in self.role_list.values():
                if role_id in role_lists:
                    return role_lists[role_id]
        elif name is not None:
            for role_lists in self.role_list.values():
                for key, item in role_lists.items():
                    if name == item.data.name:
                        return item
        return None

    def get_role_list(self, server_ip=None):
        if server_ip in self.role_list:
            return self.role_list[server_ip]
        else:
            return {}

    def get_children_id(self, parent_role_id=None, children_type=None):
        list_data = []
        parent_model = None
        if parent_role_id is not None:
            for role_lists in self.role_list.values():
                if parent_role_id in role_lists:
                    parent_model: RoleModel = role_lists[parent_role_id]
            if parent_model is None:
                return
            if children_type == ChildrenRoleType.ROLE_CAMERAS and len(parent_model.data.roleCameras) > 0:
                for item in parent_model.data.roleCameras:
                    list_data.append(item.cameraId)

            elif children_type == ChildrenRoleType.ROLE_GROUP and len(parent_model.data.roleGroups) > 0:
                for item in parent_model.data.roleGroups:
                    list_data.append(item.groupId)

            elif children_type == ChildrenRoleType.ROLE_MENUS and len(parent_model.data.roleMenus) > 0:
                for item in parent_model.data.roleMenus:
                    list_data.append(item.menuId)
            else:
                return []
        return list_data

    def delete_role(self, role_model: RoleModel = None):
        self.role_list.remove(role_model)

    def delete_server(self, server_ip=None):
        if server_ip in self.role_list:
            del self.role_list[server_ip]

    def clear(self):
        self.role_list.clear()


role_model_manager = RoleModelManager.get_instance()


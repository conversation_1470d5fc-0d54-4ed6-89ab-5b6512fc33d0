from dataclasses import dataclass
from typing import List

from src.common.model.base_model import BaseModel

@dataclass
class WarningMethodAndAlertModel(BaseModel):
    alertChannels: List[str] = None
    id: int = None
    warningMethods: List[str] = None

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}

from dataclasses import dataclass, field
from typing import List
from src.common.model.base_model import BaseModel
from src.common.model.group_tree_model import GroupTreeModel
from src.common.model.camera_model import Camera


@dataclass
class WrapGroupTreeModel(BaseModel):
    groupTreeDTOList: List['GroupTreeModel'] = None
    cameraDTOList: List['Camera'] = None

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}

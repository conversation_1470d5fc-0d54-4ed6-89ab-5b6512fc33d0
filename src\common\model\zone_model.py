from typing import List
from dataclasses import dataclass, field

from src.common.model.base_model import BaseModel
from src.utils.utils import Utils

# "active": true,
# "cameraId": 0,
# "id": 0,
# "line": "string",
# "name": "string",
# "polygon": "string",
# "type": "ZONE_FLOW_IN"

@dataclass
class ZoneModel(BaseModel):
    id: str = None
    name: str = None
    type: str = None
    polygon: str = None
    line: str = None
    active: bool = None
    aiFlowId: str = None
    aiFlowDTO: dict = None
    doorIds: List[str] = None
    doorDTOs: List[dict] = None
    objects: List[str] = None
    color: str = None
    colorLine: str = None

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    

    def copy(self):
        return ZoneModel.from_dict(self.to_dict())
    
    def convert_point(self, x_scale_to_server, y_scale_to_server):
        if self.polygon:
            self.polygon = Utils.convert_coordinate_points_to_string(Utils.convert_points_to_scale(
                Utils.convert_string_to_coordinate_points(self.polygon), x_scale_to_server, y_scale_to_server))

        # line
        if self.line:
            self.line = Utils.convert_coordinate_points_to_string(Utils.convert_points_to_scale(
                Utils.convert_string_to_coordinate_points(self.line), x_scale_to_server, y_scale_to_server))

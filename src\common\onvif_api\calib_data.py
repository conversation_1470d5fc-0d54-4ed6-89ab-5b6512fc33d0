from typing import List
from PySide6.QtCore import QPoint

class Manufacturer:
    Invalid = 'Invalid'
    Avigilon = 'Avigilon'
    Dahua = 'Dahua'
    Bosch = 'Bosch'
    IPC_Professional = 'IPC Professional'

class Avigilon:
    class PtzCap:
        AUXILIARY = "AUXILIARY"
        FOCUS_CONTINUOUS = "FOCUS_CONTINUOUS"
        PAN_TILT = "PAN_TILT"
        PAN_TILT_ABSOLUTE = "PAN_TILT_ABSOLUTE"
        PAN_TILT_CONTINUOUS = "PAN_TILT_CONTINUOUS"
        PAN_TILT_RELATIVE = "PAN_TILT_RELATIVE"
        PRESETS = "PRESETS"
        TOURS = "TOURS"
        ZOOM_ABSOLUTE = "ZOOM_ABSOLUTE"
        ZOOM_CONTINUOUS = "ZOOM_CONTINUOUS"
        ZOOM_RELATIVE = "ZOOM_RELATIVE"
        
avigilon = [
    {
        'zoom': [0,0.001],
        'pan': 0.087,
        'tilt': 0.052
    },
    {
        'zoom': [0.001,0.02],
        'pan': 0.06,
        'tilt': 0.036
    },
    {
        'zoom': [0.02,0.04],
        'pan': 0.0415,
        'tilt': 0.024
    },
    {
        'zoom': [0.04,0.06],
        'pan': 0.033,
        'tilt': 0.019
    },
    {
        'zoom': [0.06,0.1],
        'pan': 0.023,
        'tilt': 0.0142
    },
    {
        'zoom': [0.1,0.15],
        'pan': 0.017,
        'tilt': 0.01
    },
    {
        'zoom': [0.15,0.3],
        'pan': 0.01,
        'tilt': 0.005
    },
    {
        'zoom': [0.3,0.6],
        'pan': 0.005,
        'tilt': 0.0026
    },
    {
        'zoom': [0.6,1],
        'pan': 0.0025,
        'tilt': 0.0012
    },
    ]
ipc_professional = [
    {
        'zoom': [0,0.04],
        'pan': 0.15,
        'tilt': 0.45
    },
    {
        'zoom': [0.04,0.05],
        'pan': 0.1125,
        'tilt': 0.3125
    },
    {
        'zoom': [0.05,0.06],
        'pan': 0.075,
        'tilt': 0.225
    },
    {
        'zoom': [0.06,0.07],
        'pan': 0.06,
        'tilt': 0.18
    },
    {
        'zoom': [0.07,0.1],
        'pan': 0.05,
        'tilt': 0.15
    },
    {
        'zoom': [0.1,0.2],
        'pan': 0.045,
        'tilt': 0.06
    },
    {
        'zoom': [0.2,1],
        'pan': 0.04,
        'tilt': 0.03
    },
    ]

dahua = [
    {
        'zoom': [0,0.033],
        'pan': 0.17,
        'tilt': 0.39
    },
    {
        'zoom': [0.033,0.036],
        'pan': 0.162,
        'tilt': 0.36
    },
    {
        'zoom': [0.036,0.041],
        'pan': 0.14,
        'tilt': 0.34
    },
    {
        'zoom': [0.041,0.044],
        'pan': 0.13,
        'tilt': 0.32
    },
    {
        'zoom': [0.044,0.047],
        'pan': 0.118,
        'tilt': 0.22
    },
    {
        'zoom': [0.047,0.051],
        'pan': 0.112,
        'tilt': 0.2
    },
    {
        'zoom': [0.051,0.054],
        'pan': 0.1,
        'tilt': 0.2
    },
    {
        'zoom': [0.054,0.057],
        'pan': 0.095,
        'tilt': 0.18
    },
    {
        'zoom': [0.057,0.061],
        'pan': 0.092,
        'tilt': 0.16
    },
    {
        'zoom': [0.061,0.067],
        'pan': 0.088,
        'tilt': 0.14
    },
    {
        'zoom': [0.067,0.071],
        'pan': 0.084,
        'tilt': 0.135
    },
    {
        'zoom': [0.071,0.077],
        'pan': 0.08,
        'tilt': 0.13
    },
    {
        'zoom': [0.077,0.084],
        'pan': 0.07,
        'tilt': 0.12
    },
    {
        'zoom': [0.084,0.087],
        'pan': 0.065,
        'tilt': 0.10
    },
    {
        'zoom': [0.087,0.094],
        'pan': 0.06,
        'tilt': 0.095
    },
    {
        'zoom': [0.094,0.111],
        'pan': 0.055,
        'tilt': 0.09
    },
    {
        'zoom': [0.111,0.117],
        'pan': 0.05,
        'tilt': 0.08
    },
    {
        'zoom': [0.117,0.131],
        'pan': 0.045,
        'tilt': 0.07
    },
    {
        'zoom': [0.131,0.157],
        'pan': 0.04,
        'tilt': 0.06
    },
    {
        'zoom': [0.157,0.2],
        'pan': 0.03,
        'tilt': 0.05
    },
    {
        'zoom': [0.2,1],
        'pan': 0.01,
        'tilt': 0.02
    },
    ]
class CalibData:
    @staticmethod
    def get_data(type:Manufacturer = Manufacturer.Invalid):
        if type == Manufacturer.Avigilon:
            return avigilon
        elif type == Manufacturer.Dahua:
            return dahua
        elif type == Manufacturer.Bosch:
            return dahua
        else:
            return dahua
        
    @staticmethod
    def get_pan_drag_to_zoom(current_zoom = 0,calib_data:List = [],ratio = 1):
        pan = 0
        data1 = 0
        for data in calib_data:
            if current_zoom >= data['zoom'][0] and current_zoom < data['zoom'][1]:
                print(f'data["zoom"][0] = {current_zoom,data["zoom"][0],data["zoom"][1]}')
                pan = ratio * data['pan']
        return pan  
    
    @staticmethod
    def get_tilt_drag_to_zoom(current_zoom = 0,calib_data:List = [],ratio = 1):
        tilt = 0
        data1 = 0
        for data in calib_data:
            if current_zoom >= data['zoom'][0] and current_zoom < data['zoom'][1]:
                data1 = data['tilt']
                tilt = ratio * data['tilt']

        # print(f'tilt = {current_zoom} : {tilt} : {data1}')
        
        return tilt 
     
    @staticmethod
    def get_pan_ptz_arrow(current_zoom = 0,calib_data:List = [],ratio = 1,speed = 1):
        pan = 0
        data1 = 0
        for data in calib_data:
            if current_zoom >= data['zoom'][0] and current_zoom < data['zoom'][1]:
                data1 = data['pan']
                pan = ratio * data['pan'] * speed
                break
        # print(f'pan = {current_zoom} : {pan} : {data1}')
        return pan  
    
    @staticmethod
    def get_tilt_ptz_arrow(current_zoom = 0,calib_data:List = [],ratio = 1,speed = 1):
        tilt = 0
        data1 = 0
        for data in calib_data:
            if current_zoom >= data['zoom'][0] and current_zoom < data['zoom'][1]:
                data1 = data['tilt']
                tilt = ratio * data['tilt'] * speed
                break
        # print(f'tilt = {current_zoom} : {tilt} : {data1}')
        return tilt 
    
    @staticmethod
    def get_point_x(center, center_frame,current_zoom,speed,calib_data): 
        pan = 0
        if center > center_frame:
            sub = center - center_frame
            ratio = sub / center_frame
            pan = CalibData.get_pan_ptz_arrow(current_zoom,calib_data,ratio,speed)
            
        else:
            sub = center_frame - center
            ratio = sub / center_frame
            pan = CalibData.get_pan_ptz_arrow(current_zoom,calib_data,ratio,speed)
            pan = -pan
        # print(f'pan = {pan}')
        return pan
    
    @staticmethod
    def get_point_y(center, center_frame,current_zoom,speed,calib_data): 
        tilt = 0
        if center < center_frame:
            sub = center_frame - center
            ratio = sub / center_frame
            tilt = CalibData.get_tilt_ptz_arrow(current_zoom,calib_data,ratio,speed)

        else:
            sub = center - center_frame
            ratio = sub / center_frame
            tilt = CalibData.get_tilt_ptz_arrow(current_zoom,calib_data,ratio,speed)
            tilt = -tilt
        return tilt
    
    @staticmethod
    def get_coordinate_ptz_arrow(start_point: QPoint = None, end_point: QPoint = None,width = None, height = None,calib_data = None):

        try:

            current_zoom = 0
            center_x = (start_point.x() + end_point.x())/2
            center_y = (start_point.y() + end_point.y())/2
            center_frame_x = width / 2
            center_frame_y = height / 2
            pan = CalibData.get_point_x(end_point.x(), start_point.x(),current_zoom,6,calib_data)
            tilt = CalibData.get_point_y(end_point.y(), start_point.y(),current_zoom,2,calib_data)

            coordinate = {'pan': pan, 'tilt': tilt, 'zoom': 0}
            return coordinate
        except Exception as e:
            print(f'get_coordinate_arrow_ptz error {e}')
            return None
        # return None
    @staticmethod
    def get_coordinate_drag_to_zoom(current_status = None, start_point: QPoint = None, end_point: QPoint = None,width = None, height = None,calib_data = None):
        # Calib Zoom #####################
        # try:
            # current_status = self.ptz.GetStatus(profileToken)
            # print(f'get_coordinate_drag_to_zoom = {current_status}')
            # current_pan = current_status.Position.PanTilt.x
            # current_tilt = current_status.Position.PanTilt.y
            # current_zoom = current_status.Position.Zoom.x
            current_pan = current_status['Position']['PanTilt']['X']
            current_tilt = current_status['Position']['PanTilt']['Y']
            current_zoom = current_status['Position']['Zoom']['X']
            print(f'get_coordinate_drag_to_zoom = {current_pan,current_tilt,current_zoom}')
            ratio = 1
            if current_zoom < 0.4:
                ratio = 1
            elif current_zoom < 0.5:
                ratio = 2
            elif current_zoom < 0.6:
                ratio = 3
            elif current_zoom< 0.7:
                ratio = 4
            else:
                ratio = 5

            if end_point.x() > start_point.x():
                # print(f'get_coordinate_drag_to_zoom {end_point.x()} {start_point.x()} {0.5*width}')
                if abs(end_point.x() - start_point.x()) > 0.5 * width:
                    zoom = 0.009
                    if (current_zoom + zoom) <=1:
                        zoom = 0.009
                    else:
                        zoom = 1 - current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.3 * width:
                    zoom = 0.018
                    if (current_zoom + zoom) <=1:
                        zoom = 0.018
                    else:
                        zoom = 1 - current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.2 * width:
                    zoom = 0.024
                    if (current_zoom + zoom) <=1:
                        zoom = 0.024
                    else:
                        zoom = 1 - current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.1 * width:
                    zoom = 0.03
                    if (current_zoom + zoom) <=1:
                        zoom = 0.03
                    else:
                        zoom = 1 - current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.06 * width:
                    zoom =  0.04
                    if (current_zoom + zoom) <=1:
                        zoom = 0.04
                    else:
                        zoom = 1 - current_zoom
                else:
                    zoom =  0.06
                    if (current_zoom + zoom) <=1:
                        zoom = 0.06
                    else:
                        zoom = 1 - current_zoom
                zoom = zoom * ratio
                if (current_zoom + zoom) <=1:
                        pass
                else:
                    zoom = 1 - current_zoom
            else:
                if abs(end_point.x() - start_point.x()) > 0.5 * width:
                    zoom = -0.009
                    if (current_zoom + zoom) >=0:
                        zoom = -0.009
                    else:
                        zoom = -current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.3 * width:
                    zoom = -0.018
                    if (current_zoom + zoom) >=0:
                        zoom = -0.018
                    else:
                        zoom = -current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.2 * width:
                    zoom = -0.024
                    if (current_zoom + zoom) >=0:
                        zoom = -0.024
                    else:
                        zoom = -current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.1 * width:
                    zoom = -0.03
                    if (current_zoom + zoom) >=0:
                        zoom = -0.03
                    else:
                        zoom = -current_zoom
                elif abs(end_point.x() - start_point.x()) > 0.06 * width:
                    zoom = -0.04
                    if (current_zoom + zoom) >=0:
                        zoom = -0.04
                    else:
                        zoom = -current_zoom
                else:
                    zoom = -0.1
                    if (current_zoom + zoom) >=0:
                        zoom = -0.1
                    else:
                        zoom = -current_zoom
                zoom = zoom * ratio
                if (current_zoom + zoom) >= 0:
                        pass
                else:
                    zoom = -current_zoom

            # Calib Pan Tilt #####################
            center_x = (start_point.x() + end_point.x())//2
            center_y = (start_point.y() + end_point.y())//2
            center_frame_x = width // 2
            center_frame_y = height // 2
            if center_x > center_frame_x:
                sub = center_x - center_frame_x
                ratio = sub / center_frame_x
                pan = CalibData.get_pan_drag_to_zoom(current_zoom,calib_data,ratio)
            else:
                sub = center_frame_x - center_x
                ratio = sub / center_frame_x
                pan = CalibData.get_pan_drag_to_zoom(current_zoom,calib_data,ratio)
                pan = -pan
            if center_y < center_frame_y:
                sub = center_frame_y - center_y
                ratio = sub / center_frame_y
                tilt = CalibData.get_tilt_drag_to_zoom(current_zoom,calib_data,ratio)
            else:
                sub = center_y - center_frame_y
                ratio = sub / center_frame_y
                tilt = CalibData.get_tilt_drag_to_zoom(current_zoom,calib_data,ratio)
                tilt = -tilt
            # zoom = 0
            coordinate = {'pan': pan, 'tilt': tilt, 'zoom': zoom}
            return coordinate
from typing import List
from dataclasses import dataclass

from src.common.model.base_model import BaseModel
# object Camera Model dùng để lưu trữ thông tin của Camera đã khớp với dữ liệu trả về từ API
# và dùng để chuyển đổi thành dữ liệu để hiển thị trên UI
# Không được thay đổi các thuộc tính của object này trừ khi Backend API thay đổi


@dataclass
class CameraModel(BaseModel):
    cameraname: str = None  # not null
    video_source_configuration_token: str = None  # not null
    streams: List['Stream'] = None

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
@dataclass
class Stream(BaseModel):
    rtsp: str = None  # not null
    #resolutions: List['Resolution'] = None
    resolution: str = None
    fps: int = None
    supportedResolutions: str = None
    supportedFps: List[int] = None
    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}

@dataclass
class Resolution(BaseModel):
    width: str = None  # not null
    height: str = None 
    

    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}

from PySide6.QtCore import Signal
from PySide6.QtCore import QThread
from queue import Queue
from typing import Callable, List
import threading
class WorkerThread(QThread):
    result_signal = Signal(bool)

    def __init__(self, parent=None, target=None, callback=None, args=()):
        super(WorkerThread, self).__init__(parent)
        self.daemon = True
        self.callback = callback
        self._target = target
        self._args = args
        if callback is not None:
            self.result_signal.connect(self.callback)

    def run(self):
        try:
            if self._target:
                result = self._target(*self._args)
                self.result_signal.emit(result)
        except Exception as e:
            print(f'WorkerThread error = {e}')
        # finally:
        #     # Avoid a refcycle if the thread is running a function with
        #     # an argument that has a member that points to the thread.
        #     del self._target, self._args
  
def start_threads(number: int, target: Callable, *args) -> List[threading.Thread]:
    threads = []
    for _ in range(number):
        thread = threading.Thread(target=target, args=args)
        thread.daemon = True
        threads.append(thread)
        thread.start()
    return threads

def start_threads1(number: int, target: Callable, *args) -> List[WorkerThread]:
    threads = []
    for _ in range(number):
        thread = WorkerThread(target=target, args=args)
        # thread.daemon = True
        threads.append(thread)
        thread.start()
    return threads
def wait_for1(queue: Queue, threads: List[WorkerThread]):
    queue.join()
    [queue.put(None) for _ in range(len(threads))]
    [t.wait() for t in threads]
def wait_for(queue: Queue, threads: List[threading.Thread]):
    queue.join()
    [queue.put(None) for _ in range(len(threads))]
    [t.join() for t in threads]

// Copyright (C) 2023 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

import QtQuick
import QtQuick.Controls

RoundButton {
    id: button
    // implicitWidth: 30
    // implicitHeight: 30
    radius: buttonRadius
    // icon.source: getIcon()
    icon.width: 24
    icon.height: 24
    // icon.color: getIconColor()
    // include this text property as the calculator engine
    // differentiates buttons through text. The text is never drawn.

    property bool dimmable: true
    property bool dimmed: false
    property bool state: false
    property string modelType: "Camera"
    readonly property color button_focus: device_controller ? device_controller.get_color_theme_by_key("file_button_focus") : "blue"
    readonly property color backgroundColor: "#656475"
    readonly property color borderColor: "#A9A9A9"
    readonly property color on_background: "#262626"
    // readonly property color backspaceRedColor: "#DE2C2C"
    readonly property color backspaceRedColor: "#000000"
    readonly property int buttonRadius: 4

    function getBackgroundColor() {
        // console.log("modelType = ", modelType)
        // if (modelType === "Camera") {
        //     if (button.hovered)
        //         return backgroundItem
        //     if (button.pressed)
        //         return backgroundColor
        //     return "transparent"
        // }
        if (button.hovered)
            return button_focus
        if (button.pressed)
            return backgroundColor
        return "transparent"
    }

    function getBorderColor() {
        if (button.pressed || button.hovered)
            return backspaceRedColor
        return borderColor
    }

    function getIconColor() {
        if (button.pressed)
            return backgroundColor
        return backspaceRedColor
    }

    // function getIcon() {
    //     if (button.dimmable && button.dimmed)
    //         return "images/pen_ver2.svg"
    //     if (button.pressed)
    //         return "images/pen_ver2.svg"
    //     return "images/pen_ver2.svg"
    // }

    // onReleased: {
    //     console.log("ActionButton onReleased")
    //     // root.operatorPressed("bs")
    //     // updateDimmed()
    // }

    background: Rectangle {
        radius: button.buttonRadius
        color: getBackgroundColor()
        // border.color: getBorderColor()
    }
}

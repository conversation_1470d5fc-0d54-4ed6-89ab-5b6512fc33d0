import QtQuick
import QtQuick.Controls
import QtQuick.Layouts


Item {
    id: root
    width: 130
    height: 30  // <PERSON>i<PERSON>u chỉnh theo chiều cao của các phần tử bên trong
    // property alias style: Style {
    //     id: style
    // }
    // requested property var controller
    property color defaultColor: device_controller ? device_controller.get_color_theme_by_key("table_item_header_text") : "white" // Màu chữ mặc định
    property int aiType: 0
    property string buttonType: "Camera"

    readonly property int buttonRadius: 8
    property bool checked: aiType === 2
    signal clicked(str: string)
    signal checkboxClicked(flag: bool)

    Loader {
        id: buttonLoader
        anchors.fill: parent
        sourceComponent: buttonType === "Camera" ? idCamera : idGroup
    }
    Component {
        id: idCamera
        Rectangle {
            id: rectangle
            anchors.fill: parent
            radius: buttonRadius
            color: getButtonColor(1)
            
            MouseArea {
                anchors.fill: parent
                hoverEnabled: true  

                // onEntered: {
                //     rectangle.color = getBackgroundColor(0) // Thay đổi màu khi hover
                // }

                // onExited: {
                //     rectangle.color = getBackgroundColor(1) // Khôi phục lại màu ban đầu khi thoát hover
                // }
                // onReleased: {
                //     console.log("ButtonAIFlow checkbox clicked = ")
                // }
                onClicked: {
                    // root.checked = !root.checked
                    console.log("ButtonAIFlow checkbox clicked = ")
                    // scaleAnimator.running = true
                    // colorAnimator.running = true
                    // rectangle.color = getBackgroundColor(2)
                    root.clicked(ai_text)
                }
            }

            RowLayout {
                spacing: 8
                anchors {
                    fill: parent
                    leftMargin: 8
                    rightMargin: 8
                }

                Item {
                    id: idCheckbox
                    width: 16
                    height: 16
                    Layout.alignment: Qt.AlignVCenter

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            root.checkboxClicked(root.checked)
                            checkboxAnimation.running = true
                        }
                    }

                    Image {
                        id: checkBoxIcon
                        anchors.fill: parent
                        source: root.checked ? checked_icon_path : unchecked_icon_path
                        sourceSize.width: 16
                        sourceSize.height: 16
                        opacity: 1.0
                        rotation: 0
                        smooth: true
                        antialiasing: true

                        SequentialAnimation {
                            id: checkboxAnimation
                            running: false

                            ParallelAnimation {
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "scale"
                                    from: 1.0
                                    to: 1.2
                                    duration: 300
                                    easing.type: Easing.OutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "opacity"
                                    from: 1.0
                                    to: 0.9
                                    duration: 300
                                    easing.type: Easing.OutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "rotation"
                                    from: 0
                                    to: 5
                                    duration: 300
                                    easing.type: Easing.OutCubic
                                }
                            }

                            ParallelAnimation {
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "scale"
                                    from: 1.2
                                    to: 1.0
                                    duration: 300
                                    easing.type: Easing.InOutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "opacity"
                                    from: 0.9
                                    to: 1.0
                                    duration: 300
                                    easing.type: Easing.InOutCubic
                                }
                                PropertyAnimation {
                                    target: checkBoxIcon
                                    property: "rotation"
                                    from: 5
                                    to: 0
                                    duration: 300
                                    easing.type: Easing.InOutCubic
                                }
                            }
                        }
                    }
                }

                CustomText {
                    text: ai_text
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignVCenter
                    defaultColor: getButtonColor(0)
                    font.pixelSize: 12
                    elide: Text.ElideRight
                }
            }
        }
    }

    Component {
        id: idGroup
        Rectangle {
            anchors.fill: parent
            radius: buttonRadius
            color: getButtonColor(1)

            RowLayout {
                spacing: 8
                anchors {
                    fill: parent
                    leftMargin: 8
                    rightMargin: 8
                }

                CustomText {
                    text: ai_text
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignVCenter
                    horizontalAlignment: Text.AlignHCenter
                    defaultColor: getButtonColor(0)
                    font.pixelSize: 12
                    elide: Text.ElideRight
                }
            }

            MouseArea {
                anchors.fill: parent
                hoverEnabled: true
                onClicked: {
                    console.log("idGroup")
                    // root.checked = !root.checked
                    root.clicked(ai_text)
                }
            }
        }
    }
    // property int index: 0
    property string ai_text: "Default Button"
    property bool checkBoxChecked: false

    
    function getBasicButtonColor(component){ // aiType: (0: off, 1: on); component: (0: text, 1: background)
        if(aiType === 0 || aiType === 1){
            if(component === 0){
                return ai_button_text_off
            }
            else if (component === 1){
                return ai_button_background_off
            }
        }
        else if (aiType === 2){
            if(component === 0){
                return ai_button_text_on
            }
            else if (component === 1){
                return ai_button_background_on
            }
        }
        return error_color
    }

    function getCheckboxButtonColor(component){ // aiType: (0: disable, 1: off, 2: on); component: (0: text, 1: background)
        if(aiType === 0){
            if(component === 0){
                return ai_checkbox_text_disabled
            }
            else if (component === 1){
                return ai_checkbox_background_disabled
            }
        }
        else if (aiType === 1){
            if(component === 0){
                return ai_checkbox_text_enabled
            }
            else if (component === 1){
                return ai_checkbox_background_off
            }
        }
        else if (aiType === 2){
            if(component === 0){
                return ai_checkbox_text_enabled
            }
            else if (component === 1){
                return ai_checkbox_background_on
            }
        }
        return error_color
    }

    function getButtonColor(component){ // component: (0: text, 1: background)
        if (buttonType === "Camera"){
            return getCheckboxButtonColor(component)
        }
        else{
            return getBasicButtonColor(component)
        }
    }

    
    
    readonly property color error_color: "#DE2C2C"
    readonly property color ai_button_text_off: device_controller ? device_controller.get_color_theme_by_key("ai_button_text_off") : "blue"
    readonly property color ai_button_text_on: device_controller ? device_controller.get_color_theme_by_key("ai_button_text_on") : "blue"
    readonly property color ai_button_background_off: device_controller ? device_controller.get_color_theme_by_key("ai_button_background_off") : "blue"
    readonly property color ai_button_background_on: device_controller ? device_controller.get_color_theme_by_key("ai_button_background_on") : "blue"

    readonly property color ai_checkbox_background_disabled: device_controller ? device_controller.get_color_theme_by_key("ai_checkbox_background_disabled") : "blue"
    readonly property color ai_checkbox_background_off: device_controller ? device_controller.get_color_theme_by_key("ai_checkbox_background_off") : "blue"
    readonly property color ai_checkbox_background_on: device_controller ? device_controller.get_color_theme_by_key("ai_checkbox_background_on") : "blue"
    readonly property color ai_checkbox_text_enabled: device_controller ? device_controller.get_color_theme_by_key("ai_checkbox_text_enabled") : "blue"
    readonly property color ai_checkbox_text_disabled: device_controller ? device_controller.get_color_theme_by_key("ai_checkbox_text_disabled") : "blue"

    readonly property string checked_icon_path: device_controller ? device_controller.get_image_theme_by_key("checkbox_ver2_checked") : ""
    readonly property string unchecked_icon_path: device_controller ? device_controller.get_image_theme_by_key("checkbox_ver2_unchecked") : ""
}
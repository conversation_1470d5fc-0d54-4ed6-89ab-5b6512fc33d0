import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

RowLayout {
    readonly property string up_arrow_button: device_controller ? device_controller.get_image_theme_by_key("up_arrow") : ""
    readonly property string down_arrow_button: device_controller ? device_controller.get_image_theme_by_key("down_arrow") : ""
    id: rowLayout
    // anchors.fill: parent
    spacing: 10
    // Layout.leftMargin:20
    // Layout.rightMargin:20
    // Layout.horizontalStretchFactor: 1
    property var isExpand

    function getIcon(isExpand) {
        if (isExpand !== undefined) {
            return isExpand.shown? up_arrow_button : down_arrow_button
        }else{
            return down_arrow_button
        }
    }

    ActionButton {
        icon.source: "images/pen_ver2.svg"
        modelType: model.type
        onClicked: {

            console.log("state = ",model.type)
            // root.editData()
            device_controller.action_clicked("Edit",model)
        }
    }
    ActionButton {
        icon.source: "images/trash_ver2.svg"
        modelType: model.type
        onClicked: {
            console.log("state = ")
            device_controller.action_clicked("Delete",model)
        }
    }
    ActionButton {
        icon.source: getIcon(isExpand)
        modelType: model.type
        onClicked: {
            isExpand.shown = !isExpand.shown
            device_controller.action_clicked("Up",model)
        }
    }
}
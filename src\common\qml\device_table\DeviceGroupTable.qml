import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts 
// import QtQuick.Controls.Material 
Rectangle {
    // width: parent.width
    anchors.fill: parent
    // height: 500
    property color main_background: (function(){
        return device_controller ? device_controller.get_color_theme_by_key("widget_background_1") : "#1E1E1E"
    })()
    property color header_background: device_controller ? device_controller.get_color_theme_by_key("widget_background_1") : "#2D2D2D"
    property color primary_color: device_controller ? device_controller.get_color_theme_by_key("table_item_header_name_text") : "#FFFFFF"
    property color normal_text_color: device_controller ? device_controller.get_color_theme_by_key("table_item_header_text") : "#B3B3B3"
    property color hover_color: device_controller ? device_controller.get_color_theme_by_key("hover_color") : "#363636"
    color: main_background
    Item {
        id: header
        // anchors.fill: parent
        width: parent.width
        height: 50  // Điều chỉnh theo chiều cao của các phần tử bên trong
        // color: on_hover_secondary
        Rectangle {
            // width: parent.width
            anchors.fill: parent
            anchors.leftMargin: 10
            anchors.rightMargin: 10
            radius: 8
            color: header_background
            border.color: header_background
            border.width: 0
            RowLayout  {
                spacing: 0
                anchors.fill: parent
                // anchors.fill: parent
                anchors.leftMargin: 10
                anchors.rightMargin: 10
                CustomText {
                    text: qsTr("GROUP NAME")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 36
                    font.pixelSize: 14
                    color: primary_color
                    font.weight: 900
                }
                CustomText {
                    text: qsTr("BOX")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 9
                    font.pixelSize: 14
                    color: primary_color
                    font.weight: 900
                }
                CustomText {
                    text: qsTr("CAMERA")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 10
                    font.pixelSize: 14
                    color: primary_color
                    font.weight: 900
                }
                // CustomText {
                //     text: qsTr("DOOR")
                //     Layout.fillWidth: true
                //     Layout.preferredWidth: 64
                //     Layout.alignment: Qt.AlignLeft
                //     font.pixelSize: 14
                //     color: primary_color
                //     font.weight: 900
                // }
                CustomText {
                    text: qsTr("ACTION")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 8
                    color: primary_color
                    font.pixelSize: 14
                    font.weight: 900
                }
            }
        }
    }

    Flickable {
        id: flickable
        anchors.top: header.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        contentHeight: root.implicitHeight
        // boundsBehavior: Flickable.OvershootBounds

        Pane {
            id: root
            anchors.fill: parent
            background: Rectangle {
                color: main_background  // Thay đổi thành màu nền bạn muốn (ví dụ: màu trắng)
            }
            Column {
                spacing: 10
                anchors.right: parent.right
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                Repeater {
                    model: device_controller ? device_controller.listmodel : []
                    delegate: Column {
                        property bool showList: false
                        // spacing: 10
                        anchors.left: parent.left
                        anchors.right: parent.right
                        Item {
                            width: parent.width
                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong
                            // anchors.fill: parent

                            Rectangle {
                                anchors.fill: parent
                                border.color: primary_color
                                border.width: 0.5
                                radius: 8
                                property real dynamicRadius: paneSettingsList.height > 0 ? 0 : 8
                                bottomLeftRadius: dynamicRadius
                                bottomRightRadius: dynamicRadius
                                color: paneSettingsList.shown ? hover_color : header_background

                                Behavior on bottomLeftRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }
                                Behavior on bottomRightRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    onClicked: {
                                        paneSettingsList.shown = !paneSettingsList.shown
                                    }
                                    onEntered: parent.color = hover_color
                                    onExited: parent.color = paneSettingsList.shown ? hover_color : header_background
                                }
                                RowLayout  {
                                    spacing: 0
                                    anchors.fill: parent
                                    anchors.leftMargin: 10
                                    anchors.rightMargin: 10
                                    CustomText {
                                        text: model && model.name !== undefined ? model.name : "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 4
                                        font.pixelSize: 14
                                        font.bold: true
                                        font.capitalization: Font.AllUppercase
                                        elide: Text.ElideRight
                                        color: primary_color
                                    }
                                    CustomText {
                                        text: model && model.box_count !== undefined ? qsTr("BOX (" + model.box_count + ")") : qsTr("BOX (0)")
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 1
                                        font.pixelSize: 12
                                        elide: Text.ElideRight
                                        color: normal_text_color
                                    }
                                    CustomText {
                                        text: model && model.camera_count !== undefined ? qsTr("CAMERA (" + model.camera_count + ")") : qsTr("CAMERA (0)")
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 1
                                        font.pixelSize: 12
                                        elide: Text.ElideRight
                                        color: normal_text_color
                                    }
                                    // CustomText {
                                    //     text: model && model.door_count !== undefined ? qsTr("REVOLVING DOOR (" + model.door_count + ")") : qsTr("REVOLVING DOOR (0)")
                                    //     Layout.fillWidth: true
                                    //     Layout.preferredWidth: 1
                                    //     font.pixelSize: 12
                                    //     elide: Text.ElideRight
                                    //     color: normal_text_color
                                    // }
                                    Item {
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 6
                                    }
                                    ControlGroup {
                                        isExpand: paneSettingsList
                                    }


                                }

                            }
                        }
                        Pane {
                            id: paneSettingsList

                            // ## relevant part ##
                            property bool shown: false
                            visible: height > 0
                            height: shown ? implicitHeight : 0
                            Behavior on height {
                                NumberAnimation {
                                    duration: 200
                                    easing.type: Easing.InOutQuad
                                }
                            }
                            clip: true
                            // ## relevant part ##

                            // Material.background: "lightblue"
                            padding: 10
                            // anchors.topMargin: 10
                            anchors.left: parent.left
                            anchors.right: parent.right
                            background: Rectangle {
                                color: hover_color
                                radius: 8
                                property real dynamicRadius: paneSettingsList.height > 0 ? 0 : 8
                                topLeftRadius: dynamicRadius
                                topRightRadius: dynamicRadius

                                Behavior on topLeftRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }
                                Behavior on topRightRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }
                            }
                            Column {
                                // anchors.topMargin: 10
                                spacing: 10
                                anchors.right: parent.right
                                anchors.left: parent.left

                                Item {
                                    width: parent.width
                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                    // anchors.fill: parent
                                    // anchors.topMargin: 10
                                    visible: paneSettingsList
                                    RowLayout  {
                                        spacing: 10
                                        // anchors.topMargin: 10
                                        anchors.left: parent.left
                                        // anchors.top: parent.top
                                        // anchors.bottom: parent.bottom
                                        // anchors.leftMargin: 10
                                        // anchors.rightMargin: 10
                                        Image {
                                            width: 20
                                            height: 20
                                            source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                        }
                                        CustomText {
                                            Layout.preferredWidth:150
                                            text: qsTr("Recognition & Protection (%1)").arg(model.recognition_protection || 0)
                                            font.pixelSize: 12
                                            color: normal_text_color
                                        }
                                        ButtonAIFlow {
                                            ai_text: qsTr("Recognition")
                                            buttonType: type
                                            aiType: model.recognition.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("recognition",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("recognition",model,true)
                                            }
                                        } 
                                        ButtonAIFlow {
                                            ai_text: qsTr("Protection")
                                            buttonType: type
                                            aiType: model.protection.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("protection",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("protection",model,true)
                                            }
                                        }   
                                        ButtonAIFlow {
                                            ai_text: qsTr("Frequency")
                                            buttonType: type
                                            aiType: model.frequency.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("frequency",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("frequency",model,true)
                                            }
                                        }  
                                        ButtonAIFlow {
                                            ai_text: qsTr("Access")
                                            buttonType: type
                                            aiType: model.access.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("access",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("access",model,true)
                                            }
                                        }  
                                        ButtonAIFlow {
                                            ai_text: qsTr("Motion")
                                            buttonType: type
                                            aiType: model.motion.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("motion",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("motion",model,true)
                                            }
                                        } 
                                        ButtonAIFlow {
                                            ai_text: qsTr("Traffic")
                                            buttonType: type
                                            aiType: model.traffic.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("traffic",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("traffic",model,true)
                                            }
                                        }   
                                    }
                                }
                                Item {
                                    width: parent.width
                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                    // anchors.fill: parent
                                    visible: paneSettingsList
                                    RowLayout  {
                                        spacing: 10
                                        anchors.left: parent.left
                                        // anchors.leftMargin: 10
                                        // anchors.rightMargin: 10
                                        Image {
                                            width: 20
                                            height: 20
                                            source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                        }
                                        CustomText {
                                            Layout.preferredWidth:150
                                            text: qsTr("Risk Recognition (%1)").arg(model.risk_identification || 0)
                                            font.pixelSize: 12
                                            color: normal_text_color
                                        }
                                        ButtonAIFlow {
                                            ai_text: qsTr("Weapon")
                                            buttonType: type
                                            aiType: model.weapon.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("weapon",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("weapon",model,true)
                                            }
                                        }  
                                        ButtonAIFlow {
                                            ai_text: qsTr("UFO")
                                            buttonType: type
                                            aiType: model.ufo.state
                                            onClicked: {
                                                device_controller.aiflow_clicked("ufo",model,false)
                                            }
                                            onCheckboxClicked: {
                                                device_controller.aiflow_clicked("ufo",model,true)
                                            }
                                        }   
                                    }
                                }
                                Repeater {
                                    id: listSettings1
                                    property bool showList: false
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    model: child
                                    delegate: Column {
                                        property bool showList: false
                                        anchors.left: parent.left
                                        anchors.right: parent.right
                                        Item {
                                            width: parent.width
                                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                            // anchors.fill: parent
                                            Rectangle {
                                                anchors.fill: parent
                                                border.color: model.type === "AIBox" ? primary_color : "transparent"
                                                // border.color: "transparent"
                                                // border.width: model.type === "AIBox" ? 1 : 0
                                                border.width: 0.5
                                                radius: model.type === "AIBox" ? 8 : 0
                                                property real dynamicRadius: paneSettingsList1.height > 0 ? 0 : 8
                                                bottomLeftRadius: dynamicRadius
                                                bottomRightRadius: dynamicRadius
                                                color: paneSettingsList1.shown ? hover_color : model.type === "AIBox" ? header_background : "transparent"

                                                Behavior on bottomLeftRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }
                                                Behavior on bottomRightRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }

                                                MouseArea {
                                                    anchors.fill: parent
                                                    hoverEnabled: true
                                                    onClicked: {
                                                        paneSettingsList1.shown = !paneSettingsList1.shown
                                                    }
                                                    onEntered: parent.color = hover_color
                                                    onExited: parent.color = paneSettingsList1.shown ? hover_color : model.type === "AIBox" ? header_background : "transparent"
                                                }
                                                RowLayout  {
                                                    spacing: 0
                                                    anchors.fill: parent
                                                    // anchors.left: parent.left
                                                    // anchors.right: parent.right
                                                    // anchors.top: parent.top
                                                    // anchors.bottom: parent.bottom
                                                    anchors.leftMargin: model.type === "AIBox" ? 10 : 0
                                                    anchors.rightMargin: model.type === "AIBox" ? 10 : 10
                                                    CustomText {
                                                        text: name
                                                        font.capitalization: Font.AllUppercase
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 8
                                                        font.pixelSize: 14
                                                        font.bold: true
                                                        elide: Text.ElideRight
                                                        color: primary_color
                                                        // font.pixelSize: 12
                                                    }
                                                    CustomText {
                                                        text: qsTr("Number of AI selected")
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 3
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    Image {
                                                        width: 16
                                                        height: 16
                                                        source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                    }
                                                    CustomText {
                                                        text: "(" + model.human_count + ")"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 1
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    Image {
                                                        width: 16
                                                        height: 16
                                                        source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                    }
                                                    CustomText {
                                                        text: "(" + model.vehicle_count + ")"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 1
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    Item {
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 12
                                                    }
                                                    ControlGroup {
                                                        isExpand: paneSettingsList1
                                                    }

                                                }
                                                Rectangle {
                                                    anchors.bottom: parent.bottom
                                                    anchors.left: parent.left
                                                    anchors.right: parent.right
                                                    // anchors.leftMargin: 30
                                                    // anchors.rightMargin: 30
                                                    height: model.type === "Camera" ? 1 : 0  // Độ dày của line
                                                    color: header_background  // Màu của line
                                                }
                                            }
                                        }

                                        Pane {
                                            id: paneSettingsList1

                                            // ## relevant part ##
                                            property bool shown: false
                                            visible: height > 0
                                            height: shown ? implicitHeight : 0
                                            Behavior on height {
                                                NumberAnimation {
                                                    duration: 200
                                                    easing.type: Easing.InOutQuad
                                                }
                                            }
                                            clip: true
                                            // ## relevant part ##

                                            // Material.background: "lightblue"
                                            padding: 10
                                            anchors.left: parent.left
                                            anchors.right: parent.right
                                            background: Rectangle {
                                                color: hover_color
                                                radius: 8
                                                property real dynamicRadius: paneSettingsList1.height > 0 ? 0 : 8
                                                topLeftRadius: dynamicRadius
                                                topRightRadius: dynamicRadius

                                                Behavior on topLeftRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }
                                                Behavior on topRightRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }
                                            }
                                            Column {
                                                spacing: 10
                                                anchors.right: parent.right
                                                anchors.left: parent.left
                                                Item {
                                                    width: parent.width
                                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                    // anchors.fill: parent
                                                    visible: paneSettingsList1
                                                    RowLayout  {
                                                        spacing: 10
                                                        anchors.left: parent.left
                                                        // anchors.leftMargin: 20
                                                        // anchors.rightMargin: 20
                                                        Image {
                                                            width: 20
                                                            height: 20
                                                            source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                        }
                                                        CustomText {
                                                            Layout.preferredWidth:150
                                                            text: qsTr("Recognition & Protection (%1)").arg(model.recognition_protection || 0)
                                                            font.pixelSize: 12
                                                            color: normal_text_color
                                                        }
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("Recognition")
                                                            buttonType: type
                                                            aiType: model.recognition.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.recognition.state)
                                                                device_controller.aiflow_clicked("recognition",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("recognition",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("Protection")
                                                            buttonType: type
                                                            aiType: model.protection.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.protection.state)
                                                                device_controller.aiflow_clicked("protection",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("protection",model,true)
                                                            }
                                                        }    
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("Frequency")
                                                            buttonType: type
                                                            aiType: model.frequency.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.frequency.state)
                                                                device_controller.aiflow_clicked("frequency",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("frequency",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("Access")
                                                            buttonType: type
                                                            aiType: model.access.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.access.state)
                                                                device_controller.aiflow_clicked("access",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("access",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("Motion")
                                                            buttonType: type
                                                            aiType: model.motion.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.motion.state)
                                                                device_controller.aiflow_clicked("motion",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("motion",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("Traffic")
                                                            buttonType: type
                                                            aiType: model.traffic.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.traffic.state)
                                                                device_controller.aiflow_clicked("traffic",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("traffic",model,true)
                                                            }
                                                        }
                                                    }
                                                }
                                                Item {
                                                    width: parent.width
                                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                    // anchors.fill: parent
                                                    visible: paneSettingsList1
                                                    RowLayout  {
                                                        spacing: 10
                                                        anchors.left: parent.left
                                                        // anchors.leftMargin: 20
                                                        // anchors.rightMargin: 20
                                                        Image {
                                                            width: 20
                                                            height: 20
                                                            source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                        }
                                                        CustomText {
                                                            Layout.preferredWidth:150
                                                            text: qsTr("Risk Recognition (%1)").arg(model.risk_identification || 0)
                                                            font.pixelSize: 12
                                                            color: normal_text_color
                                                        }
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("Weapon")
                                                            buttonType: type
                                                            aiType: model.weapon.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.weapon.state)
                                                                device_controller.aiflow_clicked("weapon",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("weapon",model,true)
                                                            }
                                                        }
                                                        ButtonAIFlow {
                                                            ai_text: qsTr("UFO")
                                                            buttonType: type
                                                            aiType: model.ufo.state
                                                            onClicked: {
                                                                console.log("Add Abc = ",ai_text,index,model.ufo.state)
                                                                device_controller.aiflow_clicked("ufo",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                device_controller.aiflow_clicked("ufo",model,true)
                                                            }
                                                        }
                                                    }
                                                }
                                                Repeater {
                                                    id: listSettings2
                                                    property bool showList: false
                                                    anchors.left: parent.left
                                                    anchors.right: parent.right
                                                    model: child
                                                    delegate: Column {
                                                        // property bool showList: false
                                                        // property bool paneSettingsList1: false
                                                        // spacing: 10
                                                        anchors.left: parent.left
                                                        anchors.right: parent.right
                                                        Item {
                                                            width: parent.width
                                                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                            // anchors.fill: parent
                                                            MouseArea {
                                                                anchors.fill: parent
                                                                onClicked: {
                                                                    paneSettingsList2.shown = !paneSettingsList2.shown
                                                                    // console.log("visibleCameraList = ",name)
                                                                }
                                                            }
                                                            RowLayout  {
                                                                spacing: 0
                                                                anchors.fill: parent
                                                                // anchors.left: parent.left
                                                                // anchors.right: parent.right
                                                                // anchors.top: parent.top
                                                                // anchors.bottom: parent.bottom
                                                                // anchors.leftMargin: 30
                                                                // anchors.rightMargin: 30
                                                                CustomText {
                                                                    text: name
                                                                    font.capitalization: Font.AllUppercase
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 8
                                                                    font.pixelSize: 14
                                                                    font.bold: true
                                                                    elide: Text.ElideRight
                                                                    color: primary_color
                                                                    // font.pixelSize: 12
                                                                }

                                                                CustomText {
                                                                    text: qsTr("Number of AI selected")
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 3
                                                                    color: normal_text_color
                                                                    // font.pixelSize: 12
                                                                }
                                                                Image {
                                                                    width: 16
                                                                    height: 16
                                                                    source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                                }
                                                                CustomText {
                                                                    text: "(" + model.human_count + ")"
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 1
                                                                    color: normal_text_color
                                                                    // font.pixelSize: 12
                                                                }
                                                                Image {
                                                                    width: 16
                                                                    height: 16
                                                                    source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                                }
                                                                CustomText {
                                                                    text: "(" + model.vehicle_count + ")"
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 1
                                                                    color: normal_text_color
                                                                    // font.pixelSize: 12
                                                                }
                                                                Item {
                                                                    Layout.fillWidth: true
                                                                    Layout.preferredWidth: 12
                                                                }
                                                                ControlGroup {
                                                                    isExpand: paneSettingsList2
                                                                }

                                                            }

                                                            Rectangle {
                                                                anchors.bottom: parent.bottom
                                                                anchors.left: parent.left
                                                                anchors.right: parent.right
                                                                // anchors.leftMargin: 30
                                                                // anchors.rightMargin: 30
                                                                height: 1  // Độ dày của line
                                                                color: header_background  // Màu của line
                                                            }
                                                        }
                                                        Pane {
                                                            id: paneSettingsList2

                                                            // ## relevant part ##
                                                            property bool shown: false
                                                            visible: height > 0
                                                            height: shown ? implicitHeight : 0
                                                            Behavior on height {
                                                                NumberAnimation {
                                                                    duration: 200
                                                                    easing.type: Easing.InOutQuad
                                                                }
                                                            }
                                                            clip: true
                                                            // ## relevant part ##

                                                            // Material.background: "lightblue"
                                                            padding: 10
                                                            anchors.left: parent.left
                                                            anchors.right: parent.right
                                                            background: Rectangle {
                                                                color: hover_color
                                                                radius: 8
                                                                property real dynamicRadius: paneSettingsList2.height > 0 ? 0 : 8
                                                                topLeftRadius: dynamicRadius
                                                                topRightRadius: dynamicRadius
                                                            }
                                                            Column {
                                                                anchors.right: parent.right
                                                                anchors.left: parent.left
                                                                Item {
                                                                    width: parent.width
                                                                    height: 40  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                                    // anchors.fill: parent
                                                                    visible: paneSettingsList2
                                                                    RowLayout  {
                                                                        spacing: 10
                                                                        anchors.left: parent.left
                                                                        // anchors.leftMargin: 30
                                                                        // anchors.rightMargin: 30
                                                                        Image {
                                                                            width: 20
                                                                            height: 20
                                                                            source: "../../../../src/assets/ai_icons/ic_recognition_security.svg" // Thay đổi icon dựa trên trạng thái
                                                                        }
                                                                        CustomText {
                                                                            Layout.preferredWidth:150
                                                                            text: qsTr("Recognition & Protection (%1)").arg(model.recognition_protection || 0)
                                                                            font.pixelSize: 12
                                                                            color: normal_text_color
                                                                        }
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Recognition")
                                                                            buttonType: type
                                                                            aiType: model.recognition.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("recognition",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("recognition",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Protection")
                                                                            buttonType: type
                                                                            aiType: model.protection.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("protection",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("protection",model,true)
                                                                            }
                                                                        }   
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Frequency")
                                                                            buttonType: type
                                                                            aiType: model.frequency.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("frequency",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("frequency",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Access")
                                                                            buttonType: type
                                                                            aiType: model.access.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("access",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("access",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Motion")
                                                                            buttonType: type
                                                                            aiType: model.motion.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("motion",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("motion",model,true)
                                                                            }
                                                                        } 
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Traffic")
                                                                            buttonType: type
                                                                            aiType: model.traffic.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("traffic",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("traffic",model,true)
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                                Item {
                                                                    width: parent.width
                                                                    height: 40  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                                    // anchors.fill: parent
                                                                    visible: paneSettingsList2
                                                                    RowLayout  {
                                                                        spacing: 10
                                                                        anchors.left: parent.left
                                                                        // anchors.leftMargin: 30
                                                                        // anchors.rightMargin: 30
                                                                        Image {
                                                                            width: 20
                                                                            height: 20
                                                                            source: "../../../../src/assets/ai_icons/ic_risk_identification.svg" // Thay đổi icon dựa trên trạng thái
                                                                        }
                                                                        CustomText {
                                                                            Layout.preferredWidth:150
                                                                            text: qsTr("Risk Recognition (%1)").arg(model.risk_identification || 0)
                                                                            font.pixelSize: 12
                                                                            color: normal_text_color
                                                                        }
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("Weapon")
                                                                            buttonType: type
                                                                            aiType: model.weapon.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("weapon",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("weapon",model,true)
                                                                            }
                                                                        }
                                                                        ButtonAIFlow {
                                                                            ai_text: qsTr("UFO")
                                                                            buttonType: type
                                                                            aiType: model.ufo.state
                                                                            onClicked: {
                                                                                // console.log("Intrusion = ",device_controller.listmodel)
                                                                                device_controller.aiflow_clicked("ufo",model,false)
                                                                            }
                                                                            onCheckboxClicked: {
                                                                                device_controller.aiflow_clicked("ufo",model,true)
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }

                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }

        ScrollIndicator.vertical: ScrollIndicator { }

    }
}

import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts 
// import "./ScaleAnimation.qml"
// import QtQuick.Controls.Material 

Rectangle {
    // width: parent.width
    anchors.fill: parent
    property color main_background: (function(){
        return device_controller ? device_controller.get_color_theme_by_key("widget_background_1") : "#1E1E1E"
    })()
    property color header_background: device_controller ? device_controller.get_color_theme_by_key("widget_background_1") : "#2D2D2D"
    property color primary_color: device_controller ? device_controller.get_color_theme_by_key("table_item_header_name_text") : "#FFFFFF"
    property color normal_text_color: device_controller ? device_controller.get_color_theme_by_key("table_item_header_text") : "#B3B3B3"
    property color hover_color: device_controller ? device_controller.get_color_theme_by_key("hover_color") : "#363636"
    color: main_background
    Item {
        id: header
        // anchors.fill: parent
        width: parent.width
        height: 50  // Điều chỉnh theo chiều cao của các phần tử bên trong
        // color: on_hover_secondary
        Rectangle {
            // width: parent.width
            anchors.fill: parent
            anchors.leftMargin: 10
            anchors.rightMargin: 10
            radius: 8
            color: header_background
            border.color: header_background
            border.width: 0
            RowLayout  {
                spacing: 0
                anchors.fill: parent
                // anchors.fill: parent
                anchors.leftMargin: 10
                anchors.rightMargin: 10
                CustomText {
                    text: qsTr("DEVICE NAME")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 6
                    font.pixelSize: 14
                    color: primary_color
                }
                CustomText {
                    text: qsTr("BRANCH")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 3
                    font.pixelSize: 14
                    color: primary_color
                }
                CustomText {
                    text: qsTr("MODEL")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 3
                    font.pixelSize: 14
                    color: primary_color
                }
                CustomText {
                    text: qsTr("IP ADDRESS")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 3
                    font.pixelSize: 14
                    color: primary_color
                }
                CustomText {
                    text: qsTr("MAC ADDRESS")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 3
                    font.pixelSize: 14
                    color: primary_color
                }
                CustomText {
                    text: qsTr("PARTNER")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 3
                    font.pixelSize: 14
                    color: primary_color
                }
                CustomText {
                    text: qsTr("GROUP")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 3
                    font.pixelSize: 14
                    color: primary_color
                }
                CustomText {
                    text: qsTr("ACTION")
                    Layout.fillWidth: true
                    Layout.preferredWidth: 2
                    color: primary_color
                    // Layout.alignment: Qt.AlignCenter 
                    font.pixelSize: 14
                }
            }
        }
    }
    Flickable {
        id: flickable
        // anchors.fill: parent
        anchors.top: header.bottom
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        contentHeight: root.implicitHeight
        clip: true
        boundsBehavior: Flickable.OvershootBounds
        Pane {
            id: root
            anchors.fill: parent
            background: Rectangle {
                color: main_background  // Thay đổi thành màu nền bạn muốn (ví dụ: màu trắng)
            }
            Column {
                spacing: 10
                anchors.right: parent.right
                anchors.left: parent.left
                anchors.verticalCenter: parent.verticalCenter
                Repeater {
                    model: device_controller && device_controller.listmodel ? device_controller.listmodel : []
                    delegate: Column {
                        property bool showList: false
                        // spacing: 10
                        anchors.left: parent.left
                        anchors.right: parent.right
                        Item {
                            width: parent.width
                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong

                            Rectangle {
                                anchors.fill: parent
                                anchors.left: parent.left
                                radius: 8
                                property real dynamicRadius: paneSettingsList.height > 0 ? 0 : 8
                                bottomLeftRadius: dynamicRadius
                                bottomRightRadius: dynamicRadius
                                color: paneSettingsList.shown ? hover_color : header_background

                                Behavior on bottomLeftRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }
                                Behavior on bottomRightRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }

                                MouseArea {
                                    anchors.fill: parent
                                    hoverEnabled: true
                                    onClicked: {
                                        paneSettingsList.shown = !paneSettingsList.shown
                                    }
                                    onEntered: parent.color = hover_color
                                    onExited: parent.color = paneSettingsList.shown ? hover_color : header_background
                                }
                                RowLayout  {
                                    spacing: 0
                                    anchors.fill: parent
                                    anchors.leftMargin: 10
                                    anchors.rightMargin: 10
                                    CustomText {
                                        text: model && model.name !== undefined ? model.name : "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 6
                                        font.pixelSize: 14
                                        elide: Text.ElideRight
                                        color: primary_color
                                    }
                                    CustomText {
                                        text: branch !== undefined ? branch : "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 3
                                        font.pixelSize: 12
                                        color: normal_text_color
                                    }
                                    CustomText {
                                        text: device_model !== undefined ? device_model : "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 3
                                        font.pixelSize: 12
                                        color: normal_text_color
                                    }
                                    CustomText {
                                        text: model && model.ipaddress !== undefined ? model.ipaddress : "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 3
                                        font.pixelSize: 14
                                        elide: Text.ElideRight
                                        color: primary_color
                                    }
                                    CustomText {
                                        text: macaddress !== undefined ? macaddress : "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 3
                                        font.pixelSize: 12
                                        color: normal_text_color
                                    }
                                    CustomText {
                                        text: partner !== undefined ? partner: "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 3
                                        font.pixelSize: 12
                                        color: normal_text_color
                                    }
                                    CustomText {
                                        text: group !== undefined ? group : "-"
                                        Layout.fillWidth: true
                                        Layout.preferredWidth: 3
                                        font.pixelSize: 12
                                        color: normal_text_color
                                    }
                                    // CustomText {
                                    //     text: "Group"
                                    //     Layout.fillWidth: true
                                    //     Layout.preferredWidth: 1
                                    //     font.pixelSize: 12
                                    // }
                                    ControlGroup {
                                        Layout.maximumWidth: 140
                                        isExpand: paneSettingsList
                                    }

                                }

                            }
                        }
                        Pane {
                            id: paneSettingsList

                            // ## relevant part ##
                            property bool shown: false
                            visible: height > 0
                            height: shown ? implicitHeight : 0
                            Behavior on height {
                                NumberAnimation {
                                    duration: 200
                                    easing.type: Easing.InOutQuad
                                }
                            }
                            clip: true
                            // ## relevant part ##

                            // Material.background: "lightblue"
                            topPadding: 10
                            anchors.left: parent.left
                            anchors.right: parent.right
                            background: Rectangle {
                                color: hover_color
                                radius: 8
                                property real dynamicRadius: paneSettingsList.height > 0 ? 0 : 8
                                topLeftRadius: dynamicRadius
                                topRightRadius: dynamicRadius

                                Behavior on topLeftRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }
                                Behavior on topRightRadius {
                                    enabled: !paneSettingsList.shown
                                    NumberAnimation { 
                                        duration: 200
                                        easing.type: Easing.InOutQuad 
                                    }
                                }
                            }
                            Column {
                                spacing: 10
                                anchors.right: parent.right
                                anchors.left: parent.left
                                Item {
                                    width: parent.width
                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                    // anchors.fill: parent
                                    visible: paneSettingsList
                                    RowLayout  {
                                        spacing: 16
                                        anchors.left: parent.left
                                        Image {
                                            width: 20
                                            height: 20
                                            source: "../../../../src/assets/ai_icons/ic_recognition_security.svg"
                                        }
                                        Row {
                                            spacing: 0
                                            CustomText {
                                                text: qsTr("Recognition & Protection")
                                                font.pixelSize: 12
                                                color: normal_text_color
                                            }
                                            Item {
                                                width: 50
                                                height: parent.height
                                                CustomText {
                                                    anchors.centerIn: parent
                                                    text: " (" + (model.recognition_protection || 0) + ")"
                                                    font.pixelSize: 12
                                                    color: normal_text_color
                                                    
                                                    property int previousValue: model.recognition_protection || 0
                                                    
                                                    ScaleAnimation {
                                                        id: countScaleAnimation
                                                        target: parent
                                                    }
                                                    
                                                    onTextChanged: {
                                                        if (previousValue !== (model.recognition_protection || 0)) {
                                                            countScaleAnimation.start()
                                                            previousValue = model.recognition_protection || 0
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("Recognition")
                                            buttonType: type
                                            aiType: model.recognition.state
                                            onClicked: {
                                                console.log("Recognition button clicked - state:", model.recognition.state)
                                                device_controller.aiflow_clicked("recognition",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("Recognition checkbox clicked - state:", model.recognition.state)
                                                device_controller.aiflow_clicked("recognition",model,true)
                                            }
                                        } 
                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("Protection")
                                            buttonType: type
                                            aiType: model.protection.state
                                            onClicked: {
                                                console.log("Protection button clicked - state:", model.protection.state)
                                                device_controller.aiflow_clicked("protection",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("Protection checkbox clicked - state:", model.protection.state)
                                                device_controller.aiflow_clicked("protection",model,true)
                                            }
                                        }   
                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("Frequency")
                                            buttonType: type
                                            aiType: model.frequency.state
                                            onClicked: {
                                                console.log("Frequency button clicked - state:", model.frequency.state)
                                                device_controller.aiflow_clicked("frequency",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("Frequency checkbox clicked - state:", model.frequency.state)
                                                device_controller.aiflow_clicked("frequency",model,true)
                                            }
                                        }     
                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("Access")
                                            buttonType: type
                                            aiType: model.access.state
                                            onClicked: {
                                                console.log("Access button clicked - state:", model.access.state)
                                                device_controller.aiflow_clicked("access",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("Access checkbox clicked - state:", model.access.state)
                                                device_controller.aiflow_clicked("access",model,true)
                                            }
                                        }    
                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("Motion")
                                            buttonType: type
                                            aiType: model.motion.state
                                            onClicked: {
                                                console.log("Motion button clicked - state:", model.motion.state)
                                                device_controller.aiflow_clicked("motion",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("Motion checkbox clicked - state:", model.motion.state)
                                                device_controller.aiflow_clicked("motion",model,true)
                                            }
                                        }   
                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("Traffic")
                                            buttonType: type
                                            aiType: model.traffic.state
                                            onClicked: {
                                                console.log("Traffic button clicked - state:", model.traffic.state)
                                                device_controller.aiflow_clicked("traffic",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("Traffic checkbox clicked - state:", model.traffic.state)
                                                device_controller.aiflow_clicked("traffic",model,true)
                                            }
                                        }   
                                    }
                                }
                                Item {
                                    width: parent.width
                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                    // anchors.fill: parent
                                    visible: paneSettingsList
                                    RowLayout  {
                                        spacing: 16
                                        anchors.left: parent.left
                                        Image {
                                            width: 20
                                            height: 20
                                            source: "../../../../src/assets/ai_icons/ic_risk_identification.svg"
                                        }
                                        Row {
                                            spacing: 0
                                            CustomText {
                                                text: qsTr("Risk Recognition")
                                                font.pixelSize: 12
                                                color: normal_text_color
                                            }
                                            Item {
                                                width: 50
                                                height: parent.height
                                                CustomText {
                                                    anchors.centerIn: parent
                                                    text: " (" + (model.risk_identification || 0) + ")"
                                                    font.pixelSize: 12
                                                    color: normal_text_color
                                                    
                                                    property int previousValue: model.risk_identification || 0
                                                    
                                                    ScaleAnimation {
                                                        id: riskCountScaleAnimation
                                                        target: parent
                                                    }
                                                    
                                                    onTextChanged: {
                                                        if (previousValue !== (model.risk_identification || 0)) {
                                                            riskCountScaleAnimation.start()
                                                            previousValue = model.risk_identification || 0
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("Weapon")
                                            buttonType: type
                                            aiType: model.weapon.state
                                            onClicked: {
                                                console.log("Weapon button clicked - state:", model.weapon.state)
                                                device_controller.aiflow_clicked("weapon",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("Weapon checkbox clicked - state:", model.weapon.state)
                                                device_controller.aiflow_clicked("weapon",model,true)
                                            }
                                        }
                                        ButtonAIFlow {
                                            Layout.preferredWidth: 140
                                            ai_text: qsTr("UFO")
                                            buttonType: type
                                            aiType: model.ufo.state
                                            onClicked: {
                                                console.log("UFO button clicked - state:", model.ufo.state)
                                                device_controller.aiflow_clicked("ufo",model,false)
                                            }
                                            onCheckboxClicked: {
                                                console.log("UFO checkbox clicked - state:", model.ufo.state)
                                                device_controller.aiflow_clicked("ufo",model,true)
                                            }
                                        }
                                    }
                                }
                                Repeater {
                                    id: listSettings1
                                    property bool showList: false
                                    
                                    anchors.left: parent.left
                                    anchors.right: parent.right
                                    model: child
                                    delegate: Column {
                                        // property bool showList: false
                                        // property bool paneSettingsList1: false
                                        // spacing: 10
                                        anchors.left: parent.left
                                        anchors.right: parent.right
                                        Item {
                                            width: parent.width
                                            height: 45  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                            // anchors.fill: parent
                                            Rectangle {
                                                anchors.fill: parent
                                                radius: 8
                                                property real dynamicRadius: paneSettingsList1.height > 0 ? 0 : 8
                                                bottomLeftRadius: dynamicRadius
                                                bottomRightRadius: dynamicRadius
                                                color: paneSettingsList1.shown ? hover_color : "transparent"

                                                Behavior on bottomLeftRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }
                                                Behavior on bottomRightRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }

                                                MouseArea {
                                                    anchors.fill: parent
                                                    hoverEnabled: true
                                                    onClicked: {
                                                        paneSettingsList1.shown = !paneSettingsList1.shown
                                                    }
                                                    onEntered: parent.color = hover_color
                                                    onExited: parent.color = paneSettingsList1.shown ? hover_color : "transparent"
                                                }
                                                RowLayout  {
                                                    spacing: 0
                                                    anchors.fill: parent
                                                    // anchors.left: parent.left
                                                    // anchors.right: parent.right
                                                    // anchors.top: parent.top
                                                    // anchors.bottom: parent.bottom
                                                    // anchors.leftMargin: 20
                                                    // anchors.rightMargin: 20
                                                    CustomText {
                                                        text: name
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 6
                                                        font.pixelSize: 14
                                                        color: primary_color
                                                        // font.pixelSize: 12
                                                        elide: Text.ElideRight
                                                    }
                                                    CustomText {
                                                        text: branch !== undefined ? branch : "-"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 3
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    CustomText {
                                                        text: device_model !== undefined ? device_model : "-"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 3
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    CustomText {
                                                        text: ipaddress !== undefined ? ipaddress : "-"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 3
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    CustomText {
                                                        text: macaddress !== undefined ? macaddress : "-"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 3
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    CustomText {
                                                        text: partner !== undefined ? partner : "-"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 3
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    CustomText {
                                                        text: group !== undefined ? group : "-"
                                                        Layout.fillWidth: true
                                                        Layout.preferredWidth: 3
                                                        color: normal_text_color
                                                        // font.pixelSize: 12
                                                    }
                                                    ControlGroup {
                                                        Layout.maximumWidth: 140
                                                        isExpand: paneSettingsList1
                                                    }
                                                }

                                                Rectangle {
                                                    anchors.bottom: parent.bottom
                                                    anchors.left: parent.left
                                                    anchors.right: parent.right
                                                    height: 1
                                                    color: header_background
                                                }
                                            }
                                        }
                                        Pane {
                                            id: paneSettingsList1

                                            // ## relevant part ##
                                            property bool shown: false
                                            visible: height > 0
                                            height: shown ? implicitHeight : 0
                                            Behavior on height {
                                                NumberAnimation {
                                                    duration: 200
                                                    easing.type: Easing.InOutQuad
                                                }
                                            }
                                            clip: true
                                            // ## relevant part ##

                                            // Material.background: "lightblue"
                                            topPadding: 10
                                            anchors.left: parent.left
                                            anchors.right: parent.right
                                            background: Rectangle {
                                                color: hover_color
                                                radius: 8
                                                property real dynamicRadius: paneSettingsList1.height > 0 ? 0 : 8
                                                topLeftRadius: dynamicRadius
                                                topRightRadius: dynamicRadius

                                                Behavior on topLeftRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }
                                                Behavior on topRightRadius {
                                                    enabled: !paneSettingsList1.shown
                                                    NumberAnimation { 
                                                        duration: 200
                                                        easing.type: Easing.InOutQuad 
                                                    }
                                                }
                                            }
                                            Column {
                                                spacing: 10
                                                anchors.right: parent.right
                                                anchors.left: parent.left
                                                Item {
                                                    width: parent.width
                                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                    // anchors.fill: parent
                                                    visible: paneSettingsList1
                                                    RowLayout  {
                                                        spacing: 16
                                                        anchors.left: parent.left
                                                        Image {
                                                            width: 20
                                                            height: 20
                                                            source: "../../../../src/assets/ai_icons/ic_recognition_security.svg"
                                                        }
                                                        Row {
                                                            spacing: 0
                                                            CustomText {
                                                                text: qsTr("Recognition & Protection")
                                                                font.pixelSize: 12
                                                                color: normal_text_color
                                                            }
                                                            Item {
                                                                width: 50
                                                                height: parent.height
                                                                CustomText {
                                                                    anchors.centerIn: parent
                                                                    text: " (" + (model.recognition_protection || 0) + ")"
                                                                    font.pixelSize: 12
                                                                    color: normal_text_color
                                                                    
                                                                    property int previousValue: model.recognition_protection || 0
                                                                    
                                                                    ScaleAnimation {
                                                                        id: countScaleAnimation1
                                                                        target: parent
                                                                    }
                                                                    
                                                                    onTextChanged: {
                                                                        if (previousValue !== (model.recognition_protection || 0)) {
                                                                            countScaleAnimation1.start()
                                                                            previousValue = model.recognition_protection || 0
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("Recognition")
                                                            buttonType: type
                                                            aiType: model.recognition.state
                                                            onClicked: {
                                                                console.log("Recognition button clicked - state:", model.recognition.state)
                                                                device_controller.aiflow_clicked("recognition",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("Recognition checkbox clicked - state:", model.recognition.state)
                                                                device_controller.aiflow_clicked("recognition",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("Protection")
                                                            buttonType: type
                                                            aiType: model.protection.state
                                                            onClicked: {
                                                                console.log("Protection button clicked - state:", model.protection.state)
                                                                device_controller.aiflow_clicked("protection",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("Protection checkbox clicked - state:", model.protection.state)
                                                                device_controller.aiflow_clicked("protection",model,true)
                                                            }
                                                        }   
                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("Frequency")
                                                            buttonType: type
                                                            aiType: model.frequency.state
                                                            onClicked: {
                                                                console.log("Frequency button clicked - state:", model.frequency.state)
                                                                device_controller.aiflow_clicked("frequency",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("Frequency checkbox clicked - state:", model.frequency.state)
                                                                device_controller.aiflow_clicked("frequency",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("Access")
                                                            buttonType: type
                                                            aiType: model.access.state
                                                            onClicked: {
                                                                console.log("Access button clicked - state:", model.access.state)
                                                                device_controller.aiflow_clicked("access",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("Access checkbox clicked - state:", model.access.state)
                                                                device_controller.aiflow_clicked("access",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("Motion")
                                                            buttonType: type
                                                            aiType: model.motion.state
                                                            onClicked: {
                                                                console.log("Motion button clicked - state:", model.motion.state)
                                                                device_controller.aiflow_clicked("motion",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("Motion checkbox clicked - state:", model.motion.state)
                                                                device_controller.aiflow_clicked("motion",model,true)
                                                            }
                                                        } 
                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("Traffic")
                                                            buttonType: type
                                                            aiType: model.traffic.state
                                                            onClicked: {
                                                                console.log("Traffic button clicked - state:", model.traffic.state)
                                                                device_controller.aiflow_clicked("traffic",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("Traffic checkbox clicked - state:", model.traffic.state)
                                                                device_controller.aiflow_clicked("traffic",model,true)
                                                            }
                                                        }
                                                    }
                                                }
                                                Item {
                                                    width: parent.width
                                                    height: 30  // Điều chỉnh theo chiều cao của các phần tử bên trong
                                                    // anchors.fill: parent
                                                    visible: paneSettingsList1
                                                    RowLayout  {
                                                        spacing: 16
                                                        anchors.left: parent.left
                                                        Image {
                                                            width: 20
                                                            height: 20
                                                            source: "../../../../src/assets/ai_icons/ic_risk_identification.svg"
                                                        }
                                                        Row {
                                                            spacing: 0
                                                            CustomText {
                                                                text: qsTr("Risk Recognition")
                                                                font.pixelSize: 12
                                                                color: normal_text_color
                                                            }
                                                            Item {
                                                                width: 50
                                                                height: parent.height
                                                                CustomText {
                                                                    anchors.centerIn: parent
                                                                    text: " (" + (model.risk_identification || 0) + ")"
                                                                    font.pixelSize: 12
                                                                    color: normal_text_color
                                                                    
                                                                    property int previousValue: model.risk_identification || 0
                                                                    
                                                                    ScaleAnimation {
                                                                        id: riskCountScaleAnimation1
                                                                        target: parent
                                                                    }
                                                                    
                                                                    onTextChanged: {
                                                                        if (previousValue !== (model.risk_identification || 0)) {
                                                                            riskCountScaleAnimation1.start()
                                                                            previousValue = model.risk_identification || 0
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }

                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("Weapon")
                                                            buttonType: type
                                                            aiType: model.weapon.state
                                                            onClicked: {
                                                                console.log("Weapon button clicked - state:", model.weapon.state)
                                                                device_controller.aiflow_clicked("weapon",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("Weapon checkbox clicked - state:", model.weapon.state)
                                                                device_controller.aiflow_clicked("weapon",model,true)
                                                            }
                                                        }
                                                        ButtonAIFlow {
                                                            Layout.preferredWidth: 140
                                                            ai_text: qsTr("UFO")
                                                            buttonType: type
                                                            aiType: model.ufo.state
                                                            onClicked: {
                                                                console.log("UFO button clicked - state:", model.ufo.state)
                                                                device_controller.aiflow_clicked("ufo",model,false)
                                                            }
                                                            onCheckboxClicked: {
                                                                console.log("UFO checkbox clicked - state:", model.ufo.state)
                                                                device_controller.aiflow_clicked("ufo",model,true)
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }

                                }
                            }
                        }
                    }
                }
            }
        }

        ScrollIndicator.vertical: ScrollIndicator { }

    }
}

import QtQuick

Item {
    id: root
    
    property var target: null
    property real scaleTo: 1.3
    property int duration: 150
    property int loops: 1
    
    signal finished()
    
    SequentialAnimation {
        id: scaleAnimation
        running: false
        loops: root.loops
        
        PropertyAnimation {
            target: root.target
            property: "scale"
            to: root.scaleTo
            duration: root.duration
            easing.type: Easing.OutQuad
        }
        
        PropertyAnimation {
            target: root.target
            property: "scale"
            to: 1.0
            duration: root.duration
            easing.type: Easing.InQuad
        }
        
        onFinished: {
            root.finished()
        }
    }
    
    function start() {
        scaleAnimation.start()
    }
    
    function stop() {
        scaleAnimation.stop()
    }
} 
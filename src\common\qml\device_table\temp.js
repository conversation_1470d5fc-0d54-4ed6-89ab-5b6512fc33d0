.pragma library

let curVal = 0
let previousOperator = ""
let lastOp = ""
let digits = ""

function checkbox_clicked(key,controller,model,flag) {
    console.log("onCheckboxClicked Intrusion",ai_data.state)
    if (ai_data.state === 0) {
        // console.log("onCheckboxClicked Intrusion1",ai_data,controller,flag)
        controller.checkbox_clicked(ai_data,flag,model)
    }else if (ai_data.state === 1) {
        console.log("onCheckboxClicked Intrusion2",controller)
    }else {
        console.log("onCheckboxClicked Intrusion3",controller)
        controller.checkbox_clicked(ai_data,flag,model)
    }
}

// GridFullDialog.qml
import QtQuick
import QtQuick.Controls.Material
import QtQuick.Layouts

Rectangle {
    id: root
    width: 400
    height: 200
    radius: 10

    property string messageText: ""
    property bool isVisible: false

    // Theme colors - fallback to default if theme not available
    color: "#2b2b2b"

    border.color: "#ffaa00"
    border.width: borderAnim.running ? borderWidth : 0

    property real borderWidth: 0
    signal closeRequested()

    // Show/hide animation
    visible: isVisible
    opacity: isVisible ? 1.0 : 0.0

    Behavior on opacity {
        NumberAnimation {
            duration: 200
            easing.type: Easing.InOutQuad
        }
    }

    // Warning border animation
    SequentialAnimation {
        id: borderAnim
        loops: Animation.Infinite
        running: isVisible
        NumberAnimation {
            target: root
            property: "borderWidth"
            from: 0
            to: 2
            duration: 800
            easing.type: Easing.InOutQuad
        }
        NumberAnimation {
            target: root
            property: "borderWidth"
            from: 2
            to: 0
            duration: 800
            easing.type: Easing.InOutQuad
        }
    }

    Column {
        anchors.fill: parent
        anchors.margins: 20
        spacing: 10

        // Warning icon and title
        Row {
            anchors.horizontalCenter: parent.horizontalCenter
            spacing: 10

            Text {
                text: "⚠️"
                font.pixelSize: 24
                color: "#ffaa00"
                anchors.verticalCenter: parent.verticalCenter
            }

            Text {
                text: qsTr("Grid Full")
                font.pixelSize: 16
                font.bold: true
                color: "#ffffff"
                anchors.verticalCenter: parent.verticalCenter
            }
        }

        // Message text
        Text {
            id: messageLabel
            text: root.messageText
            font.pixelSize: 12
            color: "#ffffff"
            wrapMode: Text.WordWrap
            horizontalAlignment: Text.AlignHCenter
            anchors.horizontalCenter: parent.horizontalCenter
            width: parent.width - 20
        }

        // OK button container
        Item {
            width: parent.width
            height: 40

            Rectangle {
                id: okBtn
                anchors.centerIn: parent
                width: 80
                height: 32
                radius: 8
                color: okBtnMouseArea.containsMouse ? "#106ebe" : "#0078d4"
                border.color: "#106ebe"
                border.width: 1

                Text {
                    text: qsTr("OK")
                    font.pixelSize: 12
                    font.bold: true
                    color: "#ffffff"
                    anchors.centerIn: parent
                }

                MouseArea {
                    id: okBtnMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: function() {
                        root.closeRequested()
                    }
                }
            }
        }
    }

    // Functions to show/hide dialog
    function show(message) {
        messageText = message
        isVisible = true
    }

    function hide() {
        isVisible = false
    }
}

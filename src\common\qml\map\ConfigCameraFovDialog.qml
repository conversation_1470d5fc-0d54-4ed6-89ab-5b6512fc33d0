import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts

Rectangle {
    id: root
    radius: 4
    z: 10
    property var textColor: map2dController ? map2dController.get_color_theme_by_key("dialog_text") : "black"
    property var buttonBackgroundColor: map2dController ? map2dController.get_color_theme_by_key("dialog_button_background") : "white"
    property var backgroundColor: map2dController ? map2dController.get_color_theme_by_key("dialog_body_background") : "white"
    property var camData: null
    property var iconType: 0
    property var _mapState: null
    property alias redrawButton: _redrawButton
    
    property var isIconTab: {
        if (!camData) return false;
        return camData.fovMode === "ICON"
    }

    signal changeType(string type)
    signal camDataUpdated(var newData)
    signal deleteCamera()
    signal redrawPolygon()

    Component.onCompleted: {
        if (!camData){
            iconType = 0;
        }
        else iconType = JSON.parse(camData.fovData).icon_type;

    }

    function updateCamData(prop, value) {
        var newData = Object.assign({}, camData)
        newData[prop] = value
        camData = newData
        camDataUpdated(camData)
    }

    function updateFovData(prop, value) {
        if (!camData) return;

        var newData = Object.assign({}, camData);
        var fovData = JSON.parse(newData.fovData);
        
        fovData[prop] = value;
        newData.fovData = JSON.stringify(fovData);

        camData = newData;
        camDataUpdated(camData);
    }

    width: 320
    height: contentColumn.implicitHeight + 20
    color: backgroundColor

    Column{
        id: contentColumn
        anchors.centerIn: parent
        anchors.margins: 40
        spacing: 8

        Text {
            text: camData? camData.name : ""
            color: textColor
            width: parent.width
            font.bold: true
            font.pixelSize: 14
            elide: Text.ElideRight
            wrapMode: Text.NoWrap
            clip: true
        }

        Text {
            text: qsTr("Size (text & icon)")
            color: textColor
            font.pixelSize: 12
        }

        
        Row{
            height: 40
            spacing: 0
            anchors.horizontalCenter: parent.horizontalCenter
            Button{
                text: qsTr("Small")
                Material.foreground: camData ?( camData.size === 1 ? "white" : textColor) : textColor
                background: Rectangle{
                    color: camData ?( camData.size === 1 ? "#5B5B9F" : buttonBackgroundColor) : buttonBackgroundColor
                }

                onClicked: () =>{
                    updateCamData("size", 1)
                }
            }
            Button{
                text: qsTr("Medium")
                Material.foreground: camData ?( camData.size === 2 ? "white" : textColor) : textColor
                background: Rectangle{
                    color:  camData ? (camData.size === 2 ? "#5B5B9F" : buttonBackgroundColor) : buttonBackgroundColor
                }
                onClicked: () =>{
                    updateCamData("size", 2)
                }
            }
            Button{
                text: qsTr("Large")
                Material.foreground: camData ?( camData.size === 3 ? "white" : textColor) : textColor
                background: Rectangle{
                    color:  camData ? (camData.size === 3 ? "#5B5B9F" : buttonBackgroundColor) : buttonBackgroundColor
                }
                onClicked: function(){
                    updateCamData("size", 3)
                }
            }
        }

        Rectangle{
            height: 2
            width: parent.width
            color: "#C3C3C3"
        }

        Row{
            height: 32
            spacing: 12  
            Text{
                anchors.verticalCenter: parent.verticalCenter
                text: qsTr("Show as:")
                color: textColor
                font.pixelSize: 12
            }

            ButtonGroup {
                id: showAsGroup
            }

            RadioButton {
                id: iconRadio
                anchors.verticalCenter: parent.verticalCenter
                Material.foreground: textColor
                text: qsTr("Icon")
                checked: isIconTab
                ButtonGroup.group: showAsGroup

                // override the little circle (“indicator”)
                indicator: Item {
                    implicitWidth: 20; implicitHeight: 20
                    anchors.verticalCenter: parent.verticalCenter
                    // outer ring
                    Rectangle {
                        anchors.fill: parent
                        radius: width/2
                        color: "transparent"
                        border.width: 2
                        // show red when unchecked, otherwise use accent color
                        border.color: iconRadio.checked
                                    ? Material.accent
                                    : textColor
                    }

                    // inner “dot” when checked
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.width * 0.6; height: width
                        radius: width/2
                        visible: iconRadio.checked
                        color: Material.accent
                    }
                }

                onToggled: function() {
                    if (checked) {
                        isIconTab = true
                        updateCamData("fovMode", "ICON")
                        changeType("ICON")
                        iconType = 0
                    }
                }
            }

            RadioButton {
                id: shapeRadio
                anchors.verticalCenter: parent.verticalCenter
                text: qsTr("Shape")
                Material.foreground: textColor
                checked: !isIconTab
                ButtonGroup.group: showAsGroup

                // override the little circle (“indicator”)
                indicator: Item {
                    implicitWidth: 20; implicitHeight: 20
                    anchors.verticalCenter: parent.verticalCenter
                    // outer ring
                    Rectangle {
                        anchors.fill: parent
                        radius: width/2
                        color: "transparent"
                        border.width: 2
                        // show red when unchecked, otherwise use accent color
                        border.color: shapeRadio.checked
                                    ? Material.accent
                                    : textColor
                    }

                    // inner “dot” when checked
                    Rectangle {
                        anchors.centerIn: parent
                        width: parent.width * 0.6; height: width
                        radius: width/2
                        visible: shapeRadio.checked
                        color: Material.accent
                    }
                }

                onToggled: {
                    if (checked) {
                        isIconTab = false
                        updateCamData("fovMode", "RECTANGLE")
                        changeType("RECTANGLE")
                    }
                }
            }

        }

        Row{
            height: 52
            visible: isIconTab
            spacing: 8
            anchors.horizontalCenter: parent.horizontalCenter
            Repeater {
                model: 5

                Button{
                    width: 44
                    height: 54
                    background: Rectangle{
                        color:  iconType === index ? "#5B5B9F" : "#A9A9A9"
                        radius: width / 2
                        Image {
                            anchors.centerIn: parent
                            source: "qrc:/src/assets/map/iconCamera" + (index + 1) +".svg"
                            width: 28
                            height: 28
                            fillMode: Image.PreserveAspectFit
                            sourceSize: Qt.size(width, height)
                        }
                    }
                    
                    onClicked: function(){
                        iconType = index
                        updateFovData("icon_type", index)
                    }
                }
            }
        }

        Row{
            height: 52
            visible: !isIconTab
            spacing: 8
            anchors.horizontalCenter: parent.horizontalCenter
        
            Button{
                width: 44
                height: 54

                background: Rectangle{
                    color: "transparent"
                    radius: width / 2
                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/src/assets/map/iconRectangle" + (camData.fovMode === "RECTANGLE" ? "Clicked.svg" : ".svg")
                        width: 40
                        height: 40
                    }
                }
                
                onClicked: function(){
                    updateCamData("fovMode", "RECTANGLE")
                    changeType("RECTANGLE")
                }
            }
        
            Button{
                width: 44
                height: 54
                background: Rectangle{
                    color: "transparent"
                    radius: width / 2
                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/src/assets/map/iconCircle" + (camData.fovMode === "CIRCLE" ? "Clicked.svg" : ".svg")
                        width: 40
                        height: 40
                    }
                }
                
                onClicked: function(){
                    updateCamData("fovMode", "CIRCLE")
                    changeType("CIRCLE")
                }
            }
        
            Button{
                width: 44
                height: 54
                background: Rectangle{
                    color: "transparent"
                    radius: width / 2
                    Image {
                        anchors.centerIn: parent
                        source: "qrc:/src/assets/map/iconPolygon" + (camData.fovMode === "POLYGON" ? "Clicked.svg" : ".svg")
                        width: 40
                        height: 40
                    }
                }
                
                onClicked: function(){
                    updateCamData("fovMode", "POLYGON")
                    changeType("POLYGON")
                }
            }
        }

        Rectangle{
            height: 2
            width: parent.width
            color: "#C3C3C3"
        }

        Text {
            text: qsTr("Shape & Cone Color")
            color: textColor
            font.pixelSize: 12
        }

        Row{
            height: 52
            spacing: 16
            anchors.horizontalCenter: parent.horizontalCenter
            Repeater {
                model: ["#B5122E", "#D08900", "#5B5B9F", "#FEC502", "#1CD1A1"]

                Button{
                    width: 36
                    height: 48

                    background: Rectangle{
                        color: modelData
                        radius: width / 2
                        border.color: textColor
                        border.width: camData.color === modelData ? 3 : 0
                    }

                    onClicked: function(){
                        updateCamData("color", modelData)
                    }
                }
            }
        }

        Rectangle{
            height: 2
            width: parent.width
            color: "#C3C3C3"
        }

        Text{
            text: qsTr("Preferences")
            color: textColor
            font.pixelSize: 12
        }

        Row {
            visible: camData ? camData.fovMode === "ICON" : false
            height: 24
            CheckBox {
                id: showFovCheckBox
                checked: camData.fovEnable

                indicator: Rectangle {
                    implicitWidth: 20
                    implicitHeight: 20
                    radius: 2
                    color: showFovCheckBox.checked ? "#5B5B9F" : "#E0E0E0"
                    border.color: textColor
                    border.width: 1

                    Canvas {
                        anchors.fill: parent
                        anchors.margins: 6
                        visible: showFovCheckBox.checked

                        onPaint: function(){
                            var ctx = getContext("2d")
                            ctx.clearRect(0, 0, width, height)
                            ctx.strokeStyle = "white"
                            ctx.lineWidth = 2
                            ctx.beginPath()
                            ctx.moveTo(0, height / 2)
                            ctx.lineTo(width / 3, height)
                            ctx.lineTo(width, 0)
                            ctx.stroke()
                        }
                    }
                }

                onClicked: function(){
                    updateCamData("fovEnable", showFovCheckBox.checked)
                }
            }

            Text {
                text: qsTr("Show field of view")
                color: textColor
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
            }
        }

        Row {
            height: 24
            CheckBox {
                id: showNameCheckBox
                checked: camData.nameEnable

                indicator: Rectangle {
                    implicitWidth: 20
                    implicitHeight: 20
                    radius: 2
                    color: showNameCheckBox.checked ? "#5B5B9F" : "#E0E0E0"
                    border.color: textColor
                    border.width: 1

                    Canvas {
                        anchors.fill: parent
                        anchors.margins: 6
                        visible: showNameCheckBox.checked

                        onPaint: function(){
                            var ctx = getContext("2d")
                            ctx.clearRect(0, 0, width, height)
                            ctx.strokeStyle = "white"
                            ctx.lineWidth = 2
                            ctx.beginPath()
                            ctx.moveTo(0, height / 2)
                            ctx.lineTo(width / 3, height)
                            ctx.lineTo(width, 0)
                            ctx.stroke()
                        }
                    }
                }

                onClicked: function(){
                    updateCamData("nameEnable", showNameCheckBox.checked)
                }
            }

            Text {
                text: qsTr("Show name")
                color: textColor
                font.pixelSize: 12
                verticalAlignment: Text.AlignVCenter
            }
        }

        Rectangle{
            width: parent.width
            height: 2
            color: "#C3C3C3"
        }

        Button{
            id: _redrawButton
            text: qsTr("Redraw")
            visible: false
            Material.foreground: textColor
            background: Rectangle{
                radius: 9
                color: buttonBackgroundColor
            }

            onClicked: function(){
                redrawPolygon();
            }
        }

        Button{
            icon.source: "qrc:/src/assets/images/delete_icon.svg"
            icon.color: "#B5122E"
            background: Rectangle{
                radius: 9
                color: "#EEEEEE"
            }

            onClicked: function(){
                deleteCamera()
            }
        }
    }

    Connections{
        target: map2dController
        function onThemeChangeSignal(){
            textColor = map2dController.get_color_theme_by_key("dialog_text")
            buttonBackgroundColor = map2dController.get_color_theme_by_key("dialog_button_background")
            backgroundColor = map2dController.get_color_theme_by_key("dialog_body_background")
        }
    }
}

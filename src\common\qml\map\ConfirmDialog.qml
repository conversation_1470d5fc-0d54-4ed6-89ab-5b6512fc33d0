// ConfirmDialog.qml
import QtQuick
import QtQuick.Controls.Material
import QtQuick.Layouts

Rectangle {
    id: root
    width: 320
    height: 80
    radius: 10
    property string title: qsTr("The previous location of this item will be changed?")
    color: mapOnGrid.thisMapState ? mapOnGrid.thisMapState.get_color_theme_by_key("dialog_body_background") : "red"
    
    border.color: "#ff0000"
    border.width: borderAnim.running ? borderWidth : 0

    property real borderWidth: 0
    signal confirmSignal(bool confirmed);

    // Hiệu ứng mở rộng viền đỏ rồi mờ dần
    SequentialAnimation {
        id: borderAnim
        loops: Animation.Infinite
        running: true
        NumberAnimation {
            target: root
            property: "borderWidth"
            from: 0
            to: 3
            duration: 500
            easing.type: Easing.InOutQuad
        }
        NumberAnimation {
            target: root
            property: "borderWidth"
            from: 3
            to: 0
            duration: 500
            easing.type: Easing.InOutQuad
        }
    }

    Column {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 4

        Text {
            text: root.title
            font.pixelSize: 12
            color: mapOnGrid.thisMapState ? mapOnGrid.thisMapState.get_color_theme_by_key("dialog_text") : "#000000"
            horizontalAlignment: Text.AlignHCenter
            wrapMode: Text.WordWrap
            anchors.horizontalCenter: parent.horizontalCenter
        }

        Row {
            spacing: 12
            anchors.horizontalCenter: parent.horizontalCenter

            Button {
                id: cancelBtn
                background: Rectangle {
                    radius: 8
                    color: cancelBtn.hovered ? "#4b4a59" : "#656475"
                    
                }
                contentItem: Text {
                    text: qsTr("Cancel")
                    font.pixelSize: 12
                    font.bold: true
                    color: "#ffffff"
                    anchors.centerIn: parent
                }
                onClicked: function() {
                    root.confirmSignal(false)
                }
            }

            Button {
                id: confirmBtn
                background: Rectangle {
                    radius: 8
                    color: confirmBtn.hovered ? "#4B4B8F" : "#5B5B9F"
                }
                contentItem: Text {
                    text: qsTr("Confirm")
                    font.pixelSize: 12
                    font.bold: true
                    color: "#ffffff"
                    anchors.centerIn: parent
                }
                onClicked: function() {
                    root.confirmSignal(true)
                }
            }
        }
    }
}

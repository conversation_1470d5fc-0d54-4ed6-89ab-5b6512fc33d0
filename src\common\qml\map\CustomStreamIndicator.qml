import QtQuick
import QtQuick.Controls.Basic

BusyIndicator {
    id: control

    contentItem: Item {
        implicitWidth: 30
        implicitHeight: 30

        Item {
            id: item
            x: parent.width / 2 - 15
            y: parent.height / 2 - 15
            width: 30
            height: 30
            opacity: control.running ? 1 : 0

            Behavior on opacity {
                OpacityAnimator {
                    duration: 250
                }
            }

            RotationAnimator {
                target: item
                running: control.visible && control.running
                from: 0
                to: 360
                loops: Animation.Infinite
                duration: 1250
            }

            Repeater {
                id: repeater
                model: 6

                Rectangle {
                    id: delegate
                    x: item.width / 2 - width / 2
                    y: item.height / 2 - height / 2
                    implicitWidth: 5
                    implicitHeight: 5
                    radius: 2.5
                    color: "#21be2b"

                    required property int index

                    transform: [
                        Translate {
                            y: -Math.min(item.width, item.height) * 0.5 + 2.5
                        },
                        Rotation {
                            angle: delegate.index / repeater.count * 360
                            origin.x: 2.5
                            origin.y: 2.5
                        }
                    ]
                }
            }
        }
    }
}
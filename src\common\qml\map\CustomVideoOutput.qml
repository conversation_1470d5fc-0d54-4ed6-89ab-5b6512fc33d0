import QtQuick
import QtQuick.Controls
import models 1.0

Rectangle {
    id: root
    color: "black"
    property var model: null
    property bool isPlaying: false
    property string videoState: qsTr("No Data")
    VideoModel {
        id: videoItem
        anchors.fill: parent
        isPlaying: root.isPlaying
        // state: "connecting"
        model: root.model
    }

    Connections {
        target: videoItem
        
        function onCameraStateChanged(state) {
            console.log("onCameraStateChanged = ",typeof(videoItem.state),state)
            root.videoState = state

        }
        
    }
    onIsPlayingChanged: {
        if (!isPlaying) {
            videoItem.unregister_video_capture()
        } else{
            videoItem.register_video_capture(root.model)
        }
    }
    property int dotCount: 0
    property string connectingText: qsTr("Connecting")
    Timer {
        id: dotAnimationTimer
        interval: 500
        running: root.videoState === "connecting" ? true : false
        repeat: true
        onTriggered: {
            dotCount = (dotCount + 1) % 4
            connectingText = qsTr("Connecting") + ".".repeat(dotCount)
        }
    }

    Text {
        anchors.centerIn: parent
        color: "white"
        font.pixelSize: 16
        text: (function()
        {
            return root.videoState === "connecting" ? connectingText : (root.videoState === "stopped" ? qsTr("Disconnected") : qsTr("No Data"))
        })()
        visible: root.videoState !== "started" ? true : false 
        z: 2
    }
    Component.onCompleted: {
        console.log("QML load xong 111111111111! ",root.videoState)
    }
}
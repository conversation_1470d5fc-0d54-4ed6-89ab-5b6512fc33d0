import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts
import './fovShape'

Rectangle {
    id: rootItem
    anchors.fill: parent
    property bool qmlType: false
    property var floorModelFromQML: undefined
    property var mapStateFromQML: undefined
    property bool isImageReady: false

    
    property var listCameras: {
        if (map2dController !== undefined && map2dController !== null){
            return map2dController.listCameras;
        }
        else
            return [];
    }

    property var floorModel: {
        if (qmlType){
            return floorModelFromQML;
        }
        if (floorModelFromQML !== undefined)
            return floorModelFromQML;
        else if (map2dController !== undefined && map2dController !== null)
            return map2dController.floorModel;
        else
            return null;
    }

    property var mapState: {
        if (mapStateFromQML !== undefined)
            return mapStateFromQML;
        else if (map2dController)
            return map2dController.mapState;
        else
            return null;
    }

    property color main_background: map2dController ? map2dController.get_color_theme_by_key("camera_widget_background") : "white"
    color: main_background
    Image {
        id: image
        anchors.centerIn: parent
        width: parent.width
        height: parent.height
        source: floorModel ? floorModel.fileLink : ""
        fillMode: Image.PreserveAspectFit
        onStatusChanged: {
            if (status === Image.Ready) {
                isImageReady = true
            }
        }
    }

    Text{
        anchors.centerIn: parent
        text: qsTr("Loading Image...")
        color: "white"
        font.pixelSize: 16
        font.bold: true
        visible: !isImageReady
    }

    Rectangle {
        id: hoverArea
        anchors.centerIn: parent
        color: "transparent"
        // Calculate scale factor based on image source size and the available space.
        width: image.sourceSize.width * Math.min(image.width / image.sourceSize.width, image.height / image.sourceSize.height)
        height: image.sourceSize.height * Math.min(image.width / image.sourceSize.width, image.height / image.sourceSize.height)
        
        property double scaleRatio: width / image.sourceSize.width

        Repeater {
            model: mapState && mapState.editMode ? listCameras : (floorModel ? floorModel.cameras : [])
            delegate: Map2DCameraItem{
                _modelIndex: index
                _modelData: modelData
                _rootItem: rootItem
                _area: hoverArea
            }
        }
    }

    DropArea{
        anchors.fill: hoverArea
        z: -1
        onEntered: (drag) => {
            if(!mapState.editMode){
                drag.accepted = false;
                drag.dragType = Qt.IgnoreAction;
            }
        }
        onDropped: function(drop){
            if(mapState){
                var data = drop.getDataAsArrayBuffer("application/json");
                var x = drop.x / hoverArea.width;
                var y = drop.y / hoverArea.height;
                map2dController.temporarilyAddCamera(data, x, y, hoverArea.scaleRatio);
            }
        }
    }

    // Connections{
    //     target: map2dController
    //     function onThemeChangeSignal(){
    //         main_background = map2dController.get_color_theme_by_key("camera_widget_background")
    //     }
    // }
}

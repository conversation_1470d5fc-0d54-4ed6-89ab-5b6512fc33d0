import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtLocation
import QtPositioning
import QtMultimedia

Rectangle{
    property var rtspUrl: ""
    color: "#FF0000"
    
    Rectangle {
        anchors.fill: parent
        anchors.margins: 10
        id: root
        color: "#2C2C2C"
        opacity: 1.0

        CustomStreamIndicator {
            id: customIndicator
            anchors.centerIn: parent
        }

        MediaPlayer {
            id: mediaPlayer
            source: rtspUrl
            audioOutput: AudioOutput {
                volume: 0.0  // Mute by default
            }
            videoOutput: videoOutput
            loops: MediaPlayer.Infinite
            
            onErrorOccurred: function(error, errorString) {
                console.log("Media player error:", error, errorString)
                errorText.text = "Stream error: " + errorString
                errorTextField.visible = true
            }

            onSourceChanged: {
                if (source) {
                    play()
                    errorTextField.visible = false
                }
                else{
                    console.log("Source changed to empty")
                }
            }
        }

        VideoOutput {
            id: videoOutput
            anchors.fill: parent
        }

        Rectangle{
            id: errorTextField
            anchors.fill: parent
            color: "#2C2C2C"
            opacity: 1.0
            visible: false
            Text {
                id: errorText
                anchors.centerIn: parent
                color: "white"
            }
        }


        Component.onCompleted: {
            mediaPlayer.play()
        }
    }

}
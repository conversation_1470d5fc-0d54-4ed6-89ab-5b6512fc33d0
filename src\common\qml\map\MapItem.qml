import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts
import QtPositioning
import QtLocation
import models 1.0
MapQuickItem {
    id: root
    property var model
    property var itemType: "Camera"
    property var rtsp: ""
    property var itemId: ""
    property var itemName: ""
    property var camLocation: ""
    property int iconSize: 50
    signal untrackSignal(var model)
    signal buttonSignal(var model)

    anchorPoint.x: width / 2
    anchorPoint.y: height / 2
    z: 1
    
    onCoordinateChanged: {
        if (mapOnGrid.thisMapModel.isPositionChanged(itemId)) {
            cameraDetailLoader.active = true
        }
    }

    sourceItem: Item {
        id: cameraItem
        objectName: "Camera"
        width: 60
        height: 60
        RoundButton {
            id: idButton
            implicitWidth: iconSize
            implicitHeight: iconSize
            contentItem: Item {
                width: idButton.implicitWidth
                height: idButton.implicitHeight

                Image {
                    id: img
                    anchors.fill: parent
                    fillMode: Image.PreserveAspectFit
                    sourceSize: Qt.size(idButton.implicitWidth, idButton.implicitHeight)
                    source: itemType === "Camera"
                            ? (model.state === "CONNECTED"
                            ? "qrc:/src/assets/map/camera_on_map_on.svg"
                            : "qrc:/src/assets/map/camera_on_map_off.svg")
                            : "qrc:/src/assets/camera_stream/building_item.svg"
                }

                Rectangle {
                    visible: itemType === "Camera"
                    width: 8; height: 8
                    radius: width/2
                    anchors {
                        right: parent.right; bottom: parent.bottom
                        margins: 2
                    }
                    color: model.state === "CONNECTED" ? "lime" : "tomato"
                }
            }
            
            onClicked: function() {
                if (itemType !== "Camera"){
                    console.log("idButton ",model.floorIds)
                } 
                buttonSignal(model)
            }

            antialiasing: true
            anchors.centerIn: parent

            ToolTip{
                id: idToolTip
                text: itemName
                delay: 300
                contentItem: Text {
                    text: "• " + qsTr("Name:") + " " + itemName + 
                            (camLocation ? "\n• " + qsTr("Location:") + " " + camLocation : "")
                    color: "white"
                    wrapMode: Text.Wrap
                    font.pixelSize: 12
                    width: parent.width
                }

                background: Rectangle {
                    color: "#181824"
                    radius: 5
                }
                visible: hoverHandler.hovered
            }
            hoverEnabled: true
            // Ensure hover events are processed
            HoverHandler {
                id: hoverHandler
                onHoveredChanged: {
                    idToolTip.visible = hoverHandler.hovered
                }
            }
        }
        Menu {
            id: contextMenu
            MenuItem {
                action: Action {
                    id: untrackAction
                    text: itemType === "Camera" ? qsTr("Remove Camera from Map") : qsTr("Remove Building from Map")
                    onTriggered: function(){
                        untrackSignal(model)
                    }
                }
            }
        }
        Loader {
            id: cameraDetailLoader
            active: mapOnGrid.thisMapModel ? mapOnGrid.thisMapModel.isPositionChanged(itemId) : false
            z: 999
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.top: parent.bottom  // Đặt dialog bên dưới icon
            anchors.topMargin: 8
            sourceComponent: ConfirmDialog{
                onConfirmSignal: function(confirmed){
                    mapOnGrid.thisMapModel.handleConfirmLocation(itemId, confirmed)
                    cameraDetailLoader.active = false
                }
            }
        }
        MouseArea {
            anchors.fill: parent
            acceptedButtons: Qt.RightButton
            onClicked: {
                if (mapOnGrid.thisMapState.editMode) {
                    contextMenu.popup()
                }
            }
        }

        DragHandler {
            id: dragHandler
            target: (mapOnGrid.thisMapState && mapOnGrid.thisMapState.editMode) ? root : null
        }

        Drag.source: cameraItem
        Drag.active: dragHandler.active

        Drag.mimeData: {
            "text/plain": itemName,
            "application/json": JSON.stringify({
                id: itemId,
                tree_type: itemType
            })
        }
        Drag.dragType: Drag.Automatic
        Drag.supportedActions: Qt.MoveAction
    }

}

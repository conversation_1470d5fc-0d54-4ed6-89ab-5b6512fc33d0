import QtQuick 
import QtQuick.Controls 
import QtQuick.Layouts
import QtLocation
import QtPositioning
import models 1.0

Rectangle {
    id: mapOnGrid
    property bool isLongPress: false
    anchors.fill: parent
    property var cameraList: []
    property int previewWidth: 500
    property int previewHeight: 340
    property var thisMapModel: mapModel ? mapModel : null
    property var thisMapState: mapState ? mapState : null

    Loader {
        id: cameraDetailLoader
        width: previewWidth
        height: previewHeight
        visible: source !== ""
        z: 100
    }
    // Plugin {
    //     id: mapPlugin
    //     name: "osm"
    //     PluginParameter {
    //         name: "osm.mapping.providersrepository.disabled"
    //         value: "true"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.directory"
    //         value: ""
    //     }
    //     PluginParameter {
    //         name: "osm.cache.disk.cost"
    //         value: "0"
    //     }
    //     PluginParameter {
    //         name: "osm.cache.memory.cost"
    //         value: "0"
    //     }
    // }

    // ConfirmDialog {
    //     id: createDialog
    //     z: 9999
    //     onCreateBuilding: {
    //         console.log("Creating building:", buildingName)
    //         // Gọi controller xử lý tạo building ở đây
    //         createDialog.close()
    //     }
    //     onCancel: createDialog.close()
    // }
    
    DropArea {
        anchors.fill: parent
        enabled: thisMapState ? thisMapState.editMode || mapOnGrid.isMapMoveEnabled : false
        onDropped: function(drop) {
            if (thisMapState.editMode) {
                let coord = map.toCoordinate(Qt.point(drop.x, drop.y))
                thisMapModel.handleDrop(drop.getDataAsArrayBuffer("application/json"),coord.latitude,coord.longitude)
            } else {
                thisMapState.notifyChanged(thisMapState.SelectEditMode)
            }

        }
    }
    
    Map {
        id: map
        anchors.fill: parent
        plugin: Plugin {
            id: mapPlugin
            name: "osm"
            PluginParameter {
                name: "osm.mapping.custom.host"
                value: "http://**************:8080/styles/basic-preview/"
            }
            // PluginParameter {
            //     name: "osm.mapping.offline.directory"
            //     value: "e:/Project/python/VMS-Training/qml_reconstruct/cache"
            // }
            // PluginParameter {
            //     name: "osm.mapping.cache.directory"
            //     value: "e:/Project/python/VMS-Training/qml_reconstruct/cache"
            // }
            PluginParameter {
                name: "osm.cache.directory"
                value: ""
            }
            PluginParameter {
                name: "osm.cache.disk.cost"
                value: "0"
            }
            PluginParameter {
                name: "osm.cache.memory.cost"
                value: "0"
            }
        }

        zoomLevel: 14
        center: QtPositioning.coordinate(21.014506, 105.846509)
        activeMapType: supportedMapTypes[supportedMapTypes.length - 1]
        property geoCoordinate startCentroid
        antialiasing: true

        // Function to handle map movement
        function handleMapMove(dx, dy) {
            if (mapOnGrid.isMapMoveEnabled) {
                // Pan the map in the opposite direction of mouse movement
                // This creates a natural dragging effect
                map.pan(-dx, -dy)
            }
        }

        MapItemView {
            model: (function(){
                    return thisMapModel.cameraIds
                })()
            delegate: MapItem {
                id: cameraItem
                model: (function(){
                    return modelData
                })()
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                rtsp: modelData.urlMainstream
                itemId: modelData.id
                itemName: modelData.name
                camLocation: modelData.location
                onUntrackSignal: (cameraModel) => {
                    thisMapModel.removeCameraFromMap(cameraModel)
                }
                onButtonSignal: (id) => {
                    if (map.width < previewWidth) {
                        thisMapState.notifyChanged(thisMapState.SelectGridType)
                        return
                    }
                    if (cameraItem.x < 0) {
                        cameraDetailLoader.x = 0
                    }else{
                        if ((cameraItem.x + previewWidth + 30) > map.width){
                            cameraDetailLoader.x = map.width - previewWidth - 30
                        }else{
                            cameraDetailLoader.x = cameraItem.x + 30;
                        }
                    }
                    if (cameraItem.y < 0) {
                        cameraDetailLoader.y = 0
                    }else {
                        if ((cameraItem.y + previewHeight + 30) > map.height){
                            cameraDetailLoader.y = map.height - previewHeight - 30
                        }else{
                            cameraDetailLoader.y = cameraItem.y + 30;
                        }
                    }
                    cameraDetailLoader.sourceComponent = previewItemComponent
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: cameraItem.model
                        buttonType: "Camera"
                        isPlayingStream: true
                        visible: true
                        itemName: cameraItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            thisMapState.viewMode = !thisMapState.viewMode
                            if (thisMapState.viewMode){
                                cameraDetailLoader.width = map.width
                                cameraDetailLoader.height = map.height
                                cameraDetailLoader.x = 0
                                cameraDetailLoader.y = 0
                            }else{
                                cameraDetailLoader.width = previewWidth
                                cameraDetailLoader.height = previewHeight
                                if (cameraItem.x < 0) {
                                    cameraDetailLoader.x = 0
                                }else{
                                    if ((cameraItem.x + previewWidth + 30) > map.width){
                                        cameraDetailLoader.x = map.width - previewWidth - 30
                                    }else{
                                        cameraDetailLoader.x = cameraItem.x + 30;
                                    }
                                }
                                if (cameraItem.y < 0) {
                                    cameraDetailLoader.y = 0
                                }else {
                                    if ((cameraItem.y + previewHeight + 30) > map.height){
                                        cameraDetailLoader.y = map.height - previewHeight - 30
                                    }else{
                                        cameraDetailLoader.y = cameraItem.y + 30;
                                    }
                                }
                            }


                        }
                    }
                }

            }
        }
        MapItemView {
            model: thisMapModel.buildingIds
            delegate: MapItem {
                id: buildingItem
                model: modelData
                itemType: "BuildingItem"
                itemId: modelData.id
                itemName: modelData.name
                coordinate: QtPositioning.coordinate(modelData.latitude, modelData.longitude)
                onUntrackSignal: (buildingModel) => {
                    thisMapModel.removeBuildingFromMap(buildingModel)
                }
                onButtonSignal: (buildingModel) => {
                    if (map.width < previewWidth) {
                        thisMapState.notifyChanged(thisMapState.SelectGridType)
                        return
                    }
                    if (buildingItem.x < 0) {
                        cameraDetailLoader.x = 0
                    }else{
                        if ((buildingItem.x + previewWidth + 30) > map.width){
                            cameraDetailLoader.x = map.width - previewWidth - 30
                        }else{
                            cameraDetailLoader.x = buildingItem.x + 30;
                        }
                    }
                    if (buildingItem.y < 0) {
                        cameraDetailLoader.y = 0
                    }else {
                        if ((buildingItem.y + previewHeight + 30) > map.height){
                            cameraDetailLoader.y = map.height - previewHeight - 30
                        }else{
                            cameraDetailLoader.y = buildingItem.y + 30;
                        }
                    }
                    cameraDetailLoader.sourceComponent = previewItemComponent
                }
                Component {
                    id: previewItemComponent
                    PreviewItem {
                        id: previewItem
                        model: buildingItem.model
                        buttonType: "Building"
                        visible: true
                        itemName: buildingItem.itemName
                        onCloseSignal: {
                            cameraDetailLoader.sourceComponent = null;
                            cameraDetailLoader.width = previewWidth
                            cameraDetailLoader.height = previewHeight
                            thisMapState.viewMode = false
                        }
                        onFullScreenSignal: {
                            thisMapState.viewMode = !thisMapState.viewMode
                            if (thisMapState.viewMode){
                                cameraDetailLoader.width = map.width
                                cameraDetailLoader.height = map.height
                                cameraDetailLoader.x = 0
                                cameraDetailLoader.y = 0
                            }else{
                                cameraDetailLoader.width = previewWidth
                                cameraDetailLoader.height = previewHeight
                                if (buildingItem.x < 0) {
                                    cameraDetailLoader.x = 0
                                }else{
                                    if ((buildingItem.x + previewWidth + 30) > map.width){
                                        cameraDetailLoader.x = map.width - previewWidth - 30
                                    }else{
                                        cameraDetailLoader.x = buildingItem.x + 30;
                                    }
                                }
                                if (buildingItem.y < 0) {
                                    cameraDetailLoader.y = 0
                                }else {
                                    if ((buildingItem.y + previewHeight + 30) > map.height){
                                        cameraDetailLoader.y = map.height - previewHeight - 30
                                    }else{
                                        cameraDetailLoader.y = buildingItem.y + 30;
                                    }
                                }
                            }


                        }
                    }
                }              
            }
        }

        Behavior on center {
            PropertyAnimation {
                duration: 400
                easing.type: Easing.InOutQuad
            }
        }

        PinchHandler {
            id: pinch
            enabled: mapState ? !mapState.lockMode : false
            target: null
            onActiveChanged: if (active) {
                map.startCentroid = map.toCoordinate(pinch.centroid.position, false)
            }
            onScaleChanged: (delta) => {
                map.zoomLevel += Math.log2(delta)
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            onRotationChanged: (delta) => {
                map.bearing -= delta
                map.alignCoordinateToPoint(map.startCentroid, pinch.centroid.position)
            }
            grabPermissions: PointerHandler.TakeOverForbidden
        }
        WheelHandler {
            id: wheel
            enabled: mapState ? !mapState.lockMode : false
            grabPermissions: PointerHandler.ApprovesCancellation
            acceptedDevices: Qt.platform.pluginName === "cocoa" || Qt.platform.pluginName === "wayland"
                             ? PointerDevice.Mouse | PointerDevice.TouchPad
                             : PointerDevice.Mouse
            rotationScale: 1 /120
            property: "zoomLevel"
        }
        DragHandler {
            id: drag
            target: null
            grabPermissions: PointerHandler.TakeOverForbidden
            enabled: mapOnGrid.isMapMoveEnabled
            onTranslationChanged: (delta) => map.pan(-delta.x, -delta.y)
            // Make sure this handler doesn't interfere with hover events
            acceptedDevices: PointerDevice.Mouse | PointerDevice.TouchPad
        }


        Shortcut {
            enabled: map.zoomLevel < map.maximumZoomLevel
            sequence: StandardKey.ZoomIn
            onActivated: map.zoomLevel = Math.round(map.zoomLevel + 1)
        }
        Shortcut {
            enabled: map.zoomLevel > map.minimumZoomLevel
            sequence: StandardKey.ZoomOut
            onActivated: map.zoomLevel = Math.round(map.zoomLevel - 1)
        }
    }
    DragHandler {
        id: dragHandler
        enabled: (mapState && mapState.lockMode) ? true : false
        onActiveChanged: {
            if (active) {
                root.currentMimeData = {
                    "text/plain": "swap_item",
                    "application/position": JSON.stringify({
                        id: mapModel.id,
                        tree_type: "",
                        position: widget.getPosition()
                    })
                }
            }
        }
    }


    Drag.source: root
    Drag.active: dragHandler.active
    Drag.mimeData: root.currentMimeData
    // Drag.mimeData: {
    //     "text/plain": "swap_item",
    //     "application/position": JSON.stringify({
    //         id: mapModel.id,
    //         tree_type: "",
    //         position: widget.getPosition()
    //     })
    // }

    Drag.dragType: Drag.Automatic
    Drag.supportedActions: Qt.MoveAction

    Connections {
        target: thisMapModel
        function onNewCameraChanged(camera) {
            cameraList.push(camera)
        }
    }

    // Add function to handle map movement from parent
    function handleMapMove(dx, dy) {
        map.handleMapMove(dx, dy)
    }

    // Add property to control map movement mode
    property bool isMapMoveEnabled: true

    // Add signal to notify parent of map movement mode changes
    signal mapMoveModeChanged(bool enabled)

    // Update map movement mode
    function setMapMoveMode(enabled) {
        isMapMoveEnabled = enabled
        // Ensure the map's drag handler is properly updated
        drag.enabled = enabled
        // Also update the map's internal property
        map.isMapMoveEnabled = enabled
        mapMoveModeChanged(enabled)
    }
}

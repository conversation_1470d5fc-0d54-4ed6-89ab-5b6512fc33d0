import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtPositioning
import QtLocation

MapQuickItem {
    property var flashEnabled: true
    property var isUser: false

    anchorPoint.x: width / 2
    anchorPoint.y: height / 2

    sourceItem: Item {
        width: 40
        height: 80

        Image {
            id: locationIcon
            source: "qrc:/src/assets/map/location.svg"
            width: 32
            height: 32
            anchors.horizontalCenter: parent.horizontalCenter
            anchors.bottom: outerCircle.top
        }

        Rectangle {
            id: outerCircle
            width: 8
            height: 8
            radius: width / 2
            color: isUser ? "#F32626" : "#FFF719"
            border.color: isUser ? "#2BFC06" : "#2C19FF"
            border.width: 2
            antialiasing: true
            anchors.centerIn: parent 

            SequentialAnimation {
                loops: Animation.Infinite
                running: flashEnabled

                ParallelAnimation {
                    NumberAnimation {
                        target: outerCircle
                        property: "scale"
                        from: 1.0
                        to: 1.3
                        duration: 1000
                        easing.type: Easing.OutQuad
                    }
                    OpacityAnimator {
                        target: outerCircle
                        from: 1.0
                        to: 0.0
                        duration: 1000
                    }
                }
                PauseAnimation { duration: 200 }

                ParallelAnimation {
                    NumberAnimation {
                        target: outerCircle
                        property: "scale"
                        from: 1.3
                        to: 1.0
                        duration: 200
                    }
                    OpacityAnimator {
                        target: outerCircle
                        from: 0.0
                        to: 1.0
                        duration: 200
                    }
                }
            }
        }
    }

}

import QtQuick
import QtQuick.Controls.Material 
import QtQuick.Layouts

Rectangle{
    width: 512
    height: 340

    color: "#5B5B9F"
    border.color: "white"
    border.width: 2

    signal closeSignal()

    ColumnLayout{
        anchors.fill: parent
        spacing: 0
        Rectangle{
            color: "#2B2A3A"
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 50
            Text{
                anchors.verticalCenter: parent.verticalCenter
                anchors.left: parent.left
                anchors.leftMargin: 15
                text: qsTr("Select tab to move")
                color: "white"
                
                font.bold: true
                font.pixelSize: 16
            }
        }
        Rectangle{
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 200
        }
        Rectangle{
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 2
            color: "white"
        }
        Rectangle{
            color: "#0F0F15"
            Layout.fillWidth: true
            Layout.fillHeight: true
            Layout.preferredHeight: 50
            RowLayout{
                anchors.fill: parent
                spacing: 0

                Item{
                    Layout.fillWidth: true
                }

                Button{
                    Layout.preferredHeight: 44
                    text: qsTr("Cancel")
                    font.bold: true
                    font.pixelSize: 12
                    Material.foreground: "white"
                    background: Rectangle{
                        color: "#444459"
                        radius: 5
                    }

                    onClicked: closeSignal()
                }

                Button{
                    Layout.leftMargin: 5
                    Layout.rightMargin: 5
                    Layout.preferredHeight: 44
                    text: qsTr("Move")
                    icon.source: "qrc:/src/assets/map/open_change_tab_widget.svg"
                    icon.color: "white"
                    font.bold: true
                    font.pixelSize: 12
                    Material.foreground: "white"
                    background: Rectangle{
                        color: "#21A603"
                        radius: 5
                    }
                }

            }
        }
    }
}
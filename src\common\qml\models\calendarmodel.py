from PySide6.QtCore import QObject, Property, Signal,Slot
from datetime import datetime, timedelta
from src.common.qml.models.ruler_context import RulerContext
import logging
logger = logging.getLogger(__name__)

class DayModel(QObject):
    dayChanged = Signal()
    isDataChanged = Signal()
    enabledChanged = Signal()

    def __init__(self,key = 0, day = None,fullDay = None, isData = False, enabled = False):
        super().__init__()
        self._key = key
        self._day = day
        self._fullDay = fullDay
        self._isData = isData
        self._enabled = enabled

    @Property(str,notify=dayChanged)
    def day(self):
        return self._day
    
    @day.setter
    def day(self, value: str):
        if self._day != value:
            self._day = value
            self.dayChanged.emit() 

    @Property(bool,notify=isDataChanged)
    def isData(self):
        return self._isData

    @isData.setter
    def isData(self, value: bool):
        if self._isData != value:
            self._isData = value
            self.isDataChanged.emit() 

    @Property(bool,notify=enabledChanged)
    def enabled(self):
        return self._enabled

    @enabled.setter
    def enabled(self, value: bool):
        if self._enabled != value:
            self._enabled = value
            self.enabledChanged.emit() 

class HourModel(QObject):
    keyChanged = Signal()
    hourChanged = Signal()
    isDataChanged = Signal()
    enabledChanged = Signal()

    def __init__(self,key = 0, hour = None, fullHour = None, isData = False, enabled = False):
        super().__init__()
        self._key = key
        self._hour = hour
        self._fullHour = fullHour
        self._isData = isData
        self._enabled = enabled

    @Property(int,notify=keyChanged)
    def key(self):
        return self._key
    
    @Property(str,notify=hourChanged)
    def hour(self):
        return self._hour

    @hour.setter
    def hour(self, value: str):
        if self._hour != value:
            self._hour = value
            self.hourChanged.emit() 

    @Property(bool,notify=isDataChanged)
    def isData(self):
        return self._isData

    @isData.setter
    def isData(self, value: bool):
        if self._isData != value:
            self._isData = value
            self.isDataChanged.emit() 

    @Property(bool,notify=enabledChanged)
    def enabled(self):
        return self._enabled

    @enabled.setter
    def enabled(self, value: bool):
        if self._enabled != value:
            self._enabled = value
            self.enabledChanged.emit() 

class CalendarModel(QObject):
    # Signals
    weekDaysChanged = Signal()
    monthIndexChanged = Signal()
    yearValueChanged = Signal()
    selectedDatesChanged = Signal()
    selectedHoursChanged = Signal()
    monthTextChanged = Signal()
    dayListChanged = Signal()
    hourListChanged = Signal()
    def __init__(self, ctx: RulerContext):
        super().__init__(ctx)
        self._monthIndex = datetime.now().month
        self._yearValue = datetime.now().year
        self._monthNames = [
            "January", "February", "March", "April", "May", "June",
            "July", "August", "September", "October", "November", "December"
        ]
        self._weekDays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
        self._monthText = ""
        self._ctrlPressed = False
        self._dayList = []
        self._hourList = []
        self._selectedDates = []
        self._selectedHours = []
        self._availableDateRange = []
        self._recordedDateRange = []
        self._availableHourRange = []
        self._recordedHourRange = []
        self.createHourList()
        self.updateCalendar()

    @Property(list,notify=weekDaysChanged)
    def weekDays(self):
        return self._weekDays
    
    @Property(int,notify=monthIndexChanged)
    def monthIndex(self):
        return self._monthIndex

    @monthIndex.setter
    def monthIndex(self, value: int):
        if self._monthIndex != value:
            self._monthIndex = value
            self.monthIndexChanged.emit() 

    @Property(int,notify=yearValueChanged)
    def yearValue(self):
        return self._yearValue

    @yearValue.setter
    def yearValue(self, value: int):
        if self._yearValue != value:
            self._yearValue = value
            self.yearValueChanged.emit() 

    @Property(int,notify=yearValueChanged)
    def yearValue(self):
        return self._yearValue

    @yearValue.setter
    def yearValue(self, value: int):
        if self._yearValue != value:
            self._yearValue = value
            self.yearValueChanged.emit() 

    @Property(list)
    def monthNames(self) -> list:
        return self._monthNames
    
    @Property(list, notify=selectedDatesChanged)
    def selectedDates(self):
        return self._selectedDates

    @selectedDates.setter
    def selectedDates(self, value: list):
        # logger.debug(f'selectedDates = {value}')
        if self._selectedDates != value:
            self._selectedDates = value
            self.selectedDatesChanged.emit() 

    @Property(list, notify=selectedHoursChanged)
    def selectedHours(self):
        return self._selectedHours

    @selectedHours.setter
    def selectedHours(self, value: list):
        if self._selectedHours != value:
            self._selectedHours = value
            self.selectedHoursChanged.emit() 

    @Property(str,notify=monthTextChanged)
    def monthText(self):
        return self._monthText

    @monthText.setter
    def monthText(self, value: str):
        if self._monthText != value:
            self._monthText = value
            self.monthTextChanged.emit() 

    @Property(list,notify=dayListChanged)
    def dayList(self):
        return self._dayList

    @dayList.setter
    def dayList(self, value: list):
        if self._dayList != value:
            self._dayList = value
            self.dayListChanged.emit() 

    @Property(list,notify=hourListChanged)
    def hourList(self):
        return self._hourList

    @hourList.setter
    def hourList(self, value: list):
        if self._hourList != value:
            self._hourList = value
            self.hourListChanged.emit()   

    def createHourList(self):
        data = ["00:00", "01:00", "02:00", "03:00", 
                "04:00", "05:00", "06:00", "07:00", 
                "08:00", "09:00", "10:00", "11:00", 
                "12:00", "13:00", "14:00", "15:00", 
                "16:00", "17:00", "18:00", "19:00", 
                "20:00", "21:00", "22:00", "23:00"]
        temp = []
        for i,item in enumerate(data):
            hourModel = HourModel(key= i, hour=item,fullHour=f"{item}:00", isData=False,enabled=False)
            temp.append(hourModel)
        self.hourList = temp

    @Slot()
    def updateCalendar(self):
        firstDay = datetime(self._yearValue, self._monthIndex, 1).weekday()  # Thứ của ngày đầu tiên (0 = Monday)
        daysInMonth = (datetime(self._yearValue, self._monthIndex % 12 + 1, 1) - timedelta(days=1)).day
        
        daysArray = [""] * firstDay  # Thêm ngày trống để căn chỉnh đúng cột
        daysArray += [str(i) for i in range(1, daysInMonth + 1)]  # Thêm ngày thực tế
        temp = []
        for i, item in enumerate(daysArray):
            fullDay = None
            if item != "":
                fullDay = f"{self._yearValue}-{self._monthIndex:02d}-{int(item):02d}"
            dayModel = DayModel(key=i,day=item,fullDay=fullDay, isData=False,enabled=False)
            temp.append(dayModel)
        for item in temp:
            if item._fullDay in self._availableDateRange:
                item.enabled = True
            if item._fullDay in self._recordedDateRange:
                item.isData = True
        self.dayList = temp
        self.monthText = f"{self._monthNames[self._monthIndex - 1]} / {self._yearValue}"

    @Slot(list)    
    def updateSelectedDates(self,selectedDates: list) -> None:
        """
        Chỉ lấy ngày từ min cho đến ngày cuối trong mảng.
        Nếu ngày cuối trong mảng là min thì lấy dải ngày từ min -> max.
        """
        array = [item.day for item in selectedDates]
        numArray = [int(x) for x in array]  # Chuyển chuỗi số thành số nguyên
        minValue = min(numArray)
        maxValue = max(numArray)
        last = numArray[-1]
        if minValue == last:
            fullList = [str(i) for i in range(minValue, maxValue + 1)]  # Trả về mảng chuỗi
        else:
            fullList = [str(i) for i in range(minValue, last + 1)]  # Trả về mảng chuỗi
        temp = []
        for day in fullList:
            for item in self.dayList:
                if day == item.day:
                    temp.append(item)

        self.selectedDates = temp
        if len(self.selectedDates) == 1:
            self.selectedHours = []
            day = self._selectedDates[0]
            for item in self._hourList:
                key = f"{day._fullDay} {item._fullHour}"
                if key in self._availableHourRange:
                    item.enabled = True
                else: 
                    item.enabled = False
                if key in self._recordedHourRange:
                    item.isData = True
                else: 
                    item.isData = False 
        else:
            for item in self._hourList:
                item.isData = False
                item.enabled = False
            self.selectedHours = []

    @Slot(list)    
    def updateSelectedHours(self,selectedHours: list) -> None:
        """
        Chỉ lấy giờ từ min cho đến giờ cuối trong mảng.
        Nếu giờ cuối trong mảng là min thì lấy dải giờ từ min -> max.
        """
        array = [item.key for item in selectedHours]
        numArray = [int(x) for x in array]  # Chuyển chuỗi số thành số nguyên
        minValue = min(numArray)
        maxValue = max(numArray)
        last = numArray[-1]
        if minValue == last:
            fullList = [i for i in range(minValue, maxValue + 1)]  # Trả về mảng chuỗi
        else:
            fullList = [i for i in range(minValue, last + 1)]  # Trả về mảng chuỗi
        temp = []
        for hour in fullList:
            for item in self.hourList:
                if hour == item.key:
                    temp.append(item)
        self.selectedHours = temp

    def updateAvailableDateRanges(self,startDuration, endDuration):
        startDate = datetime.fromtimestamp(startDuration // 1000).date()
        endDate = datetime.fromtimestamp(endDuration // 1000).date()
        self._availableDateRange = [(startDate + timedelta(days=i)).isoformat() 
                    for i in range((endDate - startDate).days + 1)]
        for item in self._dayList:
            if item._fullDay in self._availableDateRange:
                item.enabled = True           

    def updateAvailableHourRanges(self,startDuration, endDuration):
        startDt = datetime.fromtimestamp(startDuration // 1000)
        endDt = datetime.fromtimestamp(endDuration // 1000)
        currentDt = startDt.replace(minute=0, second=0, microsecond=0)  # Đưa về đầu giờ
        hourlyRanges = {}
        while currentDt <= endDt:
            nextHour = currentDt + timedelta(hours=1)
            rangeStart = max(currentDt, startDt)  # Bắt đầu từ start_timestamp nếu giữa chừng
            rangeEnd = min(nextHour, endDt)  # Kết thúc ở end_timestamp nếu giữa chừng
            # hourly_ranges.append((range_start.strftime("%Y-%m-%d %H:%M:%S"), range_end.strftime("%Y-%m-%d %H:%M:%S")))
            hourlyRanges[rangeStart.strftime("%Y-%m-%d %H:%M:%S")] = rangeEnd.strftime("%Y-%m-%d %H:%M:%S")
            currentDt = nextHour
        self._availableHourRange =  hourlyRanges

    def updateRecordedDateRanges(self,startDuration, endDuration):
        startDate = datetime.fromtimestamp(startDuration // 1000).date()
        endDate = datetime.fromtimestamp(endDuration // 1000).date()
        dateRange = [(startDate + timedelta(days=i)).isoformat() 
                    for i in range((endDate - startDate).days + 1)]
        self._recordedDateRange.extend(dateRange)
        for item in self._dayList:
            if item._fullDay in self._recordedDateRange:
                item.isData = True

    def updateRecordedHourRanges(self,startDuration, endDuration):
        startDt = datetime.fromtimestamp(startDuration // 1000)
        endDt = datetime.fromtimestamp(endDuration // 1000)
        currentDt = startDt.replace(minute=0, second=0, microsecond=0)  # Đưa về đầu giờ
        hourlyRanges = {}
        while currentDt <= endDt:
            nextHour = currentDt + timedelta(hours=1)
            rangeStart = max(currentDt, startDt)  # Bắt đầu từ start_timestamp nếu giữa chừng
            rangeEnd = min(nextHour, endDt)  # Kết thúc ở end_timestamp nếu giữa chừng
            # hourly_ranges.append((range_start.strftime("%Y-%m-%d %H:%M:%S"), range_end.strftime("%Y-%m-%d %H:%M:%S")))
            hourlyRanges[currentDt.strftime("%Y-%m-%d %H:%M:%S")] = rangeEnd.strftime("%Y-%m-%d %H:%M:%S")
            currentDt = nextHour
        self._recordedHourRange.extend(hourlyRanges)

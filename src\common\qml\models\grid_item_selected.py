from PySide6.QtCore import Signal,QObject,Qt,QEvent
from src.utils.camera_qsettings import Camera_Qsettings
from src.common.controller.main_controller import main_controller
from PySide6.QtCore import QObject, Property, Signal,Slot,QByteArray, QEnum
import logging
logger = logging.getLogger(__name__)

class GridItemSelected(QObject):
    __instance = None
    screenChanged = Signal()
    itemTypeChanged = Signal()
    cameraIdChanged = Signal()
    widgetChanged = Signal()
    widget_changed = Signal(tuple)
    def __init__(self):
        super().__init__()
        self.data = {'screen':None,'type':None, 'tab_index': None, 'camera_id': None,'widget': None}
        self._screen = None
        self._itemType = None
        self._cameraId = "ahihi"
        self._widget = None
    @staticmethod
    def get_instance():
        if GridItemSelected.__instance is None:
            GridItemSelected.__instance = GridItemSelected()
        return GridItemSelected.__instance

    @Property(int,notify=screenChanged)
    def screen(self):
        return self._screen

    @screen.setter
    def screen(self, value: int):
        if self._screen != value:
            self._screen = value
            self.screenChanged.emit()

    @Property(str,notify=itemTypeChanged)
    def itemType(self):
        return self._itemType

    @itemType.setter
    def itemType(self, value: str):
        if self._itemType != value:
            self._itemType = value
            self.itemTypeChanged.emit()

    @Property(str,notify=cameraIdChanged)
    def cameraId(self):
        return self._cameraId

    @cameraId.setter
    def cameraId(self, value: str):
        if self._cameraId != value:
            self._cameraId = value
            self.cameraIdChanged.emit()

    @Property(QObject,notify=widgetChanged)
    def widget(self):
        return self._widget

    @widget.setter
    def widget(self, value: QObject):
        if self._widget != value:
            self._widget = value
            self.widgetChanged.emit()

    def set_data(self,screen = None,type = None,tab_index = None,camera_id = None, widget = None):
        self.data['screen'] = screen
        self.data['type'] = type
        self.data['tab_index'] = tab_index
        self.data['camera_id'] = camera_id
        self.data['widget'] = widget

    def is_tab_index(self,tab_index):
        if self.data['tab_index'] == tab_index:
            return True
        return False

    def is_same_camera(self,camera_id,widget):
        if self.data['camera_id'] == camera_id and self.data['widget'] == widget:
            return True
        return False

    def is_same_widget(self,widget):
        if self.data['widget'] == widget:
            return True
        return False

    def clear(self):
        widget = self.data['widget']
        if widget is not None:
            # print(f'widget = {widget}')
            if widget is not None and hasattr(widget, 'grid_item_unclicked'):
                widget.grid_item_unclicked()
            elif widget is not None and hasattr(widget, 'stack_item'):
                widget.stack_item.grid_item_unclicked()
        self.data = {'screen':None,'type':None, 'tab_index': None, 'camera_id': None,'widget': None}

grid_item_selected = GridItemSelected.get_instance()

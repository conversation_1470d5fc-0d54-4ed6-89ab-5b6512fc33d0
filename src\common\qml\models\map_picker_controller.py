from PySide6.QtCore import QObject, Signal, Property, Slot

class MapPickerController(QObject):
    coordinateChanged = Signal(float, float)

    def __init__(self, parent=None, lon=None, lat=None):
        super().__init__(parent)
        try:
            self._lon = float(lon) if lon is not None else 0.0
            self._lat = float(lat) if lat is not None else 0.0
        except ValueError:
            self._lon = 0.0
            self._lat = 0.0


    @Property(float, notify=coordinateChanged)
    def lon(self):
        return self._lon

    @lon.setter
    def lon(self, value):
        if self._lon != value:
            self._lon = value
            self.coordinateChanged.emit(self._lon, self._lat)

    @Property(float, notify=coordinateChanged)
    def lat(self):
        return self._lat

    @lat.setter
    def lat(self, value):
        if self._lat != value:
            self._lat = value
            self.coordinateChanged.emit(self._lon, self._lat)

    @Slot(float, float)
    def set_position(self, lon, lat):
        if self._lon != lon or self._lat != lat:
            self._lon = lon
            self._lat = lat
            self.coordinateChanged.emit(self._lon, self._lat)


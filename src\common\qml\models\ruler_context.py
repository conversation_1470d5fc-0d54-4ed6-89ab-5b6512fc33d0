from PySide6.QtCore import QObject, Property, Signal, QMutex, QTimer, QElapsedTimer, QDateTime
import threading
import logging
logger = logging.getLogger(__name__)
# Constants
DEFAULT = -1
DELAY_FOR_UPDATE_X = 5000  # 1000/2 ms

# Time constants
D1S = 1000
D5S = 5000
D10S = 10000
D30S = 30000
D1MIN = 60000
D5MIN = 300000
D10MIN = 600000
D30MIN = 1800000
D1H = 3600000
D3H = 10800000
D12H = 43200000
D1D = 86400000
D1W = 604800000
D2W = 1209600000
D3W = 1814400000
D4W = 2419200000
D8W = 4838400000
D13W = 7862400000
D26W = 15724800000
D52W = 31449600000
# CMS Levels
CMS_LEVELS = [
    D5S, D10S, D30S, D1MIN, D5MIN, D10MIN, D30MIN,
    D1H, D3H, D12H, D1D, D1W, D2W, D4W,D8W, D13W, D26W, D52W
]

# Delegate states
CDELEGATE_STATES = [
    [2, 5, 10],  # 1s
    [2, 3, 2],   # 1any
    [5, 2, 5],   # 5s
    [5, 2, 3],   # 5min
    [2, 5, 2],   # 10any
    [3, 2, 5],   # 30any
    [3, 2, 3],   # 3h
    [4, 3, 2],   # 12h
    [2, 4, 3],   # 1d
    [7, 2, 4],    # 1w
    [2, 7, 2],    # 2w
    [2, 2, 7],    # 4w
    [8, 7, 2],    # 8w
    [13, 7, 1],    # 13w
    [26, 2, 1],    # 26w
    [52, 2, 2]    # 52w
]

class RulerContext(QObject):
    # Class variables
    LARGEST_SMALL_UNIT_W = 200.0
    LARGEST_UNIT_W = 500.0
    MAXIMUM_VISIBLE_UNIT_SCALED_W = 800.0
    MAXIMUM_ITEM_COUNT = 255

    # Signals
    totalTimeChanged = Signal()
    unitsChanged = Signal()
    baseChanged = Signal()
    widthPerMiliChanged = Signal()
    visibleWidthChanged = Signal()
    visibleRangeChanged = Signal()
    absoluteVisibleRangeChanged = Signal()
    positionChanged = Signal()
    contextChanged = Signal()
    menuOpenedChanged = Signal()
    offsetChanged = Signal()
    isMaximumScaleChanged = Signal(bool)

    def __init__(self, parent=None, total_time: int = 0, highest_unit: int = 0,
                 width: float = 0, x: float = 0, offset: int = 0):
        super().__init__(parent)
        
        # Initialize instance variables
        self._total_time = total_time
        self._highest_unit = highest_unit
        self._normal_unit = 0
        self._small_unit = 0
        self._smallest_unit = 0
        self._width = width
        self._width_per_mili = total_time and width/total_time or 0
        self._x = x
        self._visible_width = 0.0
        self._absolute_visible_range = [0, 0]
        self._position = 0
        self._is_menu_opened = False
        self._offset = offset
        self._is_maximum_scale = False
        
        # Initialize mutex and timers
        self._mutex = QMutex()
        self._delay_timer = QTimer(self)
        self._process_timer = QElapsedTimer()

        # Connect signals
        self.unitsChanged.connect(self.calculateMaximunScale)
        self.widthPerMiliChanged.connect(self.calculateMaximunScale)

        # Setup delay timer
        self._delay_timer.setInterval(DELAY_FOR_UPDATE_X)
        self._delay_timer.timeout.connect(self.realTimeUpdate)
        self._delay_timer.start()

    def __del__(self):
        try:
            if self._delay_timer and not self._delay_timer.parent():
                if self._delay_timer.isActive():
                    self._delay_timer.stop()
                self._delay_timer.deleteLater()
        except (RuntimeError, AttributeError):
            pass

    @Property(float, notify=widthPerMiliChanged)
    def widthPerMili(self) -> float:
        return self._width_per_mili

    def setWidthPerMili(self, value: float):
        if self._width_per_mili == value:
            return

        value = max(0, value)
        self._width_per_mili = value
        self.updateUnits()
        self.refreshVisibleRange()
        self.widthPerMiliChanged.emit()

    def totalTime(self) -> int:
        return self._total_time

    def setTotalTime(self, new_total_time: int) -> bool:
        if self._total_time == new_total_time:
            return False

        new_total_time = max(0, new_total_time)
        self._total_time = new_total_time
        
        if self._total_time == 0:
            self.setWidthPerMili(0)
        else:
            self.setWidthPerMili(self._width / self._total_time)
        self.updateUnits()
        self.totalTimeChanged.emit()
        return True

    def offset(self) -> int:
        return self._offset

    def setOffset(self, offset: int) -> bool:
        if self._offset == offset:
            return False

        if self._width == 0 or self._total_time == 0 or self._visible_width == 0:
            self._offset = 0
            return False

        self._offset = offset
        self.refreshVisibleRange()
        self.offsetChanged.emit()
        return True

    def width(self) -> float:
        return self._width

    def x(self) -> float:
        return self._x

    def shift_for_duration(self, duration:int):
        self._x = self._x - duration * self._width_per_mili
        temp = (abs(self._x) + self._visible_width) / self._width_per_mili
        if temp > self._total_time:
            self._x = -(self._total_time*self._width_per_mili - self._visible_width)

        self.refreshVisibleRange()
        self.baseChanged.emit()
        
    def setBase(self, width: float = DEFAULT, x: float = DEFAULT):
        
        if width == DEFAULT:
            width = self._width
        if x == DEFAULT:
            x = self._x

        if self._width == width and self._x == x:
            return

        width = max(0, width)
        x = min(0, x)

        if x + width < self._visible_width:
            return

        self._width = width
        self._x = x
        if self._total_time == 0:
            self.setWidthPerMili(0)
        else:
            self.setWidthPerMili(self._width / self._total_time)
        self.refreshVisibleRange()
        self.baseChanged.emit()

    def updateZoomToSelection(self, x: int, width: int):
        startDuration = self.absoluteStart + int(x / self.widthPerMili)
        endDuration = self.absoluteStart + int((x+width) / self.widthPerMili)
        self.changeVisibleRange(startDuration,endDuration)

    def changeVisibleRange(self,startDuration: int,endDuration: int):
        
        delta = (endDuration - startDuration) / self._total_time
        self._width = self._visible_width / delta
        self._x = (self._offset - startDuration)*self._width/self._total_time
        if self._total_time == 0:
            self.setWidthPerMili(0)
        else:
            self.setWidthPerMili(self._width / self._total_time)
        self.refreshVisibleRange()
        self.baseChanged.emit()

    @Property(int)
    def normalUnit(self) -> int:
        return self._normal_unit

    @Property(int)
    def smallUnit(self) -> int:
        return self._small_unit

    @Property(int)
    def smallestUnit(self) -> int:
        return self._smallest_unit

    @Property(int)
    def highestUnit(self) -> int:
        return self._highest_unit

    def visibleWidth(self) -> float:
        return self._visible_width

    def setVisibleWidth(self, new_visible_width: float):
        if self._visible_width == new_visible_width:
            return

        new_visible_width = max(0, new_visible_width)
        self._visible_width = new_visible_width
        self.refreshVisibleRange()
        self.visibleWidthChanged.emit()

    def realTimeUpdate(self):
        def func():
            if self._offset:
                current_duration = QDateTime.currentMSecsSinceEpoch()
                self.setTotalTime(current_duration-self._offset)
                if self._total_time == 0:
                    self.setWidthPerMili(0)
                else:
                    self.setWidthPerMili(self._width / self._total_time)
                self.baseChanged.emit()
                # self.contextChanged.emit()
        threading.Thread(target=func).start()

    def getSubItemCount(self, unit: int) -> int:
        if unit == 100:
            return 0

        secs = unit // 1000
        mins = unit // 60000
        hours = unit // 3600000
        ds = unit // 86400000
        ws = unit // 604800000

        if unit == 500:
            return 5
        elif secs == 5 or mins == 5:
            return 5
        elif hours == 3:
            return 3
        elif secs == 10 or mins == 10 or hours == 10:
            return 2
        elif secs == 30 or mins == 30 or hours == 30:
            return 3
        elif secs == 1 or mins == 1 or hours == 1 or ds == 1:
            return 2
        elif hours == 12:
            return 4
        elif ws == 1:
            return 7

        return 0

    def refreshVisibleRange(self):
        if self._width_per_mili == 0 or self._highest_unit == 0:
            self._absolute_visible_range[0] = 0
            self._absolute_visible_range[1] = 0
            self.absoluteVisibleRangeChanged.emit()
            return

        abs_x = abs(self._x)
        self._absolute_visible_range[0] = abs_x / self._width_per_mili + self._offset
        self._absolute_visible_range[1] = (abs_x + self._visible_width) / self._width_per_mili + self._offset
        self.absoluteVisibleRangeChanged.emit()

    def relativeWidthFromAbsoluteStart(self, position: int = -1) -> float:
        if position == -1:
            position = self._position

        if position >= self._total_time + self._offset:
            position = self._total_time + self._offset

        if position <= self._absolute_visible_range[0]:
            return 0

        if position >= self._absolute_visible_range[1]:
            return self._visible_width - 3

        return (position - self._absolute_visible_range[0]) * self._width_per_mili

    @Property(int)
    def absoluteStart(self) -> int:
        return int(self._absolute_visible_range[0])

    @Property(int)
    def absoluteStop(self) -> int:
        return int(self._absolute_visible_range[1])

    def isMaximumScale(self) -> bool:
        return self._is_maximum_scale

    def calculateMaximunScale(self):
        new_is_maximum_scale = (self._highest_unit == CMS_LEVELS[0] and 
                              self._highest_unit * self._width_per_mili > self.MAXIMUM_VISIBLE_UNIT_SCALED_W)
        if self._is_maximum_scale == new_is_maximum_scale:
            return

        self._is_maximum_scale = new_is_maximum_scale
        self.isMaximumScaleChanged.emit(self._is_maximum_scale)

    def updatePosition(self, position: int) -> bool:
        if self._position == position:
            return False

        last_pos = self._position
        last_value = self._total_time + self._offset

        if 0 <= position <= last_value:
            self._position = position
        elif position < 0:
            self._position = 0
        else:
            self._position = last_value

        if self._width != self._visible_width:
            if (self._absolute_visible_range[0] <= last_pos <= self._absolute_visible_range[1] and 
                self._absolute_visible_range[1] < last_value):
                self._width = self.width()
                self._x = self._x - (self._position - last_pos) * self._width_per_mili
                if self._total_time == 0:
                    self.setWidthPerMili(0)
                else:
                    self.setWidthPerMili(self._width / self._total_time)
                self.refreshVisibleRange()
                self.baseChanged.emit()
        self.positionChanged.emit()
        return True
    
    def setPosition(self, position: int) -> bool:
        if self._position == position:
            return False

        last_pos = self._position
        last_value = self._total_time + self._offset

        if 0 <= position <= last_value:
            self._position = position
        elif position < 0:
            self._position = 0
        else:
            self._position = last_value

        if self._width != self._visible_width:
            if (self._absolute_visible_range[0] <= last_pos <= self._absolute_visible_range[1] and 
                self._absolute_visible_range[1] < last_value):
                self.setBase(self.width(), self._x - (self._position - last_pos) * self._width_per_mili)
        self.positionChanged.emit()
        return True

    def setPositionFromMouseX(self, mouse_x: float) -> bool:
        if self._width_per_mili == 0:
            return False

        new_pos = self.absoluteStart + int(mouse_x / self.widthPerMili)
        if self._position == new_pos:
            return False

        self._position = new_pos
        
        self.positionChanged.emit()
        return True

    def positionFromMouseX(self, mouse_x: float) -> int:
        if self._width_per_mili == 0:
            return 0
        return int(self.absoluteStart) + int(mouse_x / self.widthPerMili)

    def position(self) -> int:
        return self._position

    def isMenuOpened(self) -> bool:
        return self._is_menu_opened

    def setIsMenuOpened(self, is_opened: bool):
        if self._is_menu_opened == is_opened:
            return
        self._is_menu_opened = is_opened
        self.menuOpenedChanged.emit()

    def isZoomable(self, start: int, end: int) -> bool:
        temp_visible_range = end - start
        if temp_visible_range <= 0:
            return False

        temp_rule_width = self._visible_width * self._total_time / temp_visible_range
        temp_width_per_mili = temp_rule_width / self._total_time
        temp_max_scale_width = CMS_LEVELS[0] * temp_width_per_mili
        
        return temp_max_scale_width <= self.MAXIMUM_VISIBLE_UNIT_SCALED_W

    def minimumZoomRange(self) -> int:
        return CMS_LEVELS[0]

    def setLargestUnitW(self, value: float):
        if self.LARGEST_UNIT_W != value:
            self.LARGEST_UNIT_W = value

    def setLargestSmallUnitW(self, value: float):
        if self.LARGEST_SMALL_UNIT_W != value:
            self.LARGEST_SMALL_UNIT_W = value

    def setMaximumVisibleUnitScaledW(self, value: float):
        if self.MAXIMUM_VISIBLE_UNIT_SCALED_W != value:
            self.MAXIMUM_VISIBLE_UNIT_SCALED_W = value

    def maximumItemCount(self) -> int:
        return self.MAXIMUM_ITEM_COUNT

    def updateUnits(self):
        if self._width_per_mili == 0:
            return

        old_highest_unit = self._highest_unit
        temp_highest_unit = 0

        for level in CMS_LEVELS:
            width = self._width_per_mili * level
            # print(f'updateUnits = {level,width,self._visible_width/3}')
            if width > self._visible_width/4:
                temp_highest_unit = level
                break
            # else:
            #     temp_highest_unit = CMS_LEVELS[-1]
            # if level < D5MIN and width >= self.LARGEST_SMALL_UNIT_W:
            #     temp_highest_unit = level
            #     break
            # if level >= D5MIN and width >= self.LARGEST_UNIT_W:
            #     temp_highest_unit = level
            #     break
            # if level >= D10MIN and width >= self.LARGEST_UNIT_W:
            #     temp_highest_unit = level
            #     break
            # if level >= D1H and width >= self.LARGEST_UNIT_W:
            #     temp_highest_unit = level
            #     break
            # if level == CMS_LEVELS[-1]:
            #     temp_highest_unit = level if width < self.LARGEST_UNIT_W else CMS_LEVELS[0]
            #     break
        self._highest_unit = temp_highest_unit
        
        delegate_state = None
        secs = self._highest_unit // D1S
        mins = self._highest_unit // D1MIN
        hours = self._highest_unit // D1H
        ds = self._highest_unit // D1D
        ws = self._highest_unit // D1W
        _2ws = self._highest_unit // D2W
        _4ws = self._highest_unit // D4W
        _8ws = self._highest_unit // D8W
        _13ws = self._highest_unit // D13W
        _26ws = self._highest_unit // D26W
        _52ws = self._highest_unit // D52W
        # Determine delegate state based on time units
        if secs == 1:
            delegate_state = CDELEGATE_STATES[0]
        elif secs == 5:
            delegate_state = CDELEGATE_STATES[2]
        elif mins == 5:
            delegate_state = CDELEGATE_STATES[3]
        elif hours == 3:
            delegate_state = CDELEGATE_STATES[6]
        elif secs == 10 or mins == 10 or hours == 10:
            delegate_state = CDELEGATE_STATES[4]
        elif secs == 30 or mins == 30 or hours == 30:
            delegate_state = CDELEGATE_STATES[5]
        elif mins == 1 or hours == 1:
            delegate_state = CDELEGATE_STATES[1]
        elif hours == 12:
            delegate_state = CDELEGATE_STATES[7]
        elif ds == 1:
            delegate_state = CDELEGATE_STATES[8]
        elif ws == 1:
            delegate_state = CDELEGATE_STATES[9]
        elif _2ws == 1:
            delegate_state = CDELEGATE_STATES[10]
        elif _4ws == 1:
            delegate_state = CDELEGATE_STATES[11]
        elif _8ws == 1:
            delegate_state = CDELEGATE_STATES[12]
        elif _13ws == 1:
            delegate_state = CDELEGATE_STATES[13]
        elif _26ws == 1:
            delegate_state = CDELEGATE_STATES[14]
        elif _52ws == 1:
            delegate_state = CDELEGATE_STATES[15]
        if delegate_state:
            delegate0, delegate1, delegate2 = delegate_state
            self._smallest_unit = self._highest_unit // (delegate0 * delegate1 * delegate2)
            self._small_unit = self._highest_unit // (delegate0 * delegate1)
            self._normal_unit = self._highest_unit // delegate0

        if old_highest_unit != self._highest_unit:
            self.unitsChanged.emit()

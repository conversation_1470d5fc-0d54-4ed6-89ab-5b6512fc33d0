from PySide6.QtCore import QObject, Property, Signal, Slot
from enum import IntEnum
from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from src.common.qml.models.ruler_context import RulerContext
from src.utils.utils import Utils
import math
from src.common.qml.models.timestepnode import TimeStepValue
import logging
logger = logging.getLogger(__name__)

ANIMATION_DURATION = 500  # ms

class EdgeType(IntEnum):
    HIGHEST = 0
    NORMAL = 1
    SMALL = 2
    SMALLEST = 3
    UNDEFINED = 4

class State(IntEnum):
    Idle = 0
    UndefinedHidden = 1
    OutsideLeftHidden = 2
    OutsideRightHidden = 3
    Visible = 4

class TimeStep2(QObject):
    # Signals
    baseChanged = Signal()
    lineTypeChanged = Signal()
    textChanged = Signal()
    dateTextChanged = Signal()
    enableAnimationChanged = Signal()
    enableDateTextChanged = Signal()
    enableRuleEdgeChanged = Signal()
    enableRuleEdgeAnimationChanged = Signal()
    animationDurationChanged = Signal()

    def __init__(self, ctx: RulerContext):
        super().__init__(ctx)
        self._ctx = ctx
        self._value = TimeStepValue()
        self._current_state = State.Idle
        self._date_text = ""
        self._text = ""
        self._line_type = EdgeType.UNDEFINED
        self._relative_width = 0.0
        self._x = 0.0
        self._enable_animation = False
        self._enable_rule_edge = False
        self._enable_date_text = False
        self._animation_duration = ANIMATION_DURATION

        # Initial updates
        self.updateText()
        self.updateLineType()

        # Connect context signals
        self._ctx.unitsChanged.connect(self.updateLineType)
        self._ctx.baseChanged.connect(self.onBaseChanged)


    def onBaseChanged(self):
        self.updateBase()
        self.updateEnableRuleEdge()
        self.updateText()

    def calculateAnimationDuration(self, delta_x: float, delta_width: float) -> int:
        if delta_x * delta_width != 0:
            if pow(delta_x + delta_width, 2) != 0:
                return int(ANIMATION_DURATION * delta_x * delta_width / pow(delta_x + delta_width, 2))
            else: 
                return 0
        elif delta_x != 0:
            return int(ANIMATION_DURATION / delta_x)
        elif delta_width != 0:
            return int(ANIMATION_DURATION / delta_width)
        return ANIMATION_DURATION

    def setBase(self, new_relative_width: float, new_x: float):
        if self._relative_width == new_relative_width and self._x == new_x:
            return

        self._animation_duration = self.calculateAnimationDuration(
            new_x - self._x, new_relative_width - self._relative_width)
        self._relative_width = new_relative_width
        self._x = new_x
        self.baseChanged.emit()

    def updateBase(self):
        absolute_start = self._ctx.absoluteStart
        absolute_end = self._ctx.absoluteStop
        new_relative_width = 0
        new_x = 0

        if (self._value.start > absolute_end or 
            self._value.end < absolute_start):
            new_relative_width = 0
        elif (absolute_start <= self._value.start and 
              self._value.end <= absolute_end):
            new_relative_width = self._value.unit() * self._ctx.widthPerMili
        elif (absolute_start <= self._value.start and 
              self._value.start <= absolute_end):
            new_relative_width = (absolute_end - self._value.start) * self._ctx.widthPerMili
        elif (absolute_start <= self._value.end and 
              self._value.end <= absolute_end):
            new_relative_width = (self._value.end - absolute_start) * self._ctx.widthPerMili
        else:
            new_relative_width = self._ctx.visibleWidth()

        if self._value.start < absolute_start:
            new_x = 0
        elif self._value.start > absolute_end:
            new_x = self._ctx.visibleWidth()
        else:
            new_x = (self._value.start - absolute_start) * self._ctx.widthPerMili

        self.setBase(new_relative_width, new_x)

        # if (self._line_type == EdgeType.NORMAL and 
        #     self._ctx.offset()):
        #     self.setEnableDateText(new_relative_width > 100)
        # else:
        #     self.setEnableDateText(False)
        
        if self._value.unit() == self._ctx.highestUnit:
            # print(f'updateBase = {self._value.unit(),datetime.fromtimestamp(self._value.start / 1000)}')
            if (self._ctx.absoluteStart < self._value.start and self._value.start < self._ctx.absoluteStop) or (self._ctx.absoluteStart < self._value.end and self._value.end < self._ctx.absoluteStop):
                # print(f'updateBase = {self._value.unit(),datetime.fromtimestamp(self._value.start / 1000),self._enable_date_text}')
                self.setEnableDateText(True)
            else:
                self.setEnableDateText(False)
        else:
            self.setEnableDateText(False)

    def updateLineType(self):
        if self._value.unit() == self._ctx.highestUnit:
            self.setLineType(EdgeType.HIGHEST)
        elif self._value.unit() == self._ctx.smallestUnit:
            self.setLineType(EdgeType.SMALLEST)
        elif self._value.unit() == self._ctx.smallUnit:
            self.setLineType(EdgeType.SMALL)
        elif self._value.unit() == self._ctx.normalUnit:
            self.setLineType(EdgeType.NORMAL)
        else:
            self.setLineType(EdgeType.UNDEFINED)

    def updateEnableRuleEdge(self):
        self.setEnableRuleEdge(
            not self._value.is_equal_to_parent_end and 
            self._ctx.absoluteStart < self._value.end and 
            self._value.end < self._ctx.absoluteStop
        )

    def updateText(self):
        old_text = self._text
        old_date_text = self._date_text
        dt = datetime.fromtimestamp(self._value.end / 1000)
        self._text = Utils.getTimeStepString(
            self._value.end, 
            self._ctx.offset() > 0
        )

        # if self._ctx.normalUnit == Utils.D1H:
        #     self._date_text = dt.strftime("%d %b %Y %I %p")
        # else:
        #     self._date_text = dt.strftime("%d %b %Y")
        if self._value.unit() == self._ctx.highestUnit:
            dt = datetime.fromtimestamp(self._value.start / 1000)
            if self._ctx.highestUnit == Utils.D1S:
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D5S:
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D10S:
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D30S:
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D1MIN:
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D5MIN:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D10MIN:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D30MIN:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D1H:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D3H:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y %I:%M %p")
            elif self._ctx.highestUnit == Utils.D12H:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y %I %p")
            elif self._ctx.highestUnit == Utils.D1D:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y %I %p")
            elif self._ctx.highestUnit == Utils.D1W:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y")
            else:
                dt = datetime.fromtimestamp((self._value.end + self._value.start) / 2000)
                self._date_text = dt.strftime("%d %B %Y")

        if old_text != self._text:
            self.textChanged.emit()
        if old_date_text != self._date_text:
            self.dateTextChanged.emit()


    def setLineType(self, new_line_type: EdgeType):
        if self._line_type == new_line_type:
            return
        self._line_type = new_line_type
        self.lineTypeChanged.emit()

    def setEnableRuleEdge(self, enable: bool):
        if self._enable_rule_edge == enable:
            return
        self._enable_rule_edge = enable
        self.enableRuleEdgeChanged.emit()

    def setEnableAnimation(self, enable: bool):
        if self._enable_animation == enable:
            return
        self._enable_animation = enable
        self.enableAnimationChanged.emit()

    def setEnableDateText(self, enable: bool):
        if self._enable_date_text == enable:
            return
        self._enable_date_text = enable
        self.enableDateTextChanged.emit()

    def reset(self):
        self._value = TimeStepValue()
        self._current_state = State.Idle
        self.setLineType(EdgeType.UNDEFINED)
        self.setBase(0, 0)
        self.setEnableAnimation(False)
        self.setEnableRuleEdge(False)
        self._text = ""
        self._date_text = ""

    @Property(float, notify=baseChanged)
    def relativeWidth(self) -> float:
        return self._relative_width

    @Property(float, notify=baseChanged)
    def x(self) -> float:
        return self._x

    @Property(int, notify=lineTypeChanged)
    def lineType(self) -> int:
        return int(self._line_type)

    @Property(str, notify=textChanged)
    def text(self) -> str:
        return self._text

    @Property(str, notify=dateTextChanged)
    def dateText(self) -> str:
        return self._date_text

    @Property(bool, notify=enableDateTextChanged)
    def enableDateText(self) -> bool:
        return self._enable_date_text

    @Property(bool, notify=enableAnimationChanged)
    def enableAnimation(self) -> bool:
        return self._enable_animation

    @Property(bool, notify=enableRuleEdgeChanged)
    def enableRuleEdge(self) -> bool:
        return self._enable_rule_edge

    @Property(bool, notify=enableRuleEdgeAnimationChanged)
    def enableRuleEdgeAnimation(self) -> bool:
        return self._current_state == State.UndefinedHidden

    @Property(int, notify=animationDurationChanged)
    def animationDuration(self) -> int:
        return self._animation_duration

    # Public methods
    def state(self) -> State:
        return self._current_state

    def setState(self, new_state: State):
        if self._current_state == new_state:
            return

        # Enable animation when state is changed to Visible or from Visible
        if self._current_state == State.Visible or new_state == State.Visible:
            self.setEnableAnimation(True and self._enable_rule_edge)
        else:
            self.setEnableAnimation(False)

        self._current_state = new_state

        # Reset all data if state is Idle
        if new_state == State.Idle:
            self.reset()

    def value(self):
        return self._value
    
    def setValue(self, value: TimeStepValue):
        if self._value == value:
            return

        # Save old animation state
        old_enable_animation = self._enable_animation
        
        # Disable animation when updating visible value
        if self._value.unit() != 0:
            self.setEnableAnimation(False)

        self._value = value
        self.updateBase()
        self.updateEnableRuleEdge()
        self.updateLineType()
        self.updateText()

        # Restore animation state
        if self._value.unit() != 0:
            self.setEnableAnimation(old_enable_animation)

    def value(self) -> TimeStepValue:
        return self._value

    def unitByLineType(self) -> int:
        if self._line_type == EdgeType.HIGHEST:
            return self._ctx.highestUnit
        elif self._line_type == EdgeType.NORMAL:
            return self._ctx.normalUnit
        elif self._line_type == EdgeType.SMALL:
            return self._ctx.smallUnit
        elif self._line_type == EdgeType.SMALLEST:
            return self._ctx.smallestUnit
        return 0
    

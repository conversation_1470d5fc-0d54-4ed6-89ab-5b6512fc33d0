from dataclasses import dataclass
from typing import List, Optional
from src.utils.utils import Utils
import logging
logger = logging.getLogger(__name__)
@dataclass
class TimeStepValue:
    start: int = 0
    end: int = 0
    is_equal_to_parent_end: bool = False

    def unit(self) -> int:
        """Get the time unit (duration)"""
        if self.end >= self.start:
            return self.end - self.start
        return 0

    def __eq__(self, other: 'TimeStepValue') -> bool:
        if not isinstance(other, TimeStepValue):
            return False
        return (self.start == other.start and 
                self.end == other.end)

    def __str__(self) -> str:
        return (f"start {self.start} "
                f"{Utils.convertMilliSecondTimeToString(self.start)} "
                f"end {self.end} "
                f"{Utils.convertMilliSecondTimeToString(self.end)} "
                f"isEndEqualToParent {self.is_equal_to_parent_end}")

    def contains(self, other: 'TimeStepValue') -> bool:
        """Check if this TimeStepValue contains another TimeStepValue"""
        return self.start <= other.start and self.end >= other.end


class TimeStepNode:
    def __init__(self, start: int, end: int, child_count: int = 0, 
                 depth: int = 1, is_equal_to_parent: bool = False):
        """
        Initialize a TimeStepNode
        
        Args:
            start: Start time in milliseconds
            end: End time in milliseconds
            child_count: Number of child nodes
            depth: Current depth in the tree
            is_equal_to_parent: Whether this node's end equals its parent's end
        """
        self.value = TimeStepValue(start, end, is_equal_to_parent)
        self.child_count = child_count
        self.children: List[TimeStepNode] = []

        # Create child nodes if needed
        if child_count != 0 and depth < 5:
            delta_unit = self.value.unit() // child_count
            child_count_of_child = Utils.getSubItemCount(delta_unit)
            
            for i in range(child_count):
                child_start = self.value.start + i * delta_unit
                child_end = self.value.start + (i + 1) * delta_unit
                self.children.append(
                    TimeStepNode(
                        child_start,
                        child_end,
                        child_count_of_child,
                        depth + 1,
                        end == child_end
                    )
                )

    def depth(self) -> int:
        """Get the maximum depth of the tree from this node"""
        if not self.children:
            return 1

        max_depth = max(child.depth() for child in self.children)
        return max_depth + 1

    def captureVisible(self, left_edge: int, right_edge: int, 
                       minimum_unit: int, parent_end: int) -> List[TimeStepValue]:
        """
        Capture visible TimeStepValues within the given range
        
        Args:
            left_edge: Left boundary of visible range
            right_edge: Right boundary of visible range
            minimum_unit: Minimum time unit to consider
            parent_end: Parent node's end time
            
        Returns:
            List of visible TimeStepValues
        """
        values: List[TimeStepValue] = []
        
        # Recursively capture from children
        if self.children:
            for child in self.children:
                captured = child.captureVisible(
                    left_edge, right_edge, minimum_unit, self.value.end
                )
                values.extend(captured)

        # Check if current node is visible
        if (((left_edge <= self.value.start and self.value.start <= right_edge) or
             (left_edge <= self.value.end and self.value.end <= right_edge)) and
            self.value.unit() >= minimum_unit and
            not self.value.is_equal_to_parent_end):
            values.append(self.value)

        return values

    def captureNextVisible(self, left_edge: int, right_edge: int,
                           highest_unit: int, smallest_unit: int) -> List[TimeStepValue]:
        """
        Capture next visible TimeStepValues within the given range
        
        Args:
            left_edge: Left boundary of visible range
            right_edge: Right boundary of visible range
            highest_unit: Highest time unit to consider
            smallest_unit: Smallest time unit to consider
            
        Returns:
            List of next visible TimeStepValues
        """
        values: List[TimeStepValue] = []
        
        # Recursively capture from children
        if self.children:
            for child in self.children:
                captured = child.captureNextVisible(
                    left_edge, right_edge, highest_unit, smallest_unit
                )
                values.extend(captured)

        # Check if current node should be included
        if (((left_edge <= self.value.start and self.value.start <= right_edge) or
             (left_edge <= self.value.end and self.value.end <= right_edge)) and
            self.value.unit() < smallest_unit):
            values.append(self.value)

        return values
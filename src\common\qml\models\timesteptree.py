from typing import List
from PySide6.QtCore import QObject
from src.common.qml.models.timestepnode import TimeStepNode, TimeStepValue
from src.utils.utils import Utils
import logging
logger = logging.getLogger(__name__)
class TimeStepTree(QObject):
    """
    A tree structure to manage time steps and their hierarchical relationships
    """
    
    def __init__(self, duration: int, offset: int = 0, 
                 highest_unit: int = 0, smallest_unit: int = 0):
        """
        Initialize TimeStepTree
        
        Args:
            duration: Total duration in milliseconds
            offset: Time offset in milliseconds
            highest_unit: Highest time unit to consider
            smallest_unit: Smallest time unit to consider
        """
        super().__init__()
        self._offset = offset
        self._duration = duration
        self._highest_unit = highest_unit
        self._smallest_unit = smallest_unit
        self._nodes: List[TimeStepNode] = []

        # Initialize nodes if highest_unit is specified
        if not self._nodes and highest_unit != 0:
            unit = highest_unit
            highest_count = int(duration // unit)
            rounded_offset = Utils.roundedBy(offset, unit, offset > 0)

            for i in range(highest_count + 2):
                self._nodes.append(
                    TimeStepNode(
                        start=rounded_offset + i * unit,
                        end=rounded_offset + (i + 1) * unit,
                        child_count=Utils.getSubItemCount(unit)
                    )
                )

    def depth(self) -> int:
        """
        Get the maximum depth of the tree
        
        Returns:
            Maximum depth of the tree
        """
        if not self._nodes:
            return 1

        # Calculate depth of tree nodes
        max_depth = max(node.depth() for node in self._nodes)
        return max_depth + 1

    def captureVisibleTimeStepValues(self) -> List[TimeStepValue]:
        """
        Capture all visible TimeStepValues within the current range
        
        Returns:
            List of visible TimeStepValues
        """
        left_edge = self._offset
        right_edge = self._duration + self._offset

        values: List[TimeStepValue] = []
        for node in self._nodes:
            captured = node.captureVisible(
                left_edge,
                right_edge,
                self._smallest_unit,
                node.value.end
            )
            values.extend(captured)

        return values

    def captureNextVisibleTimeStepValues(self) -> List[TimeStepValue]:
        """
        Capture all next visible TimeStepValues within the current range
        
        Returns:
            List of next visible TimeStepValues
        """
        left_edge = self._offset
        right_edge = self._duration + self._offset

        values: List[TimeStepValue] = []
        for node in self._nodes:
            captured = node.captureNextVisible(
                left_edge,
                right_edge,
                self._highest_unit,
                self._smallest_unit
            )
            values.extend(captured)

        return values

    def nodeCount(self) -> int:
        """
        Get the number of nodes in the tree
        
        Returns:
            Number of nodes
        """
        return len(self._nodes)

    def __del__(self):
        """Cleanup when the object is deleted"""
        self._nodes.clear()
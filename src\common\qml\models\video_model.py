from PySide6.QtQuick import QQuickPaintedItem, QQuickItem
from PySide6.QtCore import Property, Signal, Slot, Qt, QEvent, QTimer
from PySide6.QtWidgets import QMenu
from PySide6.QtGui import <PERSON><PERSON><PERSON><PERSON>, QPixmap,QCursor
from PySide6.QtQml import QJSValue
from typing import Callable, List
from src.styles.style import Style, Theme
from src.common.camera.video_capture import VideoCapture,video_capture_controller
from src.common.controller.controller_manager import controller_manager,Controller
from src.common.model.camera_model import camera_model_manager,CameraModel
from src.common.controller.main_controller import main_controller
from src.common.qml.models.timelinecontroller import TimeLineController,SpeedStatus
from src.common.qml.models.common_enum import CommonEnum
from src.common.model.record_model import record_model_manager, RecordData
from queue import Queue
import uuid
import logging
import threading
logger = logging.getLogger(__name__)

class VideoModel(QQuickPaintedItem):
    clicked = Signal()
    doubleClicked = Signal()
    rightClicked = Signal()
    frameCountChanged = Signal(int)
    cameraStateChanged = Signal(str)
    actuallyVisibleChanged = Signal(bool)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.uuid = uuid.uuid4()
        self._model = None
        self._cameraModel = None
        self._q_image = None
        self.video_capture = None
        self.controller = None
        self._position = -1
        self._is_playing = False
        self._isSelected = False
        self._frame_count = 0
        self._state = self.tr("No Data")
        self._is_actually_visible = False
        #VideoPlayback
        self.cameraScreen = main_controller.list_parent["CameraScreen"]
        self.timelinecontroller = None
        self.record_data = None
        self.record_url = None
        self.start_duration = 0
        self.end_duration = 0
        self.seek_time = 0
        self.input_queue = Queue()
        self.threads = self.start_threads(1,self.process_data)
        ##################
        self._state = self.tr("No Data")
        # Enable mouse events
        self.setAcceptedMouseButtons(Qt.LeftButton | Qt.RightButton)
        self.setAcceptHoverEvents(True)

    @Property(bool, notify=actuallyVisibleChanged)
    def actuallyVisible(self):
        """Property để QML có thể truy cập trạng thái visible thực sự"""
        return self._is_actually_visible

    @Slot(bool)
    def updateEffectiveVisibility(self, effectivelyVisible):
        """Slot để QML cập nhật trạng thái effective visibility"""
        if self._is_actually_visible != effectivelyVisible:
            self._is_actually_visible = effectivelyVisible
            self.actuallyVisibleChanged.emit(effectivelyVisible)



    @Slot("QVariant")
    def register_video_capture(self, model):
        if model is not None:
            if isinstance(model,CameraModel):
                self.controller:Controller = controller_manager.get_controller(server_ip=model.data.get("server_ip"))
                video_capture_controller.register_video_capture(self,model,CommonEnum.StreamType.MAIN_STREAM)
                self._cameraModel = model
                self.record_data:RecordData = record_model_manager.register_record_data(self)
            elif isinstance(model, QJSValue):
                model_data = model.toVariant()
                camera_id = model_data.get('id')
                stream_type = model_data.get("streamType", 0)  # Default to main stream if not specified
                self._cameraModel = camera_model_manager.get_camera_model(id=camera_id)
                self.record_data:RecordData = record_model_manager.register_record_data(self)
                if self._cameraModel is not None:
                    self.controller:Controller = controller_manager.get_controller(server_ip=self._cameraModel.get_property('server_ip'))
                    # Convert stream type to StreamCameraType
                    stream_type = CommonEnum.StreamType.MAIN_STREAM if stream_type == 0 else CommonEnum.StreamType.SUB_STREAM
                    logger.debug(f"[VideoModel] Using stream type: {stream_type}")
                    video_capture_controller.register_video_capture(self, self._cameraModel, stream_type)

            elif isinstance(model, str):
                self._cameraModel = camera_model_manager.get_camera_model(id=model)
                self.record_data:RecordData = record_model_manager.register_record_data(self)
                if self._cameraModel is not None:
                    self.controller:Controller = controller_manager.get_controller(server_ip=self._cameraModel.get_property('server_ip'))
                    video_capture_controller.register_video_capture(self,self._cameraModel,CommonEnum.StreamType.SUB_STREAM)
    @Slot()
    def unregister_video_capture(self):
        try:
            video_capture_controller.unregister_video_capture(self)
            record_model_manager.unregister_record_data(self)
        except Exception as e:
            logger.error(f"Error unregistering video capture: {str(e)}")
            logger.error(f"[VideoModel] Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"[VideoModel] Traceback: {traceback.format_exc()}")

    def paint(self, painter: QPainter):
        if self._q_image is None:
            return

        target_rect = self.boundingRect()
        scaled_image = self._q_image.scaled(
            int(target_rect.width()),
            int(target_rect.height()),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )

        x = (target_rect.width() - scaled_image.width()) / 2
        y = (target_rect.height() - scaled_image.height()) / 2
        painter.drawPixmap(x, y, scaled_image)

    @Slot(QPixmap)
    def updateFrame(self, frame):
        if frame is not None:
            pass
            # logger.debug(f'updateFrameupdateFrameupdateFrameupdateFrame')
            # height, width, channels = frame.shape
            # bytes_per_line = channels * width
            self._q_image = frame
            self._frame_count += 1
            self.frameCountChanged.emit(self._frame_count)
            self.update()

    def mousePressEvent(self, event):
        if event.button() == Qt.RightButton:
            self.rightClicked.emit()
        else:
            self.clicked.emit()

    def mouseDoubleClickEvent(self, event):
        logger.debug("[VideoModel] Double click event received")
        self.doubleClicked.emit()

    @Property(int)
    def position(self):
        return self._position

    @position.setter
    def position(self, pos):
        logger.debug(f"[VideoModel] Position changed from {self._position} to {pos}")
        self._position = pos

    @Property(str,notify=cameraStateChanged)
    def state(self):
        return self._state

    @state.setter
    def state(self, value:str):
        if self._state != value:
            logger.debug(f"[VideoModel] State changed from {self._state} to {value}")
            self._state = value
            self.cameraStateChanged.emit(value)


    # @Property(bool,notify=viewModeChanged)
    # def viewMode(self):
    #     return self._viewMode
    # @viewMode.setter
    # def viewMode(self, value: bool):
    #     if self._viewMode != value:
    #         self._viewMode = value
    #         self.viewModeChanged.emit()

    @Property("QVariant")
    def model(self):
        return self._model

    @model.setter
    def model(self, value):
        if self._model != value:
            logger.debug("[VideoModel] Model changed")
            self._model = value

    @Property(bool)
    def isPlaying(self):
        return self._is_playing

    @isPlaying.setter
    def isPlaying(self, playing):
        if self._is_playing != playing:
            logger.info(f"[VideoModel] Playing state changed from {self._is_playing} to {playing}")
            self._is_playing = playing
            if not playing:
                self._q_image = None
                self._frame_count = 0
                self.update()

    @Property(int, notify=frameCountChanged)
    def frameCount(self):
        return self._frame_count

    def process_video_capture(self,video_capture:VideoCapture):
        try:
            self.video_capture = video_capture
            self.video_capture.connect_status = True
            self.video_capture.register_signal(self)

            # Use actual component size instead of fixed size
            width = int(self.width())
            height = int(self.height())
            if width <= 0 or height <= 0:
                # Use default size if component size is invalid
                width = 1920
                height = 1080

            logger.debug(f"[VideoModel] Setting video size to {width}x{height}")
            self.video_capture.update_resize(width=width, height=height, uuid=self.uuid)
            logger.debug(f'[VideoModel] Video capture started with stream type: {self.video_capture.stream_type}, size={width}x{height}')

            if not self.video_capture.isRunning():
                logger.debug('[VideoModel] Starting video capture thread')
                if self.video_capture.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                    self.video_capture.start_thread()

            def update_stream_url(reponse, streamIndex):
                if self.video_capture is not None:
                    # Giải thích logic:
                    # - Nếu streamIndex trùng với index của item và state là CONNECTED thì lấy url của item đó
                    # - Nếu không thì lấy url của item có state là CONNECTED
                    data = reponse.json()
                    logger.debug(f'update_stream_url: data = {data}')
                    # Lọc chỉ các luồng có trạng thái CONNECTED
                    connected_streams = [item for item in data if item["state"] == "CONNECTED" or item["state"] is None]
                    
                    if connected_streams:
                        # Đầu tiên tìm luồng phù hợp với chỉ số được yêu cầu
                        target_stream = next((stream for stream in connected_streams if stream["index"] == streamIndex), None)
                        
                        # Nếu không tìm thấy luồng phù hợp với chỉ số, sử dụng luồng kết nối đầu tiên có sẵn
                        if target_stream is None:
                            target_stream = connected_streams[0]
                            
                        self.video_capture.on_stream_link_changed(target_stream["url"])
                        logger.debug(f'update_stream_url = {target_stream["url"]}')
                    else:
                        logger.debug(f'update_stream_url: Không có url nào được lấy')

            # Use appropriate stream index based on stream type
            if self.video_capture.stream_type == CommonEnum.StreamType.MAIN_STREAM or self.video_capture.stream_type == CommonEnum.StreamType.SUB_STREAM:
                stream_index = 0 if self.video_capture.stream_type == CommonEnum.StreamType.MAIN_STREAM else 1
                logger.debug(f"[VideoModel] Getting stream URL with index: {stream_index}")
                self.controller.get_stream_url_thread(cameraId=self.video_capture.camera_model.id,streamIndex=stream_index,callback=update_stream_url)
            elif self.video_capture.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                self.video_capture.load_media(self.record_url, seek_time=self.seek_time,start_duration=self.start_duration,end_duration=self.end_duration)
                self.timelinecontroller.register_video_capture(video_capture=self.video_capture)
                # widget.video_capture.set_position(duration_time_need_seek)
                self.video_capture.play_video()

        except Exception as e:
            logger.error(f"[VideoModel] Error processing video capture: {str(e)}")
            logger.error(f"[VideoModel] Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"[VideoModel] Traceback: {traceback.format_exc()}")

    def share_frame_signal(self, data):
        logger.debug(f'share_frame_signal = {data}')
        try:
            grab, _, frame = data
            if grab and frame is not None:
                try:
                    self.updateFrame(frame)
                except RuntimeError:
                    logger.debug("[VideoModel] RuntimeError in share_frame_signal")
        except Exception as e:
            logger.error(f"[VideoModel] Error in share_frame_signal: {str(e)}")
            logger.error(f"[VideoModel] Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"[VideoModel] Traceback: {traceback.format_exc()}")

    def camera_state_signal(self, camera_state):
        try:
            if self.state != camera_state:
                self.state = camera_state
                logger.debug(f"[VideoModel] Camera state signal received: {camera_state}")
        except Exception as e:
            logger.error(f"[VideoModel] Error in camera_state_signal: {str(e)}")
            logger.error(f"[VideoModel] Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"[VideoModel] Traceback: {traceback.format_exc()}")

    @Slot(int, int)
    def updateVideoSize(self, width, height):
        try:
            if hasattr(self, 'video_capture') and self.video_capture is not None:
                logger.debug(f'[VideoModel] Updating video size to {width}x{height}')
                self.video_capture.update_resize(width=width, height=height, uuid=self.uuid)
        except Exception as e:
            logger.error(f"[VideoModel] Error in updateVideoSize: {str(e)}")
            logger.error(f"[VideoModel] Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"[VideoModel] Traceback: {traceback.format_exc()}")


    # VideoPlayback
    @Property(bool)
    def isSelected(self):
        return self._isSelected

    @isSelected.setter
    def isSelected(self, value:bool):
        if self._isSelected != value:
            self._isSelected = value
            self.updateStatus()

    def updateStatus(self):
        if self._isSelected:
            if self.timelinecontroller is None:
                self.create_timelinecontroller()
                self.timelinecontroller.openCalendarDialog.connect(self.cameraScreen.openCalendarDialog)
                self.cameraScreen.timeLineManager.timeLineController = self.timelinecontroller
                def callback(data):
                    response,dateFrom,dateTo = data
                    if response is not None:
                        if response.status_code == 200:
                            data = response.json()
                            # data_list = []
                            # for index, item in enumerate(data):
                            #     video = RecordModel.from_dict(item)
                            #     data_list.append(video)
                            record_model_manager.add_records(record_list = data,dateFrom = dateFrom,dateTo = dateTo)
                            # return data
                    else:
                        record_model_manager.add_records(record_list = [],dateFrom = dateFrom,dateTo = dateTo)

                    camera_id = self._cameraModel.get_property("id")
                    camera_model = camera_model_manager.get_camera_model(id=camera_id)
                    self.timelinecontroller.setCameraName(camera_model.get_property("name"))
                    if self.record_data is not None:
                        self.record_data.recordDataChanged.connect(self.recordDataChanged)

                    if self.record_data is None:
                        self.timelinecontroller.setIsTimeLine(False)
                        return
                    camera_data = {
                        record.data.id: record
                        for record in self.record_data.data
                        if record.data.cameraId == camera_id
                    }
                    if camera_data:
                        self.timelinecontroller.setIsTimeLine(False)
                        self.timelinecontroller.setIsTimeLine(True)
                    else:
                        self.timelinecontroller.setIsTimeLine(False)
                    if camera_data:
                        self.timelinecontroller.initData(self.record_data.start_duration, self.record_data.end_duration)
                        self.timelinecontroller.updateRecordDuration(self.record_data)

                self.controller.get_videos(parent=self.cameraScreen, cameraIds=self._cameraModel.get_property("id"),callback=callback)
            else:
                self.cameraScreen.timeLineManager.timeLineController = self.timelinecontroller

        else:
            self.cameraScreen.default_timelinecontroller()

    def create_timelinecontroller(self):
        self.timelinecontroller = TimeLineController(parent=self.cameraScreen)
        self.timelinecontroller.setTheme("dark" if main_controller.current_theme == Theme.DARK else "light")
        self.timelinecontroller.positionClicked.connect(self.positionClicked)
        self.timelinecontroller.isPlayChanged.connect(self.isPlayChanged)
        self.timelinecontroller.isLiveChanged.connect(self.isLiveChanged)
        self.timelinecontroller.isNextChunkClicked.connect(self.isNextChunkClicked)
        self.timelinecontroller.isPreviousChunkClicked.connect(self.isPreviousChunkClicked)
        self.timelinecontroller.speedStatusChanged.connect(self.speedStatusChanged)
        self.timelinecontroller.nextFrameChanged.connect(self.nextFrameChanged)
        self.timelinecontroller.hoverPositionChanged.connect(self.hoverPositionChanged)
        self.timelinecontroller.showMenu.connect(self.show_menu)
        return self.timelinecontroller
    
    def recordDataChanged(self):
        if self.timelinecontroller is not None:
            if not self.timelinecontroller.isTimeLine:
                self.timelinecontroller.setIsTimeLine(True)
                self.timelinecontroller.initData(self.record_data.start_duration, self.record_data.end_duration)
            self.timelinecontroller.updateRecordDuration(self.record_data)

    def positionClicked(self, position):
        """Handle timeline position click with time sync"""
        position = int(position)
        self.input_queue.put(position)

    def process_data(self)-> None:
        while True:
            position = self.input_queue.get()
            if position is None:
                logger.debug(f'process_data')
                break
            self.previous_time = None
            self.current_time = None
            self.target_time = None
            # widget = grid_item_selected.data['widget']
            # if widget is not None and isinstance(widget, CameraWidget):
            if self.record_data is not None and self.video_capture is not None:
                try:
                    data = self.record_data.get_record(position, self._cameraModel.get_property("id"))
                    if data is not None:
                        record, duration_time_need_seek, duration_to_move = data
                        source = self.video_capture.get_current_url()
                        self.record_url = record.data.url
                        self.seek_time = duration_time_need_seek
                        # start = datetime.datetime.fromtimestamp(record.data.start_duration / 1000.0)
                        # seek = datetime.datetime.fromtimestamp((record.data.start_duration+self.seek_time) / 1000.0)
                        # logger.debug(f'start time = {start.isoformat()} seek_time = {seek.isoformat()}')
                        self.start_duration = record.data.start_duration
                        self.end_duration = record.data.end_duration
                        if duration_to_move != -1:
                            self.timelinecontroller.shift_for_duration(duration_to_move)

                        if self.video_capture.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                            # self.switch_video_capture(stream_type = CommonEnum.StreamType.VIDEO_STREAM)
                            self.switchStreamType(streamType = CommonEnum.StreamType.VIDEO_STREAM)
                            self.timelinecontroller.isNextChunk = True
                            self.timelinecontroller.isNextFrame = True
                        else:
                            if (record.data.url is not None and record.data.url != source):
                                self.timelinecontroller.isNextChunk = True
                                self.timelinecontroller.isNextFrame = True
                                self.video_capture.stop_video()
                                self.video_capture.load_media(record.data.url, seek_time=duration_time_need_seek,start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                                # widget.record_capture.set_position(duration_time_need_seek)
                                # end = datetime.datetime.fromtimestamp((record.data.start_duration+self.record_capture.get_length()) / 1000.0)
                                # logger.debug(f'end time = {end.isoformat()}')
                                self.video_capture.play_video()
                            else:
                                if self.video_capture.get_length() != 0 and self.video_capture.is_playing():
                                    # end = datetime.datetime.fromtimestamp((video.data.start_duration+self.video_capture.get_length()) / 1000.0)
                                    # logger.debug(f'end time = {end.isoformat()}')
                                    position_seek = float(duration_time_need_seek)/float(self.video_capture.get_length())
                                    self.video_capture.set_position(position_seek)
                                elif self.video_capture.get_length() != 0 and not self.video_capture.is_playing():
                                    self.video_capture.play_video()
                                    # end = datetime.datetime.fromtimestamp((video.data.start_duration+self.video_capture.get_length()) / 1000.0)
                                    # logger.debug(f'end time = {end.isoformat()}')
                                    position_seek = float(duration_time_need_seek)/float(self.video_capture.get_length())
                                    self.video_capture.set_position(position_seek)

                        self.timelinecontroller.isLive = False
                        self.timelinecontroller.positionBubble = True
                except ValueError as e:
                    logger.error(f"Error parsing position timestamp: {e}")
                except Exception as e:
                    logger.error(f"Error handling position click: {e}")
            self.input_queue.task_done()

    def isPlayChanged(self):
        logger.debug(f'isPlayChanged = {self.timelinecontroller.isPlay}')
        if self.timelinecontroller.isPlay:
            if self.video_capture.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                self.video_capture.start_video()
            else:
                self.video_capture.play_live()
        else:
            if self.video_capture.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                self.video_capture.pause_video()
            else:
                self.video_capture.pause_live()


    def isLiveChanged(self):
        if self.timelinecontroller.isLive:
            self.timelinecontroller.positionBubble = False
            self.switchStreamType(streamType=CommonEnum.StreamType.SUB_STREAM)
        else:
            pass
            # logger.debug(f'isLiveChanged = {self.timelinecontroller.isLive}')

    def isNextChunkClicked(self):
        self.next_chunk_signal('')

    def isPreviousChunkClicked(self):
        self.previous_chunk_signal('')

    def speedStatusChanged(self,data):
        logger.debug(f'speedStatusChanged = {data}')
        speed = 0
        if data == 1:
            if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
                speed = SpeedStatus.SpeedEnum.Up1X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
                speed = SpeedStatus.SpeedEnum.Up2X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
                speed = SpeedStatus.SpeedEnum.Up4X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
                speed = SpeedStatus.SpeedEnum.Up8X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
                speed = SpeedStatus.SpeedEnum.Up8X
        elif data == -1:
            if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
                # speed = SpeedStatus.SpeedEnum.Pause
                # self.timelinecontroller.isPlay = False
                return
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
                speed = SpeedStatus.SpeedEnum.Up1X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
                speed = SpeedStatus.SpeedEnum.Up2X
            elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
                speed = SpeedStatus.SpeedEnum.Up4X
            else:
                speed = SpeedStatus.SpeedEnum.Pause
                self.timelinecontroller.isPlay = False
                return
        self.timelinecontroller.isPlay = True
        if self.video_capture.stream_type == CommonEnum.StreamType.VIDEO_STREAM and self.video_capture.is_playing():
            try:
                self.video_capture.set_speed(speed)
                self.timelinecontroller.nextFrame = speed
            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def nextFrameChanged(self):

        speed = 0
        if self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Pause:
            speed = SpeedStatus.SpeedEnum.Up1X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up1X:
            speed = SpeedStatus.SpeedEnum.Up1X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up2X:
            speed = SpeedStatus.SpeedEnum.Up2X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up4X:
            speed = SpeedStatus.SpeedEnum.Up4X
        elif self.timelinecontroller.nextFrame == SpeedStatus.SpeedEnum.Up8X:
            speed = SpeedStatus.SpeedEnum.Up8X

        if self.video_capture.stream_type == CommonEnum.StreamType.VIDEO_STREAM and self.video_capture.is_playing():
            try:
                self.video_capture.set_speed(speed)
                # self.timelinecontroller.nextFrame = speed
            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def hoverPositionChanged(self, position):
        pass

    def show_menu(self, position):
        logger.debug(f'showMenu = {position, type(position)}')
        def handle_clear_selection():
            logger.debug(f'handle_clear_selection')
            self.timelinecontroller.clearSelectionChanged.emit()
        def handle_zoom_to_selection():
            logger.debug(f'handle_zoom_to_selection')
            self.timelinecontroller.zoomToSelectionChanged.emit()
        def handle_export_video():
            logger.debug(f'handle_export_video')
        menu = QMenu()
        # menu.setStyleSheet(Style.StyleSheet.context_menu)
        menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        clear_selection = menu.addAction(self.tr("Clear Selection"))
        clear_selection.triggered.connect(handle_clear_selection)
        zoom_to_selection = menu.addAction(self.tr('Zoom to Selection'))
        zoom_to_selection.triggered.connect(handle_zoom_to_selection)
        export_video = menu.addAction(self.tr('Export video'))
        export_video.setDisabled(True)
        export_video.triggered.connect(handle_export_video)
        current_mouse = QCursor.pos()
        logger.debug(f'current_mouse = {current_mouse}')
        position_of_mouse = self.mapFromGlobal(current_mouse)
        logger.debug(f'position_of_mouse = {position_of_mouse}')
        menu.exec_(self.mapToGlobal(position_of_mouse))
        self.timelinecontroller.closeMenu()

    def next_chunk_signal(self, data):
        logger.debug(f'next_chunk_signal = {self.record_data}')
        if self.record_data is not None and self.video_capture is not None:
            try:
                data = self.record_data.get_next_record(self.end_duration, self._cameraModel.get_property("id"))
                if data is not None:
                    record = data
                    # source = widget.record_capture.get_current_url()
                    self.record_url = record.data.url
                    self.seek_time = None
                    self.start_duration = record.data.start_duration
                    self.end_duration = record.data.end_duration
                    old_duration = self.video_capture.current_duration
                    self.video_capture.stop_video()
                    self.video_capture.load_media(record.data.url, start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                    self.video_capture.play_video()
                    # self.timelinecontroller.scrollbarPositionChanged.emit()
                    self.timelinecontroller.shift_for_duration(self.start_duration - old_duration)
                    self.timelinecontroller.scrollbarPositionChanged.emit()
                    # self.timelinecontroller.isLive = False
                    # self.timelinecontroller.positionBubble = True
                else:
                    pass
                    # vào chế độ live
                    logger.debug(f'next_chunk_signal1')
                    self.timelinecontroller.isLive = True
                    self.timelinecontroller.positionBubble = False
                    self.timelinecontroller.isNextChunk = False
                    self.timelinecontroller.isNextFrame = False
                    self.timelinecontroller.nextFrame = SpeedStatus.SpeedEnum.Up1X

            except ValueError as e:
                logger.error(f"Error parsing position timestamp: {e}")
            except Exception as e:
                logger.error(f"Error handling position click: {e}")

    def previous_chunk_signal(self, data):
        logger.debug(f'previous_chunk_signal = {self.record_data}')
        if self.record_data is not None and self.video_capture is not None:
            if self.video_capture.stream_type == CommonEnum.StreamType.VIDEO_STREAM:
                try:
                    data = self.record_data.get_previous_record(self.start_duration, self._cameraModel.get_property("id"))
                    logger.debug(f'previous_chunk_signal = {data}')
                    if data is not None:
                        record = data
                        # source = widget.record_capture.get_current_url()
                        self.record_url = record.data.url
                        self.seek_time = None
                        self.start_duration = record.data.start_duration
                        self.end_duration = record.data.end_duration
                        self.video_capture.stop_video()
                        self.video_capture.load_media(record.data.url, start_duration=record.data.start_duration,end_duration=record.data.end_duration)
                        self.video_capture.play_video()
                        # self.timelinecontroller.isLive = False
                        # self.timelinecontroller.positionBubble = True
                    # else:
                    #     pass
                    #     # vào chế độ live
                    #     logger.debug(f'next_chunk_signal1')
                    #     self.timelinecontroller.isLive = True
                    #     self.timelinecontroller.positionBubble = False
                    #     self.timelinecontroller.isNextChunk = False
                    #     self.timelinecontroller.isNextFrame = False
                except ValueError as e:
                    logger.error(f"Error parsing position timestamp: {e}")
                except Exception as e:
                    logger.error(f"Error handling position click: {e}")

    def switchStreamType(self,streamType = CommonEnum.StreamType.MAIN_STREAM):
        if streamType != self.video_capture.stream_type:
            video_capture_controller.unregister_video_capture(self)
            self.streamType = streamType
            # self.create_video_capture()
            video_capture_controller.register_video_capture(self,self._cameraModel,streamType)
            if not self.video_capture.isRunning():
                logger.debug(f'video_capture ==========')
                if self.video_capture.stream_type != CommonEnum.StreamType.VIDEO_STREAM:
                    self.video_capture.start_thread()

    def start_threads(self,number: int, target: Callable, *args) -> List[threading.Thread]:
        threads = []
        for _ in range(number):
            thread = threading.Thread(target=target, args=args)
            thread.daemon = True
            threads.append(thread)
            thread.start()
        return threads

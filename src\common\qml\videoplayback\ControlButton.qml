// Copyright (C) 2023 The Qt Company Ltd.
// SPDX-License-Identifier: LicenseRef-Qt-Commercial OR BSD-3-Clause

import QtQuick
import QtQuick.Controls

RoundButton {
    id: button
    implicitWidth: 24
    implicitHeight: 24
    icon.width: 24
    icon.height: 24
    radius: buttonRadius

    property bool dimmable: false
    property bool dimmed: false
    readonly property int fontSize: 12
    readonly property int buttonRadius: 4
    property bool buttonType: false

    function getBackgroundColor(value) {
        if (value){
            if (button.dimmable && button.dimmed)
                return timeline_background_on
            if (button.pressed)
                return timeline_background_on
            return timeline_background_on
        } else {
            if (button.dimmable && button.dimmed)
                return timeline_background_off
            if (button.pressed)
                return timeline_background_on
            return timeline_background_off
        }
    }

    function getBorderColor() {
        if (button.dimmable && button.dimmed)
            return timeline_background_off
        if (button.pressed || button.hovered)
            return timeline_background_on
        return timeline_background_off
    }

    function getTextColor() {
        if (button.dimmable && button.dimmed)
            return Qt.darker(timeline_text_off)
        if (button.pressed)
            return timeline_text_off
        if (button.hovered)
            return timeline_text_off
        return timeline_text_off
    }

    background: Rectangle {
        radius: button.buttonRadius
        color: getBackgroundColor(buttonType)
        border.color: getBorderColor()
    }

    contentItem: Text {
        text: button.text
        font.pixelSize: button.fontSize
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter
        color: getTextColor()
        Behavior on color {
            ColorAnimation {
                duration: 120
                easing.type: Easing.OutElastic
            }
        }
    }
    readonly property color timeline_background_off: (function(){
        // console.log("timeline_background_off")
        return timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_background_off") : "blue"
    })()
    readonly property color timeline_background_on: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_background_on") : "blue"
    readonly property color timeline_text_off: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_text_off") : "blue"
    readonly property color timeline_text_on: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("timeline_text_on") : "blue"
}

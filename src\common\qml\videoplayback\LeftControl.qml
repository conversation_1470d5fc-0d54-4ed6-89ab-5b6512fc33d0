import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 2.15
import models 1.0
Rectangle {
    // anchors.fill: parent
    width: parent.width
    height: parent.height
    color: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("main_background") : "#FAFAFA"
    readonly property string previous_chunk: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_image_theme_by_key("previous_chunk") : ""
    readonly property string previous_frame: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_image_theme_by_key("previous_frame") : ""
    readonly property string new_pause: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_image_theme_by_key("new_pause") : ""
    readonly property string new_play: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_image_theme_by_key("new_play") : ""
    readonly property string next_chunk_icon: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_image_theme_by_key("next_chunk") : ""
    readonly property string next_frame: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_image_theme_by_key("next_frame") : ""

    ColumnLayout {
        // anchors.centerIn: parent.centerIn
        anchors.centerIn: parent
        spacing: 5  // Khoảng cách giữa các layout
        Text
        {
            id: clockLabel
            height: 28
            color: timeLineManager.timeLineController.theme === "dark" ? "#5B5B9F" : "#5B5B9F"
            font.pixelSize: 18
            font.weight: Font.DemiBold
            // verticalAlignment: Text.AlignVCenter
            Layout.alignment: Qt.AlignHCenter 

            Timer
            {
                interval: 1000
                repeat: true
                triggeredOnStart: true
                running: timeLineManager.timeLineController.showClock

                onTriggered:
                {
                    clockLabel.text = new Date().toLocaleTimeString(Qt.locale(), "h:mm:ss A");
                }
            }
        }
        RowLayout {
            id: rowTop
            Layout.preferredHeight:30
            Layout.preferredWidth:190
            Layout.leftMargin: 5
            Layout.rightMargin: 5
            spacing: 5  // Khoảng cách giữa các nút

            ControlButton {
                text: "1X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up1X ? true : false
                onClicked:  {
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up1X
                }
            }

            ControlButton {
                text: "2X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up2X ? true : false
                onClicked:  {
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up2X
                }
            }

            ControlButton {
                text: "4X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up4X ? true : false
                onClicked:  {
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up4X
                }
            }

            ControlButton {
                text: "8X"
                Layout.fillWidth: true
                Layout.fillHeight: true
                buttonType: timeLineManager.timeLineController.nextFrame === SpeedStatus.Up8X ? true : false
                onClicked:  {
                    timeLineManager.timeLineController.nextFrame = SpeedStatus.Up8X
                }
            }
        }
        RowLayout {
            // anchors.top: rowTop.bottom
            Layout.preferredHeight:30
            Layout.preferredWidth:190
            Layout.leftMargin: 5
            Layout.rightMargin: 5
            spacing: 5  // Khoảng cách giữa các nút
            // Layout.alignment: Qt.AlignHCenter
            IconButton {
                // height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: previous_chunk
                isDisabled: timeLineManager.timeLineController.isPreviousChunk === true ? false : true
                onClicked:  {
                    timeLineManager.timeLineController.isPreviousChunkClicked()
                }

            }
            // IconButton {
            //     // height: 30
            //     Layout.fillWidth: true
            //     Layout.fillHeight: true
            //     icon.source: previous_frame
            //     isDisabled: timeLineManager.timeLineController.isLive === true ? true : false
            //     onClicked:  {
            //         timeLineManager.timeLineController.speedStatusChanged(-1)
            //     }
            // }
            IconButton {
                // height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: timeLineManager.timeLineController.isPlay ? new_pause : new_play
                onClicked:  {
                    timeLineManager.timeLineController.isPlay = !timeLineManager.timeLineController.isPlay
                }
            }
            // IconButton {
            //     // height: 30
            //     Layout.fillWidth: true
            //     Layout.fillHeight: true
            //     icon.source: next_frame
            //     isDisabled: timeLineManager.timeLineController.isLive === true ? true : false
            //     onClicked:  {
            //         timeLineManager.timeLineController.speedStatusChanged(1)
                    
            //     }
            // }
            IconButton {
                id: next_chunk
                // height: 30
                Layout.fillWidth: true
                Layout.fillHeight: true
                icon.source: next_chunk_icon
                isDisabled: timeLineManager.timeLineController.isNextChunk === true ? false : true
                onClicked:  {
                    timeLineManager.timeLineController.isNextChunkClicked()
                }
            }
        }

    }
}
import QtQuick 2.15
import models 1.0

Rectangle{
    id: rule

    required property var model
    property bool enabledAnimation: true
    height: 60
    width: parent.width

    color: "transparent"

    Rectangle {
        id: ruleHeader
        width: parent.width
        height: 20
        anchors{
            top: parent.top
            left: parent.left
        }
        color: "#404040"
    }


    Rectangle {
        anchors {
            top: rule.top
        }

        Repeater{
            id: repeater

            model: rule.model
            TimeStepItem2{
                required property var modelData
                model: modelData
            }
        }
    }
}
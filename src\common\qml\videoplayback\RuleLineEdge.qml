import QtQuick 2.15
import models 1.0

Column {
    id: root
    property int lineType: TimeStep2.UNDEFINED
    property string text: ""
    property bool enabledAnimation: true
    Rectangle{
        id: header
        width: parent.width
        height: 20
        color: "transparent"
    }

    Rectangle {
        id: line
        width: 1
        height: getHeightFromType(lineType)
        // height: (function() {
        //     // console.log("Value of timeLineManager.timeLineController.getHoverPosition(hoverCursor.x) =", typeof(lineType));
        //     return getHeightFromType(lineType);
        // })()
        color: "#b8b8b8"
        opacity: 1-parent.lineType*0.25
        Behavior on height {
            enabled: root.enabledAnimation
            PropertyAnimation{
                duration: 200
                easing.type: Easing.InOutQuad
            }
        }
    }

    Text {
        width: 1
        height: 15
        // text: root.text
        text: (function() {
            // console.log("Value of timeLineManager.timeLineController.getHoverPosition(hoverCursor.x) =", root.text);
            return root.text;
        })()

        color: (root.lineType == 3 || root.lineType == 4)? "transparent":"#b8b8b8"
        horizontalAlignment: Text.AlignHCenter
        opacity: 1-parent.lineType*0.25
        font: timeLineManager.timeLineController.dateTimeFont

        Behavior on opacity {
            enabled: root.enabledAnimation
            PropertyAnimation{
                duration: 200
                easing.type: Easing.InOutQuad
            }
        }
    }

    function getHeightFromType(type) : real {
        switch(type){
        case 0:
            return 15
        case 1:
            return 11
        case 2:
            return 7
        case 3:
            return 3
        default:
            return 0
        }
    }
}

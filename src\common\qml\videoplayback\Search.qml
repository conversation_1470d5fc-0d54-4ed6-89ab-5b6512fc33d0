import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

Rectangle {
    width: parent.width
    height: 40
    radius: 6
    property color normal_text_color: selectCamerasController ? selectCamerasController.get_color_theme_by_key("dialog_text") : "white"
    color: "white"
    border.width: 1
    signal searchTextChanged(var text)
    Row {
        anchors.fill: parent
        spacing: 8
        anchors.margins: 8

        Label {
            text: "\u{1F50D}"  // 
            font.pixelSize: 18
            verticalAlignment: Text.AlignVCenter
            width: 20
            height: 20
            color: normal_text_color
        }

        TextField {
            id: searchField
            placeholderText: qsTr("Search...")
            placeholderTextColor: normal_text_color
            font.pixelSize: 16
            background: null
            anchors.verticalCenter: parent.verticalCenter
            onTextChanged: {
                searchTextChanged(text)
            }
            Layout.fillWidth: true
            color: normal_text_color
        }
    }
}
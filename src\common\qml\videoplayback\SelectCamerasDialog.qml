import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import '../map'
Rectangle {
    id: root
    height: 600
    // focus: true
    property color main_background: selectCamerasController ? selectCamerasController.get_color_theme_by_key("dialog_body_background") : "white"
    readonly property string search_not_found: selectCamerasController ? selectCamerasController.get_image_theme_by_key("search_not_found_qml") : ""
    property color normal_text_color: selectCamerasController ? selectCamerasController.get_color_theme_by_key("dialog_text") : "white"
    property color border_color: selectCamerasController ? selectCamerasController.get_color_theme_by_key("common_border") : "blue"
    property color treeview_on_hover: selectCamerasController ? selectCamerasController.get_color_theme_by_key("treeview_on_hover") : "blue"
    property string cameraId: ""
    property bool isPlayingStream: false
    property string tooltipText: ""
    color: main_background
    Component.onDestruction: {
        isPlayingStream = false

    }
    Timer {
        id: hoverTimer
        interval: 300
        repeat: false
        onTriggered: {
            // Chỉ tắt preview nếu không trong fullScreen và khi cả FOV lẫn preview đều không được hover
            videoFrame.isPlaying = false
            videoFrame.model = cameraId
            videoFrame.isPlaying = isPlayingStream
        }
    }
    Search {
        id: search
        border.color: border_color
        color: main_background
        onSearchTextChanged: (text) => {
            
            console.log("text: ",text)
            selectCamerasController.searchText(text)
        }
    }
    Item {
        id: temp
        height: 10 // Khoảng cách 10 pixel
        anchors {
            top: search.bottom
            left: parent.left
            right: parent.right
        }
    }
    RowLayout {
        anchors {
            top: temp.bottom
            left: parent.left
            right: parent.right
            bottom: parent.bottom
        }
        spacing: 10

        Rectangle {
            id: rectangle
            radius: 6
            color: main_background
            border.color: border_color
            Layout.fillWidth: true
            Layout.fillHeight: true
            Image {
                id: idSearchNotFound
                visible: selectCamerasController.searchNotFound
                anchors.centerIn: parent
                width: 48
                height: 48
                source: search_not_found
            }
            Text {
                visible: selectCamerasController.searchNotFound
                anchors.top: idSearchNotFound.bottom
                anchors.topMargin: 8 // khoảng cách giữa icon và text
                anchors.horizontalCenter: parent.horizontalCenter
                text: qsTr("No search results")
                font.pixelSize: 14
                color: normal_text_color
            }
            TreeView {
                id: treeView
                visible: !selectCamerasController.searchNotFound
                anchors.fill: parent
                clip: true
                model: selectCamerasController.treeModel
                delegate: Item {
                    id: item
                    implicitWidth: rectangle.width
                    implicitHeight: 30
                    readonly property real indentation: 20
                    property bool hovered: false  // trạng thái hover
                    required property TreeView treeView
                    required property bool isTreeNode
                    required property bool expanded
                    required property bool hasChildren
                    required property int depth
                    required property int row
                    required property int column
                    required property bool current
                    Rectangle {
                        anchors.fill: parent
                        color: current ? "#1CD1A1" : (hovered ? treeview_on_hover : "transparent")  // hover = xám
                        MouseArea {
                            anchors.fill: parent
                            hoverEnabled: true
                            onEntered: {
                                tooltip.visible = true  // Hiện tooltip khi hover
                                tooltipText = model.display 
                                item.hovered = true
                            }
                            onExited: {
                                tooltip.visible = false
                                item.hovered = false
                            }
                            onClicked: {
                                if (isTreeNode && hasChildren) {
                                    treeView.toggleExpanded(row);
                                }
                                if (model.itemType === "Camera") {
                                    hoverTimer.start()
                                    cameraId = model.camera_id
                                    isPlayingStream = true
                                }
                            }
                        }
                        ToolTip{
                            id: tooltip
                            text: tooltipText
                            delay: 300
                            timeout: 2000
                            contentItem: Text {
                                text: tooltipText
                                color: normal_text_color
                                wrapMode: Text.Wrap
                                font.pixelSize: 12
                                width: parent.width
                            }

                            background: Rectangle {
                                color: treeview_on_hover
                                radius: 5
                            }
                        }  
                        RowLayout {
                            anchors.fill: parent
                            anchors.leftMargin: depth * 20
                            anchors.rightMargin: 10
                            spacing: 10
                            Image {
                                visible: isTreeNode && hasChildren
                                source: expanded ? "qrc:/src/assets/treeview_and_menu_treeview/down_light.svg":"qrc:/src/assets/treeview_and_menu_treeview/right_light.svg"
                            }
                            Image {
                                source: model.iconRecord
                                visible: model.itemType === "Camera"
                            }
                            Image {
                                source: model.iconCamera
                                visible: model.itemType === "Camera"
                            }

                            Label {
                                text: model.display
                                font.pixelSize: 14
                                Layout.alignment: Qt.AlignVCenter
                                Layout.fillWidth: true
                                elide: Text.ElideRight
                                color: normal_text_color
                            }

                            CheckBox {
                                id: check
                                checked: model.checked
                                visible: model.itemType === "Camera" ? true : false
                                onToggled: model.checked = checked
                                Layout.alignment: Qt.AlignVCenter | Qt.AlignRight
                            }
                        }
                    }
                    Component.onCompleted: {
                        if (hasChildren && !expanded) {
                            treeView.expand(row)
                        }
                    }
                }
                onModelChanged: {
                    console.log("Model changed!")
                    expandAll()
                }
                // function expandAll() {
                //     // Đợi model load hoàn tất (nếu async) rồi mới expand
                //     Qt.callLater(() => {
                //         for (let i = 0; i < treeView.rows; ++i) {
                //             treeView.expand(i)
                //         }
                //     })
                // }
                // function expandAll() {
                //     const rowCount = treeView.model.rowCount()
                //     for (let i = 0; i < rowCount; ++i) {
                //         const index = treeView.model.index(i, 0)
                //         expandAllRows(index)
                //     }
                // }
                // function expandAllRows(index) {
                //     if (!index || index.column === undefined) return;

                //     treeView.expand(index);
                //     const count = treeView.model.rowCount(index);
                //     console.log("Expanding index:", index, "rowCount:", count);

                //     for (let i = 0; i < count; ++i) {
                //         const childIndex = treeView.model.index(i, 0, index);
                //         expandAllRows(childIndex); // Đệ quy
                //     }
                // }
                // Component.onCompleted: {
                //     expandAll()
                //     // treeView.forceActiveFocus() 
                // }
            }

        }
        Rectangle {
            color: "white"
            radius: 6
            border.color: "#CDCDCD"
            Layout.fillWidth: true
            height:200
            CustomVideoOutput {
                anchors.fill: parent
                id: videoFrame
                isPlaying: isPlayingStream
                model: cameraId
            }
        }
    }
    Component.onCompleted: {
        // treeView.forceActiveFocus() 
        Qt.callLater(() => {
            root.forceActiveFocus()
        })
    }
}
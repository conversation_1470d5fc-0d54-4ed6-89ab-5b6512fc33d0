import QtQuick
import QtQuick.Controls
import QtQuick.Layouts

Item {
    // width: 300
    // height: 40
    width: parent.width
    height: parent.height
    anchors.fill: parent
    Slider {
        id: slider
        width: parent.width
        height: 6
        from: 0
        to: 4
        stepSize: 1
        value: 1  // Gi<PERSON> trị mặc định
        // 🔹 Thanh ngang của Slider (mặc định)
        background: Rectangle {
            width: parent.width
            height: 6
            color: "#D0D0D0"  // M<PERSON><PERSON> nền (thanh chưa đi qua)
            radius: height / 2
            // anchors.verticalCenter: parent.verticalCenter
        }
        // Thanh progress (phần đã đi qua)
        contentItem: Rectangle {
            width: slider.position * parent.width
            height: 10
            color: "#5B5B9F"
        }

        // Handle (nút kéo)
        handle: Rectangle {
            width: 22
            height: 22
            radius: width / 2
            color: slider.pressed ? "#005BBB" : "#5B5B9F"
            border.color: "#FFFFFF"
            border.width: 2
            anchors.verticalCenter: parent.verticalCenter
            x: slider.position * (slider.width - width)  // Cập nhật vị trí handle
        }
        onMoved: {
            console.log("Slider đang di chuyển, gi<PERSON> trị tạm thời:", value);
        }
        onValueChanged: {
            console.log("Giá trị Slider thay đổi:", value);
        }

        // Khi hoàn tất kéo hoặc click xong
        onPressedChanged: {
            if (!pressed) { // Chỉ gọi khi người dùng thả chuột (hoàn tất thao tác)
                console.log("Người dùng đã hoàn thành thao tác! Giá trị cuối:", value);
                // value = 2
            }
        }
    }

    Text {
        id: textItem
        text: `${slider.value}`  // Hiển thị giá trị hiện tại
        font.pixelSize: 16
        height: 30
        anchors{
            bottom: slider.top
        }
        x: slider.handle.x + (slider.handle.width / 2) - (width / 2)

    }
}

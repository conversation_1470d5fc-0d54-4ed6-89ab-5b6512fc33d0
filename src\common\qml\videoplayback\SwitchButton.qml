import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Window 2.15


Rectangle {
    id: root
    width: 200
    height: 40
    color: "transparent"
    property color normal_text_color: schedule_controller ? schedule_controller.get_color_theme_by_key("dialog_text") : "white"
    property bool checked: false
    property string title: ""
    signal toggled(bool value)
    Row {
        spacing: 20
        anchors.centerIn: parent

        Text {
            id: statusText
            text: title
            color: normal_text_color
            font.pointSize: 14
            verticalAlignment: Text.AlignVCenter
            height: parent.height
        }

        Item {
            id: switchRoot
            width: 40
            height: 20

            // property bool checked: false
            // signal toggled(bool value)

            Rectangle {
                id: background
                width: parent.width
                height: parent.height
                radius: height / 2
                color: root.checked ? "#1CD1A1" : "#ccc"
                border.color: "#999"

                Rectangle {
                    id: handle
                    width: switchRoot.height - 4
                    height: switchRoot.height - 4
                    radius: width / 2
                    color: "white"
                    anchors.verticalCenter: parent.verticalCenter
                    x: root.checked ? parent.width - width - 2 : 2

                    Behavior on x {
                        NumberAnimation { duration: 150; easing.type: Easing.InOutQuad }
                    }
                    border.color: "#aaa"
                }

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        root.checked = !root.checked
                        toggled(checked)
                        // statusText.text = switchRoot.checked ? "Switch is ON" : "Switch is OFF"
                    }
                    hoverEnabled: true
                    cursorShape: Qt.PointingHandCursor
                }
            }
        }
    }
}
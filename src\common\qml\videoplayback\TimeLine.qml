import QtQuick.Window 2.2

import QtQuick 2.15
import QtQuick.Controls 2.15
import models 1.0
import "math_utils.js" as MMath

    
Rectangle {
    id: timeline
    width: timeline.width
    height: timeline.height
    color: timeLineManager.timeLineController.theme ? timeLineManager.timeLineController.get_color_theme_by_key("main_background") : "#FAFAFA"
    clip: true
    

    MouseArea{
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        anchors{
            left: timeline.left
            right: timeline.right
            bottom: timeline.bottom
            top: rule.top
        }

        hoverEnabled: true
        onPositionChanged: (mouse) => {
            if(!timeLineManager.timeLineController.isMenuOpened){
                if(pressed){
                    if(mouseX > hoverCursor.x)
                        hoverCursor.width = mouseX-hoverCursor.x
                    else
                    {
                        var oldWidth = hoverCursor.width
                        hoverCursor.width = hoverCursor.x-mouseX+oldWidth
                        hoverCursor.x = mouseX
                    }
                }
                else if(hoverCursor.width === 2)
                    hoverCursor.x = mouseX
                // console.log("onPressed = ",timeLineManager.timeLineController.pressedX,mouseX)
                timeLineManager.timeLineController.mouseX = mouseX
            }
        }

        onPressed:(mouse) => {
            // console.log("onPressed = ",timeLineManager.timeLineController.pressedX,mouseX,timeLineManager.timeLineController.viewX)
            if(mouse.button === Qt.RightButton && hoverCursor.x <= mouseX && mouseX < (hoverCursor.x+hoverCursor.width)){
                // timeLineManager.timeLineController.onShowMenu(mouseX)
            }
            else if(!timeLineManager.timeLineController.isMenuOpened){
                hoverCursor.isSelecting = false
                hoverCursor.width = 2
                hoverCursor.x = mouseX
                timeLineManager.timeLineController.pressedX = mouseX
                timeLineManager.timeLineController.positionBubble = true

            }
        }

        onReleased:(mouse) => {
            if(mouse.button === Qt.RightButton && hoverCursor.x <= mouseX && mouseX < (hoverCursor.x+hoverCursor.width) && hoverCursor.width > 2){
                timeLineManager.timeLineController.onShowMenu(mouseX)
            }
            if(mouse.button !== Qt.LeftButton)
                return

            if(hoverCursor.width > 2){
                timeLineManager.timeLineController.onSelectedDurationChanged(hoverCursor.x,hoverCursor.width)
                timeLineManager.timeLineController.selectedDuration.relativeX = hoverCursor.x
                hoverCursor.isSelecting = true
                timeLineManager.timeLineController.selectedDuration.relativeWidth = hoverCursor.width
            }
        }

        onEntered: {
            if(hoverCursor.width === 2){
                hoverCursor.x = mouseX
                hoverCursor.visible = true
            }
        }

        onExited:{
            if(hoverCursor.width === 2){
                hoverCursor.visible = false
            }
        }

        onWheel: {
            var ruleWidth = timeLineManager.timeLineController.ruleWidth
            var ruleX = timeLineManager.timeLineController.viewX

            if(wheel.angleDelta.y < 0 && ruleWidth <= timeline.width){
                return
            }

            if(wheel.angleDelta.y > 0 && timeLineManager.timeLineController.isMaximumScale)
                return

            //calculate new ruleSize
            //fomule: xn = alphaN*xn_1+mouseX*(1-alphaN)
            //with: xn_1 is last x of rule
            //    : alphaN = newWidth/oldWidth
            var alpha0 = Math.pow(1.2,wheel.angleDelta.y/120)
            var wn_1 = ruleWidth
            var xn_1 = ruleX
            var wn = wn_1*alpha0
            if(MMath.differentPercent(wn,timeline.width) < 15){
                wn = 1
                timeLineManager.timeLineController.updateRule(timeline.width,0)
            }else {//over 20%
                var alphaN = wn/wn_1
                timeLineManager.timeLineController.updateRule(wn,alphaN*xn_1+mouseX*(1-alphaN))
            }

            scrollbar.size = timeline.width/wn
            scrollbar.position = Math.abs(timeLineManager.timeLineController.viewX/timeLineManager.timeLineController.ruleWidth)
        }
    }

    onWidthChanged: {
        timeLineManager.timeLineController.viewWidth = width
    }

    RuleContextCapturedView {
        id: rule
        model: timeLineManager.timeLineController.timeStep2s
        width: timeline.width
        height: 55

        anchors{
            bottom: timeZone.top
            left: timeline.left
        }

        onWidthChanged: {
            timeLineManager.timeLineController.updateRule(width,0)
        }
    }

    TimeZoneRuler{
        id: timeZone
        anchors{
            bottom: scrollbar.top
            left: timeline.left
        }
        model: timeLineManager.timeLineController.recordDurations
    }

    ScrollBar {
        id: scrollbar
        width: timeline.width
        size: 1
        position: 0
        active: true
        orientation: Qt.Horizontal
        policy: ScrollBar.AlwaysOn
        anchors{
            bottom: timeline.bottom
        }

        contentItem: Rectangle {
            implicitWidth: timeline.width
            implicitHeight: 15
            color: scrollbar.pressed ? "#606060" : (scrollbar.hovered ? "#686868" : "#585858")
            // Hide the ScrollBar when it's not needed.
            opacity: scrollbar.policy === ScrollBar.AlwaysOn || (scrollbar.active && scrollbar.size < 1.0) ? 0.75 : 0

            // Animate the changes in opacity (default duration is 250 ms).
            Behavior on opacity {
                NumberAnimation {}
            }

            ScrollCenterThumbnail {

            }
        }

        onPositionChanged: {
            timeLineManager.timeLineController.updateRule(-1,-position*timeLineManager.timeLineController.ruleWidth) // -1 for DEFAULT value
            // console.log("aaaaaaaaaaaa ",position,timeLineManager.timeLineController.ruleWidth)
        }

        onPressedChanged: {
            if(pressed)
                rule.enabledAnimation = false
            else
                rule.enabledAnimation = true
        }

    }

    MediaCursor{
        id: runningCusor
        x: timeLineManager.timeLineController.relativePosition
        anchors{
            top: rule.top
            bottom: scrollbar.top
        }
        visible: timeLineManager.timeLineController.positionBubble
    }

    MediaCursor {
        id: hoverCursor
        anchors{
            top: rule.top
            bottom: scrollbar.top
        }
        Connections{
            target: timeLineManager.timeLineController.selectedDuration
            function onRelativeWidthChanged() {
                if(hoverCursor.isSelecting){
                    if(timeLineManager.timeLineController.selectedDuration.relativeWidth >= 2 && !MMath.fuzzyIntegerCompare(hoverCursor.width, timeLineManager.timeLineController.selectedDuration.relativeWidth))
                        hoverCursor.width = timeLineManager.timeLineController.selectedDuration.relativeWidth
                    else if(timeLineManager.timeLineController.selectedDuration.relativeWidth <= 2)
                        hoverCursor.width = 2
                }
            }
            function onRelativeXChanged() {
                if(!MMath.fuzzyIntegerCompare(hoverCursor.x,timeLineManager.timeLineController.selectedDuration.relativeX) && hoverCursor.isSelecting){
                    hoverCursor.x = timeLineManager.timeLineController.selectedDuration.relativeX
                }
            }
        }
    }

    TimePositionBubble{
        id: positionBubble
        x : ((runningCusor.x+positionBubble.width) <= timeline.width? runningCusor.x : timeline.width-positionBubble.width) - 2 // offsetPoint
        anchors{
            bottom: rule.top
        }
        pointOffset: runningCusor.x-positionBubble.x

        position: timeLineManager.timeLineController.position
        visible: timeLineManager.timeLineController.positionBubble
    }

    TimePositionBubble{
        id: hoverBubble
        x : ((hoverCursor.x+hoverBubble.width) <= timeline.width? hoverCursor.x : timeline.width-hoverBubble.width) - 2 // offsetPoint
        anchors{
            bottom: rule.top
        }
        pointOffset: hoverCursor.x-hoverBubble.x
        position: timeLineManager.timeLineController.getHoverPosition(hoverCursor.x)
        visible: hoverCursor.visible
    }
    Connections{
        target: timeLineManager.timeLineController
        function onRuleWidthChanged() {
            // console.log("onRuleWidthChanged ",timeline.width)
            if(timeLineManager.timeLineController.ruleWidth==timeline.width)
                return

            scrollbar.size = timeline.width/timeLineManager.timeLineController.ruleWidth
        }

        function onViewXChanged(){
            //Disable selectedDuration
            // hoverCursor.width = 2
            console.log("onViewXChanged ",timeLineManager.timeLineController.viewX)
            scrollbar.position = Math.abs(timeLineManager.timeLineController.viewX/timeLineManager.timeLineController.ruleWidth)
        }
        function onScrollbarPositionChanged(){
            //Disable selectedDuration
            // hoverCursor.width = 2
            console.log("scrollbar.position ",scrollbar.position)
            // scrollbar.position = Math.abs(timeLineManager.timeLineController.viewX/timeLineManager.timeLineController.ruleWidth)
            // scrollbar.position = 1
            scrollbar.size = timeline.width/timeLineManager.timeLineController.ruleWidth
            scrollbar.position = Math.abs(timeLineManager.timeLineController.viewX/timeLineManager.timeLineController.ruleWidth)
        }
        function onClearSelectionChanged(){
            hoverCursor.width = 1
            hoverCursor.isSelecting = false
        }
        function onZoomToSelectionChanged(){
            timeLineManager.timeLineController.updateZoomToSelection(hoverCursor.x,hoverCursor.width)
        }
    }
}



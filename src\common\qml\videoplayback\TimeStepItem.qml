import QtQuick 2.15
import models 1.0

Rectangle{
    id: root
    required property TimeStep model
    property Component delegate: Item{}
    property bool enabledAnimation: true

    width: model.relativeWidth

    height: 15
    color: "transparent"

    Behavior on width {
        enabled: root.enabledAnimation
        PropertyAnimation{
            duration: 500
            easing.type: model.offset === 0?Easing.OutQuad:Easing.OutQuart
        }
    }

    Row{
        anchors {
            top: parent.top
        }

        Repeater{
            model: root.model.subItems
            delegate: root.delegate
        }
    }

    RuleLineEdge{
        anchors{
            top: parent.top
            right: parent.right
        }

        lineType: model.lineType
        text: model.text
        monthText: model.dateText
        visible: !model.isDisableText && root.width > 0
    }

    Rectangle {
        width: parent.width
        height: 20
        color: "transparent"
        Text {
            height: parent.height
            text: model.dateText
            color: "#b8b8b8"
            font: instance.dateTimeFont
            anchors {
                centerIn: parent
            }
        }
    }
}

import QtQuick 2.15
import models 1.0

Rectangle {
    id: root
    required property TimeStep2 model
    property bool enabledAnimation: true

    width: model.relativeWidth
    x: model.x

    height: 15
    color: "transparent"

    Behavior on width {
        enabled: root.enabledAnimation && model.enableAnimation
        PropertyAnimation{
            duration: 500
            easing.type: Easing.OutQuart
        }
    }

    Behavior on x {
        enabled: root.enabledAnimation && model.enableAnimation
        PropertyAnimation{
            duration: 500
            easing.type: Easing.OutQuart
        }
    }

    RuleLineEdge{
        id: ruleLineEdge
        anchors{
            top: parent.top
            right: parent.right
        }

        lineType: model.lineType
        text: model.text
        visible: model.enableRuleEdge
        enabledAnimation: model.enableRuleEdgeAnimation && root.enabledAnimation

    }

    Rectangle {
        width: parent.width
        height: 20
        color: "transparent"
        visible: model.enableDateText
        Text {
            height: parent.height
            text: model.dateText
            color: "#b8b8b8"
            font: timeLineManager.timeLineController.dateTimeFont
            anchors {
                centerIn: parent
            }
        }
    }
}
from src.utils.config import Config
import threading
import requests
import time
import socket
import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)


class NetworkScanner:
    protocol = 'http://'
    ping_path_websocket = '/api/vms/ping'
    ping_path_vms = '/api/ping'

    def __init__(self, port_websocket=Config.SERVER_EVENT_PORT_DEFAULT, port_server=Config.SERVER_VMS_PORT_DEFAULT):
        self.stop_flag = threading.Event()
        self.ip_server_scanned = None
        self.number_threads_done = 0
        self.total_number_unable_to_retrieve_current_ip_address = 0
        self.port_websocket = port_websocket
        self.port_server = port_server

    def get_ipv4_address(self):
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

        try:
            sock.connect(("*******", 80))
            ip_address = sock.getsockname()[0]
        except socket.error:
            ip_address = None

        sock.close()

        return ip_address

    def scan_ip_range(self, start, end, callback, callback_all_thread_done, callback_unable_to_retrieve_current_ip_address):
        current_ipv4_address = self.get_ipv4_address()

        if current_ipv4_address is None:
            logger.debug("Unable to retrieve current IP address.")
            callback_unable_to_retrieve_current_ip_address()
            return

        network_prefix = '.'.join(current_ipv4_address.split('.')[:-1])

        for i in range(start, end+1):
            try:
                if self.stop_flag.is_set():
                    break
                ip_address = f"{network_prefix}.{i}"
                # logger.debug("Scanning IP:", ip_address)
                if self.ping_with_ip_address(ip_address):
                    # logger.debug("Found IP:", ip_address)
                    self.ip_server_scanned = ip_address
                    callback(ip_address)
                    self.stop_flag.set()
                    break
                else:
                    # logger.debug("Not found IP:", ip_address)
                    callback_all_thread_done()
            except requests.RequestException as e:
                # logger.debug("Error scanning IP:", e)
                callback_all_thread_done()
                pass

    def scan_network(self, callback=None):
        logger.debug("Scanning network for IP address...")
        start_scan = time.time()
        # do 255 / 5 = 51 threads
        # nen chia ra 51 thread de scan cho nhanh
        total_ip = 255
        start = 0
        end = 14
        step = 15
        number_threads = total_ip//step
        logger.debug(f'number_threads: {number_threads}')

        # create callback after all thread done
        def callback_all_thread_done():
            self.number_threads_done += 1
            if self.number_threads_done == total_ip:
                logger.debug("All threads done.")
                callback(None)

        def callback_unable_to_retrieve_current_ip_address():
            self.total_number_unable_to_retrieve_current_ip_address += 1
            if self.total_number_unable_to_retrieve_current_ip_address == number_threads:
                logger.debug("Unable to retrieve current IP address.")
                callback(None)

        threads = []
        for i in range(number_threads):
            thread = threading.Thread(target=self.scan_ip_range, args=(
                start, end, callback, callback_all_thread_done, callback_unable_to_retrieve_current_ip_address))
            threads.append(thread)
            thread.start()
            start = end + 1
            end += step

        # block until all threads finish (i.e. until all IP addresses have been scanned)
        # join method: wait for thread to terminate and block UI Thread
        # for thread in threads:
        #     thread.join()

        # if self.stop_flag.is_set():
        #     logger.debug("Stopping all threads.")
        #     end_scan = time.time()
        #     # convert total time to seconds
        #     total_time = end_scan - start_scan
        #     # show %f format second
        #     logger.debug(f"Total time scan IP: {total_time:.2f} seconds")

        # return self.ip_server_scanned

        # total time: 0.24 - 0.46 seconds

    def ping_with_ip_address(self, ip_address, port_websocket=None, port_server=None):
        try:
            if port_server:
                self.port_server = port_server
            if port_websocket:
                self.port_websocket = port_websocket
            url_ping_vms = f"{self.protocol}{ip_address}:{self.port_server}{self.ping_path_vms}"
            url_ping_websocket = f"{self.protocol}{ip_address}:{self.port_websocket}{self.ping_path_websocket}"
            response_vms = requests.get(url_ping_vms, timeout=1)
            response_websocket = requests.get(url_ping_websocket, timeout=1)
            if ((response_websocket.status_code == 200) and (response_vms.status_code == 200)):
                server_websocket_available = response_websocket.json(
                )['service_name'] == 'CCCDServerDjango'
                server_vms_available = response_vms.json()[
                    'server name'] == 'VMS-BE'
                if server_vms_available and server_websocket_available:
                    return True
                return False
        except Exception as e:
            logger.error("Error scanning IP:", exc_info=True)
            return False
        return False

# # Create an instance of the NetworkScanner class
# scanner = NetworkScanner()

# def callback_scan_ip(ip_address):
#     logger.debug("callback_scan_ip: Found IP:", ip_address)

# # Call the scan_network method to initiate the network scanning
# scanner.scan_network(callback=callback_scan_ip)

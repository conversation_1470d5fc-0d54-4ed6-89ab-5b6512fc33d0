from typing import List
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal

@dataclass
class ServerInfo:
    id: int = None
    username: str = None
    server_ip: str = None
    server_port: int = None
    websocket_port: int = None
    password: str = None
    captcha: str = None
    captcha_id: str = None
    # status: bool = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    # def to_dict(self):
    #     return asdict(self)
    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
    
class ServerInfoModel(QObject):
    change_status_signal = Signal(bool)
    change_server_info_signal = Signal(str)
    def __init__(self,server: ServerInfo = None):
        super().__init__()
        self.data = server
        self.status = False
        self.is_connecting = False

    def register_signal(self,widget = None):
        # đăng ký các widget nhận signal từ object video capture
        if widget is not None:
            if hasattr(widget, 'change_status_signal'):
                self.change_status_signal.connect(widget.change_status_signal)
    def unregister_signal(self,widget = None):
        # hủy đăng ký các widget nhận signal từ object video capture
        if widget is not None:
            if hasattr(widget, 'change_status_signal'):
                self.change_status_signal.disconnect(widget.change_status_signal)

    def edit_server(self,username = None, password = None,server_ip=None,server_port=None,websocket_port=None,captcha = None,captcha_id = None):
        self.data.username = username
        self.data.password = password
        self.data.server_ip = server_ip
        self.data.server_port = server_port
        self.data.websocket_port = websocket_port
        self.data.captcha = captcha
        self.data.captcha_id = captcha_id
        
    def set_status(self,flag):
        # self.data.status = flag
        self.status = flag
        self.change_status_signal.emit(flag)   

class ServerInfoModelManager(QObject):
    add_server_signal = Signal(tuple)
    add_server_list_signal = Signal(str)
    delete_server_signal = Signal(tuple)
    __instance = None
    def __init__(self):
        super().__init__()
        self.server_list = {}

    @staticmethod
    def get_instance():
        if ServerInfoModelManager.__instance is None:
            ServerInfoModelManager.__instance = ServerInfoModelManager()
        return ServerInfoModelManager.__instance
    
    def add_server_list(self,server_list:List[ServerInfoModel] = []):
        for server in server_list:
            self.server_list[server.data.id] = server
        self.add_server_list_signal.emit('add_server_list_signal')

    def add_server(self,server:ServerInfoModel= None):
        if server.data.id not in self.server_list:
            self.server_list[server.data.id] = server
            self.add_server_signal.emit((server))
        else:
            self.server_list[server.data.id] = server

    def delete_server(self,server:ServerInfoModel= None):
        self.delete_server_signal.emit((server))
        del self.server_list[server.data.id] 

    def get_serverinfo_model(self,id = None, server_ip = None):
        if id is not None:
            return self.server_list.get(id,None)
        if server_ip is not None:
            for id, server_info in self.server_list.items():
                if server_info.data.server_ip == server_ip:
                    return server_info
                
server_info_model_manager = ServerInfoModelManager.get_instance()

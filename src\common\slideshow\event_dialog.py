
import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
from concurrent.futures import ThreadPoolExecutor
import datetime
from enum import Enum
import json
import threading
from typing import List
from PySide6.QtWidgets import <PERSON>App<PERSON>, QPushButton, QHBoxLayout, QDialog, QLabel, QWidget, QVBoxLayout, QFrame, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QGraphicsTextItem,QSizePolicy
from PySide6.QtCore import Qt, QSize, QRect,QEvent, Slot
from PySide6.QtGui import QIcon, QPainter, QColor, QPixmap, QPainterPath, QGuiApplication,QFont
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from src.common.widget.image_widget import ImageLoader, ImageWidget
from src.utils.auth_qsettings import AuthQSettings
from src.common.widget.event.event_combobox import <PERSON><PERSON>omboBox
from src.common.widget.event.calendar_combobox import CalendarComboBox
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
from src.common.model.group_model import Group, GroupModel, GroupModelManager
from src.common.widget.event.filter_mode import FilterMode
from src.common.slideshow.slideShow import SlideShow
from src.common.model.event_data_model import EventAI, EventData, event_manager
class EventDialog(QDialog):
    def __init__(self, parent=None):
    # def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(self.tr("Event"))
        # remove title bar
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        GroupModelManager.get_instance().add_group_list_signal.connect(self.update_group_list)
        camera_model_manager.add_camera_list_signal.connect(self.update_camera_list)


        self.event_bar = main_controller.list_parent['EventBar']
        
        self.filter_selected = {}
        self.create_filter_selected()
        self.main_layout = QVBoxLayout()
        self.font = QFont()
        # self.setFixedHeight(500)
        self.setObjectName('EventDialog')
        self.setStyleSheet(f'''
            QDialog#EventDialog {{ 
                background-color: {Style.PrimaryColor.background}; 
            }}
            ''')
        # self.setStyleSheet(f'''
        #     background-color: {Style.PrimaryColor.background}; 
        #     color: {Style.PrimaryColor.text_camera_name};
        #     ''')
        # self.setStyleSheet(f'''
        #     background-color: {Style.PrimaryColor.background}; 
        #     color: {Style.PrimaryColor.text_camera_name};
        #     ''')
        self.combobox_style = f'''
            QComboBox {{ 
                background-color: {Style.PrimaryColor.on_background}; 
                border: 1px solid #8D9DB1; border-radius: 2px;
            }}
            QComboBox::drop-down {{
                 border: none;
             }}
            QComboBox::down-arrow {{
                image: url({Style.PrimaryImage.down_arrow_custom}); 
            }}
            '''
        title_bar_widget = self.create_title_bar()
        
        content_widget = QWidget()
        layout = QHBoxLayout(content_widget)
        self.status_combobox = self.create_status_combobox()
        layout.addWidget(self.status_combobox)
        self.group_combobox = self.create_group_combobox()
        layout.addWidget(self.group_combobox)
        # self.ai_combobox = self.create_ai_combobox()
        # layout.addWidget(self.ai_combobox)
        # self.camera_combobox = self.create_camera_combobox()
        # layout.addWidget(self.camera_combobox)
        # # self.create_calendar()
        # calendar = CalendarComboBox()
        # calendar.setObjectName(FilterMode.TimeRange)
        # calendar.data_changed.connect(self.data_changed_signal)
        # layout.addWidget(calendar)
        # self.main_layout.addWidget(calendar)
        # self.setMaximumSize(100, 100)
        main_widget = QWidget()
        main_widget.setLayout(self.main_layout)
        main_widget.setObjectName('filter_event_type_dialog')
        main_widget.setStyleSheet(
            f'''
                QWidget#filter_event_type_dialog{{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 4px;
                    color: {Style.PrimaryColor.white};
                }}
                QWidget#calendar{{
                    border: 1px solid #efefef;
                }}
            '''
        )
        # layout = QVBoxLayout()
        # layout.setContentsMargins(0, 0, 0, 0)
        # layout.setSpacing(0)
        # layout.addWidget(main_widget)
        slide_show_layout = QHBoxLayout()
        self.slide_show = SlideShow()
        # self.slide_show.setFixedSize(400,400)
        # self.slide_show.setEventData(event_manager.event_list)
        # self.slide_show.setFilenames([Style.PrimaryImage.ahihi, Style.PrimaryImage.face, Style.PrimaryImage.ahihi, Style.PrimaryImage.face])
        self.slide_show.setGradientEnabled(True)
        for btn in self.slide_show.getButtonGroup().buttons():
            btn.setFixedSize(40, 20)
        prevBtn = self.slide_show.getPrevBtn()
        prevBtn.setFixedSize(60, 50)
        nextBtn = self.slide_show.getNextBtn()
        nextBtn.setFixedSize(60, 50)
        slide_show_layout.addWidget(self.slide_show)
        self.main_layout.addWidget(title_bar_widget)
        self.main_layout.addWidget(content_widget)
        self.main_layout.addLayout(slide_show_layout)
        self.setLayout(self.main_layout)

    def create_status_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel('Status')
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px;')
        self.event_combobox = EventComboBox()
        self.event_combobox.setObjectName(FilterMode.Status)
        self.event_combobox.data_changed.connect(self.data_changed_signal)
        self.event_combobox.setFont(self.font)
        self.event_combobox.setFixedWidth(300)
        self.event_combobox.setStyleSheet(self.combobox_style)
        self.event_combobox.addItem('All')
        self.event_combobox.addItem('Check-In')
        self.event_combobox.addItem('Check-Out')
        self.event_combobox.addItem('Appear')
        layout.addWidget(title)
        layout.addWidget(self.event_combobox)
        return widget

    def create_group_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel('Group')
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px;')
        self.group_combobox = EventComboBox()
        self.group_combobox.setObjectName(FilterMode.Group)
        self.group_combobox.data_changed.connect(self.data_changed_signal)
        self.group_combobox.setFont(self.font)
        self.group_combobox.setFixedWidth(300)
        self.group_combobox.setStyleSheet(self.combobox_style)
        self.group_combobox.addItem('All')
        layout.addWidget(title)
        layout.addWidget(self.group_combobox)
        return widget

    def create_ai_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel('AI')
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px;')
        self.ai_combobox = EventComboBox()
        self.ai_combobox.setObjectName(FilterMode.AI)
        self.ai_combobox.data_changed.connect(self.data_changed_signal)
        self.ai_combobox.setFont(self.font)
        self.ai_combobox.setFixedWidth(300)
        self.ai_combobox.setStyleSheet(self.combobox_style)
        self.ai_combobox.addItem('All')
        self.ai_combobox.addItem('Human')
        self.ai_combobox.addItem('Vehicle')
        self.ai_combobox.addItem('Crowd')
        layout.addWidget(title)
        layout.addWidget(self.ai_combobox)
        return widget

    def create_camera_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel('Camera')
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px;')
        self.camera_combobox = EventComboBox()
        self.camera_combobox.setObjectName(FilterMode.Camera)
        self.camera_combobox.data_changed.connect(self.data_changed_signal)
        self.camera_combobox.setFont(self.font)
        self.camera_combobox.setFixedWidth(300)
        self.camera_combobox.setStyleSheet(self.combobox_style)
        self.camera_combobox.addItem('All')
        layout.addWidget(title)
        layout.addWidget(self.camera_combobox)
        return widget
        
    def create_filter_selected(self):
        self.filter_selected[FilterMode.Status] = ['All']
        self.filter_selected[FilterMode.Group] = ['All']
        self.filter_selected[FilterMode.AI] = ['All']
        self.filter_selected[FilterMode.Camera] = ['All']
        start_date_converted = datetime.date.today().strftime("%Y-%m-%d 00:00:00")
        end_date_converted = datetime.date.today().strftime("%Y-%m-%d 23:59:59")
        self.filter_selected[FilterMode.TimeRange] = {'start_time': start_date_converted,'end_time':end_date_converted}

    def update_camera_list(self):
        camera_list = camera_model_manager.get_camera_list()
        for camera_model in camera_list.values():
            self.camera_combobox.addItem(camera_model.name)

    def update_group_list(self):
        group_list = GroupModelManager.get_instance().get_group_list()
        for group_model in group_list:
            self.group_combobox.addItem(group_model.get_property('name'))

    def data_changed_signal(self,data):
        logger.debug(f'data_changed_signal = {data}')
        key, value = data
        self.filter_selected[key] = value
        self.event_bar.filter_mode.emit(self.filter_selected)    

    def create_title_bar(self):
        # layout
        widget = QWidget()
        widget.setObjectName("title_bar")
        # set background Style.PrimaryColor.primary
        widget.setStyleSheet(f"background-color: {Style.PrimaryColor.primary}; border-top-left-radius: 10px; border-top-right-radius: 10px;")

        self.title_bar_layout = QHBoxLayout()
        # event name
        self.title_name_label = QLabel(self.tr("Event Information"))
        self.title_name_label.setStyleSheet(f"color: {Style.PrimaryColor.white}; font-weight: bold")
        close_icon = QIcon(Style.PrimaryImage.close_dialog)
        self.close_button = QPushButton(close_icon, "")
        self.close_button.setIconSize(QSize(15, 15))
        self.close_button.setFixedSize(30, 30)
        self.close_button.setStyleSheet("background-color: transparent")
        self.close_button.clicked.connect(self.close)
        # add widget
        self.title_bar_layout.addWidget(self.title_name_label, 90)
        self.title_bar_layout.addWidget(self.close_button, 10)
        widget.setLayout(self.title_bar_layout)
        return widget

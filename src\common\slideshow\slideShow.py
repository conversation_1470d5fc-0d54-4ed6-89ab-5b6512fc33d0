from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPainter
from PySide6.QtWidgets import QWidget, QGridLayout, QHBoxLayout, QButtonGroup, QSizePolicy
from src.common.slideshow.widgets.aniButton import AniRadioButton
from src.common.slideshow.widgets.graphicsView import SingleImageGraphicsView
from src.common.slideshow.widgets.svgButton import SvgButton
from src.common.model.event_data_model import EventAI, EventData, event_manager

class SlideShow(QWidget):
    def __init__(self):
        super().__init__()
        self.__initVal()
        self.__initUi()

    def __initVal(self):
        self.__btn = []
        self.__filenames = []
        self.__event_list = []
        self.__interval = 5000

    def __initUi(self):
        self.__imageFullUrl = SingleImageGraphicsView()
        self.__imageFullUrl.setAspectRatioMode(Qt.KeepAspectRatio)
        self.__imageFullUrl.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.__imageFullUrl.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.__imageFullUrl.setStyleSheet('QGraphicsView { background: transparent; border: none; }')
        self.__imageFullUrl.installEventFilter(self)

        self.__imageUrl = SingleImageGraphicsView()
        self.__imageUrl.setAspectRatioMode(Qt.KeepAspectRatio)
        self.__imageUrl.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.__imageUrl.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.__imageUrl.setStyleSheet('QGraphicsView { background: transparent; border: none; }')
        self.__imageUrl.installEventFilter(self)

        self.__btnGroup = QButtonGroup()
        self.__btnGroup.buttonClicked.connect(self.__showImageOfIdx)

        self.__btnWidget = QWidget()

        self.__prevBtn = SvgButton(self)
        self.__prevBtn.setIcon('ico/left.svg')
        self.__prevBtn.setFixedSize(30, 50)
        self.__prevBtn.clicked.connect(self.__prev)
        self.__prevBtn.setEnabled(False)

        self.__nextBtn = SvgButton(self)
        self.__nextBtn.setIcon('ico/right.svg')
        self.__nextBtn.setFixedSize(30, 50)
        self.__nextBtn.clicked.connect(self.__nextClicked)

        lay = QHBoxLayout()
        lay.addWidget(self.__prevBtn, alignment=Qt.AlignLeft)
        lay.addWidget(self.__nextBtn, alignment=Qt.AlignRight)

        self.__navWidget = QWidget()
        self.__navWidget.setLayout(lay)

        # lay = QGridLayout()
        # lay.addWidget(self.__view, 0, 0, 3, 1)
        # lay.addWidget(self.__navWidget, 0, 0, 3, 1)
        # lay.addWidget(self.__btnWidget, 2, 0, 1, 1, Qt.AlignCenter)
        # self.setLayout(lay)
        lay = QGridLayout()
        lay.addWidget(self.__imageFullUrl, 0, 0, 3, 1)
        lay.addWidget(self.__imageUrl, 0, 1, 3, 1)
        lay.addWidget(self.__navWidget, 0, 0, 3, 2)
        lay.addWidget(self.__btnWidget, 2, 0, 1, 1, Qt.AlignCenter)
        self.setLayout(lay)
        self.__timer = QTimer(self)
        self.__timer.setInterval(self.__interval)
        self.__timer.timeout.connect(self.__nextByTimer)
        self.__timer.start()

        self.setEventData()

    def __showImageOfIdx(self, btn):
        idx = self.__btnGroup.id(btn)
        # self.__view.setFilename(self.__filenames[idx])
        self.__imageFullUrl.setEventData(self.__event_list[idx].pixmapFullUrl)
        self.__imageUrl.setEventData(self.__event_list[idx].pixmapUrl)
        self.__prevNextBtnToggled(idx)
        self.__timer.start()

    def __prev(self):
        # if len(self.__filenames) > 0:
        if len(self.__event_list) > 0:
            idx = max(0, self.__btnGroup.checkedId()-1)
            self.__updateViewAndBtnBasedOnIdx(idx)
            self.__timer.start()

    def __nextByTimer(self):
        # if len(self.__filenames) > 0:
        if len(self.__event_list) > 0:
            self.__next()

    def __nextClicked(self):
        # if len(self.__filenames) > 0:
        if len(self.__event_list) > 0:
            self.__next()
            self.__timer.start()

    def __next(self):
        idx = (self.__btnGroup.checkedId()+1) % len(self.__btnGroup.buttons())
        self.__updateViewAndBtnBasedOnIdx(idx)

    def __updateViewAndBtnBasedOnIdx(self, idx):
        self.__btnGroup.button(idx).setChecked(True)
        # self.__view.setFilename(self.__filenames[idx])
        self.__imageFullUrl.setEventData(self.__event_list[idx].pixmapFullUrl)
        self.__imageUrl.setEventData(self.__event_list[idx].pixmapUrl)
        self.__prevNextBtnToggled(idx)

    def __prevNextBtnToggled(self, idx):
        self.__prevBtn.setEnabled(idx != 0)
        self.__nextBtn.setEnabled(idx != len(self.__btnGroup.buttons())-1)

    def setInterval(self, milliseconds: int):
        self.__timer.setInterval(milliseconds)

    def setEventData(self):
        data = event_manager.event_list
        for page_index, event_list in data.items():
            for event in event_list.data.content:
                self.__event_list.append(event)
        lay = QHBoxLayout()
        for i in range(len(self.__event_list)):
            # print(f'ahhi = {self.__event_list[0].pixmapFullUrl,self.__event_list[i].pixmapUrl}')
            btn = AniRadioButton()
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
            lay.addWidget(btn)
            self.__btn.append(btn)
            self.__btnGroup.addButton(btn, i)
        self.__btn[0].setChecked(True)
        
        self.__imageFullUrl.setEventData(self.__event_list[0].pixmapFullUrl)
        self.__imageUrl.setEventData(self.__event_list[0].pixmapUrl)
        self.__btnWidget.setLayout(lay)


    def setFilenames(self, filenames: list):
        self.__filenames = filenames
        lay = QHBoxLayout()
        for i in range(len(self.__filenames)):
            btn = AniRadioButton()
            btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
            lay.addWidget(btn)
            self.__btn.append(btn)
            self.__btnGroup.addButton(btn, i)
        self.__btn[0].setChecked(True)
        self.__imageFullUrl.setFilename(self.__filenames[0])
        self.__btnWidget.setLayout(lay)

    def setNavigationButtonVisible(self, f: bool):
        self.__navWidget.setVisible(f)

    def setBottomButtonVisible(self, f: bool):
        self.__btnWidget.setVisible(f)

    def setTimerEnabled(self, f: bool):
        if f:
            self.__timer.start()
        else:
            self.__timer.stop()

    def setGradientEnabled(self, f: bool):
        self.__imageFullUrl.setGradientEnabled(f)
        self.__imageUrl.setGradientEnabled(f)

    # to set the bottom buttons' size by user
    # here's how to do it:
    # for btn in self.__btnGroup.buttons():
    #     btn.setFixedSize(w, h)
    def getButtonGroup(self):
        return self.__btnGroup

    # get the btn widget
    # to set the spacing (currently)
    # here's how to do it:
    # self.__btnWidget.layout().setSpacing(5)
    def getBtnWidget(self):
        return self.__btnWidget

    # get the prev button
    # to set the prev nav button's size by user
    def getPrevBtn(self):
        return self.__prevBtn

    # get the next button
    # to set the next nav button's size by user
    def getNextBtn(self):
        return self.__nextBtn
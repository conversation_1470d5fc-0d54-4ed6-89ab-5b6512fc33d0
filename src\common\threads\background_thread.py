import time
from queue import Queue
from threading import Thread
from typing import Callable
import logging
logger = logging.getLogger(__name__)
class BackgroundThread(Thread):
    def __init__(self, target: Callable,args=()):
        super().__init__()
        self.target = target
        self.args = args
        self.queue = Queue()
        self.is_running = True
        self.daemon = True
    def run(self):
        while True:
            try:
                # Block cho đến khi có event mới trong queue
                msg = self.queue.get()
                
                # Ki<PERSON>m tra signal dừng thread
                if msg is None:  # Sentinel value để dừng thread
                    break
                # Xử lý event
                if self.target is not None:
                    self.target(msg)
                # <PERSON><PERSON><PERSON> dấu task đã hoàn thành
                self.queue.task_done()
                
            except Exception as e:
                logger.error(f"Error processing event: {e}")
        
    def stop(self):
        self.is_running = False
        # Thêm sentinel value để unblock thread
        self.queue.put(None)
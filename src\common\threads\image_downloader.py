from PySide6.QtCore import QThr<PERSON>, Signal, QUrl
from PySide6.QtGui import QPixmap
from PySide6.QtNetwork import QNetworkAccessManager, QNetworkRequest


class ImageDownloader(QThread):
    # Define a signal to emit the downloaded image data
    image_downloaded_signal = Signal(QPixmap)

    def __init__(self, url):
        super().__init__()
        self.url = url

    def run(self):
        network_manager = QNetworkAccessManager()
        # Create and send the network request
        request = QNetworkRequest(QUrl(self.url))
        reply = network_manager.get(request)
        reply.finished.connect(self.on_image_downloaded)
        self.exec_()  # Start the event loop for the thread

    def on_image_downloaded(self):
        # Get the reply object
        reply = self.sender()

        # Read the image data
        image_data = reply.readAll()

        # Load the image into a QPixmap
        pixmap = QPixmap()
        pixmap.loadFromData(image_data)

        # Emit the signal with the QPixmap object
        self.image_downloaded_signal.emit(pixmap)

        # Exit the thread's event loop
        self.quit()

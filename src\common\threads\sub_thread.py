
from PySide6.QtCore import Signal
from PySide6.QtCore import QThread
import logging
logger = logging.getLogger(__name__)

class SubThread(QThread):
    result_signal = Signal(tuple)

    def __init__(self, parent=None, target=None, callback=None, args=()):
        super(SubThread, self).__init__(parent)
        self.daemon = True
        self.callback = callback
        self._target = target
        self._args = args
        if callback is not None:
            self.result_signal.connect(self.callback)

    def run(self):
        try:
            if self._target:
                result = self._target(*self._args)
                self.result_signal.emit((result))
        except Exception as e:
            logger.debug(f'SubThread error = {e}')

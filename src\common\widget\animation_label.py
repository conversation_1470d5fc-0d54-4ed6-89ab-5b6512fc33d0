from PySide6.QtCore import QVariantAnimation, QEasingCurve
from PySide6.QtGui import QColor
from PySide6.QtWidgets import QLabel


class AnimationBorderQLabel(QLabel):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.default_color = QColor(255, 165, 0, 255)  # Default color
        self.animation_color = self.default_color  # Current animation color
        self.animation = QVariantAnimation()
        self.animation.valueChanged.connect(self.changeColor)
        self.animation.finished.connect(self.toggleFade)
        self.changeColor(self.animation_color)  # Initialize with default color

    def changeColor(self, color):
        color_string = f'rgba({color.red()}, {color.green()}, {color.blue()}, {color.alphaF()})'
        self.setStyleSheet(f'''
                        QLabel {{
                            background-color: rgba(0,0,0,0);
                            border: 3px solid {color_string};
                            border-bottom-left-radius: 0px;
                            border-bottom-right-radius: 0px;
                            border-top-right-radius: 0px;
                            border-top-right-radius: 0px;
                        }}
                    ''')

    def startFadeIn(self):
        self.animation.stop()
        self.animation.setStartValue(QColor(0, 0, 0, 0))
        self.animation.setEndValue(self.animation_color)
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)
        self.animation.start()

    def startFadeOut(self):
        self.animation.stop()
        self.animation.setStartValue(self.animation_color)
        self.animation.setEndValue(QColor(0, 0, 0, 0))
        self.animation.setDuration(500)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)
        self.animation.start()

    def startFadeInAnimation(self):
        self.startFadeIn()

    def stopAllAnimation(self):
        self.animation.stop()

    def toggleFade(self):
        if self.animation.endValue().alpha() == 255:  # If fading in
            self.startFadeOut()
        else:
            self.startFadeIn()

    def setAnimationColor(self, color):
        self.animation_color = color
        self.changeColor(color)  # Update the color immediately

    def resetToDefaultColor(self):
        self.animation_color = self.default_color
        self.changeColor(self.default_color)  # Update the color immediately

    def closeEvent(self, event):
        self.stopAllAnimation()
        self.deleteLater()



# import platform
# import src.utils.log_utils as LogUtils
# import logging
# logger = logging.getLogger(__name__)
# from PySide6.QtCore import QThread,Signal
# import pyaudio
# import subprocess
# #from pydub import AudioSegment
# import numpy as np
# class AudioThread(QThread):
#     #finished = Signal(int)
#     def __init__(self, stream_link = None, current_volume = 50, parent = None):
#         super().__init__(parent)
#         self.stream_link =stream_link
#         self.stop = False
#         self.p = pyaudio.PyAudio()
#         self.current_volume = current_volume
#     def connect(self):
#         if self.stream_link != None:
#             # Use CREATE_NO_WINDOW flag on Windows to suppress the command window
#             creation_flags = subprocess.CREATE_NO_WINDOW if platform.system().lower() == 'windows' else 0
#             startupinfo = None
#             if platform.system().lower() == 'windows':
#                 startupinfo = subprocess.STARTUPINFO()
#                 startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW

#             #self.cmd = ['ffmpeg', '-i', self.stream_link,'-loglevel','error', '-f', 's16le', '-acodec', 'pcm_s16le', '-ac', '2', '-ar', '44100', '-']
#             self.cmd = ['ffmpeg', '-i', self.stream_link,'-loglevel','error', '-f', 's16le', '-ac', '2', '-ar', '44100', '-']
#             self.pipe = subprocess.Popen(self.cmd, stdout=subprocess.PIPE, bufsize=10**8,creationflags=creation_flags, startupinfo=startupinfo)
#             self.stream = self.p.open(format=pyaudio.paInt16, channels=2, rate=44100, output=True)
#             return True
#         return False

#     def run(self):
#         if self.connect():
#             while not self.stop:
#                 #logger.debug(f'ahihihii')
#                 raw_audio = self.pipe.stdout.read(44100)
#                 if not raw_audio:
#                     break
#                 audio_data = np.frombuffer(raw_audio, dtype=np.int16)

#                 out_audio = self.set_volume(audio_data,self.current_volume)
#                 self.stream.write(raw_audio)
#     def set_volume(self,input_audio, volume):
#         sound_level = (volume / 100)
#         levels = np.zeros(len(input_audio), dtype=np.int16)
#         for i in range(len(input_audio)):
#             chunk = np.fromstring(input_audio[i], np.int16)

#             chunk = chunk * sound_level

#             levels[i] = chunk.astype(np.int16)
#         return levels
#     def stop_thread(self):
#         self.stop = True
#         self.wait()
#         self.stream.stop_stream()
#         self.stream.close()
#         self.pipe.kill()
#         self.p.terminate()

import enum
from PySide6.QtWidgets import QPushButton
from src.styles.style import Style
from PySide6.QtCore import QSize

class ButtonType(enum.Enum):
    POSITIVE = 1
    NEGATIVE = 2
    NEUTRAL = 3

button_positive_style = Style.StyleSheet.button_style1

button_negative_style = Style.StyleSheet.button_style2

button_neutral_style = Style.StyleSheet.button_style3

class BaseButton(QPushButton):
    def __init__(self, text = None, parent=None, icon=None, icon_size=QSize(20, 20), button_type=None, size=QSize(50, 20)):
        if text is None:
            super().__init__(parent)
        else:
            super().__init__(text, parent)
        self.setMinimumWidth(50)
        self.setMinimumHeight(20)

        if size is not None:
            self.setFixedSize(size)

        if button_type == ButtonType.POSITIVE:
            self.setStyleSheet(Style.StyleSheet.button_style1)
        elif button_type == ButtonType.NEGATIVE:
            self.setStyleSheet(Style.StyleSheet.button_style2)
        elif button_type == ButtonType.NEUTRAL:
            self.setStyleSheet(Style.StyleSheet.button_style3)
        if icon is not None:
            self.setIcon(icon)
            self.setIconSize(icon_size)


# if __name__ == "__main__":
#     app = QApplication([])
#     button = BaseButton("Test", button_type=ButtonType.POSITIVE)
#     button.show()
#     app.exec_()
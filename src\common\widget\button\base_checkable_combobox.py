from PySide6.QtWidgets import QComboBox, QStyledItemDelegate, QAbstractItemView, QSizePolicy
from PySide6.QtCore import Qt, QEvent, Signal
from PySide6.QtGui import QStandardItem, QFontMetrics, QPalette

from src.styles.style import Style


class BaseCheckableComboBox(QComboBox):
    itemSelected = Signal()

    class Delegate(QStyledItemDelegate):
        def sizeHint(self, option, index):
            size = super().sizeHint(option, index)
            size.setHeight(20)
            return size

    def __init__(self, placeholder_text=None, mode=QAbstractItemView.SelectionMode.SingleSelection, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.placeholder_text = placeholder_text if placeholder_text else self.tr("Select items...")
        self.mode = mode
        self.texts = []
        self.setEditable(True)
        self.lineEdit().setReadOnly(True)
        palette = self.lineEdit().palette()
        palette.setBrush(QPalette.Base, palette.button())
        self.lineEdit().setPalette(palette)
        self.setItemDelegate(BaseCheckableComboBox.Delegate())
        self.model().dataChanged.connect(self.updateText)
        self.lineEdit().installEventFilter(self)
        self.closeOnLineEditClick = False
        self.view().viewport().installEventFilter(self)
        self.lineEdit().textChanged.connect(self.text_changed)

        # set stylesheet
        self.setStyleSheet(
            f'''
            QComboBox{{
                border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                border-radius: 4px;
                background: transparent;
                padding: 4px 16px;
            }}
            QComboBox::disabled{{
                border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                color: {Style.PrimaryColor.text_disable};
            }}

            QComboBox QAbstractItemView {{
                 border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                 color: {Style.PrimaryColor.white_2};
                 selection-background-color: {Style.PrimaryColor.on_background};
                 background-color: {Style.PrimaryColor.background};
                 border-radius: 4px;
            }}

            QComboBox QAbstractItemView::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}

            QComboBox QAbstractItemView::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}

            QComboBox::drop-down {{
                url({Style.PrimaryImage.down_arrow_linedit});
             }}
            '''
        )
    
    def setSelectionMode(self, mode):
        self.mode = mode

    def text_changed(self, text):
        if not self.texts:
            self.lineEdit().setText(self.placeholder_text)

    def resizeEvent(self, event):
        self.updateText()
        super().resizeEvent(event)

    def eventFilter(self, object, event):
        if object == self.lineEdit():
            if event.type() == QEvent.MouseButtonRelease:
                if self.closeOnLineEditClick:
                    self.hidePopup()
                else:
                    self.showPopup()
                return True
            return False
        if object == self.view().viewport():
            if event.type() == QEvent.MouseButtonRelease:
                # print('click')
                index = self.view().indexAt(event.pos())
                item = self.model().item(index.row())
                
                if item.checkState() == Qt.Checked:
                    if self.mode == QAbstractItemView.SelectionMode.SingleSelection:
                        self.uncheck_all()
                    item.setCheckState(Qt.Unchecked)
                else:
                    if self.mode == QAbstractItemView.SelectionMode.SingleSelection:
                        self.uncheck_all()
                    item.setCheckState(Qt.Checked)
                return True
        return False

    def showPopup(self):
        super().showPopup()
        self.closeOnLineEditClick = True

    def hidePopup(self):
        super().hidePopup()
        self.startTimer(100)
        self.closeOnLineEditClick = False

    def timerEvent(self, event):
        self.killTimer(event.timerId())
        self.closeOnLineEditClick = False

    def updateText(self):
        self.texts = []
        for i in range(self.model().rowCount()):
            if self.model().item(i).checkState() == Qt.Checked:
                self.texts.append(self.model().item(i).text())

        if not self.texts:
            self.lineEdit().setText(self.placeholder_text)
        else:
            text = ", ".join(self.texts)
            metrics = QFontMetrics(self.lineEdit().font())
            elidedText = metrics.elidedText(text, Qt.ElideRight, self.lineEdit().width())
            self.lineEdit().setText(elidedText)
        self.itemSelected.emit()
    
    def addItem(self, text, data=None, checked=False):
        item = QStandardItem()
        item.setText(text)
        if data is None:
            item.setData(text)
        else:
            item.setData(data)
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsUserCheckable)
        if checked:
            item.setData(Qt.Checked, Qt.CheckStateRole)
        else:
            item.setData(Qt.Unchecked, Qt.CheckStateRole)
        self.model().appendRow(item)

    def addItems(self, texts, datalist=None):
        for i, text in enumerate(texts):
            try:
                data = datalist[i]
            except (TypeError, IndexError):
                data = None
            self.addItem(text, data)

    def currentData(self):
        res = []
        for i in range(self.model().rowCount()):
            if self.model().item(i).checkState() == Qt.Checked:
                res.append(self.model().item(i).data())
        return res


    def uncheck_all(self):
        for i in range(self.model().rowCount()):
            if self.model().item(i).checkState() == Qt.Checked:
                self.model().item(i).setCheckState(Qt.Unchecked)

    def clear(self):
        self.texts = []
        self.lineEdit().setText(self.placeholder_text)
        super().clear()

    def update_data(self, data):
        self.clear()
        self.addItems(data)
        self.updateText()
        self.itemSelected.emit()

    def set_current_data(self, index):
        if index is not None and self.model().item(index) is not None:
            self.model().item(index).setCheckState(Qt.Checked)
            self.updateText()
        else:
            print(f'index: {index} is out of range')


    def get_data(self):
        # get in self.model()
        data = []
        for i in range(self.model().rowCount()):
            data.append(self.model().item(i).data())
        return data
    
    def find_index_by_value(self, value):
        for i in range(self.model().rowCount()):
            if self.model().item(i).data() == value:
                return i
        return None
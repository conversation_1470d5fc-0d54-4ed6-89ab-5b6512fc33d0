from enum import Enum

from src.common.model.item_grid_model import ItemGridModel
from src.styles.style import Style
from src.utils.theme_setting import theme_setting
from src.common.controller.main_controller import main_controller


class ButtonState:
    ON = 0
    OFF = 1
    DISABLE = -1

    # create enum
    class ButtonType(Enum):
        # on/off, menu
        ON_OFF = 0
        MENU = 1
        DIALOG = 2

    class ButtonFeature(Enum):
        STREAM_FLOW = 0
        FULLSCREEN = 1
        CAPTURE = 2
        RECORD = 3
        SOUND = 4
        MICRO = 5
        GRID = 6
        ASPECT_RATIO = 7
        EXIT_STREAM = 8
        EDIT_MAP_CAMERA = 9
        EDIT_MAP_NAME = 10

    class ExitStreamType(Enum):
        DISABLE = -1
        EXIT_STREAM = 0

    class AspectRatioType(Enum):
        DISABLE = -1
        ASPECT_RATIO_4_3_WINDOW_SIZE = 0
        ASPECT_RATIO_16_9_WINDOW_SIZE = 1
        ASPECT_RATIO_ORIGINAL_WINDOW_SIZE = 0

    class GridType:
        GRID_1 = "1x1"
        GRID_4 = "2x2"
        GRID_9 = "3x3"
        GRID_16 = "4x4"
        GRID_36 = "6x6"
        GRID_64 = "8x8"
        GRID_6_CUSTOM = "2x3"
        GRID_8_CUSTOM = "2x4"
        GRID_10_CUSTOM = "4x2"
        GRID_13_CUSTOM = "1x3"

    class DivisionType:
        STANDARD_DIVISIONS = "STANDARD_DIVISIONS"
        CUSTOM_DIVISIONS = "CUSTOM_DIVISIONS"

    class FullscreenType(Enum):
        DISABLE = -1
        FULLSCREEN = 0
        EXIT_FULLSCREEN = 1

    class CaptureType(Enum):
        DISABLE = -1
        CAPTURE = 0
        STOP_CAPTURE = 1

    class RecordType(Enum):
        DISABLE = -1
        RECORD = 0
        STOP_RECORD = 1

    class MicroType(Enum):
        DISABLE = -1
        MICRO = 0
        MUTE = 1

    class StreamFlowType(Enum):
        DISABLE = -1
        MAIN_STREAM = 0
        SUB_STREAM = 1
        THIRD_STREAM = 2

    class SoundType(Enum):
        DISABLE = -1
        SOUND = 0
        MUTE = 1

    class Status:
        DISABLE = 'disable'
        ENABLE = 'enable'


class GridButtonModel:
    class StandardGrid:
        DIVISIONS_1 = ItemGridModel(name_grid="1 Divisions", data=[], total_grid_count=1, row=1, column=1,
                                    image_url=main_controller.get_theme_attribute("Image", "grid_1x1"),
                                    grid_type=ButtonState.GridType.GRID_1,
                                    divisions=1, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                                    grid_path="grid_1x1")

        DIVISIONS_4 = ItemGridModel(name_grid="4 Divisions", data=[], total_grid_count=4, row=2, column=2,
                                    image_url=main_controller.get_theme_attribute("Image", "grid_2x2"),
                                    grid_type=ButtonState.GridType.GRID_4,
                                    divisions=4, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                                    grid_path="grid_2x2")

        DIVISIONS_9 = ItemGridModel(name_grid="9 Divisions", data=[], total_grid_count=9, row=3, column=3,
                                    image_url=main_controller.get_theme_attribute("Image", "grid_3x3"),
                                    grid_type=ButtonState.GridType.GRID_9,
                                    divisions=9, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                                    grid_path="grid_3x3")

        DIVISIONS_16 = ItemGridModel(name_grid="16 Divisions", data=[], total_grid_count=16, row=4, column=4,
                                     image_url=main_controller.get_theme_attribute("Image", "grid_4x4"),
                                     grid_type=ButtonState.GridType.GRID_16,
                                     divisions=16, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                                     grid_path="grid_4x4")

        DIVISIONS_36 = ItemGridModel(name_grid="36 Divisions", data=[], total_grid_count=36, row=6, column=6,
                                     image_url=main_controller.get_theme_attribute("Image", "grid_6x6"),
                                     grid_type=ButtonState.GridType.GRID_36,
                                     divisions=36, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                                     grid_path="grid_6x6")

        DIVISIONS_64 = ItemGridModel(name_grid="64 Divisions", data=[], total_grid_count=64, row=8, column=8,
                                     image_url=main_controller.get_theme_attribute("Image", "grid_8x8"),
                                     grid_type=ButtonState.GridType.GRID_64,
                                     divisions=64, divisions_type=ButtonState.DivisionType.STANDARD_DIVISIONS,
                                     grid_path="grid_8x8")

    class CustomGrid:
        DIVISIONS_6 = ItemGridModel(name_grid=f"6 Divisions", data=[{(0, 1), (1, 0), (1, 1), (0, 0)}], row=3, column=3,
                                    total_grid_count=9,
                                    image_url=main_controller.get_theme_attribute("Image", "custom_6_grid"),
                                    grid_type=ButtonState.GridType.GRID_6_CUSTOM, divisions=6,
                                    divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS)

        DIVISIONS_8 = ItemGridModel(name_grid=f"8 Divisions",
                                    data=[{(0, 1), (1, 2), (2, 1), (0, 0), (1, 1), (2, 0), (0, 2), (2, 2), (1, 0)}],
                                    row=4, column=4, total_grid_count=16,
                                    image_url=main_controller.get_theme_attribute("Image", "custom_8_grid"),
                                    grid_type=ButtonState.GridType.GRID_8_CUSTOM, divisions=8,
                                    divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS)

        DIVISIONS_10 = ItemGridModel(name_grid=f"10 Divisions",
                                     data=[{(0, 1), (1, 0), (1, 1), (0, 0)}, {(1, 2), (0, 2), (0, 3), (1, 3)}],
                                     row=4, column=4, total_grid_count=16,
                                     image_url=main_controller.get_theme_attribute("Image", "custom_10_grid"),
                                     grid_type=ButtonState.GridType.GRID_10_CUSTOM, divisions=10,
                                     divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS)

        DIVISIONS_13 = ItemGridModel(name_grid=f"13 Divisions", data=[{(1, 1), (1, 2), (2, 1), (2, 2)}],
                                     row=4, column=4, total_grid_count=16,
                                     image_url=main_controller.get_theme_attribute("Image", "custom_13_grid"),
                                     grid_type=ButtonState.GridType.GRID_13_CUSTOM, divisions=13,
                                     divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS)


original_list_data_grid = [
    ItemGridModel(name_grid=f"6 Divisions", data=[{(0, 1), (1, 0), (1, 1), (0, 0)}], row=3,
                  column=3,
                  total_grid_count=9,
                  image_url=main_controller.get_theme_attribute("Image", "custom_8_grid"),
                  grid_type=ButtonState.GridType.GRID_6_CUSTOM, divisions=6,
                  divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                  grid_path="custom_6_grid"),

    ItemGridModel(name_grid=f"8 Divisions",
                  data=[{(0, 1), (1, 2), (2, 1), (0, 0), (1, 1), (2, 0), (0, 2), (2, 2), (1, 0)}],
                  row=4, column=4, total_grid_count=16, image_url=main_controller.get_theme_attribute("Image", "custom_8_grid"),
                  grid_type=ButtonState.GridType.GRID_8_CUSTOM, divisions=8,
                  divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                  grid_path="custom_8_grid"),

    ItemGridModel(name_grid=f"10 Divisions",
                  data=[{(0, 1), (1, 0), (1, 1), (0, 0)}, {(1, 2), (0, 2), (0, 3), (1, 3)}],
                  row=4, column=4, total_grid_count=16,
                  image_url=main_controller.get_theme_attribute("Image", "custom_10_grid"),
                  grid_type=ButtonState.GridType.GRID_10_CUSTOM, divisions=10,
                  divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                  grid_path="custom_10_grid"),

    ItemGridModel(name_grid=f"13 Divisions", data=[{(1, 1), (1, 2), (2, 1), (2, 2)}],
                  row=4, column=4, total_grid_count=16,
                  image_url=main_controller.get_theme_attribute("Image", "custom_13_grid"),
                  grid_type=ButtonState.GridType.GRID_13_CUSTOM, divisions=13,
                  divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                  grid_path="custom_13_grid")
]

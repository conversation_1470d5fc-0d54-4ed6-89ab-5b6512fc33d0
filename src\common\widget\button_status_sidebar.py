from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QToolButton

class ButtonStatusSidebar(QToolButton):
    def __init__(self, parent=None, init_svg_path=None, close_svg_path=None, open_svg_path=None):
        super().__init__(parent)
        self.init_svg_path = init_svg_path
        self.close_svg_path = close_svg_path
        self.open_svg_path = open_svg_path
        self.load_ui()

    def load_ui(self):
        self.setIcon(QIcon(self.init_svg_path))
        self.setFocusPolicy(Qt.FocusPolicy.NoFocus)
        self.setFixedSize(10, 20)
        self.setStyleSheet('''
            QToolButton { 
                background-color: transparent; 
                color: white; 
                border: None; 
            }
        ''')

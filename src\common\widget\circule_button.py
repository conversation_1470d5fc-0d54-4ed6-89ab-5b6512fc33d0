
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor, QFont, QPainter
from PySide6.QtWidgets import QPushButton

from src.styles.style import Style


class CircularButton(QPushButton):
    def __init__(self, text, parent=None, width=100, height=100, border_color=QColor(0, 0, 0), background_color=QColor(100, 100, 100), text_color=QColor(Style.PrimaryColor.white)):
        super().__init__(text, parent)
        self.setFixedSize(width, height)
        self._border_color = border_color
        self._background_color = background_color
        self._text_color = text_color

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)  # Smooth edges
        painter.setBrush(self._background_color)  # Button color
        painter.setPen(Qt.NoPen)  # Border color
        # Draw a circular button
        painter.drawEllipse(self.rect())
        # You can add additional customization, like text, here:
        painter.setPen(self._text_color)  # Text color
        painter.drawText(self.rect(), Qt.AlignCenter, self.text())
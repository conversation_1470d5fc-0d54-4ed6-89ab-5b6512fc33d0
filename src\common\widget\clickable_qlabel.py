import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
from PySide6.QtGui import QFont
from PySide6.QtWidgets import QWidget,  QVBoxLayout, QLabel

class ClickableQLabel(QLabel):
    def __init__(self, title):
        super().__init__()
        self.title = title
        self.setText(self.title)

    def enterEvent(self, event):
        f = QFont()
        f.setUnderline(True)
        self.setFont(f)

    def leaveEvent(self, event):
        f = QFont()
        f.setUnderline(False)
        self.setFont(f)

    def mousePressEvent(self, ev):
        logger.debug("HanhLT: mouse press click ")
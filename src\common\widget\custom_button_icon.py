from PySide6.QtWidgets import QToolButton
from PySide6.QtCore import QSize

from src.styles.style import Style


class CustomButtonIcon(QToolButton):
    def __init__(self, icon, w=28, h=28, parent=None, show_border: bool = False, background_color=None):
        super(CustomButtonIcon, self).__init__(parent)
        self.parent = parent
        self.show_border = show_border
        self.background_color = background_color
        self.icon = icon
        self.width = w
        self.height = h
        # self.margin = 10
        # self.padding = 10
        self.setup_ui()

    def setup_ui(self):
        self.setIcon(self.icon)

        # custom state button hover and pressed
        self.setMouseTracking(True)
        if self.background_color:
            self.setStyleSheet(
                f'QToolButton {{background-color: {self.background_color}; border-radius: 4px; padding: 4px}}'
                f'QToolButton:hover {{ background-color: {Style.PrimaryColor.on_hover_secondary}; border-radius: 4px; padding: 4px}}'
                f'QToolButton:pressed {{ background-color: #B2B2B2; border-radius: 4px; padding: 4px}}'
            )
        else:
            self.setStyleSheet(
                f'QToolButton {{background-color: #454F67; border-radius: 4px; padding: 4px}}'
                f'QToolButton:hover {{ background-color: {Style.PrimaryColor.on_hover_secondary}; border-radius: 4px; padding: 4px}}'
                f'QToolButton:pressed {{ background-color: #B2B2B2; border-radius: 4px; padding: 4px}}'
            )

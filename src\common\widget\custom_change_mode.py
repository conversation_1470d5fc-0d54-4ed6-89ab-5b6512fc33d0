from PySide6.QtWidgets import <PERSON>Widget, QLineEdit, QVBoxLayout, QHBoxLayout, QLabel, QMenu, QPushButton
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap, QAction
from src.styles.style import Style
from src.utils.theme_setting import theme_setting
from src.common.controller.main_controller import main_controller


class ChangeModeButtonSideMenu(QPushButton):
    change_mode_signal = Signal(str)
    def __init__(self):
        super().__init__()

        self.setIconSize(QSize(24, 24))
        # set size
        self.setMaximumSize(28, 28)

        # X<PERSON> lý sự kiện khi ấn vào icon
        self.mousePressEvent = lambda event: self.show_menu(event.globalPos())
        # self.clicked.connect(lambda: self.show_menu(self.pos()))

        self.set_dynamic_stylesheet()

    def show_menu(self, position):
        self.menu = QMenu()
        all_action = QAction(icon=QIcon(main_controller.get_theme_attribute('Image', 'change_mode')),
                                 text=self.tr("All"), parent=self.menu)
        all_action.triggered.connect(lambda: self.change_mode_signal.emit("All"))

        server_action = QAction(icon=QIcon(main_controller.get_theme_attribute('Image', 'treeview_server')),
                                 text=self.tr("Server"), parent=self.menu)
        server_action.triggered.connect(lambda: self.change_mode_signal.emit("Server"))

        camera_group_action = QAction(icon=QIcon(main_controller.get_theme_attribute('Image', 'list_devices')),
                                    text=self.tr("Camera Group"), parent=self.menu)
        camera_group_action.triggered.connect(lambda: self.change_mode_signal.emit("Camera Group"))

        virtual_window_action = QAction(icon=QIcon(main_controller.get_theme_attribute('Image', 'list_virtual_window')),
                                  text=self.tr("Virtual Window"), parent=self.menu)
        virtual_window_action.triggered.connect(lambda: self.change_mode_signal.emit("Virtual Window"))

        saved_view_action = QAction(icon=QIcon(main_controller.get_theme_attribute('Image', 'list_save_view')),
                                  text=self.tr("Saved View"), parent=self.menu)
        saved_view_action.triggered.connect(lambda: self.change_mode_signal.emit("Saved View"))

        map_action = QAction(icon=QIcon(main_controller.get_theme_attribute('Image', 'list_map')),
                                  text=self.tr("Map"), parent=self.menu)
        map_action.triggered.connect(lambda: self.change_mode_signal.emit("Map"))

        self.menu.addAction(all_action)
        self.menu.addAction(server_action)
        self.menu.addAction(camera_group_action)
        self.menu.addAction(virtual_window_action)
        self.menu.addAction(saved_view_action)
        self.menu.addAction(map_action)
        self.menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.menu.exec_(position)

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(
            f'''
                QPushButton {{
                    background-color: transparent;
                    border-radius: 4px;
                    qproperty-icon: url({main_controller.get_theme_attribute("Image", "change_mode")});
                }}
                QPushButton:hover {{
                    background-color: {main_controller.get_theme_attribute('Color', 'hover_button')};
                    border-radius: 4px;
                }}
                QPushButton:pressed {{
                    background-color: {Style.PrimaryColor.primary};
                    border-radius: 4px;
                }}
                '''
        )


from PySide6.QtCore import Qt
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QCheckBox, QHBoxLayout, QSizePolicy
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from src.utils.theme_setting import theme_setting


class CustomCheckBoxDialogs(QWidget):
    def __init__(self, parent=None, index=None):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.parent1 = parent
        self.index = index
        self.checkbox = QCheckBox()
        self.checkbox.setObjectName('checkbox')
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        # self.checkbox.setStyleSheet(
        #     f"""
        #     QCheckBox {{
        #             background-color: transparent;
        #         }}
        #     QCheckBox::indicator:checked {{
        #         border: none;
        #         image: url({Style.PrimaryImage.checkbox_checked});
        #         background-color: transparent;
        #         width: 16px;
        #         height: 16px;
        #     }}
        #     QCheckBox::indicator:unchecked {{
        #         border: none;
        #         image: url({Style.PrimaryImage.checkbox_unchecked});
        #         background-color: transparent;
        #         width: 16px;
        #         height: 16px;
        #     }}
        #     """)
        self.checkbox.stateChanged.connect(self.checkbox_clicked)
        self.update_list_state()
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        layout.addWidget(self.checkbox)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setStyleSheet('background-color: transparent')
        self.set_dynamic_stylesheet()

    def update_list_state(self):
        pass

    def checkbox_clicked(self, state):
        pass

    def set_dynamic_stylesheet(self):
        self.checkbox.setStyleSheet(
            f"""
            QCheckBox {{
                    background-color: transparent;
                }}
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            """)
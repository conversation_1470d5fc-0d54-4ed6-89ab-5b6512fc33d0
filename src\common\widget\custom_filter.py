from PySide6.QtWidgets import QWidget, QLineEdit, QVBoxLayout, QHBoxLayout, QLabel, QMenu, QPushButton, QWidgetAction, \
    QCheckBox
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap, QAction
from src.styles.style import Style
from src.utils.theme_setting import theme_setting


class FilterButtonSideMenu(QPushButton):
    def __init__(self):
        super().__init__()

        # Tạo icon
        icon = QIcon(Style.PrimaryImage.Filter)
        self.setIcon(icon)

        self.setIconSize(QSize(24, 24))
        # set size
        self.setMaximumSize(28, 28)

        # Xử lý sự kiện khi ấn vào icon
        self.mousePressEvent = lambda event: self.show_menu(event.globalPos())

        self.setStyleSheet(
            f'''
                QPushButton {{
                    background-color: transparent;
                    border-radius: 4px;
                }}
                QPushButton:hover {{
                    /* background-color: rgba(204, 80, 81, 1); */
                    border-radius: 4px;
                }}
                QPushButton:pressed {{
                    background-color: rgba(181, 18, 46, 1);
                    border-radius: 4px;
                }}
                '''
        )

    def show_menu(self, position):
        self.menu = QMenu()

        state_action = QWidgetAction(self.menu)
        ai_flows_action = QWidgetAction(self.menu)

        ai_flows_widget = ChildItemWidget(icon=Style.PrimaryImage.ai_menu_filter, title=self.tr("AI flows"), is_title=True,
                                          item="AI flows")
        state_widget = ChildItemWidget(icon=Style.PrimaryImage.state_menu_filter, title=self.tr("State"), is_title=True,
                                       item="State")

        state_action.setDefaultWidget(state_widget)
        ai_flows_action.setDefaultWidget(ai_flows_widget)

        connected_widget = ChildItemWidget(icon=Style.PrimaryImage.ai_menu_filter, title=self.tr("Connected"))
        disconnected_widget = ChildItemWidget(icon=Style.PrimaryImage.ai_menu_filter, title=self.tr("Disconnected"))
        face_recognition_widget = ChildItemWidget(icon=Style.PrimaryImage.ai_menu_filter, title=self.tr("Face Recognition"))
        lpr_widget = ChildItemWidget(icon=Style.PrimaryImage.ai_menu_filter, title="LPR")
        vehicle_widget = ChildItemWidget(icon=Style.PrimaryImage.ai_menu_filter, title=self.tr("Vehicle"), item="Vehicle")

        connected_action = QWidgetAction(self.menu)
        connected_action.setDefaultWidget(connected_widget)
        disconnected_action = QWidgetAction(self.menu)
        disconnected_action.setDefaultWidget(disconnected_widget)
        face_recognition_action = QWidgetAction(self.menu)
        face_recognition_action.setDefaultWidget(face_recognition_widget)
        lpr_action = QWidgetAction(self.menu)
        lpr_action.setDefaultWidget(lpr_widget)
        vehicle_action = QWidgetAction(self.menu)
        vehicle_action.setDefaultWidget(vehicle_widget)

        self.menu.addAction(state_action)
        self.menu.addAction(connected_action)
        self.menu.addAction(disconnected_action)
        self.menu.addAction(ai_flows_action)
        self.menu.addAction(face_recognition_action)
        self.menu.addAction(lpr_action)
        self.menu.addAction(vehicle_action)
        self.menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.menu.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.menu.exec_(position)


class ChildItemWidget(QWidget):
    def __init__(self, icon, title, item="", is_title=False):
        super().__init__()
        self.icon = icon
        self.title = title
        self.is_title = is_title
        self.item_type = item
        self.load_ui()

    def load_ui(self):
        self.layout = QHBoxLayout()
        self.label = QLabel(self.title)

        if self.is_title:
            icon = QIcon(self.icon)
            icon_label = QLabel()
            icon_label.setPixmap(icon.pixmap(24, 24))
            self.layout.addWidget(icon_label)
            self.layout.addWidget(self.label)
        else:
            checkbox = QCheckBox(self.title)
            self.layout.addWidget(checkbox)

        self.layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_widget = QWidget()

        self.main_widget.setLayout(self.layout)
        self.main_widget.setObjectName("parent")

        self.main_layout = QHBoxLayout()
        self.main_layout.addWidget(self.main_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        self.setLayout(self.main_layout)
        self.change_style_sheet()

    def change_style_sheet(self):
        if self.item_type == "State":
            self.setStyleSheet(f'''
                        QLabel {{
                                font-size: 14px;
                                color: #5C687F;
                                font-weight: 600;
                            }}

                        QWidget#parent{{
                            border-bottom: 1px solid #5C687F;
                            background-color: #E2F3FF;
                            border-top-left-radius: 8px; 
                            border-top-right-radius: 8px;
                        }}
                        
                    ''')
        elif self.item_type == "AI flows":
            self.setStyleSheet(f'''
                QLabel {{
                    font-size: 14px;
                    color: #5C687F;
                    font-weight: 600;
                }}

                QWidget#parent{{
                    border-bottom: 1px solid #5C687F;
                    background-color: #E2F3FF;
                }}
            ''')
        elif self.item_type == "Vehicle":
            self.setStyleSheet(f'''
                QWidget#parent{{
                    border-bottom-left-radius: 8px; 
                    border-bottom-right-radius: 8px;
                    background-color: #ffffff;
                }}
                /* Unchecked state */
                QCheckBox {{
                    font-size: 14px;
                    color: #5C687F;
                }}
                /* Hover state */
                QCheckBox:hover {{
                    background-color: #eeeeee;
                }}
            ''')
        else:
            self.setStyleSheet(f'''
                        QWidget#parent{{
                            border-bottom: 1px solid #5C687F;
                        }}
                        /* Unchecked state */
                        QCheckBox {{
                            font-size: 14px;
                            color: #5C687F;
                        }}
                        /* Hover state */
                        QCheckBox:hover {{
                            background-color: #eeeeee;
                        }}
                    ''')

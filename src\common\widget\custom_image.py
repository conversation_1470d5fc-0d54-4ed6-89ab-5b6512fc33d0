from PySide6.QtGui import QPixmap
from PySide6.QtGui import QResizeEvent
from PySide6.QtWidgets import QLabel
from PySide6.QtCore import Qt
import uuid

class CustomImage(QLabel):

    def __init__(self, parent=None, scale_contents=True):
        super(CustomImage, self).__init__(parent)
        self.setScaledContents(scale_contents)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.uuid = uuid.uuid4()
        self.pixmap_width: int = 1
        self.pixmap_height: int = 1
        self.width_widget = 0
        self.height_widget = 0
    def set_scale_contents(self, scale_contents):
        self.setScaledContents(scale_contents)

    def setPixmap(self, pm: QPixmap) -> None:
        self.pixmap_width = pm.width()
        self.pixmap_height = pm.height()

        self.updateMargins()
        super(CustomImage, self).setPixmap(pm)

    def resizeEvent(self, a0: QResizeEvent) -> None:
        self.updateMargins()
        super(CustomImage, self).resizeEvent(a0)
    def update_resize(self,width,height):
        self.width_widget = width
        self.height_widget = height
    def updateMargins(self):
        if self.pixmap() is None:
            return
        # pixmapWidth = self.pixmap().width()
        # pixmapHeight = self.pixmap().height()
        if self.pixmap_width <= 0 or self.pixmap_height <= 0:
            return
        w, h = self.width_widget, self.height_widget
        if w <= 0 or h <= 0:
            return

        if w * self.pixmap_height > h * self.pixmap_width:
            m = int((w - (self.pixmap_width * h / self.pixmap_height)) / 2)
            self.setContentsMargins(m, 0, m, 0)
        else:
            m = int((h - (self.pixmap_height * w / self.pixmap_width)) / 2)
            self.setContentsMargins(0, m, 0, m)

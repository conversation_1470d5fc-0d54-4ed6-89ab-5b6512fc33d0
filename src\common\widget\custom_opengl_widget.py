import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
# from PySide6.QtOpenGLWidgets import QOpenGLWidget
from PySide6.QtWidgets import QWidget
# import OpenGL.GL as gl
class CustomOpenGLWidget(QWidget):
# class CustomOpenGLWidget(QOpenGLWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.texture = None
        self.texture_width = 0
        self.texture_height = 0

    # def set_video_frame(self, frame):
    #     try:
    #         # make current
    #         self.makeCurrent()
    #         # if current texture frame size is different from the new frame size, delete the current texture
    #         if self.texture is not None:
    #             if frame.shape[1] != self.texture_width or frame.shape[0] != self.texture_height:
    #                 # logger.debug(frame.shape[1],' - ', self.texture_width, ' - ', frame.shape[0], ' - ', self.texture_height)
    #                 self.cleanup()

    #         if self.texture is None:
    #             self.texture_width = frame.shape[1]
    #             self.texture_height = frame.shape[0]
    #             self.texture = gl.glGenTextures(1)
    #             gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture)
    #             gl.glTexParameterf(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_WRAP_S, gl.GL_CLAMP_TO_EDGE)
    #             gl.glTexParameterf(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_WRAP_T, gl.GL_CLAMP_TO_EDGE)
    #             gl.glTexParameterf(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MAG_FILTER, gl.GL_LINEAR)
    #             gl.glTexParameterf(gl.GL_TEXTURE_2D, gl.GL_TEXTURE_MIN_FILTER, gl.GL_LINEAR)
    #             gl.glTexImage2D(gl.GL_TEXTURE_2D, 0, gl.GL_RGB, frame.shape[1], frame.shape[0], 0, gl.GL_RGB, gl.GL_UNSIGNED_BYTE, None)
    #         # logger.debug(frame.shape[1],' - ', self.texture_width, ' - ', frame.shape[0], ' - ', self.texture_height)
    #         gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture)
    #         gl.glTexSubImage2D(gl.GL_TEXTURE_2D, 0, 0, 0, frame.shape[1], frame.shape[0], gl.GL_RGB, gl.GL_UNSIGNED_BYTE, frame)

    #         self.update()
    #     except Exception as e:
    #         logger.debug('Exception OPENGL: ', frame.shape[1],' - ', self.texture_width, ' - ', frame.shape[0], ' - ', self.texture_height, ' - ', e)

    # def initializeGL(self):
    #     gl.glClearColor(0.0, 0.0, 0.0, 1.0)

    # def resizeGL(self, width, height):
    #     gl.glViewport(0, 0, width, height)

    # def paintGL(self):
    #     gl.glClear(gl.GL_COLOR_BUFFER_BIT)
    #     gl.glMatrixMode(gl.GL_PROJECTION)
    #     gl.glLoadIdentity()
    #     gl.glOrtho(-1, 1, -1, 1, -1, 1)
    #     gl.glMatrixMode(gl.GL_MODELVIEW)
    #     gl.glLoadIdentity()

    #     if self.texture is not None:
    #         gl.glEnable(gl.GL_TEXTURE_2D)
    #         gl.glBindTexture(gl.GL_TEXTURE_2D, self.texture)
    #         gl.glBegin(gl.GL_QUADS)
    #         gl.glTexCoord2f(0.0, 0.0); gl.glVertex2f(-1.0, -1.0)
    #         gl.glTexCoord2f(1.0, 0.0); gl.glVertex2f(1.0, -1.0)
    #         gl.glTexCoord2f(1.0, 1.0); gl.glVertex2f(1.0, 1.0)
    #         gl.glTexCoord2f(0.0, 1.0); gl.glVertex2f(-1.0, 1.0)
    #         gl.glEnd()
    #         gl.glDisable(gl.GL_TEXTURE_2D)
    
    # def cleanup(self):
    #     if self.texture is not None:
    #         gl.glDeleteTextures([self.texture])
    #         self.texture = None


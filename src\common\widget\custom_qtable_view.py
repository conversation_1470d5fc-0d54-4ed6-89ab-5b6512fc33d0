import sys
from PySide6.QtCore import Qt, Q<PERSON><PERSON>, QTimer, QEvent, QModelIndex
from PySide6.QtGui import QIcon, QGuiApplication, QAction, QStandardItemModel, QStandardItem, QPainter, QColor, QBrush, \
    QCursor
from PySide6.QtWidgets import QA<PERSON>lication, QMainWindow, QTableWidget, QTableWidgetItem, QVBoxLayout, QWidget, \
    QHeaderView, QCheckBox, QStyleOptionButton, QStyle, QHBoxLayout, QAbstractItemView, QPushButton, QLabel, QDialog, \
    QLineEdit, QItemDelegate, QComboBox, QStackedWidget, QFrame, QTextEdit, QTableView, QSizePolicy, QProgressBar, \
    QStyledItemDelegate

from src.presentation.device_management_screen.widget.tableview_base import TableViewBase
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from src.utils.theme_setting import theme_setting


class TableWithCustomHeader(QWidget):
    def __init__(self, horizontal_label_list=None, model_for_table: QStandardItemModel=None,
                 list_widget_to_header: dict[int, QWidget] = None, use_stylesheet_header=False,
                 use_default_header=False):
        super().__init__()
        self.use_default_header = use_default_header
        self.use_stylesheet_header = use_stylesheet_header
        self.horizontal_label_list = horizontal_label_list
        self.model_for_table = model_for_table
        self.list_widget_to_header = list_widget_to_header
        self.layout_main = QVBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignCenter)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        self.table = TableViewBase(is_use_paint_event=False)
        self.table.setObjectName('table_with_custom_header')
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setMouseTracking(True)
        self.model_for_table.setHorizontalHeaderLabels(self.horizontal_label_list)
        # Set the model for the table view
        self.table.setModel(self.model_for_table)
        if self.use_default_header:
            pass
        else:
            # Set the custom header for the first column
            header = CheckableHeader(Qt.Orientation.Horizontal, self.table, list_header_element=self.list_widget_to_header)
            # if self.use_stylesheet_header:
            #     header.setStyleSheet(f'background-color: {Style.PrimaryColor.on_hover_secondary}; color: {Style.PrimaryColor.white_2}; border-radius: 4px;')
            self.table.setHorizontalHeader(header)
        self.scroll_bar = self.table.verticalScrollBar()
        self.layout_main.addWidget(self.table)
        self.setLayout(self.layout_main)

    def set_horizontal_header(self):
        self.model_for_table.setHorizontalHeaderLabels(self.horizontal_label_list)

    def set_dynamic_stylesheet(self):
        self.table.setStyleSheet(f"""
            QTableView {{
                border: None;
                gridline-color: #dcdcdc;
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
            }}
            QHeaderView::section {{
                background-color: transparent;
                padding: 4px;
                border: None;
                font-weight: bold;
                font-size: 14px;
            }}
            QTableView::item {{
                padding: 5px;
            }}
            QTableView::item:selected {{
                background-color: white;
                color: #ffffff;
            }}
        """)

        self.scroll_bar.setStyleSheet(
            f'''    
                QScrollBar:vertical {{
                    background-color: transparent;
                    width: 4px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {Style.PrimaryColor.divider};
                    border-radius: 4px;
                    min-height: 10px;
                    margin-right: 1px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            '''
        )

        if self.table.horizontalHeader() and self.use_stylesheet_header:
            self.table.horizontalHeader().setStyleSheet(f"""
                background-color: {main_controller.get_theme_attribute("Color", "table_item_header_background")}; 
                color: {main_controller.get_theme_attribute("Color", "table_header_text")}; 
                border-radius: 4px;""")

class HoverDelegate(QStyledItemDelegate):
    def initStyleOption(self, option, index):
        super().initStyleOption(option, index)

        model = index.model()
        col = index.column()
        view = self.parent()

        if option.state & QStyle.State_MouseOver:
            option.backgroundBrush = QBrush(QColor(Style.PrimaryColor.on_hover_secondary))
        else:
            pos = view.viewport().mapFromGlobal(QCursor.pos())
            for c in range(model.columnCount()):
                if c != col:
                    r = view.visualRect(index.siblingAtColumn(c))
                    if r.adjusted(0, 0, 1, 1).contains(pos):
                        option.backgroundBrush = QBrush(QColor(Style.PrimaryColor.on_hover_secondary))
                        break

class CheckableHeader(QHeaderView):
    def __init__(self, orientation, parent=None, list_header_element: dict[int, QWidget] = None):
        super().__init__(orientation, parent)
        if list_header_element is None:
            list_header_element = {}
        self.isChecked = False
        self.list_header_element = list_header_element
        self.setSectionsClickable(True)
        self.setSectionResizeMode(QHeaderView.ResizeMode.Fixed)
        self.setStyleSheet(f'background-color: transparent; color: {Style.PrimaryColor.white_2}')

    def paintSection(self, painter, rect, logicalIndex):
        super().paintSection(painter, rect, logicalIndex)
        for index, widget in self.list_header_element.items():
            if index == logicalIndex:
                widget.setParent(self)
                widget.setGeometry(rect)
                widget.show()


class LoadingAnimationBar(QWidget):
    def __init__(self):
        super().__init__()
        # Create a QVBoxLayout
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)

        # Create a QProgressBar
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(10)
        self.progress_bar.setMaximum(0)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)

        # Timer for the animation
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_loading)
        self.setLayout(layout)
        self.setFixedHeight(10)

    def start_loading(self):
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(0)  # Set to 0 for an indeterminate state (loading)
        self.timer.start(2000)  # Start the timer

    def stop_loading(self):
        self.timer.stop()
        self.progress_bar.setVisible(False)

    def update_loading(self):
        # No need to update the progress bar value as it is indeterminate
        pass

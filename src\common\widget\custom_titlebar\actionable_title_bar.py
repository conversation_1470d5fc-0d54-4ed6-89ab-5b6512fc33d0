from PySide6.QtCore import Qt, QEvent
from PySide6.QtWidgets import QWidget

class ActionableTitleBar(QWidget):
    def __init__(self, parent=None, window_parent=None):
        super().__init__(parent)
        self.initial_pos = None
        self.window_parent = window_parent
        self.installEventFilter(self)

    def _move(self):
        window_move = self.window_parent.window().windowHandle()
        window_move.startSystemMove()

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.initial_pos = event.position().toPoint()
        super().mousePressEvent(event)
        event.accept()

    def mouseMoveEvent(self, event):
        if self.initial_pos is not None:
            self._move()
        super().mouseMoveEvent(event)
        event.accept()

    def mouseReleaseEvent(self, event):
        self.initial_pos = None
        super().mouseReleaseEvent(event)
        event.accept()

    def eventFilter(self, watched, event):
        if watched == self:
            if event.type() == QEvent.Type.MouseButtonDblClick:
                mouse_button = event.button()
                if mouse_button == Qt.LeftButton:
                    # Toggle between full screen and normal mode
                    if self.window_parent.window().isMaximized():
                        self.window_parent.window().showNormal()
                    else:
                        self.window_parent.window().showMaximized()
        return super().eventFilter(watched, event)

    def set_dynamic_stylesheet(self):
        pass
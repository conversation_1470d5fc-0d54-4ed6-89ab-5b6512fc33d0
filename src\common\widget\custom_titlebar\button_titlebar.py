from PySide6.QtGui import QIcon
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QVBoxLayout, QWidget, QToolButton
from PySide6.QtCore import Qt, Signal

from src.styles.style import Style
from src.common.controller.main_controller import main_controller


class ButtonTitleBar(QWidget):
    def __init__(self, parent=None, svg_path_enterred=None, svg_path_normal=None, type_button=None):
        super().__init__(parent)
        self.svg_path_enterred = svg_path_enterred
        self.svg_path_normal = svg_path_normal
        self.type_button = type_button
        self.load_ui()

    def load_ui(self):
        self.tool_button = QToolButton()
        self.tool_button.enterEvent = self.enter_event
        self.tool_button.leaveEvent = self.leave_event
        self.tool_button.setIcon(QIcon(self.svg_path_normal))
        self.set_dynamic_stylesheet()
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(self.tool_button)
        self.setLayout(self.layout)

    def enter_event(self, event):
        self.tool_button.setIcon(QIcon(self.svg_path_enterred))
        # super().enterEvent(event)

    def leave_event(self, event):
        self.tool_button.setIcon(QIcon(self.svg_path_normal))
        # super().leaveEvent(event)

    def set_svg_path_normal_icon(self, svg_path):
        self.svg_path_normal = svg_path
        self.tool_button.setIcon(QIcon(svg_path))

    def set_dynamic_stylesheet(self):
        if self.type_button == "Close":
            self.tool_button.setStyleSheet(f'''
                QToolButton {{
                    background-color: transparent; /* Set background transparent */
                    color: #ffffff; /* Set text color */
                    border: None; /* Set border */
                    padding: 5px; /* Set padding */
                    font-size: 14px; /* Set font size */
                    border-radius: 4px
                }}
                QToolButton:hover {{
                    background-color: {main_controller.get_theme_attribute("Color", "titlebar_close_hoverred")}; /* Change background color on hover */
                }}
            ''')
        else:
            self.tool_button.setStyleSheet(f'''
                QToolButton {{
                    background-color: transparent; /* Set background transparent */
                    color: #ffffff; /* Set text color */
                    border: None; /* Set border */
                    padding: 5px; /* Set padding */
                    font-size: 14px; /* Set font size */
                    border-radius: 4px
                }}
                QToolButton:hover {{
                    background-color: {Style.PrimaryColor.primary}; /* Change background color on hover */
                }}
            ''')

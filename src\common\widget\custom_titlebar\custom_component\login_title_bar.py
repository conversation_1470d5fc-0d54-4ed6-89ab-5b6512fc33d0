from PySide6.QtCore import Qt, QSize, QEvent
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget

from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar
from src.common.widget.custom_titlebar.button_titlebar import ButtonTitleBar
from src.styles.style import Style

from src.common.controller.main_controller import main_controller


class LoginTitleBar(ActionableTitleBar):
    def __init__(self, parent=None, window_parent=None):
        super().__init__(parent, window_parent)
        self.window_parent = window_parent
        self.load_ui()

    def load_ui(self):
        # create layout
        self.layout_main = QHBoxLayout(self)
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.setSpacing(2)

        minimize_window = main_controller.get_theme_attribute("Image", "titlebar_minimize_window_normal")
        maximize_window = main_controller.get_theme_attribute("Image", "titlebar_maximize_window_normal")
        close_application = main_controller.get_theme_attribute("Image", "titlebar_close_application_normal")
        normal_window = main_controller.get_theme_attribute("Image", "titlebar_normal_window_normal")

        # Min button
        self.min_button = ButtonTitleBar(self, svg_path_enterred=Style.PrimaryImage.minimize_window_white, svg_path_normal=minimize_window, type_button="Mini")
        self.min_button.tool_button.clicked.connect(self.min_button_clicked)

        # Max button
        self.max_button = ButtonTitleBar(self, svg_path_enterred=Style.PrimaryImage.maximize_window_white, svg_path_normal=maximize_window)
        self.max_button.tool_button.clicked.connect(self.max_button_clicked)

        # Close button
        self.close_button = ButtonTitleBar(self, svg_path_enterred=Style.PrimaryImage.close_application_white, svg_path_normal=close_application, type_button="Close")
        self.close_button.tool_button.clicked.connect(self.window_parent.window().close)

        # Normal button
        self.normal_button = ButtonTitleBar(self, svg_path_enterred=Style.PrimaryImage.normal_window_white, svg_path_normal=normal_window)
        self.normal_button.tool_button.clicked.connect(self.normal_button_clicked)
        self.normal_button.setVisible(False)

        self.stacked_button = QStackedWidget()
        self.stacked_button.addWidget(self.max_button)
        self.stacked_button.addWidget(self.normal_button)

        buttons = [
            self.min_button,
            self.stacked_button,
            self.close_button,
        ]

        self.layout_button = QHBoxLayout()
        self.layout_button.setContentsMargins(0, 0, 0, 0)
        self.layout_button.setSpacing(2)
        self.layout_button.setAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignRight)
        for button in buttons:
            button.setFocusPolicy(Qt.FocusPolicy.NoFocus)
            button.setFixedSize(QSize(32, 32))
            self.layout_button.addWidget(button)

        self.layout_title = QHBoxLayout()

        self.layout_main.addLayout(self.layout_title, 80)
        self.layout_main.addLayout(self.layout_button)

    def min_button_clicked(self):
        self.window_parent.window().showMinimized()
        self.min_button.tool_button.clearFocus()

    def max_button_clicked(self):
        self.window_parent.window().showFullScreen()
        self.max_button.tool_button.clearFocus()

    def normal_button_clicked(self):
        self.window_parent.window().showNormal()
        self.normal_button.tool_button.clearFocus()

    def window_state_changed(self, state):
        if state == Qt.WindowState.WindowFullScreen:
            self.stacked_button.setCurrentIndex(1)
        else:
            self.stacked_button.setCurrentIndex(0)

    def set_dynamic_stylesheet(self):
        minimize_window = main_controller.get_theme_attribute("Image", "titlebar_minimize_window_normal")
        maximize_window = main_controller.get_theme_attribute("Image", "titlebar_maximize_window_normal")
        close_application = main_controller.get_theme_attribute("Image", "titlebar_close_application_normal")
        normal_window = main_controller.get_theme_attribute("Image", "titlebar_normal_window_normal")
        self.min_button.set_dynamic_stylesheet()
        self.min_button.set_svg_path_normal_icon(minimize_window)
        self.max_button.set_dynamic_stylesheet()
        self.max_button.set_svg_path_normal_icon(maximize_window)
        self.normal_button.set_dynamic_stylesheet()
        self.normal_button.set_svg_path_normal_icon(normal_window)
        self.close_button.set_dynamic_stylesheet()
        self.close_button.set_svg_path_normal_icon(close_application)

from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout

from src.common.widget.custom_change_mode import ChangeModeButtonSideMenu
from src.common.widget.custom_filter import FilterButtonSideMenu
from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar
from src.common.widget.search_widget.search_bar import SearchBar
from src.styles.style import Style
from src.utils.theme_setting import theme_setting


class WidgetSearchTitle(ActionableTitleBar):
    def __init__(self, parent=None, hide_filter=False, hide_change_mode=False, window_parent=None):
        super().__init__(parent, window_parent)
        self.window_parent = window_parent
        self.hide_filter = hide_filter
        self.hide_change_mode = hide_change_mode
        self.load_ui()

    def load_ui(self):
        # create layout
        self.layout = QVBoxLayout()
        self.layout.setSpacing(0)
        self.layout.setContentsMargins(0, 2, 0, 2)
        self.layout.setAlignment(Qt.AlignTop)
        self.setLayout(self.layout)

        self.menu_top = QWidget()
        self.layout_top = QHBoxLayout()
        self.layout_top.setSpacing(4)
        # create search widget
        self.search_widget = SearchBar(parent=self)
        # self.search_widget.search_items_signal.connect(self.search_items)
        self.filter = FilterButtonSideMenu()
        self.change_mode = ChangeModeButtonSideMenu()
        # self.change_mode.change_mode_signal.connect(self.change_mode_trigger)
        if not self.hide_filter:
            self.layout_top.addWidget(self.search_widget)

        if not self.hide_change_mode:
            self.layout_top.addWidget(self.change_mode)

        self.layout_top.setContentsMargins(0, 0, 0, 0)

        if not self.hide_filter or not self.hide_change_mode:
            self.menu_top.setLayout(self.layout_top)
            self.layout.addWidget(self.menu_top)

    def retransalate_ui(self):
        self.search_widget.retranslateUi_searchbar()

    def set_dynamic_stylesheet(self):
        self.search_widget.set_dynamic_stylesheet()
        self.change_mode.set_dynamic_stylesheet()
        # self.filter.set_dynamic_stylesheet()
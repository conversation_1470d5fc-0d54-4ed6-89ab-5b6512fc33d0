from enum import Enum

from PySide6.QtCore import Qt
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout

from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar

class ScreenName(Enum):
    CAMERA_SCREEN = 'CAMERA_SCREEN'
    DEVICE_SCREEN = 'DEVICE_SCREEN'
    PLAYBACK_SCREEN = 'PLAYBACK_SCREEN'
    MAP_SCREEN = 'MAP_SCREEN'
    SETTING_SCREEN = 'SETTING_SCREEN'

class WidgetTabTitle(ActionableTitleBar):
    def __init__(self, parent=None, screen_name='CAMERA_SCREEN'):
        super().__init__(parent)
        self.screen_name = screen_name
        self.load_ui()

    def load_ui(self):
        # create layout
        self.layout = QVBoxLayout()
        self.layout.setAlignment(Qt.AlignTop)
        self.layout.setSpacing(0)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.layout)


from PySide6.QtCore import Qt, QSize, QEvent, Signal
from PySide6.QtGui import QIcon, QGuiApplication
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QToolButton, QSizePolicy, QStackedWidget, QTabWidget, \
    QTabBar, QVBoxLayout

from src.common.widget.custom_titlebar.actionable_title_bar import ActionableTitleBar
from src.common.widget.custom_titlebar.button_titlebar import ButtonTitleBar
from src.common.widget.custom_titlebar.custom_component.widget_button_system import WidgetButtonSystem
from src.styles.style import Style
from src.common.controller.main_controller import main_controller

class CustomTabBar(QTabBar):
    def __init__(self, window_parent=None):
        super().__init__(window_parent)
        self.initial_pos = None
        self.installEventFilter(self)
        self.setAutoHide(False)
        self.setExpanding(True)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        self.window_parent = window_parent

    def _move(self):
        window_move = self.window_parent.window().windowHandle()
        window_move.startSystemMove()

    def mousePressEvent(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.initial_pos = event.position().toPoint()
        super().mousePressEvent(event)
        event.accept()

    def mouseMoveEvent(self, event):
        if self.initial_pos is not None:
            self._move()
        super().mouseMoveEvent(event)
        event.accept()

    def mouseReleaseEvent(self, event):
        self.initial_pos = None
        super().mouseReleaseEvent(event)
        event.accept()

    def eventFilter(self, watched, event):
        if watched == self:
            if event.type() == QEvent.Type.MouseButtonDblClick:
                mouse_button = event.button()
                if mouse_button == Qt.LeftButton:
                    # Toggle between full screen and normal mode
                    if self.window_parent.window().isMaximized():
                        self.window_parent.window().showNormal()
                    else:
                        self.window_parent.window().showMaximized()
        return super().eventFilter(watched, event)

class CustomTitleBarWithTab(ActionableTitleBar):
    signal_change_tab = Signal(int)

    def __init__(self, parent=None, is_show_tab_bar=False, window_parent=None):
        super().__init__(parent, window_parent)
        self.window_parent = window_parent
        self.is_show_tab_bar = is_show_tab_bar
        if self.is_show_tab_bar:
            self.create_tab_bar()
        self.load_ui()
        self.set_dynamic_stylesheet()
        

    def load_ui(self):
        # create layout
        self.layout_main = QHBoxLayout()
        self.layout_main.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.setSpacing(2)

        self.widget_button_system = WidgetButtonSystem(parent=self, window_parent=self.window_parent)

        if self.is_show_tab_bar:
            self.layout_tab = QHBoxLayout()
            self.layout_tab.setContentsMargins(0, 0, 0, 0)
            self.layout_tab.setAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignBottom)
            self.layout_tab.addWidget(self.tab_widget)
            self.widget_tab = QWidget()
            self.widget_tab.setLayout(self.layout_tab)
            self.layout_main.addWidget(self.widget_tab, 80)

        self.layout_main.addWidget(self.widget_button_system, 20)
        self.setLayout(self.layout_main)

    def create_tab_bar(self):
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabBar(CustomTabBar(window_parent=self.window_parent))
        self.tab_widget.setTabBarAutoHide(True)
        self.tab_widget.tabBar().adjustSize()
        self.tab_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tab_widget.setStyleSheet("""
                    QTabWidget::tab-bar {
                        left: 0; 
                    }
                    QTabWidget::pane {
                        background: transparent;
                        border: none;
                    }
                """)
        self.frame_size = self.frameGeometry()
        self.tab_widget.tabBar().setMinimumWidth(self.frame_size.size().width() * 0.3)
        self.tab_widget.tabBar().setObjectName("tab_bar_device_screen")
        
        self.tab_widget.tabBar().currentChanged.connect(self.tab_changed)
        # Connect the tabCloseRequested signal to a custom slot
        self.tab_widget.tabBar().tabCloseRequested.connect(self.close_tab)


    def tab_changed(self, index):
        self.signal_change_tab.emit(index)

    def close_tab(self, index):
        self.tab_widget.tabBar().removeTab(index)

    def add_Tab(self, tab_name):
        self.tab_widget.tabBar().addTab(tab_name)
        self.tab_widget.setCurrentIndex(self.tab_widget.count() - 1)

    def set_dynamic_stylesheet(self):
        self.tab_widget.tabBar().setStyleSheet(
            f"""
                    QTabBar::pane#tab_bar_device_screen {{
                        background: transparent;
                        border: none;
                        font-weight: None
                    }}
                    QTabBar::tab#tab_bar_device_screen {{
                        background-color: {main_controller.get_theme_attribute("Color", "tabbar_background_normal")};
                        /*color: {main_controller.get_theme_attribute("Color", "test")};*/
                        padding: 8px 12px;
                        border: 1px solid red;
                        font-weight: None;
                        border: None;
                    }}
                    QTabBar::tab:selected#tab_bar_device_screen {{
                        background-color: {main_controller.get_theme_attribute("Color", "tabbar_background_selected")};
                        color: {main_controller.get_theme_attribute("Color", "tabbar_text_selected")};
                        border: 1px solid {main_controller.get_theme_attribute('Color','common_border')};
                        padding: 8px 12px;
                        font-weight: bold;
                    }}
                    QTabBar::scroller {{width: 0px;}}
                    QTabBar QToolButton {{ /* the scroll buttons are tool buttons */
                        width: 0;
                        border-width: 0px;
                    }}
                    QTabBar::tear {{
                        width: 0px; 
                        border: none;
                    }}
                    """
        )

        self.widget_button_system.set_dynamic_stylesheet()

import re
from functools import partial
from urllib.parse import urlparse
import threading
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QAbstractItemView
from src.presentation.device_management_screen.widget.list_custom_widgets import SpinBoxWithTitle, InputWithTitle, \
    InputWithTitleAndIPValidator, ComboBoxNewStyle
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox, QStackedWidget, QSizePolicy

from src.utils.config import Config
from src.common.widget.dialogs.base_dialog import NewBaseDialog
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from src.common.model.group_model import GroupModelManager
from src.common.model.camera_model import Camera, filter_camera_model, camera_model_manager
from PySide6.QtCore import Qt
from PySide6.QtGui import QStandardItem, QStandardItemModel, QColor, QGuiApplication, QBrush
import logging
from src.common.widget.custom_qtable_view import TableWithCustomHeader, LoadingAnimationBar
from src.common.widget.custom_checkbox_dialog import CustomCheckBoxDialogs
from src.common.model.scan_camera_model import CameraData
logger = logging.getLogger(__name__)

class AddCameraDialog(NewBaseDialog):

    def __init__(self, parent=None):
        self.block_signal = False
        self.list_camera_subnet = []
        self.list_camera_know_address = []
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.rows_init_table = 4
        self.column_init_table = 5
        self.is_programmatically_changing_combobox = True
        self.style_sheet_active_button = Style.PrimaryStyleSheet.get_dialog_tabbutton_active_style(theme_instance=main_controller)
        self.style_sheet_inactive_button = Style.PrimaryStyleSheet.get_dialog_tabbutton_inactive_style(theme_instance=main_controller)

        self.create_content_widget()
        self.load_ui()
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_MEDIUM)
        widget_main.setLayout(self.layout_dialog)
        title = self.tr("ADD CAMERAS")
        super().__init__(parent, title=title, content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_MEDIUM, min_height_dialog=705)
        self.setObjectName("AddCameraDialog")
        self.update_style()
        self.save_update_signal.connect(self.save_clicked)
        # main_controller.scan_result.connect(self.scan_result)

    def scan_result(self, data):
        logger.info(f'scan_result = {type(data)}')
        if data != "Done":
            cameraModel = CameraData.fromDict(model_dict=data)
            self.add_result_search_camera_to_table(cameraModel)
        else:
            self.add_result_search_camera_to_table(data)
    def load_ui(self):
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setContentsMargins(0, 0, 0, 0)
        self.layout_dialog.setSpacing(0)
        self.layout_dialog.addWidget(self.content_widget)

    def create_content_widget(self):
        self.create_layout_main_content()
        self.content_widget = QWidget()
        self.layout_content_widget = QVBoxLayout()
        self.layout_content_widget.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_content_widget.addLayout(self.layout_tab_content)
        self.content_widget.setLayout(self.layout_content_widget)

    def create_layout_main_content(self):
        self.create_widget_known_address()
        self.create_widget_subnet_scan()

        self.layout_tab_content = QVBoxLayout()
        self.layout_tab_content.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout_tab_content.setSpacing(0)

        self.widget_button_top = QWidget()
        self.widget_button_top.setStyleSheet(f"border-bottom: 1px solid {Style.PrimaryColor.button_second_background}")
        self.layout_button_top = QHBoxLayout(self.widget_button_top)
        self.layout_button_top.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignBottom)
        self.layout_button_top.setSpacing(0)
        self.layout_button_top.setContentsMargins(0, 0, 0, 0)
        widget_test = QWidget()
        layout_contain_button_top = QHBoxLayout(widget_test)
        layout_contain_button_top.addWidget(self.widget_button_top)

        self.button_known_address = QPushButton(self.tr('Known Address'))
        self.button_known_address.setFixedHeight(30)
        self.button_known_address.setStyleSheet(self.style_sheet_active_button)
        self.button_known_address.clicked.connect(self.btn_known_address_clicked)
        self.button_subnet_scan = QPushButton(self.tr('Subnet Scan'))
        self.button_subnet_scan.setFixedHeight(30)
        self.button_subnet_scan.setStyleSheet(self.style_sheet_inactive_button)
        self.button_subnet_scan.clicked.connect(self.btn_subnet_scan_clicked)

        self.layout_button_top.addWidget(self.button_known_address)
        self.layout_button_top.addWidget(self.button_subnet_scan)

        self.stacked_widget_main = QStackedWidget()
        self.stacked_widget_main.setContentsMargins(0, 0, 0, 0)
        self.stacked_widget_main.addWidget(self.widget_content_known_address)
        self.stacked_widget_main.addWidget(self.widget_subnet_scan)

        self.layout_tab_content.addWidget(widget_test)
        self.layout_tab_content.addWidget(self.stacked_widget_main)

    def create_widget_known_address(self):
        self.widget_content_known_address = QWidget()
        self.widget_content_known_address.setObjectName('widget_content_known_address')
        self.widget_content_known_address.setStyleSheet('background-color: transparent')
        self.layout_content_known_address = QVBoxLayout(self.widget_content_known_address)
        self.layout_content_known_address.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_content_known_address.setSpacing(10)

        self.widget_address_know_address = QWidget()
        layout_address = QHBoxLayout(self.widget_address_know_address)
        layout_address.setContentsMargins(0, 0, 0, 0)
        self.known_address_input_IP = InputWithTitle(title=self.tr("Address"), text_placeholder=self.tr('IP/Hostname/RTSP/UDP Link'))
        self.port_know_address = SpinBoxWithTitle(title=self.tr('Port'))
        self.port_know_address.setDisabled(True)

        layout_checkbox = QVBoxLayout()
        layout_checkbox.setAlignment(Qt.AlignmentFlag.AlignBottom)
        layout_checkbox.setContentsMargins(0, 0, 0, 0)
        self.known_address_checkbox_port = QCheckBox(self.tr('Default'))
        self.known_address_checkbox_port.setStyleSheet(f"""
            QCheckBox{{
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")};
            }}
            """)
        self.known_address_checkbox_port.setChecked(True)
        self.known_address_checkbox_port.stateChanged.connect(self.knownaddress_checkbox_port_state_change)
        layout_checkbox.addWidget(self.known_address_checkbox_port)

        layout_address.addWidget(self.known_address_input_IP, 90)
        layout_address.addWidget(self.port_know_address, 5)
        layout_address.addLayout(layout_checkbox, 5)

        layout_user_infor = QHBoxLayout()
        layout_user_infor.setContentsMargins(0, 0, 0, 0)
        layout_user_infor.setAlignment(Qt.AlignmentFlag.AlignBottom)
        self.know_address_input_username = InputWithTitle(title=self.tr("Login"), text_placeholder=self.tr('Username'))
        self.know_address_input_password = InputWithTitle(title=self.tr("Password"), text_placeholder=self.tr('Password'), is_password_line=True)

        layout_button_search = QVBoxLayout()
        layout_button_search.setContentsMargins(0, 0, 0, 0)
        layout_button_search.setAlignment(Qt.AlignmentFlag.AlignBottom)
        self.button_search_know_address = QPushButton(self.tr('Search'))
        self.button_search_know_address.setFixedHeight(self.know_address_input_password.line_edit.sizeHint().height())
        self.button_search_know_address.clicked.connect(self.search_or_scan_clicked)
        self.button_search_know_address.setStyleSheet(f'''
            QPushButton {{
                background-color: {Style.PrimaryColor.primary};
                color: white;
                border: None;
                border-radius: 4px;   
            }}
            QPushButton::hover {{
                background-color: {Style.PrimaryColor.primary};
                color: white;
                border: None;
                border-radius: 4px;   
            }}
            QPushButton::pressed {{
                background-color: {Style.PrimaryColor.button_color};
                color: white;
                border: None;
                border-radius: 4px;   
            }}
        ''')
        layout_button_search.addWidget(self.button_search_know_address)

        layout_user_infor.addWidget(self.know_address_input_username, 40)
        layout_user_infor.addWidget(self.know_address_input_password, 40)
        layout_user_infor.addLayout(layout_button_search, 10)

        # connect Press Enter
        self.known_address_input_IP.line_edit.returnPressed.connect(self.know_address_input_username.line_edit.setFocus)
        self.know_address_input_username.line_edit.returnPressed.connect(
            self.know_address_input_password.line_edit.setFocus)
        self.know_address_input_password.line_edit.returnPressed.connect(self.search_or_scan_clicked)

        self.widget_input_to_search = QWidget()
        self.layout_input_to_search = QVBoxLayout(self.widget_input_to_search)
        self.layout_input_to_search.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_input_to_search.setContentsMargins(0, 0, 0, 0)

        self.progress_loading_know_address = LoadingAnimationBar()
        layout_animation_and_warning = QHBoxLayout()
        layout_animation_and_warning.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout_animation_and_warning.setContentsMargins(0, 0, 0, 0)
        self.label_warning_known_address = QLabel(self.tr('Please enter all required information'))
        self.label_warning_known_address.setObjectName('warning_label')
        self.label_warning_known_address.setVisible(False)
        layout_animation_and_warning.addWidget(self.label_warning_known_address)
        layout_animation_and_warning.addWidget(self.progress_loading_know_address)

        self.layout_input_to_search.addWidget(self.widget_address_know_address, 40)
        self.layout_input_to_search.addLayout(layout_user_infor, 40)
        self.layout_input_to_search.addLayout(layout_animation_and_warning, 10)

        widget_table_result_search = QWidget()
        layout_table_result_search = QVBoxLayout(widget_table_result_search)
        layout_table_result_search.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
        layout_table_result_search.setContentsMargins(0, 0, 0, 0)
        list_horizontal_header = ["", self.tr("BRAND"), self.tr("MODEL"), self.tr("ADDRESS"), self.tr("STATUS")]
        widget_checkbox = QWidget()
        layout_checkbox = QVBoxLayout()
        layout_checkbox.setContentsMargins(0, 0, 0, 0)
        layout_checkbox.setAlignment(Qt.AlignmentFlag.AlignCenter)
        checkbox = QCheckBox()
        checkbox.setTristate(False)
        checkbox.setStyleSheet(f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            """)
        checkbox.stateChanged.connect(self.check_all)
        checkbox.setTristate(False)
        layout_checkbox.addWidget(checkbox)
        widget_checkbox.setLayout(layout_checkbox)
        list_widget_for_header = {0: widget_checkbox}

        self.model_table_known_address = QStandardItemModel()
        self.table_result_known_address = TableWithCustomHeader(horizontal_label_list=list_horizontal_header,
                                                                list_widget_to_header=list_widget_for_header,
                                                                model_for_table=self.model_table_known_address,
                                                                use_stylesheet_header=True)
        self.table_result_known_address.table.clicked.connect(
            partial(self.on_clicked_table, model_table=self.model_table_known_address, table=self.table_result_known_address.table, checkbox_all=checkbox))
        self.table_result_known_address.table.setColumnWidth(0, 44)
        self.table_result_known_address.table.setColumnWidth(1, 170)
        self.table_result_known_address.table.setColumnWidth(2, 180)
        self.table_result_known_address.table.setColumnWidth(3, 400)
        self.table_result_known_address.table.setColumnWidth(4, 90)
        self.table_result_known_address.table.verticalHeader().setVisible(False)
        self.table_result_known_address.table.verticalHeader().setDefaultSectionSize(38)
        self.table_result_known_address.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_result_known_address.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table_result_known_address.table.setSelectionMode(QAbstractItemView.SelectionMode.NoSelection)
        self.table_result_known_address.table.setFocusPolicy(Qt.NoFocus)
        self.table_result_known_address.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.table_result_known_address.table.setShowGrid(False)

        widget_total_device_in_known_address = QWidget()
        self.label_total_device_know_address = QLabel()
        self.label_total_device_know_address.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout_group_with_combobox = QHBoxLayout()
        layout_group_with_combobox.setContentsMargins(0, 0, 0, 0)
        layout_group_with_combobox.setSpacing(10)
        layout_group_with_combobox.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.label_group_known_address = QLabel(self.tr('Add to Groups:'))
        self.known_address_combo_box_group = ComboBoxNewStyle()
        self.known_address_combo_box_group.setFixedSize(200, 28)

        # Create a QStandardItemModel
        self.model_know_address_combobox = QStandardItemModel()
        item_select = QStandardItem(self.tr('Select group'))
        self.model_know_address_combobox.appendRow(item_select)
        group_list = GroupModelManager.get_instance().get_group_list(server_ip=main_controller.current_controller.server.data.server_ip)
        for group_model in group_list.values():
            item = QStandardItem(group_model.get_property('name'))
            item.setData(group_model.get_property('id'), Config.USER_ROLE_COMBOBOX)
            self.model_know_address_combobox.appendRow(item)
        self.known_address_combo_box_group.setModel(self.model_know_address_combobox)

        self.label_warning_choose_group_known = QLabel(self.tr('Please choose a group to add cameras.'))
        self.label_warning_choose_group_known.setStyleSheet('''
                    QLabel {
                        color: #B5122E;
                        font-style: italic;
                    }
                ''')
        self.label_warning_choose_group_known.setVisible(False)
        layout_group_with_combobox.addWidget(self.label_group_known_address)
        layout_group_with_combobox.addWidget(self.known_address_combo_box_group)
        layout_group_with_combobox.addWidget(self.label_warning_choose_group_known)
        layout_group_with_total_device = QHBoxLayout(widget_total_device_in_known_address)
        layout_group_with_total_device.setAlignment(Qt.AlignmentFlag.AlignBottom)
        layout_group_with_total_device.setContentsMargins(0, 0, 0, 4)
        layout_group_with_total_device.setSpacing(2)
        layout_group_with_total_device.addLayout(layout_group_with_combobox)
        layout_group_with_total_device.addWidget(self.label_total_device_know_address)

        layout_table_result_search.addWidget(self.table_result_known_address, 90)
        layout_table_result_search.addWidget(widget_total_device_in_known_address)

        widget_no_data = QWidget()
        layout_no_data = QHBoxLayout(widget_no_data)
        layout_no_data.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # Create a QSvgWidget
        svg_widget = QSvgWidget()
        svg_widget.load(main_controller.get_theme_attribute("Image", "no_data_image"))
        self.label_know_address_no_data = QLabel(self.tr('No Data'))
        self.label_know_address_no_data.setStyleSheet(f'font-size: {Style.Size.body_large}px; '
                                                      f'font-weight: bold; color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")}')
        layout_no_data.addWidget(svg_widget)
        layout_no_data.addWidget(self.label_know_address_no_data)

        widget_searching = QWidget()
        layout_searching = QVBoxLayout(widget_searching)
        layout_searching.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label_searching = QLabel(self.tr("Searching"))
        layout_searching.addWidget(label_searching)

        self.know_address_stacked_widget_result_search = QStackedWidget()
        self.know_address_stacked_widget_result_search.setContentsMargins(0, 0, 0, 0)
        self.know_address_stacked_widget_result_search.setObjectName("stacked_result")
        self.know_address_stacked_widget_result_search.addWidget(widget_no_data)  # 0
        self.know_address_stacked_widget_result_search.addWidget(widget_searching)  # 1
        self.know_address_stacked_widget_result_search.addWidget(widget_table_result_search)  # 2

        self.layout_content_known_address.addWidget(self.widget_input_to_search, 10)
        self.layout_content_known_address.addWidget(self.know_address_stacked_widget_result_search, 90)

    def create_widget_subnet_scan(self):
        self.widget_subnet_scan = QWidget()
        self.widget_subnet_scan.setObjectName('widget_subnet_scan')
        self.widget_subnet_scan.setStyleSheet('background-color: transparent')
        self.layout_content_subnet_scan = QVBoxLayout(self.widget_subnet_scan)
        self.layout_content_subnet_scan.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_content_subnet_scan.setSpacing(10)

        self.subnet_widget_address_IP = QWidget()
        layout_address = QHBoxLayout(self.subnet_widget_address_IP)
        layout_address.setContentsMargins(0, 0, 0, 0)
        self.subnet_from_IP = InputWithTitleAndIPValidator(title=self.tr("From IP"), text_placeholder=self.tr('0.0.0.0'))
        self.subnet_to_IP = InputWithTitleAndIPValidator(title=self.tr("To IP"), text_placeholder=self.tr('*********'))
        # self.subnet_from_IP.line_edit.textChanged.connect(self.sync_from_ip_to_ip)
        # self.subnet_to_IP.line_edit.textChanged.connect(self.sync_to_ip_to_from_ip)
        self.port_subnet = SpinBoxWithTitle(title=self.tr('Port'))
        self.port_subnet.setDisabled(True)

        layout_checkbox = QVBoxLayout()
        layout_checkbox.setAlignment(Qt.AlignmentFlag.AlignBottom)
        layout_checkbox.setContentsMargins(0, 0, 0, 0)
        self.subnet_checkbox_port = QCheckBox(self.tr('Default'))
        self.subnet_checkbox_port.setStyleSheet(f"""
            QCheckBox{{
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")};
            }}
            """)
        self.subnet_checkbox_port.setChecked(True)
        self.subnet_checkbox_port.stateChanged.connect(self.subnet_checkbox_port_state_change)
        layout_checkbox.addWidget(self.subnet_checkbox_port)

        layout_address.addWidget(self.subnet_from_IP, 45)
        layout_address.addWidget(self.subnet_to_IP, 45)
        layout_address.addWidget(self.port_subnet, 5)
        layout_address.addLayout(layout_checkbox, 5)

        layout_user_infor = QHBoxLayout()
        layout_user_infor.setContentsMargins(0, 0, 0, 0)
        layout_user_infor.setAlignment(Qt.AlignmentFlag.AlignBottom)
        self.subnet_input_username = InputWithTitle(title=self.tr("Login"), text_placeholder=self.tr('Username'))
        self.subnet_input_password = InputWithTitle(title=self.tr("Password"), text_placeholder=self.tr('Password'), is_password_line=True)

        # connect Press Enter
        self.subnet_from_IP.line_edit.returnPressed.connect(self.subnet_to_IP.line_edit.setFocus)
        self.subnet_to_IP.line_edit.returnPressed.connect(self.subnet_input_username.line_edit.setFocus)
        self.subnet_input_username.line_edit.returnPressed.connect(self.subnet_input_password.line_edit.setFocus)
        self.subnet_input_password.line_edit.returnPressed.connect(self.search_or_scan_clicked)

        layout_button_scan = QVBoxLayout()
        layout_button_scan.setContentsMargins(0, 0, 0, 0)
        layout_button_scan.setAlignment(Qt.AlignmentFlag.AlignBottom)
        self.button_scan = QPushButton(self.tr('Scan'))
        self.button_scan.setFixedHeight(self.subnet_input_password.line_edit.sizeHint().height())
        self.button_scan.setStyleSheet(f'''
            QPushButton {{
                background-color: {Style.PrimaryColor.primary};
                color: white;
                border: None;
                border-radius: 4px;   
            }}
            QPushButton:hover {{
                background-color: {Style.PrimaryColor.primary};
                color: white;
                border: None;
                border-radius: 4px;   
            }}
            QPushButton:pressed {{
                background-color: {Style.PrimaryColor.primary};
                color: white;
                border: None;
                border-radius: 4px;   
            }}
        ''')
        self.button_scan.clicked.connect(self.search_or_scan_clicked)
        layout_button_scan.addWidget(self.button_scan)

        layout_user_infor.addWidget(self.subnet_input_username, 40)
        layout_user_infor.addWidget(self.subnet_input_password, 40)
        layout_user_infor.addLayout(layout_button_scan, 10)

        self.progress_scan = LoadingAnimationBar()
        layout_animation_warning = QVBoxLayout()
        layout_animation_warning.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout_animation_warning.setContentsMargins(0, 0, 0, 0)
        self.label_warning_scan = QLabel(self.tr('Please enter all required information'))
        self.label_warning_scan.setObjectName('warning_label')
        self.label_warning_scan.setVisible(False)
        layout_animation_warning.addWidget(self.label_warning_scan)
        layout_animation_warning.addWidget(self.progress_scan)

        self.widget_input_to_scan = QWidget()
        self.layout_input_to_scan = QVBoxLayout(self.widget_input_to_scan)
        self.layout_input_to_scan.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_input_to_scan.setContentsMargins(0, 0, 0, 0)
        self.layout_input_to_scan.addWidget(self.subnet_widget_address_IP)
        self.layout_input_to_scan.addLayout(layout_user_infor)
        self.layout_input_to_scan.addLayout(layout_animation_warning)

        widget_table_result_scan = QWidget()
        layout_table_result_scan = QVBoxLayout(widget_table_result_scan)
        layout_table_result_scan.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout_table_result_scan.setContentsMargins(0, 0, 0, 0)
        list_horizontal_header = ["", self.tr("BRAND"), self.tr("MODEL"), self.tr("ADDRESS"), self.tr("STATUS")]
        widget_checkbox = QWidget()
        layout_checkbox = QVBoxLayout()
        layout_checkbox.setContentsMargins(0, 0, 0, 0)
        layout_checkbox.setAlignment(Qt.AlignmentFlag.AlignCenter)
        checkbox = QCheckBox()
        checkbox.stateChanged.connect(self.check_all)
        checkbox.setTristate(False)
        checkbox.setStyleSheet(f"""
                    QCheckBox::indicator:checked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
                    QCheckBox::indicator:unchecked {{
                        border: none;
                        image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                        background-color: transparent;
                        width: 16px;
                        height: 16px;
                    }}
                    """)
        checkbox.setTristate(False)
        layout_checkbox.addWidget(checkbox)
        widget_checkbox.setLayout(layout_checkbox)
        list_widget_for_header = {0: widget_checkbox}

        self.model_table_scan = QStandardItemModel()
        self.table_result_scan = TableWithCustomHeader(horizontal_label_list=list_horizontal_header,
                                                       list_widget_to_header=list_widget_for_header,
                                                       model_for_table=self.model_table_scan,
                                                       use_stylesheet_header=True)
        self.table_result_scan.table.clicked.connect(partial(self.on_clicked_table, model_table=self.model_table_scan, table=self.table_result_scan.table, checkbox_all=checkbox))
        self.table_result_scan.table.setColumnWidth(0, 44)
        self.table_result_scan.table.setColumnWidth(1, 170)
        self.table_result_scan.table.setColumnWidth(2, 180)
        self.table_result_scan.table.setColumnWidth(3, 400)
        self.table_result_scan.table.setColumnWidth(4, 90)
        self.table_result_scan.table.verticalHeader().setVisible(False)
        self.table_result_scan.table.verticalHeader().setDefaultSectionSize(38)
        self.table_result_scan.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_result_scan.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table_result_scan.table.setSelectionMode(QAbstractItemView.SelectionMode.NoSelection)
        self.table_result_scan.table.setFocusPolicy(Qt.NoFocus)
        self.table_result_scan.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.table_result_scan.table.setShowGrid(False)

        widget_total_device_in_subnet_scan = QWidget()
        self.label_total_device_subnet_scan = QLabel()
        self.label_total_device_subnet_scan.setAlignment(Qt.AlignmentFlag.AlignRight)
        layout_group_with_combobox = QHBoxLayout()
        layout_group_with_combobox.setContentsMargins(0, 0, 0, 0)
        layout_group_with_combobox.setSpacing(10)
        layout_group_with_combobox.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.label_group_subnet = QLabel(self.tr('Add to Groups:'))
        self.subnet_scan_combo_box_group = ComboBoxNewStyle()
        self.subnet_scan_combo_box_group.setFixedSize(200, 28)
        # Create a QStandardItemModel
        self.model_subnet_scan_combobox = QStandardItemModel()
        item_select = QStandardItem(self.tr('Select group'))
        self.model_subnet_scan_combobox.appendRow(item_select)
        group_list = GroupModelManager.get_instance().get_group_list(server_ip=main_controller.current_controller.server.data.server_ip)
        for group_model in group_list.values():
            item = QStandardItem(group_model.get_property('name'))
            item.setData(group_model.get_property('id'), Config.USER_ROLE_COMBOBOX)
            self.model_subnet_scan_combobox.appendRow(item)
        # Set the model for the ComboBox
        self.subnet_scan_combo_box_group.setModel(self.model_subnet_scan_combobox)

        self.label_warning_choose_group_subnet = QLabel(self.tr('Please choose a group to add cameras.'))
        self.label_warning_choose_group_subnet.setStyleSheet('''
                            QLabel {
                                color: #B5122E;
                                font-style: italic;
                            }
                        ''')
        self.label_warning_choose_group_subnet.setVisible(False)
        layout_group_with_combobox.addWidget(self.label_group_subnet)
        layout_group_with_combobox.addWidget(self.subnet_scan_combo_box_group)
        layout_group_with_combobox.addWidget(self.label_warning_choose_group_subnet)
        layout_group_with_total_device = QHBoxLayout(widget_total_device_in_subnet_scan)
        layout_group_with_total_device.setContentsMargins(0, 0, 0, 4)
        layout_group_with_total_device.setAlignment(Qt.AlignmentFlag.AlignBottom)
        layout_group_with_total_device.setSpacing(2)
        layout_group_with_total_device.addLayout(layout_group_with_combobox)
        layout_group_with_total_device.addWidget(self.label_total_device_subnet_scan)

        layout_table_result_scan.addWidget(self.table_result_scan, 90)
        layout_table_result_scan.addWidget(widget_total_device_in_subnet_scan)

        widget_no_data = QWidget()
        layout_no_data = QHBoxLayout(widget_no_data)
        layout_no_data.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # Create a QSvgWidget
        svg_widget = QSvgWidget()
        svg_widget.load(main_controller.get_theme_attribute("Image", "no_data_image"))
        label_know_address_no_data = QLabel(self.tr('No Data'))
        label_know_address_no_data.setStyleSheet(f'font-size: {Style.Size.body_large}px; font-weight: bold; color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")}')
        layout_no_data.addWidget(svg_widget)
        layout_no_data.addWidget(label_know_address_no_data)

        widget_searching = QWidget()
        layout_searching = QVBoxLayout(widget_searching)
        layout_searching.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label_searching = QLabel(self.tr("Scanning"))
        layout_searching.addWidget(label_searching)

        self.subnet_stacked_widget_result_search = QStackedWidget()
        self.subnet_stacked_widget_result_search.setObjectName("stacked_result")
        self.subnet_stacked_widget_result_search.addWidget(widget_no_data)  # 0
        self.subnet_stacked_widget_result_search.addWidget(widget_searching)  # 1
        self.subnet_stacked_widget_result_search.addWidget(widget_table_result_scan)  # 2

        # self.layout_content_subnet_scan.addWidget(self.subnet_stacked_widget_result_search)

        self.layout_content_subnet_scan.addWidget(self.widget_input_to_scan, 10)
        self.layout_content_subnet_scan.addWidget(self.subnet_stacked_widget_result_search, 90)

    def subnet_checkbox_port_state_change(self, state):
        if state == Qt.Checked.value:
            self.port_subnet.spin_box.setValue(80)
            self.port_subnet.setDisabled(True)
        else:
            self.port_subnet.setDisabled(False)

    def knownaddress_checkbox_port_state_change(self, state):
        if state == Qt.Checked.value:
            self.port_know_address.spin_box.setValue(80)
            self.port_know_address.setDisabled(True)
        else:
            self.port_know_address.setDisabled(False)


    def btn_known_address_clicked(self):
        self.stacked_widget_main.setCurrentIndex(0)
        self.button_known_address.setStyleSheet(self.style_sheet_active_button)
        self.button_subnet_scan.setStyleSheet(self.style_sheet_inactive_button)

    def btn_subnet_scan_clicked(self):
        self.stacked_widget_main.setCurrentIndex(1)
        self.button_subnet_scan.setStyleSheet(self.style_sheet_active_button)
        self.button_known_address.setStyleSheet(self.style_sheet_inactive_button)

    def update_style(self):
        self.setStyleSheet(
            f'''
                QWidget {{
                    background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                    color: {Style.PrimaryColor.white_2};
                }}
                QLabel {{
                    color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                }}
            
                QLabel:disabled {{
                    color: gray;
                    font-style: italic;
                }}
                
                QLineEdit:disabled {{
                    color: gray;
                    background-color: lightgray;
                }}
                
                QCheckBox:disabled {{
                    color: gray;
                }}
                QLabel#warning_label {{
                    color: {Style.PrimaryColor.primary};
                    font-style: italic;
                }}
                QStackedWidget#stacked_result {{
                    border: 2px dashed {main_controller.get_theme_attribute("Color", "main_border")}; 
                    border-radius: 4px; 
                    padding: 4px;
                }}
                QCheckBox::indicator:checked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                    background-color: transparent;
                    width: 16px;
                    height: 16px;
                }}
                QCheckBox::indicator:unchecked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                    background-color: {Style.PrimaryColor.on_background};
                    width: 16px;
                    height: 16px;
                }}
                '''
        )

    def clear_model(self, model: QStandardItemModel, table: TableWithCustomHeader):
        model.clear()
        table.set_horizontal_header()
        table.table.setColumnWidth(0, 44)
        table.table.setColumnWidth(1, 170)
        table.table.setColumnWidth(2, 180)
        table.table.setColumnWidth(3, 400)
        table.table.setColumnWidth(4, 90)

    def search_or_scan_clicked(self):
        if self.stacked_widget_main.currentWidget().objectName() == 'widget_content_known_address':
            self.button_search_know_address.clearFocus()
            if self.button_search_know_address.text() == self.tr('Search'):
                main_controller.scan_result.connect(self.scan_result)
                if not self.known_address_input_IP.text():
                    self.label_warning_known_address.setVisible(True)
                    return
                else:
                    if self.is_valid_ip(self.known_address_input_IP.text()):
                        # la IP
                        # if not self.know_address_input_password.text() or not self.know_address_input_username.text():
                        #     self.label_warning_known_address.setText(self.tr('Please enter all required information'))
                        #     self.label_warning_known_address.setVisible(True)
                        #     return
                        pass
                    elif self.is_rtsp_link(self.known_address_input_IP.text()):
                        pass
                    else:
                        self.label_warning_known_address.setText(self.tr('Please enter correct IP or RTSP format'))
                        self.label_warning_known_address.setVisible(True)
                        return
                if self.label_warning_known_address.isVisible():
                    self.label_warning_known_address.setVisible(False)
                # start search here
                self.list_camera_know_address.clear()
                from_ip = self.known_address_input_IP.text()
                if self.know_address_input_username.text() != '':
                    user_name = [x for x in self.know_address_input_username.text().split(',')]
                else:
                    user_name = None
                if self.know_address_input_password.text() != '':
                    password = [x for x in self.know_address_input_password.text().split(',')]
                else:
                    password = None
                data = {
                    "fromIp": f"{from_ip}",
                    "toIp": None,
                    "usernames": user_name,
                    "passwords": password
                }
                self.clear_model(self.model_table_known_address, self.table_result_known_address)
                main_controller.current_controller.id_event_search_camera = None
                # main_controller.current_controller.search_camera(self, data)
                # main_controller.current_controller.api_client.search_single(data = data,signal = main_controller.scan_result)
                threading.Thread(target=main_controller.current_controller.api_client.search_single, args=(data,main_controller.scan_result,)).start()
                
                
                self.button_search_know_address.clearFocus()
                self.widget_button_top.setDisabled(True)
                self.progress_loading_know_address.start_loading()
                self.widget_address_know_address.setDisabled(True)
                self.know_address_input_username.setDisabled(True)
                self.know_address_input_password.setDisabled(True)
                self.known_address_checkbox_port.setDisabled(True)
                self.disable_common_component(True)
                self.button_search_know_address.setText(self.tr('Stop'))
                self.know_address_stacked_widget_result_search.setCurrentIndex(1)
            else:
                main_controller.scan_result.disconnect(self.scan_result)
                main_controller.current_controller.id_event_search_camera = None
                self.widget_button_top.setDisabled(False)
                self.label_warning_known_address.setVisible(False)
                self.progress_loading_know_address.stop_loading()
                self.widget_address_know_address.setDisabled(False)
                self.know_address_input_username.setDisabled(False)
                self.know_address_input_password.setDisabled(False)
                self.known_address_checkbox_port.setDisabled(False)
                self.disable_common_component(False)
                self.button_search_know_address.setText(self.tr('Search'))
                # cancel search here
                if self.model_table_known_address.rowCount() == 0:
                    self.know_address_stacked_widget_result_search.setCurrentIndex(0)
                

        else:
            self.button_scan.clearFocus()
            if self.button_scan.text() == self.tr('Scan'):
                main_controller.scan_result.connect(self.scan_result)
                if not self.subnet_from_IP.text() or not self.subnet_to_IP.text():
                    self.label_warning_scan.setVisible(True)
                    return
                if self.label_warning_scan.isVisible():
                    self.label_warning_scan.setVisible(False)
                self.list_camera_subnet.clear()
                # start scan here
                from_ip = self.subnet_from_IP.text()
                to_ip = self.subnet_to_IP.text()
                if self.subnet_input_username.text() != '':
                    user_name = [x for x in self.subnet_input_username.text().split(',')]
                else:
                    user_name = None
                if self.subnet_input_password.text() != '':
                    password = [x for x in self.subnet_input_password.text().split(',')]
                else:
                    password = None

                data = {
                    "fromIp": f"{from_ip}",
                    "toIp": f"{to_ip}",
                    "usernames": user_name,
                    "passwords": password
                }
                self.clear_model(self.model_table_scan, self.table_result_scan)
                main_controller.current_controller.id_event_search_camera = None
                # main_controller.current_controller.search_camera(self, data)
                threading.Thread(target=main_controller.current_controller.api_client.search_single, args=(data,main_controller.scan_result,)).start()
                self.widget_button_top.setDisabled(True)
                self.progress_scan.start_loading()
                self.subnet_widget_address_IP.setDisabled(True)
                self.subnet_input_username.setDisabled(True)
                self.subnet_input_password.setDisabled(True)
                self.subnet_checkbox_port.setDisabled(True)
                self.disable_common_component(True)
                self.button_scan.setText(self.tr('Stop'))
                self.subnet_stacked_widget_result_search.setCurrentIndex(1)
            else:
                main_controller.current_controller.id_event_search_camera = None
                main_controller.scan_result.disconnect(self.scan_result)
                self.widget_button_top.setDisabled(False)
                self.label_warning_scan.setVisible(False)
                self.progress_scan.stop_loading()
                self.disable_common_component(False)
                self.subnet_widget_address_IP.setDisabled(False)
                self.subnet_input_username.setDisabled(False)
                self.subnet_input_password.setDisabled(False)
                self.subnet_checkbox_port.setDisabled(False)
                self.button_scan.setText(self.tr('Scan'))
                # stop scan here
                if self.model_table_scan.rowCount() == 0:
                    self.subnet_stacked_widget_result_search.setCurrentIndex(0)
        pass

    def save_clicked(self):
        if self.stacked_widget_main.currentWidget().objectName() == 'widget_content_known_address':
            current_index = self.known_address_combo_box_group.currentIndex()
            list_rtsp = []
            for row in range(self.model_table_known_address.rowCount()):
                index = self.model_table_known_address.index(row, 0)
                item = self.table_result_known_address.table.indexWidget(index)
                if item is not None and item.checkbox.checkState() == Qt.CheckState.Checked:
                    item_rtsp = self.model_table_known_address.item(row, 3)
                    list_rtsp.append(item_rtsp.text())
            if len(list_rtsp) > 0:
                # Nếu chọn group thì add camera vào group
                if current_index > 0:
                    item = self.model_know_address_combobox.item(current_index)
                    group_id = item.data(Config.USER_ROLE_COMBOBOX)
                    list_camera_to_add = []
                    for camera in self.list_camera_know_address:
                        if camera.urlMainstream in list_rtsp:
                            camera.cameraGroupIds = [group_id]
                            list_camera_to_add.append(camera)
                    if len(list_camera_to_add) > 0:
                        # do chưa hỗ trợ add nhiều camera lên tạm thời sẽ add tuần tự
                        list_cameras = []
                        for camera in list_camera_to_add:
                            list_cameras.append({"id": camera.id,"cameraGroupIds":camera.cameraGroupIds})
                            # main_controller.current_controller.create_camera(parent=self, data=camera)
                        if len(list_cameras) > 0:
                            print(list_cameras)
                            # main_controller.current_controller.discovered_camera(data = list_cameras)
                            main_controller.current_controller.add_discovered_camera(data = list_cameras)
                # nếu không chọn group thì add camera non group
                else:
                    list_camera_to_add = []
                    logger.debug(f'self.list_camera_know_address = {self.list_camera_know_address}')
                    logger.debug(f'list_rtsp = {list_rtsp}')
                    for camera in self.list_camera_know_address:
                        if camera.urlMainstream in list_rtsp:
                            list_camera_to_add.append(camera)
                    if len(list_camera_to_add) > 0:
                        # do chưa hỗ trợ add nhiều camera lên tạm thời sẽ add tuần tự
                        list_cameras = []
                        for camera in list_camera_to_add:
                            list_cameras.append({"id": camera.id})
                            # main_controller.current_controller.create_camera(parent=self, data=camera)
                        if len(list_cameras) > 0:
                            # main_controller.current_controller.discovered_camera(data = list_cameras)
                            main_controller.current_controller.add_discovered_camera(data = list_cameras)
            self.list_camera_know_address.clear()
            self.close()
        else:
            current_index = self.subnet_scan_combo_box_group.currentIndex()
            list_rtsp = []
            for row in range(self.model_table_scan.rowCount()):
                index = self.model_table_scan.index(row, 0)
                item = self.table_result_scan.table.indexWidget(index)
                if item is not None and item.checkbox.checkState() == Qt.CheckState.Checked:
                    item_rtsp = self.model_table_scan.item(row, 3)
                    list_rtsp.append(item_rtsp.text())
            if len(list_rtsp) > 0:
                # Nếu chọn group thì add camera vào group
                if current_index > 0:
                    item = self.model_subnet_scan_combobox.item(current_index)
                    group_id = item.data(Config.USER_ROLE_COMBOBOX)
                    list_camera_to_add = []
                    for camera in self.list_camera_subnet:
                        if camera.urlMainstream in list_rtsp:
                            camera.cameraGroupIds = [group_id]
                            list_camera_to_add.append(camera)
                    if len(list_camera_to_add) > 0:
                        # do chưa hỗ trợ add nhiều camera lên tạm thời sẽ add tuần tự
                        list_cameras = []
                        for camera in list_camera_to_add:
                            list_cameras.append({"id": camera.id,"cameraGroupIds":camera.cameraGroupIds})
                            # main_controller.current_controller.create_camera(parent=self, data=camera)
                        if len(list_cameras) > 0:
                            # main_controller.current_controller.discovered_camera(data = list_cameras)
                            main_controller.current_controller.add_discovered_camera(data = list_cameras)
                # nếu không chọn group thì add camera non group
                else:
                    list_camera_to_add = []
                    for camera in self.list_camera_subnet:
                        if camera.urlMainstream in list_rtsp:
                            list_camera_to_add.append(camera)
                    if len(list_camera_to_add) > 0:
                        # do chưa hỗ trợ add nhiều camera lên tạm thời sẽ add tuần tự
                        list_cameras = []
                        for camera in list_camera_to_add:
                            list_cameras.append({"id": camera.id})
                            # main_controller.current_controller.create_camera(parent=self, data=camera)
                        if len(list_cameras) > 0:
                            # main_controller.current_controller.discovered_camera(data = list_cameras)
                            main_controller.current_controller.add_discovered_camera(data = list_cameras)
            self.list_camera_subnet.clear()
            self.close()

            # if current_index > 0:
            #     self.label_warning_choose_group_subnet.setVisible(False)
            #     item = self.model_subnet_scan_combobox.item(current_index)
            #     group_id = item.data(Config.USER_ROLE_COMBOBOX)
            #     # Add camera to group here
            #     # self.list_camera_subnet
            #     list_rtsp = []
            #     for row in range(self.model_table_scan.rowCount()):
            #         index = self.model_table_scan.index(row, 0)
            #         item = self.table_result_scan.table.indexWidget(index)
            #         if item is not None and item.checkbox.checkState() == Qt.CheckState.Checked:
            #             item_rtsp = self.model_table_scan.item(row, 3)
            #             list_rtsp.append(item_rtsp.text())
            #     list_camera_to_add = []
            #     for camera in self.list_camera_subnet:
            #         if camera.urlMainstream in list_rtsp:
            #             camera.cameraGroupIds = [group_id]
            #             list_camera_to_add.append(camera)
            #     if len(list_camera_to_add) > 0:
            #         # do chưa hỗ trợ add nhiều camera lên tạm thời sẽ add tuần tự
            #         for camera in list_camera_to_add:
            #             main_controller.create_camera(parent=self, data=camera)
            #     self.close()
            # else:
            #     self.label_warning_choose_group_subnet.setVisible(True)

    def on_clicked_table(self, index=None, model_table=None, table=None, checkbox_all=None):
        item_status = model_table.item(index.row(), 4)
        if item_status.data(Config.USER_ROLE_STATUS) == 'Added':
            return
        index_find = model_table.index(index.row(), 0)
        item = table.indexWidget(index_find)
        all_checked = True
        all_unchecked = True
        if item is not None:
            if item.checkbox.checkState() == Qt.CheckState.Checked:
                item.checkbox.setCheckState(Qt.CheckState.Unchecked)
                for row in range(model_table.rowCount()):
                    index = model_table.index(row, 0)
                    item = table.indexWidget(index)
                    if item is not None:
                        if item.checkbox.checkState() != Qt.CheckState.Checked:
                            all_unchecked = False
                            break
                if checkbox_all.checkState() == Qt.CheckState.Checked:
                    if not all_unchecked:
                        self.is_programmatically_changing_combobox = False
                        checkbox_all.setCheckState(Qt.CheckState.Unchecked)
                        self.is_programmatically_changing_combobox = True
            else:
                item.checkbox.setCheckState(Qt.CheckState.Checked)
                for row in range(model_table.rowCount()):
                    index = model_table.index(row, 0)
                    item = table.indexWidget(index)
                    if item is not None:
                        if item.checkbox.checkState() != Qt.CheckState.Checked:
                            all_checked = False

                if all_checked:
                    checkbox_all.setCheckState(Qt.CheckState.Checked)
        count = 0
        for row in range(model_table.rowCount()):
            idx = model_table.index(row, 0)
            item = table.indexWidget(idx)
            if item is not None and item.checkbox.checkState() == Qt.CheckState.Checked:
                count = count+1
        if self.stacked_widget_main.currentWidget().objectName() == 'widget_content_known_address':
            self.label_group_known_address.setText(self.tr('Add') + f' {count} ' + self.tr('Devices to Group:'))
        else:
            self.label_group_subnet.setText(self.tr('Add') + f' {count} ' + self.tr('Devices to Group:'))

    def disable_common_component(self, active):
        self.disable_ui(active)

    def add_result_search_camera_to_table(self, camera_data_model):
        self.widget_button_top.setDisabled(False)
        if self.stacked_widget_main.currentWidget().objectName() == 'widget_content_known_address':
            logger.info(f'add_result_search_camera_to_table = {camera_data_model}')
            if camera_data_model == "Done":
                logger.info(f'add_result_search_camera_to_table1 = {camera_data_model}')
                main_controller.current_controller.id_event_search_camera = None
                self.widget_address_know_address.setDisabled(False)
                self.know_address_input_username.setDisabled(False)
                self.know_address_input_password.setDisabled(False)
                self.label_warning_known_address.setVisible(False)
                self.progress_loading_know_address.stop_loading()
                self.disable_common_component(False)
                self.button_search_know_address.setText(self.tr('Search'))
            if camera_data_model is None:
                self.know_address_stacked_widget_result_search.setCurrentIndex(0)  # Show No Data message
            else:
                if camera_data_model == "Done" and self.model_table_known_address.rowCount() == 0:
                    # trường hợp không tìm thấy camera nào
                    self.know_address_stacked_widget_result_search.setCurrentIndex(0)  # Show No Data message
                else:
                    self.know_address_stacked_widget_result_search.setCurrentIndex(2)  # Show table
                    # add data camera to table
                    if camera_data_model != "Done":
                        self.filter_camera_status(camera_data_raw=camera_data_model,
                                                  model_table=self.model_table_known_address,
                                                  table=self.table_result_known_address.table,
                                                  list_camera_not_exist=self.list_camera_know_address)
                    # self.add_data_to_table(data=list_camera, model_table=self.model_table_known_address, table=self.table_result_known_address.table)
                    else:
                        self.label_total_device_know_address.setText(f'{self.model_table_known_address.rowCount()} '+self.tr('Devices'))
                        self.label_group_known_address.setText(self.tr('Add 0 Devices to Group:'))
        else:
            if camera_data_model == "Done":
                main_controller.current_controller.id_event_search_camera = None
                self.label_warning_scan.setVisible(False)
                self.progress_scan.stop_loading()
                self.disable_common_component(False)
                self.subnet_widget_address_IP.setDisabled(False)
                self.subnet_input_username.setDisabled(False)
                self.subnet_input_password.setDisabled(False)
                self.button_scan.setText(self.tr('Scan'))
            if camera_data_model is None:
                self.subnet_stacked_widget_result_search.setCurrentIndex(0)  # Show No Data message
            else:
                if camera_data_model == "Done" and self.model_table_scan.rowCount() == 0:
                    # trường hợp không tìm thấy camera nào
                    self.subnet_stacked_widget_result_search.setCurrentIndex(0)  # Show No Data message
                else:
                    self.subnet_stacked_widget_result_search.setCurrentIndex(2)  # Show table
                    # add data camera to table
                    if camera_data_model != "Done":
                        self.filter_camera_status(camera_data_raw=camera_data_model, model_table=self.model_table_scan, table=self.table_result_scan.table,
                                                  list_camera_not_exist=self.list_camera_subnet)
                    else:
                        self.label_total_device_subnet_scan.setText(f'{self.model_table_scan.rowCount()} '+self.tr('Devices'))
                        self.label_group_subnet.setText(self.tr('Add 0 Devices to Group:'))

    def filter_camera_status(self, camera_data_raw=None, model_table=None, table=None, list_camera_not_exist=[]):
        logger.info(f'filter_camera_status = {camera_data_raw}')
        camera_model_list = []
        list_camera_exist = []
        # for index, camera in enumerate(camera_data_raw):
        # ip, username, password, port,rtsp_list, name = camera
        camera_model = Camera()
        # camera_model.name = f'Camera_{camera.ip}_{index}'
        camera_model.id = camera_data_raw.id
        camera_model.name = self.format_camera_name(camera_data_raw.urlMainstream)
        camera_model.ipAddress = camera_data_raw.ip
        camera_model.username = camera_data_raw.username
        camera_model.password = camera_data_raw.password
        camera_model.port = camera_data_raw.port
        camera_model.profileToken = camera_data_raw.profileToken
        camera_model.ptzCap = camera_data_raw.ptzCap
        camera_model.cameraType = camera_data_raw.camera_type
        camera_model.cameraModel = camera_data_raw.camera_model
        camera_model.cameraBranch = camera_data_raw.manufacturer
        camera_model.urlMainstream = camera_data_raw.urlMainstream
        camera_model.urlSubstream = camera_data_raw.urlSubStream

        camera_model_list.append(camera_model)

        if len(camera_model_list) > 0:
            camera_list = camera_model_manager.get_camera_list(server_ip=main_controller.current_controller.server.data.server_ip)
            list_temp = filter_camera_model(
                camera_list=camera_model_list, camera_list_full=camera_list.values())
            if len(list_temp) > 0:
                for item in list_temp:
                    list_camera_not_exist.append(item)
            for camera in camera_model_list:
                if camera not in list_camera_not_exist:
                    list_camera_exist.append(camera)
        self.add_camera_data_to_table(camera_data=camera_model, list_camera_exist=list_camera_exist, model_table=model_table, table=table, added_status=camera_data_raw.added)

    def add_camera_data_to_table(self, camera_data=None, list_camera_exist=None, model_table: QStandardItemModel = None, table=None, added_status=False):
        row_number = model_table.rowCount()
        model_table.insertRow(row_number)
        for column in range(self.column_init_table):
            if column == 0:
                # Create a custom checkbox widget
                model_index = model_table.index(row_number, 0)
                widget = CustomCheckBoxDialogs(parent=table, index=model_index)
                widget.checkbox.setCheckable(added_status is False)
                # Add the widget to the cell in the table view
                table.setIndexWidget(model_index, widget)
            elif column == 1:
                # Set Manufacture data
                if camera_data.cameraBranch is not None:
                    item_manufacture = QStandardItem(f"{camera_data.cameraBranch}")
                else:
                    item_manufacture = QStandardItem(f"-")
                item_manufacture.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "table_row_text"))))
                item_manufacture.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                model_table.setItem(row_number, column, item_manufacture)
            elif column == 2:
                # Set Camera Model data
                if camera_data.cameraModel is not None:
                    item_camera_model = QStandardItem(f"{camera_data.cameraModel}")
                else:
                    item_camera_model = QStandardItem(f"-")
                item_camera_model.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "table_row_text"))))
                item_camera_model.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                model_table.setItem(row_number, column, item_camera_model)
            elif column == 3:
                # Set RTSP Link data
                if camera_data.urlMainstream is not None:
                    item_rtsp_link = QStandardItem(f"{camera_data.urlMainstream}")
                else:
                    item_rtsp_link = QStandardItem(f"-")
                item_rtsp_link.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "table_row_text"))))
                item_rtsp_link.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                model_table.setItem(row_number, column, item_rtsp_link)
            elif column == 4:
                # Set Status based on existence in list_camera_exist
                if added_status:
                    item_status = QStandardItem(self.tr("Added"))
                    item_status.setData('Added', Config.USER_ROLE_STATUS_ADDED)
                else:
                    item_status = QStandardItem(self.tr("New"))
                    item_status.setData('New', 2590)
                item_status.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "table_row_text"))))
                item_status.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                model_table.setItem(row_number, column, item_status)

        row_number += 1
        if camera_data in list_camera_exist:
            self.disable_row(table, row_number, model_table)

    def check_all(self, checked):
        if not self.is_programmatically_changing_combobox:
            return
        if self.stacked_widget_main.currentWidget().objectName() == 'widget_content_known_address':
            for row in range(self.model_table_known_address.rowCount()):
                index = self.model_table_known_address.index(row, 0)
                item = self.table_result_known_address.table.indexWidget(index)
                item_status = self.model_table_known_address.item(row, 4)
                if item is not None and item_status.data(Config.USER_ROLE_STATUS) is None:
                    item.checkbox.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)
        else:
            for row in range(self.model_table_scan.rowCount()):
                index = self.model_table_scan.index(row, 0)
                item = self.table_result_scan.table.indexWidget(index)
                item_status = self.model_table_scan.item(row, 4)
                if item is not None and item_status.data(Config.USER_ROLE_STATUS) is None:
                    item.checkbox.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)

    def format_camera_name(self, rtsp_url):
        parsed_url = urlparse(rtsp_url)
        ip_address = parsed_url.hostname
        path = parsed_url.path
        logger.debug(f'format_camera_name rtsp_url{rtsp_url} - {parsed_url} - {type(path)} - {path}')
        if "realmonitor" in path:
            return f"Camera_{ip_address}_realmonitor"
        else:
            # Extracting ChannelsNumber if present in the path
            match = re.search(r'/Channels/(\d+)', path)
            if match:
                channels_number = match.group(1)
                return f"Camera_{ip_address}_Channels_{channels_number}"
            else:
                return f"Camera_{rtsp_url}"

    # Function to disable a row
    def disable_row(self, table, row, model_table):
        for column in range(model_table.columnCount()):
            item = model_table.item(row, column)
            if item is not None:
                item.setFlags(item.flags() & ~Qt.ItemIsSelectable)
                item.setForeground(QBrush(QColor(217, 217, 217)))

    def is_valid_ip(self, ip):
        ip_regex = re.compile(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$')
        return ip_regex.match(ip) is not None

    def is_rtsp_link(self, text):
        # Simple check if 'rtsp' is in the text
        return 'rtsp' in text.lower()

    def sync_from_ip_to_ip(self, text):
        if self.block_signal:
            return
        self.block_signal = True

        from_segments = text.split('.')
        to_segments = self.subnet_from_IP.text().split('.')

        # Update segments in to_ip based on from_ip
        for i in range(len(from_segments)):
            if i < len(to_segments):
                to_segments[i] = from_segments[i]
            else:
                to_segments.append(from_segments[i])

        while len(to_segments) < 4:
            to_segments.append('0')

        to_segments[-1] = '255'
        self.subnet_to_IP.line_edit.setText('.'.join(to_segments[:4]))
        self.block_signal = False

    def sync_to_ip_to_from_ip(self, text):
        if self.block_signal:
            return
        self.block_signal = True

        to_segments = text.split('.')
        from_segments = self.subnet_from_IP.text().split('.')

        # Ensure from_segments has at least 4 segments
        while len(from_segments) < 4:
            from_segments.append('0')

        # Update the first three segments of from_ip based on to_ip
        for i in range(3):
            if i < len(to_segments):
                from_segments[i] = to_segments[i]

        # Preserve the last segment of from_ip
        # The last segment of from_ip should not be overwritten
        self.subnet_from_IP.line_edit.setText('.'.join(from_segments[:4]))
        self.block_signal = False

    def closeEvent(self, event):
        self.parent().new_add_camera_dialog = None

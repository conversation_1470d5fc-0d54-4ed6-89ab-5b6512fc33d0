# import re

# from src.presentation.device_management_screen.widget.list_custom_widgets import InputWithTitle, InputWithTitleAndIPValidator
# from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel
# from dataclasses import replace
# from src.common.widget.dialogs.base_dialog import NewBaseDialog
# from src.common.controller.main_controller import main_controller
# from src.styles.style import Style
# from PySide6.QtCore import Qt
# from PySide6.QtGui import QGuiApplication
# from src.common.model.door_model import Door,DoorModel


# class AddIntegratedDeviceDialog(NewBaseDialog):

#     def __init__(self, parent=None,door_model:DoorModel = None):

#         screen = QGuiApplication.primaryScreen()
#         self.desktop_screen_size = screen.availableGeometry()
#         self.door_model = door_model
#         self.style_sheet_active_button = f'''
#             QPushButton{{
#                 background-color: {Style.PrimaryColor.button_second_background};
#                 padding: 4px 16px 4px 16px;
#                 color: {Style.PrimaryColor.text_on_primary};
#                 border: None;
                
#                 border-top-left-radius: 4px;  /* Set top-left border radius */
#                 border-top-right-radius: 4px;
#             }}
#         '''
#         self.style_sheet_inactive_button = f'''
#             QPushButton{{
#                 background-color: transparent;
#                 padding: 4px 16px 4px 16px;
                
#                 color: {Style.PrimaryColor.text_not_select};
#                 border: None;
#             }}
#         '''

#         self.load_ui()
#         widget_main = QWidget()
#         widget_main.setFixedWidth(700)

#         widget_main.setLayout(self.layout_dialog)
#         if self.door_model is not None:
#             title = self.tr("EDIT INTEGRATED DEVICE")
#         else:
#             title = self.tr("ADD INTEGRATED DEVICE")
#         super().__init__(parent, title=title, content_widget=widget_main, width_dialog=700, min_height_dialog=600)
#         self.setObjectName("AddIntegratedDeviceDialog")
#         self.update_style()
#         self.save_update_signal.connect(self.save_clicked)

#     def load_ui(self):
#         content_widget = self.create_content_widget()
#         self.layout_dialog = QVBoxLayout()
#         self.layout_dialog.setContentsMargins(0, 0, 0, 0)
#         self.layout_dialog.setSpacing(0)
#         self.layout_dialog.addWidget(content_widget)

#     def create_content_widget(self):
#         if self.door_model is not None:
#             integrated_device_id = QLabel(self.tr("INTEGRATED DEVICE ID: ") + self.door_model.data.id)
#             self.door_name = InputWithTitle(title=self.tr("Device name"), is_use_arrow_menu=False)
#             self.door_name.setText(self.door_model.data.name)
#             self.device_group = InputWithTitle(title=self.tr("Device group"), is_use_arrow_menu=True)
#             self.input_gate_type = InputWithTitle(title=self.tr("Gate type"), is_use_arrow_menu=True)
#             self.input_gate_type.setText(self.door_model.data.doorType)
#             self.input_brand = InputWithTitle(title=self.tr("Brand"), is_use_arrow_menu=True)
#             self.input_brand.setText(self.door_model.data.brandName)
#             self.input_description = InputWithTitle(title=self.tr("Description"), is_use_arrow_menu=False)

#             h_layout1 = QHBoxLayout()
#             self.ip_address = InputWithTitleAndIPValidator(title=self.tr("IP address"), text_placeholder=self.tr('0.0.0.0'))
#             if self.door_model.data.ipAddress is not None:
#                 self.ip_address.line_edit.setText(self.door_model.data.ipAddress)
#             self.input_port = InputWithTitle(title=self.tr("Port"), is_use_arrow_menu=False)
#             if self.door_model.data.port is not None:
#                 self.input_port.setText(str(self.door_model.data.port))
#             h_layout1.addWidget(self.ip_address)
#             h_layout1.addWidget(self.input_port)

#             h_layout2 = QHBoxLayout()
#             self.input_username = InputWithTitle(title=self.tr("Username"), is_use_arrow_menu=False)
#             if self.door_model.data.username is not None:
#                 self.input_username.setText(self.door_model.data.username)
#             self.input_password = InputWithTitle(title=self.tr("Password"), is_use_arrow_menu=False)
#             if self.door_model.data.password is not None:
#                 self.input_password.setText(self.door_model.data.password)
#             h_layout2.addWidget(self.input_username)
#             h_layout2.addWidget(self.input_password)

#             widget = QWidget()
#             layout = QVBoxLayout()
#             layout.setAlignment(Qt.AlignmentFlag.AlignTop)
#             layout.setSpacing(10)
#             layout.addWidget(integrated_device_id)
#             layout.addWidget(self.door_name)
#             layout.addWidget(self.device_group)
#             layout.addWidget(self.input_gate_type)
#             layout.addWidget(self.input_brand)
#             layout.addWidget(self.input_description)  
#             layout.addLayout(h_layout1)
#             layout.addLayout(h_layout2)   
#             widget.setLayout(layout)
#         else:
#             self.input_partner = InputWithTitle(title=self.tr("Partner"), is_use_arrow_menu=True)
#             self.input_gate_type = InputWithTitle(title=self.tr("Gate type"), is_use_arrow_menu=True)
#             self.input_brand = InputWithTitle(title=self.tr("Brand"), is_use_arrow_menu=True)
#             self.input_description = InputWithTitle(title=self.tr("Description"), is_use_arrow_menu=False)

#             h_layout1 = QHBoxLayout()
#             self.ip_address = InputWithTitleAndIPValidator(title=self.tr("IP address"), text_placeholder=self.tr('0.0.0.0'))
#             self.input_port = InputWithTitle(title=self.tr("Port"), is_use_arrow_menu=False)
#             h_layout1.addWidget(self.ip_address)
#             h_layout1.addWidget(self.input_port)

#             h_layout2 = QHBoxLayout()
#             self.input_username = InputWithTitle(title=self.tr("Username"), is_use_arrow_menu=False)
#             self.input_password = InputWithTitle(title=self.tr("Password"), is_use_arrow_menu=False)
#             h_layout2.addWidget(self.input_username)
#             h_layout2.addWidget(self.input_password)

#             widget = QWidget()
#             layout = QVBoxLayout()
#             # layout.setAlignment(Qt.AlignmentFlag.AlignTop)
#             # layout.setSpacing(20)
#             layout.addWidget(self.input_partner)
#             layout.addWidget(self.input_gate_type)
#             layout.addWidget(self.input_brand)
#             layout.addWidget(self.input_description)  
#             layout.addLayout(h_layout1)
#             layout.addLayout(h_layout2)   
#             widget.setLayout(layout)
#         return widget

#     def update_style(self):
#         self.setStyleSheet(
#             f'''
#                 QWidget {{
#                     background-color: {Style.PrimaryColor.on_background};
#                     color: {Style.PrimaryColor.white_2};
#                 }}
            
#                 QLabel:disabled {{
#                     color: gray;
#                     font-style: italic;
#                 }}
                
#                 QLineEdit:disabled {{
#                     color: gray;
#                     background-color: lightgray;
#                 }}
                
#                 QCheckBox:disabled {{
#                     color: gray;
#                 }}
#                 QComboBox:disabled {{
#                     color: gray;
#                     background-color: lightgray;
#                 }}
#                 QLabel#warning_label {{
#                     color: {Style.PrimaryColor.primary};
#                     font-style: italic;
#                 }}
#                 QStackedWidget#stacked_result {{
#                     border: 1px dashed {Style.PrimaryColor.border_line_edit}; 
#                     border-radius: 4px; 
#                     padding: 4px;
#                 }}
#                 QCheckBox::indicator:checked {{
#                     border: none;
#                     image: url({Style.PrimaryImage.checkbox_checked});
#                     background-color: transparent;
#                     width: 16px;
#                     height: 16px;
#                 }}
#                 QCheckBox::indicator:unchecked {{
#                     border: none;
#                     image: url({Style.PrimaryImage.checkbox_unchecked});
#                     background-color: {Style.PrimaryColor.on_background};
#                     width: 16px;
#                     height: 16px;
#                 }}
#                 QComboBox {{ 
#                    background-color: transparent; 
#                    border-radius: 4px;
#                    border: 1px solid {Style.PrimaryColor.border_line_edit};
#                    color: {Style.PrimaryColor.border_line_edit}
#                 }}
#                 QComboBox::drop-down {{
#                     background-color: {Style.PrimaryColor.on_background};
#                     border: None;
#                     border-radius: 4px;
#                 }}
#                 QComboBox::down-arrow {{ 
#                    image: url({Style.PrimaryImage.down_arrow_linedit}); 
#                 }}
#                 '''
#         )

#     def save_clicked(self):
#         self.input_gate_type.setText("Cửa quay cánh")
#         self.input_brand.setText("Bisen")
#         # self.ip_address.setText("*************")
#         if self.input_gate_type.text() != '' and self.input_brand.text() != '' and self.ip_address.text() != '':
#             if self.door_model is not None:
#                 door = replace(self.door_model.data)
#                 door.doorType = self.input_gate_type.text()
#                 door.brandName = self.input_brand.text()
#                 door.ipAddress = self.ip_address.text()
#                 door.name = self.door_name.text()
#                 # if self.input_partner.text() != '':
#                 #     pass
#                 if self.input_description.text() != '':
#                     pass
#                 if self.input_port.text() != '':
#                     door.port = self.input_port.text()
#                 if self.input_username.text() != '':
#                     door.port = self.input_username.text()
#                 if self.input_password.text() != '':
#                     door.port = self.input_password.text()
#                 if self.door_model is not None:
#                     main_controller.current_controller.update_door_by_put(data = door)
#             else:
#                 door = Door()
#                 door.doorType = self.input_gate_type.text()
#                 door.brandName = self.input_brand.text()
#                 door.ipAddress = self.ip_address.text()
#                 # door.name = self.input_partner.text()
#                 # if self.input_partner.text() != '':
#                 #     pass
#                 if self.input_description.text() != '':
#                     pass
#                 if self.input_port.text() != '':
#                     door.port = self.input_port.text()
#                 if self.input_username.text() != '':
#                     door.port = self.input_username.text()
#                 if self.input_password.text() != '':
#                     door.port = self.input_password.text()
#                 if self.door_model is not None:
#                     main_controller.current_controller.update_door_by_put(data = door)

#                 main_controller.current_controller.create_door(data = door.to_dict())
#             self.close()

#     def is_valid_ip(self, ip):
#         ip_regex = re.compile(r'^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$')
#         return ip_regex.match(ip) is not None



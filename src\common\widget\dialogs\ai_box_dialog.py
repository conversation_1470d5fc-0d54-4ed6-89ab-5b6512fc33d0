
from PySide6.QtSvgWidgets import QSvgWidget
from src.presentation.device_management_screen.widget.list_custom_widgets import <PERSON>ComboBox, InputWithDataCallback, InputText
from PySide6.QtWidgets import QWidget, QGridLayout, QScrollArea, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox, QStackedWidget
from dataclasses import replace
from .base_dialog import NewBaseDialog
from src.styles.style import Style
from src.utils.config import Config
from src.common.model.group_model import GroupModel
from src.common.widget.notifications.notify import Notifications
from src.common.controller.main_controller import main_controller,connect_slot
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QPixmap, QGuiApplication

import logging
from src.common.model.discovery_camera_model import DiscoveryCameraModel
logger = logging.getLogger(__name__)

class RowItem(QWidget):
    def __init__(self, parent=None,idx = None, camera_info = None):
        super().__init__(parent)
        self.idx = idx
        self.camera_info = camera_info
        camera_layout = QGridLayout(self)
        self.label_idx = QLabel('null')
        self.label_ip_address= QLabel('null')
        self.label_username = QLabel('null')
        self.label_username.setMaximumWidth(150)
        self.label_password = QLabel('null')
        self.label_password.setMaximumWidth(150)
        self.label_url = QLabel('null')
        self.label_url.setMaximumWidth(150)
        self.label_status = QLabel()
        self.action_group = self.create_action_group()
        self.action_group.setMaximumWidth(100)
        camera_layout.addWidget(self.label_idx,0,0)
        camera_layout.addWidget(self.label_ip_address,0,1,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.label_username,0,2,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.label_password,0,3,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.label_url,0,4,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.label_status,0,5,Qt.AlignmentFlag.AlignLeft)
        camera_layout.addWidget(self.action_group,0,6,Qt.AlignmentFlag.AlignLeft)
        camera_layout.setColumnStretch(0, 1)
        camera_layout.setColumnStretch(1, 3)
        camera_layout.setColumnStretch(2, 3)
        camera_layout.setColumnStretch(3, 3)
        camera_layout.setColumnStretch(4, 3)
        camera_layout.setColumnStretch(5, 1)
        camera_layout.setColumnStretch(6, 1)
        self.update_data()

    def update_data(self):
        self.label_idx.setText(str(self.idx))
        self.label_ip_address.setText(self.camera_info.ipAddress)
        self.label_username.setText(self.camera_info.username)
        self.label_password.setText(self.camera_info.password)
        self.label_url.setText(self.camera_info.urlMainstream)
        if self.camera_info.added:
            pixmap = QPixmap(Style.PrimaryImage.state_online)
            self.label_status.setPixmap(pixmap) 
            self.checkbox.setCheckState(Qt.CheckState.Checked)
        else:
            pixmap = QPixmap(Style.PrimaryImage.state_offline)
            self.label_status.setPixmap(pixmap) 

    def create_action_group(self):
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0,0,0,0)
        self.btn_eye = QPushButton(self, objectName='btn_eye')
        self.btn_eye.setFixedSize(22,22)
        self.btn_eye.setStyleSheet(Style.StyleSheet.button_style4)
        self.btn_eye.setIconSize(QSize(20, 20))
        icon_eye = QIcon(QPixmap(Style.PrimaryImage.eye))
        self.btn_eye.setIcon(icon_eye)
        self.btn_eye.setCheckable(False)
        self.btn_eye.setChecked(True)
        self.btn_eye.setToolTip('show')
        self.btn_eye.clicked.connect(self.btn_edit_clicked)
        self.checkbox = self.create_checkbox()
        layout.addWidget(self.btn_eye)
        layout.addWidget(self.checkbox)
        return widget
    
    def create_checkbox(self):
        checkbox = QCheckBox(self, objectName='checkbox')
        checkbox.setStyleSheet(
            f"""
            QCheckBox {{
                border: none;
            }}
            QCheckBox::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                background-color: {Style.PrimaryColor.on_background};
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                background-color: {Style.PrimaryColor.on_background};
                width: 16px;
                height: 16px;
            }}
            """)
        checkbox.stateChanged.connect(self.checkbox_clicked)
        return checkbox
    
    def btn_edit_clicked(self):
        pass
    def checkbox_clicked(self):
        pass

class ResultScanTable(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        main_layout = QVBoxLayout(self)
        header_widget = self.create_header_widget()
        self.scroll_area = self.create_scroll_area()
        self.table = None
        main_layout.addWidget(header_widget)
        main_layout.addWidget(self.scroll_area)

    def create_scroll_area(self):
        widget = QScrollArea()
        widget.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        widget.setWidgetResizable(True)
        widget.setStyleSheet(
            f'''   
                QScrollArea {{
                    border: none;  /* Ẩn border của QScrollArea */
                }}
                QScrollBar:vertical {{
                    background-color: {Style.PrimaryColor.background};
                    width: 10px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 5px;
                    min-height: 20px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            ''')
        return widget
    
    def create_header_widget(self):
        widget = QWidget()
        layout = QGridLayout(widget)
        layout.addWidget(QLabel(self.tr('ORDER')),0,0)
        layout.addWidget(QLabel(self.tr('IP ADDRESS')),0,1,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('USERNAME')),0,2,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('PASSWORD')),0,3,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('ENDPOINT')),0,4,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('STATUS')),0,5,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(QLabel(self.tr('ACTION')),0,6,Qt.AlignmentFlag.AlignLeft)
        layout.setColumnStretch(0, 1)
        layout.setColumnStretch(1, 3)
        layout.setColumnStretch(2, 3)
        layout.setColumnStretch(3, 3)
        layout.setColumnStretch(4, 3)
        layout.setColumnStretch(5, 1)
        layout.setColumnStretch(6, 1)
        return widget
    
    def clear_scroll_area(self):
        # Lấy widget chứa tất cả các widget con bên trong scroll area
            # Kiểm tra xem widget có tồn tại không
        if self.table is not None:
            # Lấy layout của widget đó
            layout = self.table.layout()
            
            # Xóa tất cả các widget con trong layout
            if layout is not None:
                while layout.count():
                    item = layout.takeAt(0)
                    widget = item.widget()
                    if widget is not None:
                        widget.deleteLater()

    def update_table(self,data:DiscoveryCameraModel = None):
        self.clear_scroll_area()
        self.table = QWidget()
        layout = QVBoxLayout(self.table)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.setContentsMargins(0,0,0,0)
        layout.setSpacing(0)
        for idx,camera_info in enumerate(data.discoveredCameraDTOs):
            # if idx == 1:
            camera_widget = RowItem(idx =idx,camera_info = camera_info)
            layout.addWidget(camera_widget)
        self.scroll_area.setWidget(self.table)
    
class AIBoxDialog(NewBaseDialog):

    def __init__(self, parent=None, data = None):
        self.data:GroupModel = data
        self.table = None
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()

        # self.style_sheet_active_button = f'''
        #     QPushButton{{
        #         background-color: {Style.PrimaryColor.button_second_background};
        #         padding: 4px 16px 4px 16px;
        #         color: {Style.PrimaryColor.text_on_primary};
        #         border: None;
                
        #         border-top-left-radius: 4px;  /* Set top-left border radius */
        #         border-top-right-radius: 4px;
        #     }}
        # '''
        # self.style_sheet_inactive_button = f'''
        #     QPushButton{{
        #         background-color: transparent;
        #         padding: 4px 16px 4px 16px;
                
        #         color: {Style.PrimaryColor.text_not_select};
        #         border: None;
        #     }}
        # '''
        widget_main = self.load_ui()
        title = self.tr("Edit AIBox")
        super().__init__(parent, title=title, content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_MEDIUM, min_height_dialog=705)
        self.setObjectName("AIBoxDialog")
        self.update_style()
        self.connect_slot()

    def connect_slot(self):
        connect_slot(
            (main_controller.discovery_in_group_signal, self.discovery_in_group_signal),
            (self.save_update_signal,self.save_clicked)
        )

    def load_ui(self):
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_MEDIUM)
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_dialog.setContentsMargins(0, 0, 0, 0)
        self.layout_dialog.setSpacing(0)
        # self.layout_dialog.addWidget(self.content_widget)
        self.ai_box_info_widget = self.create_ai_box_info_widget()
        self.scan_widget = self.create_scan_widget()
        self.result_widget = self.create_result_widget()

        self.layout_dialog.addWidget(self.ai_box_info_widget)
        self.layout_dialog.addWidget(self.scan_widget)
        self.layout_dialog.addWidget(self.result_widget)
        widget_main.setLayout(self.layout_dialog)
        return widget_main

    def create_ai_box_info_widget(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        h_layout = QHBoxLayout()
        layout.setContentsMargins(0,0,0,0)
        self.ai_box_name = InputText(title=self.tr('AI Box Name'))
        # self.ai_box_name.callback_on_text_changed = self.callback_on_text_changed
        self.ai_box_name.line_edit.setText(self.data.get_property('name'))

        self.device_group = SearchComboBox(title=self.tr('Device Group'), data=None,
                                           combobox_clicked=None)
        # self.device_group.combobox.setFixedHeight(30)
        self.device_path = InputText(title=self.tr('Device path'))
        # self.ai_box_name.callback_on_text_changed = self.callback_on_text_changed
        self.device_path.line_edit.setText('null')

        h_layout.addWidget(self.ai_box_name)
        h_layout.addWidget(self.device_group)
        h_layout.addWidget(self.device_path)
        h_layout.setStretch(0,1)
        h_layout.setStretch(1,1)
        h_layout.setStretch(2,1)

        self.description = InputText(title=self.tr('Description'))

        layout.addLayout(h_layout)
        layout.addWidget(self.description)
        # widget.setFixedHeight(100)
        return widget
    
    def create_scan_widget(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        h_input_layout = QHBoxLayout()
        self.from_ip = InputWithDataCallback(title=self.tr('From Ip'), key='from_ip')
        self.from_ip.line_edit.setText('***********')
        self.to_ip = InputWithDataCallback(title=self.tr('To Ip'), key='to_ip')
        self.to_ip.line_edit.setText('*************')
        self.username = InputWithDataCallback(title=self.tr('Username'), key='username')
        self.username.line_edit.setText('admin')
        self.password = InputWithDataCallback(title=self.tr('Password'), key='password')
        self.password.line_edit.setText('abcd1234')
        h_input_layout.addWidget(self.from_ip)
        h_input_layout.addWidget(self.to_ip)
        h_input_layout.addWidget(self.username)
        h_input_layout.addWidget(self.password)
        h_control_layout = QHBoxLayout()
        h_control_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.btn_cancel = QPushButton(self.tr('Cancel'))
        self.btn_cancel.setStyleSheet(Style.StyleSheet.button_negative)
        self.btn_check = QPushButton(self.tr('Check'))
        self.btn_check.setStyleSheet(Style.StyleSheet.button_positive)
        self.btn_check.clicked.connect(self.btn_check_clicked)
        h_control_layout.addWidget(self.btn_cancel)
        h_control_layout.addWidget(self.btn_check)

        # self.btn_check.clicked.connect(lambda: (self.save_update_signal.emit()))
        layout.addLayout(h_input_layout)
        layout.addLayout(h_control_layout)
        return widget
    
    def create_result_widget(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        widget_no_data = QWidget()
        layout_no_data = QHBoxLayout(widget_no_data)
        layout_no_data.setAlignment(Qt.AlignmentFlag.AlignCenter)
        svg_widget = QSvgWidget()
        svg_widget.load(main_controller.get_theme_attribute("Image", "no_data_image"))
        self.label_know_address_no_data = QLabel(self.tr('No Data'))
        self.label_know_address_no_data.setStyleSheet(f'font-size: {Style.Size.body_large}px; font-weight: bold; color: {main_controller.get_theme_attribute("Color", "dialog_text")}')
        layout_no_data.addWidget(svg_widget)
        layout_no_data.addWidget(self.label_know_address_no_data)

        widget_searching = QWidget()
        layout_searching = QVBoxLayout(widget_searching)
        layout_searching.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label_searching = QLabel(self.tr("Searching"))
        layout_searching.addWidget(label_searching)

        self.result_scan_table = ResultScanTable()
        self.widget_result_search = QStackedWidget()
        self.widget_result_search.setContentsMargins(0, 0, 0, 0)
        self.widget_result_search.setObjectName("stacked_result")
        self.widget_result_search.addWidget(widget_no_data)  # 0
        self.widget_result_search.addWidget(widget_searching)  # 1
        self.widget_result_search.addWidget(self.result_scan_table)  # 2
        layout.addWidget(self.widget_result_search)

        return widget
    
    def discovery_in_group_signal(self,data):
        self.widget_result_search.setCurrentIndex(2)
        self.result_scan_table.update_table(data = data)

    def btn_check_clicked(self):
        self.widget_result_search.setCurrentIndex(0)

        if self.from_ip.line_edit.text() != '' and self.to_ip.line_edit.text() != '' and self.username.line_edit.text() != '' and self.password.line_edit.text() != '':
            self.widget_result_search.setCurrentIndex(1)
            data = [
                    {
                        "usernames": [self.username.line_edit.text()],
                        "passwords": [self.password.line_edit.text()],
                        "fromIp": self.from_ip.line_edit.text(),
                        "toIp": self.to_ip.line_edit.text(),
                        "cameraGroupId": self.data.get_property('id')
                    }
                    ]
            main_controller.current_controller.create_discovery_in_group(parent=self,data = data)
            main_controller.current_controller.get_discovery_in_group(parent=self,cameraGroupId = self.data.get_property('id'))
        else:
            self.widget_result_search.setCurrentIndex(0)

    def update_style(self):
        self.setStyleSheet(
            f'''
                QWidget {{
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
            
                QLabel:disabled {{
                    color: gray;
                    font-style: italic;
                }}
                
                QLineEdit:disabled {{
                    color: gray;
                    background-color: lightgray;
                }}
                
                QCheckBox:disabled {{
                    color: gray;
                }}
                QComboBox:disabled {{
                    color: gray;
                    background-color: lightgray;
                }}
                QLabel#warning_label {{
                    color: {Style.PrimaryColor.primary};
                    font-style: italic;
                }}
                QStackedWidget#stacked_result {{
                    border: 2px dashed {main_controller.get_theme_attribute("Color", "main_border")}; 
                    border-radius: 4px; 
                    padding: 4px;
                }}
                QCheckBox::indicator:checked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                    background-color: transparent;
                    width: 16px;
                    height: 16px;
                }}
                QCheckBox::indicator:unchecked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                    background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                    width: 16px;
                    height: 16px;
                }}
                QComboBox {{ 
                   background-color: transparent; 
                   border-radius: 4px;
                   border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                }}
                QComboBox::drop-down {{
                    background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                    border: None;
                    border-radius: 4px;
                }}
                QComboBox::down-arrow {{ 
                   image: url({Style.PrimaryImage.down_arrow_linedit}); 
                }}
                '''
        )

    def save_clicked(self):
        if self.ai_box_name.line_edit.text() != '':
            if self.result_scan_table.table is not None:
                data = []
                for i in range(self.result_scan_table.table.layout().count()):
                    widget = self.result_scan_table.table.layout().itemAt(i).widget()
                    # logger.debug(f'camera_widget_clicked = {widget}')
                    if widget is not None and isinstance(widget,RowItem):
                        # logger.debug(f'sssssssssssss = {widget.camera_info.name, widget.checkbox.checkState()}')
                        if widget.checkbox.checkState() == Qt.CheckState.Checked:
                            data.append({"id":widget.camera_info.id,"name": widget.camera_info.name})
                logger.debug(f'sssssssssssss = {data}')
                if len(data) > 0:
                    main_controller.current_controller.create_camera_in_group(parent = self,data = data)
            group = GroupModel(group=replace(self.data.data))
            group.set_property("name",self.ai_box_name.line_edit.text())
            # group.data.childGroupIds = list_ai_box
            # group.data.cameraIds = list_camera
            main_controller.current_controller.update_camera_group_by_put(data=group.data)
            self.close()
        else:
            Notifications(parent=main_controller.list_parent['DeviceScreen'], title=self.tr("Vui lòng nhập đủ thông tin"),icon=Style.PrimaryImage.fail_result)

from dataclasses import replace
from PySide6.QtCore import Qt, QEvent
from PySide6.QtGui import QColor, QPainter, QGuiApplication
from PySide6.QtWidgets import QWidget, QApplication, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QDialog, QStackedWidget
from src.common.qml.models.map_picker_controller import MapPickerController
from src.common.widget.dialogs.map_dialog import MapDialog
from src.common.widget.inputs.address_suggestion_input import AddressSuggestionInput
from src.common.widget.dialogs.base_dialog import NewBaseDialog
from src.common.widget.toggle.custom_toggle import CustomSwitch
from src.common.qml.models.recording_schedule import RecordingSchedule
from src.common.widget.dialogs.warning_dialog import WarningDialog
from src.presentation.device_management_screen.widget.list_custom_widgets import InputWithDataCallback
from src.presentation.device_management_screen.widget.multidropdown import CameraGroupType
from src.common.controller.main_controller import main_controller
from src.common.model.device_models import CameraParameters
from src.styles.style import Style
from src.common.model.group_model import group_model_manager, GroupModel
from src.common.model.camera_model import CameraModel
from src.common.widget.tree_view_widget import TreeViewType
from src.utils.config import Config
from copy import deepcopy
import logging
logger = logging.getLogger(__name__)
class CameraInfoDialog(NewBaseDialog):
    # signal_update_camera = Signal(Camera)
    def __init__(self, parent=None, data: CameraModel = None, title=None):
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.x_scale = None
        self.camera_model = data

        self.camera = CameraModel(camera=deepcopy(self.camera_model.data))
        self.is_updating = False

        self.load_ui()
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_LARGE)
        widget_main.setLayout(self.layout)

        super().__init__(parent, title=self.tr("CAMERA INFORMATION"), content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_LARGE, min_height_dialog=800)
        self.setObjectName("CameraInfoDialog")
        self.save_update_signal.connect(self.update_clicked)
        self.widget_stacked_info.addWidget(self.tab_camera_configuration)
        self.widget_stacked_info.addWidget(self.camera_record_widget)
        self.update_style()

        if data.get_property("coordinateLat") != 0 and data.get_property("coordinateLong") != 0 and data.get_property("coordinateLat") != None and data.get_property("coordinateLong") != None \
            and (data.get_property("address") == None or data.get_property("address") == ''):
            self.on_finding_detail_address()
        elif data.get_property("address") != None and data.get_property("address") != ''\
             and (data.get_property("coordinateLat") == 0 or data.get_property("coordinateLong") == 0 or data.get_property("coordinateLat") == None or data.get_property("coordinateLong") == None):
            self.on_finding_coordinate_address()

    def load_ui(self):
        self.widget_camera_record()

        self.layout = QVBoxLayout()
        self.layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.setContentsMargins(4, 4, 4, 4)
        # self.scroll = QScrollArea()
        # self.scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # self.scroll.setWidgetResizable(True)
        # self.scroll.resize(self.desktop_screen_size.width() * 0.6,
        #                    self.desktop_screen_size.height() * 0.8)
        # self.scroll.setStyleSheet(Style.StyleSheet.scrollbar_ver_style)
        self.widget = QWidget()
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.widget_stacked_info = QStackedWidget()
        self.widget_stacked_info.setStyleSheet(
            f'''
                QStackedWidget {{
                    background-color: transparent;
                }}
            '''
        )
        self.widget.setLayout(self.layout_dialog)
        self.layout.addWidget(self.widget)
        # self.layout.addWidget(self.scroll)
        # self.scroll.setWidget(self.widget)

        self.label_camera_id = QLabel(f"Camera ID: {self.camera_model.get_property('id')}")
        # set label allow copy
        self.label_camera_id.setTextInteractionFlags(Qt.TextInteractionFlag.TextSelectableByMouse)
        self.label_camera_id.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.label_camera_id.setText(self.label_camera_id.text().upper())
        self.label_camera_id.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "tabbar_text_normal")}')

        self.init_widget_button_config()
        self.widget_camera_configuration()
        self.layout_dialog.addWidget(self.label_camera_id, 5)
        self.layout_dialog.addWidget(self.widget_button_config, 5)
        self.layout_dialog.addWidget(self.widget_stacked_info, 90)

        self.update_parameters()

    def init_widget_button_config(self):
        self.widget_button_config = QWidget()
        self.widget_button_config.setStyleSheet(f'border-bottom: 1px solid {Style.PrimaryColor.button_second_background}')
        self.layout_button_config = QHBoxLayout()
        self.layout_button_config.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.layout_button_config.setContentsMargins(0, 0, 0, 0)
        self.btn_camera_config = QPushButton(self.tr('Camera Configuration'))
        self.btn_camera_config.clicked.connect(self.camera_config_clicked)
        self.btn_recording_config = QPushButton(self.tr('Recording Configuration'))
        self.btn_recording_config.clicked.connect(self.recording_config_clicked)

        self.layout_button_config.addWidget(self.btn_camera_config)
        self.layout_button_config.addWidget(self.btn_recording_config)
        self.widget_button_config.setLayout(self.layout_button_config)
        self.update_stylesheet_button()

    def update_stylesheet_button(self):
        self.style_sheet_active_button = Style.PrimaryStyleSheet.get_dialog_tabbutton_active_style(theme_instance=main_controller)
        self.style_sheet_inactive_button = Style.PrimaryStyleSheet.get_dialog_tabbutton_inactive_style(theme_instance=main_controller)

        self.btn_camera_config.setStyleSheet(self.style_sheet_active_button)
        self.btn_recording_config.setStyleSheet(self.style_sheet_inactive_button)

    def widget_camera_info(self):
        self.text_camera_name = InputWithDataCallback(title=self.tr('Camera Name'), key='text_camera_name')
        self.text_camera_name.callback_on_text_changed = self.callback_on_text_changed
        self.text_camera_name.line_edit.setText(self.camera_model.get_property('name'))

        self.text_url = InputWithDataCallback(title=self.tr('Camera URL'), key='text_url')
        self.text_url.callback_on_text_changed = self.callback_on_text_changed

        self.text_url.line_edit.setText(self.camera_model.get_property("urlMainstream"))
        self.text_url.line_edit.setReadOnly(True)

        text = self.get_text_camera_group(self.camera_model)
        self.camera_group = InputWithDataCallback(model=self.camera_model,
            list_group=group_model_manager.get_group_list(), tree_view_type=TreeViewType.GROUP,
            group_type=CameraGroupType.CAMERA_GROUP,
            text=text, title=self.tr('Group Camera'), enable_only_item_checkbox=True, show_arrow=False, is_read_only=True
        )

        self.label_description = InputWithDataCallback(
            title=(self.tr('Description')), key='label_description')
        self.label_description.callback_on_text_changed = self.callback_on_text_changed
        self.label_description.line_edit.setText(self.camera_model.get_property("description"))

        self.label_latitude = InputWithDataCallback(title=(self.tr('Latitude')), key='lat_label', double_validator=True)
        self.label_latitude.callback_on_text_changed = self.callback_on_text_changed
        self.label_latitude.line_edit.setText(str(self.camera_model.get_property("coordinateLat")) if self.camera_model.get_property("coordinateLat") is not None else "")

        self.label_longitude = InputWithDataCallback(title=(self.tr('Longitude')), key='long_label', double_validator=True)
        self.label_longitude.callback_on_text_changed = self.callback_on_text_changed
        self.label_longitude.line_edit.setText(str(self.camera_model.get_property("coordinateLong")) if self.camera_model.get_property("coordinateLong") is not None else "")

        self.layout_lat_long = QHBoxLayout()
        self.layout_lat_long.setContentsMargins(0, 0, 0, 0)
        self.layout_lat_long.setSpacing(8)
        self.layout_lat_long.addWidget(self.label_latitude)
        self.layout_lat_long.addWidget(self.label_longitude)
        self.label_find_address_status = QLabel(self.tr('Status'))
        self.label_find_address_status.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_find_address_status.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')}; "
                                       f"background-color: transparent;"
                                       f"font-weight: 500;")
        self.label_find_address_status.hide()


        self.button_convert_address = QPushButton(self.tr('Find address'))
        self.button_convert_coordinates = QPushButton(self.tr('Find coordinates'))
        self.button_convert_address.setStyleSheet(Style.StyleSheet.button_positive)
        self.button_convert_coordinates.setStyleSheet(Style.StyleSheet.button_positive)
        self.button_convert_address.clicked.connect(self.on_finding_detail_address)
        self.button_convert_coordinates.clicked.connect(self.on_finding_coordinate_address)

        self.layout_convert_buttons = QHBoxLayout()
        self.layout_convert_buttons.setContentsMargins(0, 0, 0, 0)
        self.layout_convert_buttons.setSpacing(8)
        self.layout_convert_buttons.addWidget(self.button_convert_address)
        self.layout_convert_buttons.addWidget(self.button_convert_coordinates)

        self.label_detail_address = AddressSuggestionInput(title=(self.tr('Address')), text_placeholder=self.tr('Enter address'))
        self.label_detail_address.line_edit.setText(self.camera_model.get_property("address") if self.camera_model.get_property("address") != None else '')
        self.button_open_map = QPushButton(self.tr('Open map'))
        self.button_open_map.setStyleSheet(Style.StyleSheet.button_positive)
        self.button_open_map.clicked.connect(self.on_open_map_widget)
    def widget_camera_record(self):
        self.camera_record_widget = QWidget()
        self.camera_record_widget.setStyleSheet(f"background-color: {main_controller.get_theme_attribute('Color', 'dialog_body_background')}")
        self.record_schedule = RecordingSchedule(camera_model=self.camera_model)
        layout_record = QVBoxLayout()
        layout_record.setSpacing(0)
        layout_record.setContentsMargins(0, 0, 0, 0)
        layout_record.setAlignment(Qt.AlignmentFlag.AlignTop)
        # layout_record.addLayout(camera_record_layout)
        layout_record.addWidget(self.record_schedule)

        self.camera_record_widget.setLayout(layout_record)

    # def widget_main_stream(self):
    #     ################### Main Stream ######################
    #     self.content_main_stream = QWidget()
    #     self.layout_main_stream = QHBoxLayout()
    #     self.layout_main_stream.setContentsMargins(4, 0, 4, 0)
    #     self.layout_main_stream.setSpacing(8)
    #
    #     if self.camera_model.data.id in main_controller.current_controller.main_stream_resolution:
    #         self.main_resolution = CustomComboBox(title=(self.tr('Resolution(*)')), data=main_controller.current_controller.main_stream_resolution[self.camera_model.data.id])
    #         self.main_resolution.combobox_clicked = self.main_resolution_clicked
    #         self.current_main_resolution = None
    #     else:
    #         self.main_resolution = CustomComboBox(title=(self.tr('Resolution(*)')), data=None)
    #     if self.camera_model.data.id in main_controller.current_controller.main_stream_fps:
    #         self.fps = CustomComboBox(title='FPS(*)', data=main_controller.current_controller.main_stream_fps[self.camera_model.data.id])
    #         self.fps.combobox_clicked = self.fps_clicked
    #         self.current_fps = None
    #     else:
    #         self.fps = CustomComboBox(title='FPS(*)', data=None)
    #
    #     self.layout_main_stream.addWidget(self.main_resolution)
    #     self.layout_main_stream.addWidget(self.fps)
    #     self.content_main_stream.setLayout(self.layout_main_stream)
    #
    #     self.main_stream = ExpandableWidget(title=self.tr('Main Stream'), content=self.content_main_stream)
    #
    # def widget_sub_stream(self):
    #     ################### Sub Stream ######################
    #     self.content_sub_stream = QWidget()
    #     self.layout_sub_stream = QHBoxLayout()
    #     self.layout_sub_stream.setContentsMargins(4, 0, 4, 0)
    #     self.layout_sub_stream.setSpacing(8)
    #
    #     if self.camera_model.data.id in main_controller.current_controller.sub_stream_resolution:
    #         self.sub_resolution = CustomComboBox(title=(self.tr('Resolution(*)')), data=main_controller.current_controller.sub_stream_resolution[self.camera_model.data.id])
    #         self.sub_resolution.combobox_clicked = self.sub_resolution_clicked
    #         self.current_sub_resolution = None
    #     else:
    #         self.sub_resolution = CustomComboBox(title=(self.tr('Resolution(*)')), data=None)
    #     if self.camera_model.data.id in main_controller.current_controller.sub_stream_fps:
    #         self.sub_fps = CustomComboBox(title='FPS(*)', data=main_controller.current_controller.sub_stream_fps[self.camera_model.data.id])
    #         self.sub_fps.combobox_clicked = self.sub_fps_clicked
    #         self.current_sub_fps = None
    #     else:
    #         self.sub_fps = CustomComboBox(title='FPS(*)', data=None)
    #     self.layout_sub_stream.addWidget(self.sub_resolution)
    #     self.layout_sub_stream.addWidget(self.sub_fps)
    #     self.content_sub_stream.setLayout(self.layout_sub_stream)
    #
    #     self.sub_stream = ExpandableWidget(title=self.tr('Sub Stream'), content=self.content_sub_stream)
    #
    # def widget_video_adjustment(self):
    #     self.content_video_adj = QWidget()
    #     self.layout_video_adj = QVBoxLayout()
    #     self.layout_video_adj.setContentsMargins(20, 0, 20, 0)
    #     self.layout_video_adj.setSpacing(8)
    #
    #     self.brightness = CustomSlider(title=self.tr('Brightness'), min=0, max=100, default=50)
    #     self.sharpness = CustomSlider(title=self.tr('Sharpness'), min=0, max=100, default=50)
    #     self.contrast = CustomSlider(title=self.tr('Contrast'), min=0, max=100, default=50)
    #     self.color_saturation = CustomSlider(title=self.tr('Saturation'), min=0, max=100, default=50)
    #     self.layout_video_adj.addWidget(self.brightness)
    #     self.layout_video_adj.addWidget(self.sharpness)
    #     self.layout_video_adj.addWidget(self.contrast)
    #     self.layout_video_adj.addWidget(self.color_saturation)
    #     self.content_video_adj.setLayout(self.layout_video_adj)
    #
    #     self.video_adjustment = ExpandableWidget(title=self.tr('Video Adjustment'), content=self.content_video_adj)

    def widget_record_setting(self):
        pass
        # self.content_record_setting = QWidget()
        # self.layout_record_setting = QVBoxLayout()
        # self.layout_record_setting.setContentsMargins(4, 0, 4, 0)
        # self.layout_record_setting.setSpacing(8)
        #
        # self.widget_record_quality = CustomComboBox(title=self.tr('Record Quality (*)'), data=CameraParameters().record_quality)
        # if self.camera_model.data.id in main_controller.current_controller.record_resolution:
        #     self.widget_record_resolution = CustomComboBox(title=(self.tr('Resolution')), data=main_controller.current_controller.record_resolution[self.camera_model.data.id])
        #     self.widget_record_resolution.combobox_clicked = self.record_resolution_clicked
        #     self.current_record_resolution = None
        # else:
        #     self.widget_record_resolution = CustomComboBox(title=self.tr('Resolution'), data=None)
        #
        # self.widget_timelapse = CustomComboBox(title=self.tr('Timelapse speed'), data=CameraParameters().timelapse_speed)
        # self.widget_record_segment = CustomComboBox(title=self.tr('Record Segment Interval (*)'), data=CameraParameters().record_segment)
        #
        # self.layout1 = QHBoxLayout()
        # self.layout1.addWidget(self.widget_record_quality)
        # self.layout1.addWidget(self.widget_record_resolution)
        # self.layout2 = QHBoxLayout()
        # self.layout2.addWidget(self.widget_timelapse)
        # self.layout2.addWidget(self.widget_record_segment)
        # self.layout_record_setting.addLayout(self.layout1)
        # self.layout_record_setting.addLayout(self.layout2)
        # self.content_record_setting.setLayout(self.layout_record_setting)
        # self.record_setting = ExpandableWidget(title=self.tr('Record Setting'), content=self.content_record_setting, enable_switch=True)
        # self.record_setting.switch.state_changed_signal.connect(
        #     lambda state: self.switch_clicked(state))

        # self.record_widget = QWidget()
        # self.record_layout = QHBoxLayout()

        # record_label = QLabel(self.tr("Recording"))
        # record_label.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')};"
        #                            f"background-color: transparent;"
        #                            f"font-weight: 500;")
        # self.button_switch = CustomSwitch()
        # self.button_switch.state_changed_signal.connect(
        #     lambda state: self.switch_clicked(state))
        # self.record_layout.addWidget(record_label)
        # self.record_layout.addWidget(self.button_switch)
        # self.record_widget.setLayout(self.record_layout)

    def widget_camera_configuration(self):
        self.widget_camera_info()
        # self.widget_main_stream()
        # self.widget_sub_stream()
        # self.widget_video_adjustment()
        self.widget_record_setting()

        self.tab_camera_configuration = QWidget()
        self.layout_info = QVBoxLayout(self.tab_camera_configuration)
        self.layout_info.setContentsMargins(0, 0, 0, 0)
        self.layout_info.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_info.setSpacing(10)

        self.layout_info.addWidget(self.text_camera_name)
        self.layout_info.addWidget(self.text_url)
        self.layout_info.addWidget(self.camera_group)
        self.layout_info.addWidget(self.label_description)
        self.layout_info.addLayout(self.layout_lat_long)

        # self.layout_info.addWidget(self.main_stream)
        # self.layout_info.addWidget(self.sub_stream)
        # self.layout_info.addWidget(self.video_adjustment)
        # self.layout_info.addWidget(self.record_widget)
        self.layout_info.addLayout(self.layout_convert_buttons)
        self.layout_info.addWidget(self.label_find_address_status)
        self.layout_info.addWidget(self.label_detail_address)
        self.layout_info.addWidget(self.button_open_map)

    def callback_on_text_changed(self, text=None, key=None):
        if key == 'text_camera_name':
            self.camera.set_property("name",text.strip())
        if key == 'label_description':
            self.camera.set_property("description",text.strip())
        if key == 'lat_label':
            try:
                self.camera.set_property("coordinateLat",float(text.strip()))
            except ValueError:
                self.camera.set_property("coordinateLat",0)
        if key == 'long_label':
            try:
                self.camera.set_property("coordinateLong",float(text.strip()))
            except ValueError:
                self.camera.set_property("coordinateLong",0)

    def on_finding_detail_address(self):
        self.label_find_address_status.setVisible(True)

        lon = self.label_longitude.line_edit.text()
        lat = self.label_latitude.line_edit.text()

        if lon.strip() == "" or lat.strip() == "":
            self.label_find_address_status.setText(self.tr('Latitude or Longitude is empty'))
            return

        try:
            self.label_find_address_status.setText(self.tr('Finding address...'))
            main_controller.current_controller.get_address_by_coord(float(lon), float(lat), self.callback_on_finding_detail_address)
        except ValueError:
            self.label_find_address_status.setText(self.tr('Latitude and Longitude must be a number'))

    def on_finding_coordinate_address(self):
        self.label_find_address_status.setVisible(True)
        detail_address = self.label_detail_address.line_edit.text()

        if detail_address and detail_address.strip() != "":
            print(f'detail_address = {detail_address}')
            self.label_find_address_status.setText(self.tr('Finding coords...'))
            main_controller.current_controller.get_coord_by_address(detail_address, self.callback_on_finding_coordinate_address)
            return
        else:
            self.label_find_address_status.setText(self.tr('Address is empty'))

    def callback_on_finding_detail_address(self, response):
        if response.status_code != 200:
            self.label_find_address_status.setText(self.tr('Finding address failed'))
            return
        self.label_find_address_status.setVisible(False)
        self.label_detail_address.line_edit.setText(response.json()['display_name'])

    def callback_on_finding_coordinate_address(self, response):
        if response.status_code != 200:
            self.label_find_address_status.setText(self.tr('Finding coordinate failed'))
            return
        self.label_find_address_status.setVisible(False)
        if self.label_longitude.line_edit.text() == "" or self.label_longitude.line_edit.text() == "":
            self.label_longitude.line_edit.setText(response.json()[0]['lon'])
        if self.label_latitude.line_edit.text() == "" or self.label_latitude.line_edit.text() == "":
            self.label_latitude.line_edit.setText(response.json()[0]['lat'])

    def on_open_map_widget(self):
        # signal and data controller for qml connection
        map_picker_controller = MapPickerController(lon=self.label_longitude.line_edit.text(), lat=self.label_latitude.line_edit.text())

        map_dialog = MapDialog()
        map_dialog.set_property('map_picker_controller', map_picker_controller)
        map_dialog.save_update_signal.connect(lambda: self.set_coordinate(map_picker_controller._lon, map_picker_controller._lat, map_dialog))
        map_dialog.exec()

    def set_coordinate(self, lon, lat, widget):
        self.label_longitude.line_edit.setText(str(lon))
        self.label_latitude.line_edit.setText(str(lat))
        widget.close()
        self.on_finding_detail_address()

    def update_style(self):
        self.setStyleSheet(
            f'''
            QWidget {{
                background-color: transparent;
                color: {Style.PrimaryColor.white_2};
            }}
            '''
        )

    def record_resolution_clicked(self, index):
        self.current_record_resolution = self.widget_record_resolution.combobox.currentText()
        # logger.debug(f'self.current_record_resolution = {self.current_record_resolution}')

    def main_resolution_clicked(self, index):
        self.current_main_resolution = self.main_resolution.combobox.currentText()
        self.camera.set_property("mainstreamResolution",main_controller.current_controller.main_stream_resolution[
            self.camera_model.get_property('id')][index][0])
        # logger.debug(f'self.camera.mainstreamResolution = {self.camera.mainstreamResolution}')

    def sub_resolution_clicked(self, index):
        self.current_sub_resolution = self.sub_resolution.combobox.currentText()
        self.camera.set_property("substreamResolution",main_controller.current_controller.sub_stream_resolution[
            self.camera_model.get_property('id')][index][0])
        # logger.debug(f'self.camera.substreamResolution = {self.camera.substreamResolution}')

    def fps_clicked(self, index):
        self.current_fps = self.fps.combobox.currentText()
        self.camera.set_property("mainstreamFps",main_controller.current_controller.main_stream_fps[self.camera_model.get_property('id')][index][0])
        # logger.debug(f'self.camera.mainstreamFps = {self.camera.mainstreamFps}')

    def sub_fps_clicked(self, index):
        self.current_sub_fps = self.sub_fps.combobox.currentText()
        self.camera.set_property("substreamFps",main_controller.current_controller.sub_stream_fps[self.camera_model.get_property('id')][index][0]) 
        # logger.debug(f'self.camera.substreamFps = {self.camera.substreamFps}')

    def camera_configuration(self):
        api_onvif = None
        match_main_resolution = None
        match_sub_resolution = None
        match_sub_fps = None
        match_main_fps = None
        if self.camera_model.get_property("mainstreamResolution") != None or self.camera_model.get_property("mainstreamFps") != None or self.camera_model.get_property("substreamFps") != None:
            main_resolution = main_controller.current_controller.main_stream_resolution[
                self.camera_model.get_property('id')][-1][self.camera_model.get_property("mainstreamResolution")]
            main_fps = main_controller.current_controller.main_stream_fps[self.camera_model.get_property('id')][-1][int(
                self.camera_model.get_property("mainstreamFps"))]

            if self.current_main_resolution != None and self.current_main_resolution != main_resolution:
                # cau hinh tham so main resolution toi camera qua Onvif
                for item in main_controller.current_controller.main_stream_resolution[self.camera_model.get_property('id')]:
                    if item != main_controller.current_controller.main_stream_resolution[self.camera_model.get_property('id')][-1] and item[1] == self.current_main_resolution:
                        match_main_resolution = item[2].to_dict()
                        # api_onvif = ApiOnvif(self.camera_model.ipAddress,self.camera_model.port,self.camera_model.username,self.camera_model.password)
            if self.current_fps != None and self.current_fps != main_fps:
                # cau hinh tham so main resolution toi camera qua Onvif
                for item in main_controller.current_controller.main_stream_fps[self.camera_model.get_property('id')]:
                    if item != main_controller.current_controller.main_stream_fps[self.camera_model.get_property('id')][-1] and item[1] == self.current_fps:
                        match_main_fps = item[2]
                # match_main_fps = int(self.current_fps)

        if self.camera_model.get_property("substreamResolution") != None or self.camera_model.get_property("substreamFps")!= None:
            sub_resolution = main_controller.current_controller.sub_stream_resolution[
                self.camera_model.get_property('id')][-1][self.camera_model.get_property("substreamResolution")]
            sub_fps = main_controller.current_controller.sub_stream_fps[self.camera_model.get_property('id')][-1][self.camera_model.get_property("substreamFps")]
            if self.current_sub_resolution != None and self.current_sub_resolution != sub_resolution:
                # cau hinh tham so main resolution toi camera qua Onvif
                for item in main_controller.current_controller.sub_stream_resolution[self.camera_model.get_property('id')]:
                    if item != main_controller.current_controller.sub_stream_resolution[self.camera_model.get_property('id')][-1] and item[1] == self.current_sub_resolution:
                        match_sub_resolution = item[2].to_dict()
            if self.current_sub_fps != None and self.current_sub_fps != sub_fps:
                # cau hinh tham so main resolution toi camera qua Onvif
                # match_sub_fps = int(self.current_sub_fps)
                for item in main_controller.current_controller.sub_stream_fps[self.camera_model.get_property('id')]:
                    if item != main_controller.current_controller.sub_stream_fps[self.camera_model.get_property('id')][-1] and item[1] == self.current_sub_fps:
                        match_sub_fps = item[2]

        if match_main_resolution != None or match_main_fps != None or match_sub_resolution != None or match_sub_fps != None:
            pass
            # if camera_manager.get_onvif_available(camera_id=self.camera_model.data.id):
            #     api_onvif = camera_manager.get_ptz_onvif(
            #         camera_id=self.camera_model.data.id)
            #     # api_onvif = ApiOnvif(self.camera_model.ipAddress,self.camera_model.port,self.camera_model.username,self.camera_model.password)
            #     api_onvif.set_resolution(profileToken=self.camera_model.data.profileToken,
            #                              resolution=match_main_resolution, fps=match_main_fps, type_stream=0)
            #     api_onvif.set_resolution(profileToken=self.camera_model.data.profileToken,
            #                              resolution=match_sub_resolution, fps=match_sub_fps, type_stream=1)

        self.camera.set_property("recordQuality",CameraParameters(
        ).record_quality[self.widget_record_quality.combobox.currentIndex()][0])
        self.camera.set_property("recordSegmentInterval",CameraParameters.record_segment[self.widget_record_segment.combobox.currentIndex(
        )][0])
        self.camera.set_property("recordTimelapseSpeed",CameraParameters.timelapse_speed[self.widget_timelapse.combobox.currentIndex(
        )][0])
        if self.camera_model.get_property("recordResolution") != None:
            self.camera_model.set_property("recordResolution",main_controller.current_controller.record_resolution[
                self.camera_model.get_property('id')][self.widget_record_resolution.combobox.currentIndex()][0])

    def switch_clicked(self, state):
        if not state:
            self.camera.set_property("recordSetting",False)
            print('switch_clicked False')
        else:
            print('switch_clicked True')
            self.camera.set_property("recordSetting",False)

    def update_parameters(self):
        '''Comment đợi onvif'''
        # if self.camera_model.data.supportedMainResolution == None:
        #     self.content_main_stream.setEnabled(False)
        # else:
        #     main_resolutions = Utils.resolution_to_dict(
        #         self.camera_model.data.supportedMainResolution)
        #     main_fps = json.loads(self.camera_model.data.supportedMainFps)
        #
        #     if self.camera_model.data.mainstreamResolution != None:
        #         # logger.debug(f"Setting main resolution = {main_controller.current_controller.main_stream_resolution}")
        #         self.main_resolution.combobox.setCurrentText(
        #             main_controller.current_controller.main_stream_resolution[self.camera_model.data.id][-1][self.camera_model.data.mainstreamResolution])
        #     if self.camera_model.data.mainstreamFps != None:
        #         self.fps.combobox.setCurrentText(
        #             main_controller.current_controller.main_stream_fps[self.camera_model.data.id][-1][self.camera_model.data.mainstreamFps])
        #
        # if self.camera_model.data.supportedSubResolution == None:
        #     self.content_sub_stream.setEnabled(False)
        # else:
        #     sub_resolutions = Utils.resolution_to_dict(
        #         self.camera_model.data.supportedSubResolution)
        #     sub_fps = json.loads(self.camera_model.data.supportedSubFps)
        #     if self.camera_model.data.substreamResolution != None:
        #         self.sub_resolution.combobox.setCurrentText(
        #             main_controller.current_controller.sub_stream_resolution[self.camera_model.data.id][-1][self.camera_model.data.substreamResolution])
        #     if self.camera_model.data.substreamFps != None:
        #         self.sub_fps.combobox.setCurrentText(
        #             main_controller.current_controller.sub_stream_fps[self.camera_model.data.id][-1][self.camera_model.data.substreamFps])
        #     # if self.camera_model.substreamFps != None:
        #     #     self.sub_fps.combobox.setCurrentText(
        #     #         CameraParameters.sub_stream_fps[-1][self.camera_model.substreamFps])
        #     pass

        if self.camera_model.get_property("recordSetting"):
            pass
            # self.button_switch.setChecked(True)
            # if self.camera_model.data.recordQuality in CameraParameters().record_quality[-1]:
            #     self.widget_record_quality.combobox.setCurrentIndex(
            #         CameraParameters().record_quality[-1][self.camera_model.data.recordQuality])
            #
            # if self.camera_model.data.recordTimelapseSpeed in CameraParameters().timelapse_speed[-1]:
            #     self.widget_timelapse.combobox.setCurrentIndex(
            #         CameraParameters().timelapse_speed[-1][self.camera_model.data.recordTimelapseSpeed])
            #
            # if self.camera_model.data.recordSegmentInterval in CameraParameters().record_segment[-1]:
            #     self.widget_record_segment.combobox.setCurrentIndex(
            #         CameraParameters().record_segment[-1][self.camera_model.data.recordSegmentInterval])
            #
            # if self.camera_model.data.recordResolution in CameraParameters().record_resolution[-1]:
            #     self.widget_record_resolution.combobox.setCurrentIndex(
            #         CameraParameters().record_resolution[-1][self.camera_model.data.recordResolution])
            # else:
            #     self.widget_record_resolution.setEnabled(False)
        else:
            pass
            # self.button_switch.setChecked(False)

    def get_text_camera_group(self, camera_model: CameraModel):
        text = ''
        if camera_model.get_property("cameraGroupIds") is not None:
            for id in camera_model.get_property("cameraGroupIds"):
                group_model:GroupModel = group_model_manager.get_group_model(id = id)
                if group_model is not None:
                    if id == camera_model.get_property("cameraGroupIds")[-1]:
                        text = text + group_model.get_property('name')
                    else:
                        text = text + group_model.get_property('name') + ','
        return text

    def checkbox_fire_detection_clicked(self, state):
        if state == 2:
            self.widget_other.show()
        else:
            self.widget_other.hide()

    def checkbox_lpr_clicked(self, state):
        if state == 2:
            self.widget_vehicle2.show()
        else:
            self.widget_vehicle2.hide()

    def checkbox_range_clicked(self, state):
        if state == 2:
            self.widget_vehicle1.show()
        else:
            self.widget_vehicle1.hide()

    def checkbox_face_recognition_clicked(self, state):
        if state == 2:
            self.widget_content_human.show()
        else:
            self.widget_content_human.hide()

    def camera_config_clicked(self):
        self.btn_camera_config.setStyleSheet(self.style_sheet_active_button)
        self.btn_recording_config.setStyleSheet(self.style_sheet_inactive_button)
        self.widget_stacked_info.setCurrentIndex(0)

    def recording_config_clicked(self):
        self.btn_recording_config.setStyleSheet(self.style_sheet_active_button)
        self.btn_camera_config.setStyleSheet(self.style_sheet_inactive_button)
        self.widget_stacked_info.setCurrentIndex(1)

    def btn_trash_clicked(self):
        # logger.debug('btn_trash_clicked')
        camera = self.camera_model

        # Import RemoveCameraDialog
        from src.common.widget.dialogs.remove_camera_dialog import RemoveCameraDialog

        # Kiểm tra xem có nên hiển thị dialog hay không
        should_show_dialog = RemoveCameraDialog.shouldShow()
        proceed_with_deletion = True

        if should_show_dialog:
            # Hiển thị dialog xác nhận
            dialog_title = "Delete Camera"
            dialog_description = f"Are you sure you want to delete camera '{camera.get_property('name')}'?"
            if not camera.get_property('name'):
                dialog_description = "Are you sure you want to delete this camera?"

            dialog = RemoveCameraDialog(
                title=dialog_title,
                description=dialog_description
            )

            # Nếu người dùng xác nhận xóa
            proceed_with_deletion = dialog.exec()

        # Nếu người dùng xác nhận xóa hoặc đã chọn "Không hiển thị lại"
        if proceed_with_deletion:
            main_controller.delete_camera(data=camera)
            self.close()

    def update_clicked(self):
        print(f"update_clicked = {self.camera_model.get_property('server_ip')}")
        # self.camera_configuration()
        # self.camera.aiFlowId = self.tab_ai_configuration.get_id_aiflows()
        if self.camera.get_property('name') != '':
            pass
        if self.camera_group.line_edit.text().strip() != '':
            self.camera.set_property("cameraGroupIds",self.camera_group.list_id)
        else:
            self.camera.set_property("cameraGroupIds",[])
        self.camera.set_property("address",self.label_detail_address.line_edit.text())
        if self.label_latitude.line_edit.text() != "":
            self.camera.set_property("coordinateLat",float(self.label_latitude.line_edit.text()))
        if self.label_longitude.line_edit.text() != "":
            self.camera.set_property("coordinateLong",float(self.label_longitude.line_edit.text()))
        main_controller.current_controller.update_camera_by_put(data=self.camera.data)
        self.record_schedule.schedule_controller.get_recording_schedule()
        self.close()
        # main_controller.current_controller.progress_dialog.exec()

    def cancel_clicked(self):
        self.close()

    def closeEvent(self, event):
        pass

    def combobox_resolution_changed(self, index):
        selected_item = self.combobox_resolution.itemText(index)
        # main_controller.current_controller.current_page = int(selected_item)
        # self.updateTable(main_controller.current_controller.current_page)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QColor(Style.PrimaryColor.white))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(self.rect(), 10, 10)

    def eventFilter(self, source, event):
        if event.type() == QEvent.MouseButtonPress:
            # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

            global_pos = event.globalPosition().toPoint()
            if not self.geometry().contains(global_pos):
                QApplication.instance().removeEventFilter(self)
                self.close()
        return super().eventFilter(source, event)

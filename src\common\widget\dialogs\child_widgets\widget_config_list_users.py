from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QStandardItemModel, QGuiApplication, QPixmap, QStandardItem, QBrush, QColor
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QAbstractItemView, \
    QSizePolicy

from src.common.controller.controller_manager import Controller
from src.common.model.user_model import user_model_manager, UserModel
from src.common.model.user_role_model import RoleModel
from src.common.widget.custom_qtable_view import TableWith<PERSON>ustomHeader
from src.common.widget.custom_standard_item import CustomStandardItem
from src.common.widget.search_widget.search_bar import SearchBar
from src.common.widget.tree_view_widget import TreeViewType
from src.common.controller.main_controller import main_controller
from src.presentation.device_management_screen.widget.multidropdown import MultiDropDownDialog
from src.styles.style import Style


class WidgetConfigListUsersRole(QWidget):
    def __init__(self, parent=None, role_model: RoleModel = None, controller: Controller = None, is_view_only=False):
        super().__init__(parent)
        screen = QGuiApplication.primaryScreen()
        self.desktop_screen_size = screen.availableGeometry()
        self.is_view_only = is_view_only
        self.controller = controller
        self.role_model = role_model
        self.list_user_id = []
        if self.role_model is not None:
            list_user = self.role_model.data.userRoles
            if list_user is not None and len(list_user) > 0:
                for user in list_user:
                    self.list_user_id.append(user.userId)
        self.column_init_table = 5
        self.model_table_users: QStandardItemModel = None
        self.table_users = None
        self.load_ui()
        self.setup_stylesheet()
        self.resizeEvent = self.on_resize_event
        self.update_data_to_ui()

    def load_ui(self):
        # create layout
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignTop)

        # Widget top
        self.widget_top = QWidget()
        self.layout_top = QHBoxLayout()

        layout_number = QHBoxLayout()
        layout_number.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.label_list_user = QLabel(self.tr(f'Number of users: '))
        self.label_number_user = QLabel(self.tr('0'))
        self.label_number_user.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "dialog_text")}; font-weight: bold')
        self.label_list_user.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "dialog_text")}')
        layout_number.addWidget(self.label_list_user)
        layout_number.addWidget(self.label_number_user)

        layout_search_add = QHBoxLayout()
        layout_search_add.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.search_widget = SearchBar(parent=self)
        self.search_widget.search_bar.setPlaceholderText(self.tr('Search by name, email, phone number'))
        self.search_widget.search_items_signal.connect(self.search_items)
        self.btn_add_user = QPushButton()
        self.btn_add_user.setDisabled(self.is_view_only)
        self.btn_add_user.setObjectName('btn_add_user')
        self.btn_add_user.setIcon(QIcon(Style.PrimaryImage.add))
        self.btn_add_user.setFixedSize(32, 32)
        self.btn_add_user.clicked.connect(self.click_button_add_user)
        layout_search_add.addWidget(self.search_widget)
        layout_search_add.addWidget(self.btn_add_user)

        self.layout_top.addLayout(layout_number, 5)
        self.layout_top.addLayout(layout_search_add, 5)
        self.widget_top.setLayout(self.layout_top)

        # Widget content
        self.widget_content = QWidget()
        self.widget_content.setObjectName('widget_content')
        self.layout_content = QVBoxLayout()
        self.layout_content.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_content.setSpacing(0)
        self.setup_ui_table()

        self.widget_no_data = QWidget()
        layout_no_data = QHBoxLayout(self.widget_no_data)
        layout_no_data.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # Create a QSvgWidget
        svg_widget = QSvgWidget()
        svg_widget.load(main_controller.get_theme_attribute("Image", "no_data_image"))
        label_know_address_no_data = QLabel(self.tr('No Data'))
        label_know_address_no_data.setStyleSheet(
            f'font-size: {Style.Size.body_large}px; font-weight: bold; color: {main_controller.get_theme_attribute("Color", "dialog_text")}')
        layout_no_data.addWidget(svg_widget)
        layout_no_data.addWidget(label_know_address_no_data)

        self.layout_content.addWidget(self.table_users)
        self.layout_content.addWidget(self.widget_no_data)
        self.widget_content.setLayout(self.layout_content)

        self.main_layout.addWidget(self.widget_top)
        self.main_layout.addWidget(self.widget_content)
        self.setLayout(self.main_layout)

    def setup_ui_table(self):
        self.model_table_users = QStandardItemModel()
        list_horizontal_header = [self.tr("FULLNAME"), self.tr("USER GROUP"), self.tr("EMAIL"),
                                  self.tr("PHONE NUMBER"), self.tr("ACTIONS")]

        self.table_users = TableWithCustomHeader(horizontal_label_list=list_horizontal_header,
                                                 model_for_table=self.model_table_users,
                                                 use_stylesheet_header=True)
        
        # self.table_result_users.table.clicked.connect(
        #     partial(self.on_clicked_table, model_table=self.model_table_users,
        #             table=self.table_result_users.table, checkbox_all=self.checkbox_header))
        self.table_users.table.verticalHeader().setVisible(False)
        self.table_users.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_users.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table_users.table.setSelectionMode(QAbstractItemView.SelectionMode.NoSelection)
        self.table_users.table.setFocusPolicy(Qt.NoFocus)
        self.table_users.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.table_users.table.setShowGrid(False)
        horizontal_scroll_bar = self.table_users.table.horizontalScrollBar()
        horizontal_scroll_bar.setStyleSheet(f'''
                                        QScrollBar::horizontal {{
                                            background-color: transparent;
                                            height: 8px;
                                            width: 48px;
                                            margin: 0px 0px 0px 0px;
                                        }}
                                        QScrollBar::handle:horizontal {{
                                            background-color: #656475;
                                            border-radius: 4px;
                                            min-width: 8px;
                                            width: 48px;
                                        }}
                                        QScrollBar::add-line:horizontal {{
                                            background: none;
                                        }}
                                        QScrollBar::sub-line:horizontal {{
                                            background: none;
                                        }}
                                        QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                                            background: none;
                                        }}
                                        QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                                            width: 0px;
                                            height: 0px;
                                            background: none;
                                        }}
                                ''')

    def on_resize_event(self, event):
        self.set_up_dimension_row()

    def set_up_dimension_row(self):
        self.table_users.table.setColumnWidth(0, 0.2 * self.width())  # Full name
        self.table_users.table.setColumnWidth(1, 0.3 * self.width())  # User group
        self.table_users.table.setColumnWidth(2, 0.2 * self.width())  # email
        self.table_users.table.setColumnWidth(3, 0.15 * self.width())  # phone number
        self.table_users.table.setColumnWidth(4, 0.11 * self.width())  # action
        self.table_users.table.verticalHeader().setDefaultSectionSize(0.12 * self.height())

    def setup_stylesheet(self):
        self.setStyleSheet(f'''
            QPushButton#btn_add_user {{
                background-color: {Style.PrimaryColor.primary};
                border-radius: 4px;
            }}
            QPushButton#btn_add_user:disabled {{
                background-color: {Style.PrimaryColor.disable_color};
                border-radius: 4px;
            }}
        ''')

    def search_items(self):
        pass

    def click_button_add_user(self):
        pos = self.btn_add_user.mapToGlobal(self.btn_add_user.rect().bottomLeft())
        pos.setX(pos.x())
        self.multidropdown_dialog = MultiDropDownDialog(model=self.role_model, tree_view_type=TreeViewType.USER,
                                                        list_id_checked=self.list_user_id)
        self.multidropdown_dialog.item_selected_signal.connect(self.item_dropdown_selected)
        if (self.desktop_screen_size.height() - pos.y()) < self.multidropdown_dialog.height():
            pos.setY(self.desktop_screen_size.height() - self.multidropdown_dialog.height())
        self.multidropdown_dialog.move(pos)
        self.multidropdown_dialog.exec()

    def item_dropdown_selected(self, data):
        dict_data = data['data']
        if len(dict_data) > len(self.list_user_id):
            for user_id, user in dict_data.items():
                if user_id not in self.list_user_id:
                    self.list_user_id.append(user_id)
                    self.add_data_to_table(user_id)
            self.show_hide_widget_no_data(False)
        else:
            for id_user in self.list_user_id:
                if id_user not in dict_data:
                    self.list_user_id.remove(id_user)
                    index = self.find_item_index(self.model_table_users, id_user)
                    if index != -1:
                        self.model_table_users.removeRow(index)
        self.label_number_user.setText(str(len(self.list_user_id)))
        if len(dict_data) == 0:
                self.show_hide_widget_no_data(True)
                self.clear_model_and_refill_header()

    def clear_model_and_refill_header(self):
        if self.model_table_users is not None:
            self.model_table_users.clear()
        if self.table_users is not None:
            self.model_table_users.setHorizontalHeaderLabels(self.table_users.horizontal_label_list)
            self.table_users.table.setModel(self.model_table_users)
        self.set_up_dimension_row()

    def add_data_to_table(self, user_id):
        user_list = user_model_manager.get_user_list(server_ip=self.controller.server.data.server_ip)
        user_model: UserModel = user_list[user_id]
        row_number = self.model_table_users.rowCount()
        self.model_table_users.insertRow(row_number)
        self.set_up_dimension_row()

        # Set fullname data
        if user_model.data.fullName is not None and user_model.data.fullName != '':
            item_fullname = CustomStandardItem(text=f"{user_model.data.fullName}", item_model=user_model)
        else:
            item_fullname = QStandardItem(f"-")
        # Set role data
        if user_model.data.rolesName is not None and user_model.data.rolesName != '':
            item_user_roles = QStandardItem(f"{user_model.data.rolesName}")
        else:
            item_user_roles = QStandardItem(f"-")
        # Set email data
        if user_model.data.email is not None and user_model.data.email != '':
            item_email = QStandardItem(f"{user_model.data.email}")
        else:
            item_email = QStandardItem(f"-")
        # Set phone data
        if user_model.data.phone is not None and user_model.data.phone != '':
            item_phone = QStandardItem(f"{user_model.data.phone}")
        else:
            item_phone = QStandardItem(f"-")

        item_fullname.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        item_fullname.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "dialog_text"))))
        item_user_roles.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        item_user_roles.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "dialog_text"))))
        item_email.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        item_email.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "dialog_text"))))
        item_phone.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        item_phone.setForeground(QBrush(QColor(main_controller.get_theme_attribute("Color", "dialog_text"))))

        self.model_table_users.setItem(row_number, 0, item_fullname)
        self.model_table_users.setItem(row_number, 1, item_user_roles)
        self.model_table_users.setItem(row_number, 2, item_email)
        self.model_table_users.setItem(row_number, 3, item_phone)

        for column in range(self.column_init_table):
            if column == 4:
                model_index = self.model_table_users.index(row_number, 4)
                widget = WidgetButtonDelete(parent=self.table_users, index=model_index, is_view_only=self.is_view_only, item_model=user_model)
                widget.delete_user_signal.connect(self.delete_user_slot)
                # Add the widget to the cell in the table view
                self.table_users.table.setIndexWidget(model_index, widget)
        row_number += 1

    def show_hide_widget_no_data(self, visible):
        self.widget_no_data.setVisible(visible)

    def find_item_index(self, model, id_user, column=0):
        # Loop through the rows in the model
        for row in range(model.rowCount()):
            item = model.item(row, column)  # Get the item in the specified column
            if item and item.item_model.data.id == id_user:  # Compare the item text with id_user
                return row  # Return the row index if a match is found
        return -1

    def update_data_to_ui(self):
        if self.role_model is not None:
            list_user = self.role_model.data.userRoles
            if list_user is not None and len(list_user) > 0:
                for user in list_user:
                    self.add_data_to_table(user.userId)
                self.show_hide_widget_no_data(False)
        self.label_number_user.setText(str(self.model_table_users.rowCount()))

    def delete_user_slot(self, item_id):
        if item_id in self.list_user_id:
            self.list_user_id.remove(item_id)

class WidgetButtonDelete(QWidget):
    delete_user_signal = Signal(object)

    def __init__(self, parent=None, index=None, controller: Controller = None, is_view_only=False, item_model=None):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.item_model = item_model
        self.controller = controller
        self.index = index
        self.is_view_only = is_view_only
        self.btn_trash = QPushButton()
        self.btn_trash.setObjectName('btn_trash')
        self.btn_trash.setFixedSize(22, 22)
        self.btn_trash.setStyleSheet(f"""
            QPushButton {{border: None; background-color: transparent;}}
            QPushButton:hover {{background-color: {main_controller.get_theme_attribute("Color", "widget_background_2")};border-radius: 4px;margin-right: 0px;margin-left: 0px;margin-top: 0px;
            margin-bottom: 0px;}}
            QPushButton:pressed {{background-color: {main_controller.get_theme_attribute("Color", "widget_background_2")};border-radius: 4px;margin-right: 0.5px;margin-left: 0.5px;margin-top: 0.5px;
            margin-bottom: 0.5px;}}
        """)
        self.btn_trash.setIconSize(QSize(20, 20))
        if is_view_only:
            self.icon_trash = QIcon(QPixmap(Style.PrimaryImage.delete_icon_disable))
            self.btn_trash.setDisabled(True)
        else:
            self.icon_trash = QIcon(QPixmap(main_controller.get_theme_attribute("Image", "delete")))
            self.btn_trash.setDisabled(False)
        self.btn_trash.setIcon(self.icon_trash)
        self.btn_trash.setToolTip('Remove')
        self.btn_trash.clicked.connect(lambda: self.btn_trash_clicked(index))
        self.event_clicked = None

        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.btn_trash)

    def btn_trash_clicked(self, index):
        self.delete_user_signal.emit(self.item_model.data.id)




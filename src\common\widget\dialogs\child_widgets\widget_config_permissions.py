from PySide6.QtCore import Qt
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QStackedWidget, \
    QFileSystemModel, QTreeView, QSizePolicy

from src.common.controller.main_controller import main_controller
from src.common.controller.controller_manager import Controller
from src.common.model.camera_model import camera_model_manager
from src.common.model.group_model import group_model_manager
from src.common.model.user_role_model import RoleModel, role_model_manager, ChildrenRoleType
from src.presentation.device_management_screen.widget.list_custom_widgets import Input<PERSON>ithRequireField, \
    ComboBoxWithRequireField, RecursiveTreeview
from src.styles.style import Style


class WidgetConfigPermissionRole(QWidget):
    def __init__(self, parent=None, is_view_only=False, controller: Controller = None, data_permission=None, data_profile_group=None,
                 role_model: RoleModel = None):
        super().__init__(parent)
        self.role_model = role_model
        self.controller = controller
        self.is_view_only = is_view_only
        self.list_button_tab: list[QPushButton] = []
        self.data_permission = data_permission
        self.data_profile_group = data_profile_group
        self.init_style_sheet()
        self.load_ui()
        self.update_data_to_ui()

    def load_ui(self):
        # create layout
        self.main_layout = QVBoxLayout()
        self.main_layout.setAlignment(Qt.AlignTop)

        self.init_widget_button_config()
        self.init_content_stacked_widget()
        self.init_widget_tab_function()

        # layout input
        self.widget_input = QWidget()
        self.widget_input.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        self.layout_input = QHBoxLayout()
        group_name_text = self.tr('User group name')
        self.name_group_input = InputWithRequireField(title=group_name_text, text_placeholder=self.tr('Enter Group Name'),
                                                      key='name_group_input', is_required_fields=True, limit_length=50,
                                                      just_view_infor=self.is_view_only)

        description_text = self.tr('Description')
        self.description_input = InputWithRequireField(title=description_text, text_placeholder=self.tr('Enter Description'),
                                                       key='description_input', is_required_fields=False, limit_length=200,
                                                       just_view_infor=self.is_view_only)

        status_text = self.tr('Status')
        self.status_input = ComboBoxWithRequireField(title=status_text,
                                                     list_data={self.tr('Active'): 1, self.tr('In-active'): 0},
                                                     key='status_input', just_view_infor=self.is_view_only)
        self.layout_input.addWidget(self.name_group_input)
        self.layout_input.addWidget(self.description_input)
        self.layout_input.addWidget(self.status_input)
        self.widget_input.setLayout(self.layout_input)

        # layout tab function
        self.widget_content = QWidget()
        self.widget_content.setLayout(self.layout_content)

        self.main_layout.addWidget(self.widget_input)
        self.main_layout.addWidget(self.widget_content)
        self.setLayout(self.main_layout)

    def init_widget_tab_function(self):
        self.layout_content = QVBoxLayout()
        self.layout_content.setAlignment(Qt.AlignmentFlag.AlignTop)

        self.layout_content.addWidget(self.widget_button_tab)
        self.layout_content.addWidget(self.stacked_content_widget)

    def init_content_stacked_widget(self):
        self.stacked_content_widget = QStackedWidget()

        # System permissions
        self.widget_content_system_permissions = QWidget()
        self.layout_content_system_permission = QVBoxLayout()

        # Menu system
        self.treeview_system_permission = RecursiveTreeview(data_list=self.data_permission, is_view_only=self.is_view_only,
                                                            role_model=self.role_model, recursive_tree_type='tree_type_menu')
        self.layout_content_system_permission.addWidget(self.treeview_system_permission)
        self.widget_content_system_permissions.setLayout(self.layout_content_system_permission)

        # Image management
        self.widget_content_image_management = QWidget()
        self.layout_content_image_management = QVBoxLayout()
        self.widget_content_image_management.setLayout(self.layout_content_image_management)
        self.treeview_image_management = RecursiveTreeview(data_list=None, is_view_only=self.is_view_only, role_model=self.role_model)
        self.layout_content_image_management.addWidget(self.treeview_image_management)

        # Case management group
        self.widget_content_case_management = QWidget()
        self.layout_content_case_management = QVBoxLayout()
        self.widget_content_case_management.setLayout(self.layout_content_case_management)
        self.treeview_case_management = RecursiveTreeview(data_list=self.data_profile_group, is_view_only=self.is_view_only, role_model=self.role_model,
                                                          recursive_tree_type='tree_type_profile_group')
        self.layout_content_case_management.addWidget(self.treeview_case_management)

        # Camera management
        self.widget_content_camera_management = QWidget()
        self.layout_content_camera_management = QVBoxLayout()
        self.widget_content_camera_management.setLayout(self.layout_content_camera_management)
        group_list = group_model_manager.get_group_list(server_ip=self.controller.server.data.server_ip)
        camera_list = camera_model_manager.get_camera_list(server_ip=self.controller.server.data.server_ip)
        self.treeview_camera_management = RecursiveTreeview(data_list=None, camera_list=camera_list, group_list=group_list,
                                                            is_camera_tree=True, is_view_only=self.is_view_only, role_model=self.role_model)
        self.layout_content_camera_management.addWidget(self.treeview_camera_management)

        self.stacked_content_widget.addWidget(self.widget_content_system_permissions)
        self.stacked_content_widget.addWidget(self.widget_content_image_management)
        self.stacked_content_widget.addWidget(self.widget_content_case_management)
        self.stacked_content_widget.addWidget(self.widget_content_camera_management)

    def init_widget_button_config(self):
        self.widget_button_tab = QWidget()
        self.widget_button_tab.setStyleSheet(f'border-bottom: 1px solid {main_controller.get_theme_attribute("Color", "dialog_header_background")}')
        self.layout_button_tab = QHBoxLayout()
        self.layout_button_tab.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.layout_button_tab.setContentsMargins(0, 0, 0, 0)
        # Function
        self.btn_function_permissions = QPushButton(self.tr('System Permissions'))
        self.btn_function_permissions.setObjectName('btn_function_permissions')
        self.btn_function_permissions.clicked.connect(lambda: self.btn_tab_clicked(self.btn_function_permissions.objectName()))
        # Image management
        self.btn_image_management = QPushButton(self.tr('Image Management'))
        self.btn_image_management.setObjectName('btn_image_management')
        self.btn_image_management.clicked.connect(lambda: self.btn_tab_clicked(self.btn_image_management.objectName()))
        # Case Management Group
        self.btn_management_group = QPushButton(self.tr('Case Management Group'))
        self.btn_management_group.setObjectName('btn_management_group')
        self.btn_management_group.clicked.connect(lambda: self.btn_tab_clicked(self.btn_management_group.objectName()))
        # Camera Management
        self.btn_camera_management = QPushButton(self.tr('Camera Management'))
        self.btn_camera_management.setObjectName('btn_camera_management')
        self.btn_camera_management.clicked.connect(lambda: self.btn_tab_clicked(self.btn_camera_management.objectName()))

        self.list_button_tab.append(self.btn_function_permissions)
        self.list_button_tab.append(self.btn_image_management)
        self.list_button_tab.append(self.btn_management_group)
        self.list_button_tab.append(self.btn_camera_management)

        self.layout_button_tab.addWidget(self.btn_function_permissions)
        self.layout_button_tab.addWidget(self.btn_image_management)
        self.layout_button_tab.addWidget(self.btn_management_group)
        self.layout_button_tab.addWidget(self.btn_camera_management)

        self.widget_button_tab.setLayout(self.layout_button_tab)
        self.update_stylesheet_button()

    # UPDATE DATA
    def update_data_to_ui(self):
        if self.role_model is not None:
            self.name_group_input.setText(self.role_model.data.name)
            if self.role_model.data.description is not None and self.role_model.data.description != '':
                self.description_input.setText(self.role_model.data.description)
            if self.role_model.data.status:
                self.status_input.setValue(self.role_model.data.status)


    # Slot
    def btn_tab_clicked(self, object_name):
        for idx, btn in enumerate(self.list_button_tab):
            if btn.objectName() == object_name:
                btn.setStyleSheet(self.style_sheet_active_button)
                self.stacked_content_widget.setCurrentIndex(idx)
            else:
                btn.setStyleSheet(self.style_sheet_inactive_button)

    # Style sheet
    def update_stylesheet_button(self):
        for btn in self.list_button_tab:
            if btn.objectName() == 'btn_function_permissions':
                btn.setStyleSheet(self.style_sheet_active_button)
            else:
                btn.setStyleSheet(self.style_sheet_inactive_button)

    def init_style_sheet(self):
        self.style_sheet_active_button = f'''
                                            QPushButton{{
                                                background-color: {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                                                padding: 4px 16px 4px 16px;
                                                color: {Style.PrimaryColor.white_3};
                                                border: None;
                                                border-top-left-radius: 4px;  /* Set top-left border radius */
                                                border-top-right-radius: 4px;
                                                font-size: 14px
                                            }}
                                        '''
        self.style_sheet_inactive_button = f'''
                                            QPushButton{{
                                                background-color: transparent;
                                                padding: 4px 16px 4px 16px;
                                                color: {main_controller.get_theme_attribute("Color", "subtabbar_text_normal")};
                                                border: None;
                                                font-size: 14px
                                            }}
                                        '''


from PySide6.QtWidgets import QWidget, Q<PERSON>oxLayout, QHBoxLayout, QLabel
from PySide6.QtGui import QPixmap, QFont, Qt
from src.common.widget.toggle.custom_toggle import CustomSwitch
from src.common.controller.main_controller import main_controller
from src.styles.style import Style

class ExpandableWidget(QWidget):
    def __init__(self, parent=None, title=None, tap=None, content=None, enable_switch=False):
        super().__init__(parent)
        self.title = title
        self.layout_main = QVBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.title_widget = QWidget()
        self.layout_title = QHBoxLayout()
        self.layout_title.setContentsMargins(0, 0, 0, 0)
        self.header_title = HeaderTreeView(title=self.title, click=self.tap)
        self.switch = CustomSwitch()
        self.layout_title.addWidget(self.header_title)
        if enable_switch:
            self.layout_title.addWidget(self.switch)
        self.title_widget.setLayout(self.layout_title)
        self.content = QWidget() if content == None else content
        self.content.hide()
        self.layout_main.addWidget(self.title_widget)
        self.layout_main.addWidget(self.content)
        self.setLayout(self.layout_main)

    def tap(self, direct_right):
        if direct_right:
            if self.content != None:
                self.content.hide()
        else:
            if self.content != None:
                self.content.show()

class HeaderTreeView(QWidget):
    def __init__(self, title = None, click=None, icon_right=None, icon_bottom=None):
        super().__init__()
        self.title = title
        self.click = click
        self.direct_right = True
        self.label_title = QLabel(self.title)
        self.label_icon = QLabel()
        self.icon_right = Style.PrimaryImage.expand_right if icon_right == None else icon_right
        self.icon_bottom = Style.PrimaryImage.expand_bottom if icon_bottom == None else icon_bottom
        self.pixmap_right = QPixmap(self.icon_right)
        self.pixmap_bottom = QPixmap(self.icon_bottom)
        self.label_icon.setPixmap(self.pixmap_right)

        self.layout_main = QHBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.addWidget(self.label_icon)
        self.layout_main.addWidget(self.label_title)
        self.layout_main.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.setLayout(self.layout_main)
        self.setStyleSheet(f"""
            QLabel {{
                font: bold;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                font-size: {Style.Size.caption}px;
            }}""")

    def mousePressEvent(self, event):
        if self.direct_right:
            self.label_icon.setPixmap(self.pixmap_bottom)
            self.label_icon.setFixedSize(16, 16)
            self.direct_right = False
        else:
            self.label_icon.setPixmap(self.pixmap_right)
            self.label_icon.setFixedSize(16, 16)
            self.direct_right = True

        self.click(self.direct_right)

    def enterEvent(self, event):
        f = QFont()
        f.setUnderline(True)
        self.label_title.setFont(f)

    def leaveEvent(self, event):
        f = QFont()
        f.setUnderline(False)
        self.label_title.setFont(f)

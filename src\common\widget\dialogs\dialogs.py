import threading
import uuid

from PySide6.QtWidgets import <PERSON><PERSON>ram<PERSON>,  QLineEdit
from src.common.widget.custom_image import CustomImage
from src.common.widget.camera_widget import CameraWidget, CameraState, StreamCameraType
from src.presentation.device_management_screen.widget.list_custom_widgets import CustomLineEdit, IconButton, CustomIcon, InputWithDataCallback
from PySide6.QtWidgets import QWidget, QApplication, QDialogButtonBox, QTableView, QStackedLayout, QSlider, \
    QScrollArea, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox, QDialog, QStackedWidget, QProgressDialog, \
    QSizePolicy
from src.common.camera.video_capture import VideoCapture
from dataclasses import replace
from src.utils.config import Config
from src.common.widget.notifications.notify import Notifications
from src.common.widget.dialogs.base_dialog import <PERSON>B<PERSON><PERSON><PERSON>og
from src.presentation.device_management_screen.widget.multidropdown import MultiDropDown, CameraGroupType
from src.common.controller.main_controller import main_controller
from src.common.model.device_models import CameraType, GroupType, CameraParameters
from src.styles.style import Style
from src.presentation.device_management_screen.widget.list_custom_widgets import SearchWidget, CustomComboBox
from src.common.model.group_model import Group, GroupModel, group_model_manager
from src.common.model.camera_model import Camera, camera_model_manager, AddCameraType, CameraModel
from src.common.widget.tree_view_widget import TreeViewType
from PySide6.QtCore import Qt, Signal, QEvent, QSize
from PySide6.QtGui import QStandardItem, QStandardItemModel, QColor, QIcon, QPixmap, QPainter, QGuiApplication
import src.utils.log_utils as LogUtils
from src.common.widget.custom_tree_view import TreeViewBase
from src.common.qml.models.common_enum import CommonEnum
from src.common.widget.node_graph.nodegraph_library import (
    NodeGraph,
    PropertiesBinWidget,
    NodesTreeWidget,
    NodesPaletteWidget
)
from src.common.widget.node_graph.custom_widget_node.custom_nodes import (
    basic_nodes,
    custom_ports_node,
    widget_nodes,
)
import logging
from ..hover_combobox import ComboBoxScreensWithHover
from src.common.widget.dialogs.warning_dialog import WarningDialog
from ..notifications.listen_message_notifications import listen_show_notification

logger = logging.getLogger(__name__)


class CameraSettingDialog(QDialog):
    def __init__(self, parent=None, title=''):
        super().__init__(parent)
        self.device_management = parent
        self.setModal(False)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        QApplication.instance().installEventFilter(self)
        self.create_title_bar()
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.is_cb_all = True
        # self.setWindowFlags(Qt.CustomizeWindowHint | Qt.FramelessWindowHint)
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setAlignment(Qt.AlignTop)
        self.layout_dialog.setSpacing(20)
        self.layout_dialog.setContentsMargins(8, 8, 8, 8)
        # self.layout_dialog.setAlignment(Qt.AlignTop)
        self.title = QLabel(
            self.tr("<b>Choose columns to display in table</b>"))
        self.title.setText(self.title.text().upper())
        self.title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title.setStyleSheet(
            f'font-size: 20px;color: {Style.PrimaryColor.primary}')
        self.search_widget = SearchWidget(parent=self)

        self.layout1 = QHBoxLayout()
        self.layout2 = QHBoxLayout()
        self.layout3 = QHBoxLayout()
        self.layout4 = QHBoxLayout()
        self.layout5 = QHBoxLayout()
        self.layout_button_save_cancel = QHBoxLayout()
        self.layout_button_save_cancel.setSpacing(10)
        self.layout_button_save_cancel.setContentsMargins(8, 8, 8, 8)
        self.checkbox_style = f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                background-color: #FFFFFF;
                width: 20px;
                height: 20px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                background-color: #FFFFFF;
                width: 20px;
                height: 20px;
            }}
            QCheckBox::title {{
                color: {Style.PrimaryColor.background};
                font-weight: bold;
            }}
            """
        self.cb_all = QCheckBox(self.tr('All'))
        self.cb_all.setStyleSheet(self.checkbox_style)
        self.cb_all.setCheckState(
            main_controller.list_camera_settings[CameraType().all][0])
        self.cb_all.stateChanged.connect(self.cb_all_clicked)
        # self.cb_id = QCheckBox(CameraType().id)
        # self.cb_id.setStyleSheet(self.checkbox_style)
        # self.cb_id.setCheckState(
        #     main_controller.list_camera_settings[CameraType().id][0])
        # self.cb_id.stateChanged.connect(self.cb_clicked)
        # self.cb_image = QCheckBox(self.tr('Image'))
        # self.cb_image.setStyleSheet(self.checkbox_style)
        # self.cb_image.setCheckState(
        #     main_controller.list_camera_settings[CameraType().image][0])
        # self.cb_image.stateChanged.connect(self.cb_clicked)
        self.cb_camera_name = QCheckBox(self.tr('Camera Name'))
        self.cb_camera_name.setStyleSheet(self.checkbox_style)
        self.cb_camera_name.setCheckState(
            main_controller.list_camera_settings[CameraType().camera_name][0])
        self.cb_camera_name.stateChanged.connect(self.cb_clicked)
        self.cb_camera_group = QCheckBox(self.tr('Camera Group'))
        self.cb_camera_group.setStyleSheet(self.checkbox_style)
        self.cb_camera_group.setCheckState(
            main_controller.list_camera_settings[CameraType().camera_group][0])
        self.cb_camera_group.stateChanged.connect(self.cb_clicked)
        self.cb_status = QCheckBox(self.tr('Status'))
        self.cb_status.setStyleSheet(self.checkbox_style)
        self.cb_status.setCheckState(
            main_controller.list_camera_settings[CameraType().status][0])
        self.cb_status.stateChanged.connect(self.cb_clicked)
        self.cb_ai_flows = QCheckBox(self.tr('AI Flows'))
        self.cb_ai_flows.setStyleSheet(self.checkbox_style)
        self.cb_ai_flows.setCheckState(
            main_controller.list_camera_settings[CameraType().ai_flows][0])
        self.cb_ai_flows.stateChanged.connect(self.cb_clicked)
        self.cb_action = QCheckBox(self.tr('Action'))
        self.cb_action.setStyleSheet(self.checkbox_style)
        self.cb_action.setCheckState(
            main_controller.list_camera_settings[CameraType().action][0])
        self.cb_action.stateChanged.connect(self.cb_clicked)
        self.btn_cancel = QPushButton(self.tr('Cancel'))
        self.btn_cancel.setStyleSheet(Style.StyleSheet.button_style2)
        self.btn_cancel.setFixedWidth(100)
        self.btn_cancel.setFixedHeight(40)
        # self.btn_cancel.setStyleSheet('background-color: #0D64E8; color: white;')
        self.btn_cancel.clicked.connect(self.btn_cancel_clicked)
        self.btn_apply = QPushButton(self.tr('Apply'))
        self.btn_apply.setStyleSheet(Style.StyleSheet.button_style1)
        self.btn_apply.setFixedWidth(100)
        self.btn_apply.setFixedHeight(40)
        self.btn_apply.clicked.connect(self.btn_apply_clicked)
        self.layout1.addWidget(self.cb_all)
        # self.layout2.addWidget(self.cb_id)
        # self.layout2.addWidget(self.cb_image)
        self.layout3.addWidget(self.cb_camera_name)
        self.layout3.addWidget(self.cb_camera_group)
        self.layout4.addWidget(self.cb_status)
        self.layout4.addWidget(self.cb_ai_flows)
        self.layout5.addWidget(self.cb_action)
        self.layout_button_save_cancel.addWidget(QWidget())
        self.layout_button_save_cancel.addWidget(self.btn_cancel)
        self.layout_button_save_cancel.addWidget(self.btn_apply)
        self.layout_dialog.addWidget(self.title)
        self.layout_dialog.addWidget(self.search_widget)
        self.layout_dialog.addLayout(self.layout1)
        self.layout_dialog.addLayout(self.layout2)
        self.layout_dialog.addLayout(self.layout3)
        self.layout_dialog.addLayout(self.layout4)
        self.layout_dialog.addLayout(self.layout5)

        self.layout_main = QVBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.setSpacing(0)
        self.layout_main.addWidget(self.title_bar_widget, 5)
        self.layout_main.addLayout(self.layout_dialog, 80)
        self.layout_main.addLayout(self.layout_button_save_cancel, 5)
        self.setLayout(self.layout_main)
        self.setStyleSheet("background-color: white;")

    def create_title_bar(self):
        # layout
        self.title_bar_widget = QWidget()
        self.title_bar_widget.setObjectName("title_bar")
        # set background Style.PrimaryColor.primary
        self.title_bar_widget.setStyleSheet(
            f"background-color: {Style.PrimaryColor.primary}; border-top-left-radius: 10px; border-top-right-radius: 10px;")

        self.title_bar_layout = QHBoxLayout()
        # event name
        self.title_name_label = QLabel(
            self.tr("Set the table's display fields"))
        self.title_name_label.setStyleSheet(
            f"color: {Style.PrimaryColor.white}; font-weight: bold")
        close_icon = QIcon(Style.PrimaryImage.close_tab)
        self.close_button = QPushButton(close_icon, "")
        self.close_button.setIconSize(QSize(30, 30))
        self.close_button.setFixedSize(30, 30)
        self.close_button.setStyleSheet(Style.StyleSheet.close_button_dialog_style)
        self.close_button.clicked.connect(self.close)
        # add widget
        self.title_bar_layout.addWidget(self.title_name_label, 90)
        self.title_bar_layout.addWidget(self.close_button, 10)
        self.title_bar_widget.setLayout(self.title_bar_layout)

    def cb_clicked(self, state):
        self.is_cb_all = False
        if state == 2:
            pass
        else:
            # logger.debug('cb_unclicked')
            self.cb_all.setCheckState(Qt.CheckState.Unchecked)
        self.is_cb_all = True

    def cb_all_clicked(self, state):
        if self.is_cb_all:
            if state == 2:
                # logger.debug('cb_all_clicked')
                # self.cb_id.setCheckState(Qt.CheckState.Checked)
                # self.cb_image.setCheckState(Qt.CheckState.Checked)
                self.cb_camera_name.setCheckState(Qt.CheckState.Checked)
                self.cb_camera_group.setCheckState(Qt.CheckState.Checked)
                self.cb_status.setCheckState(Qt.CheckState.Checked)
                self.cb_ai_flows.setCheckState(Qt.CheckState.Checked)
                self.cb_action.setCheckState(Qt.CheckState.Checked)
            else:
                # logger.debug('cb_all_unclicked')
                # self.cb_id.setCheckState(Qt.CheckState.Unchecked)
                # self.cb_image.setCheckState(Qt.CheckState.Unchecked)
                self.cb_camera_name.setCheckState(Qt.CheckState.Unchecked)
                self.cb_camera_group.setCheckState(Qt.CheckState.Unchecked)
                self.cb_status.setCheckState(Qt.CheckState.Unchecked)
                self.cb_ai_flows.setCheckState(Qt.CheckState.Unchecked)
                self.cb_action.setCheckState(Qt.CheckState.Unchecked)

    def btn_cancel_clicked(self):
        self.close()

    def btn_apply_clicked(self):
        main_controller.list_state_button.clear()
        # main_controller.list_camera_settings[CameraType().id][0] = Qt.CheckState.Checked if self.cb_id.checkState(
        # ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        # main_controller.list_camera_settings[CameraType().image][0] = Qt.CheckState.Checked if self.cb_image.checkState(
        # ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_camera_settings[CameraType().camera_name][0] = Qt.CheckState.Checked if self.cb_camera_name.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_camera_settings[CameraType().camera_group][0] = Qt.CheckState.Checked if self.cb_camera_group.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_camera_settings[CameraType().status][0] = Qt.CheckState.Checked if self.cb_status.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_camera_settings[CameraType().ai_flows][0] = Qt.CheckState.Checked if self.cb_ai_flows.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_camera_settings[CameraType().action][0] = Qt.CheckState.Checked if self.cb_action.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_camera_settings[CameraType().all][0] = Qt.CheckState.Checked if self.cb_all.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.update_camera_table()
        self.device_management.set_header_camera_table_data()
        # self.device_management.camera_table.custom_header.update_header_data()
        self.device_management.set_camera_data()
        self.device_management.camera_table.model = QStandardItemModel(
            len(main_controller.camera_data), main_controller.camera_columns)

        self.device_management.camera_table.table.setModel(
            self.device_management.camera_table.model)
        # self.device_management.camera_table.create_model()
        self.device_management.camera_table.fitler_camera_thread.start()
        self.close()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QColor(Style.PrimaryColor.white))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(self.rect(), 10, 10)

    def eventFilter(self, source, event):
        if event.type() == QEvent.MouseButtonPress:
            # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

            global_pos = event.globalPosition().toPoint()
            if not self.geometry().contains(global_pos):
                QApplication.instance().removeEventFilter(self)
                self.close()
        return super().eventFilter(source, event)

class GroupSettingDialog(QDialog):
    def __init__(self, parent=None, title=''):
        super().__init__(parent)
        self.device_management = parent
        self.setModal(False)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        QApplication.instance().installEventFilter(self)
        self.create_title_bar()
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        self.is_cb_all = True
        # self.setWindowFlags(Qt.CustomizeWindowHint | Qt.FramelessWindowHint)
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setAlignment(Qt.AlignTop)
        self.layout_dialog.setSpacing(20)
        self.layout_dialog.setContentsMargins(8, 8, 8, 8)
        self.title = QLabel(
            self.tr("<b>Choose columns to display in table</b>"))
        self.title.setText(self.title.text().upper())
        self.title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title.setStyleSheet(
            f'font-size: 20px;color: {Style.PrimaryColor.primary}')
        self.search_widget = SearchWidget(parent=self)

        self.layout1 = QHBoxLayout()
        self.layout2 = QHBoxLayout()
        self.layout3 = QHBoxLayout()
        self.layout4 = QHBoxLayout()
        self.layout5 = QHBoxLayout()
        self.layout_button_save_cancel = QHBoxLayout()
        self.layout_button_save_cancel.setSpacing(10)
        self.layout_button_save_cancel.setContentsMargins(8, 8, 8, 8)
        self.checkbox_style = f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                background-color: #FFFFFF;
                width: 20px;
                height: 20px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                background-color: #FFFFFF;
                width: 20px;
                height: 20px;
            }}
            QCheckBox::title {{
                color: #5C687F;
                font-weight: bold;
            }}
            """
        self.cb_all = QCheckBox(self.tr("All"))
        self.cb_all.setStyleSheet(self.checkbox_style)
        # self.cb_all.setStyleSheet(
        #     "QCheckBox::indicator { width: 30px; height: 30px;}")
        self.cb_all.setCheckState(
            main_controller.list_group_settings[GroupType().all][0])
        self.cb_all.stateChanged.connect(self.cb_all_clicked)
        # self.cb_id = QCheckBox(GroupType().id)
        # self.cb_id.setStyleSheet(self.checkbox_style)
        # self.cb_id.setCheckState(
        #     main_controller.list_group_settings[GroupType().id][0])
        # self.cb_id.stateChanged.connect(self.cb_clicked)
        self.cb_group_name = QCheckBox(self.tr("Group Name"))
        self.cb_group_name.setStyleSheet(self.checkbox_style)
        self.cb_group_name.setCheckState(
            main_controller.list_group_settings[GroupType().group_name][0])
        self.cb_group_name.stateChanged.connect(self.cb_clicked)
        self.cb_sub_group = QCheckBox(self.tr('Sub Group'))
        self.cb_sub_group.setStyleSheet(self.checkbox_style)
        self.cb_sub_group.setCheckState(
            main_controller.list_group_settings[GroupType().sub_group][0])
        self.cb_sub_group.stateChanged.connect(self.cb_clicked)
        self.cb_number_cameras = QCheckBox(self.tr('Number of Cameras'))
        self.cb_number_cameras.setStyleSheet(self.checkbox_style)
        self.cb_number_cameras.setCheckState(
            main_controller.list_group_settings[GroupType().number_cameras][0])
        self.cb_number_cameras.stateChanged.connect(self.cb_clicked)
        self.cb_description = QCheckBox(self.tr('Description'))
        self.cb_description.setStyleSheet(self.checkbox_style)
        self.cb_description.setCheckState(
            main_controller.list_group_settings[GroupType().description][0])
        self.cb_description.stateChanged.connect(self.cb_clicked)

        self.cb_aiflows = QCheckBox(self.tr('AI Flows'))
        self.cb_aiflows.setStyleSheet(self.checkbox_style)
        self.cb_aiflows.setCheckState(
            main_controller.list_group_settings[GroupType().ai_flows][0])
        self.cb_aiflows.stateChanged.connect(self.cb_clicked)

        self.cb_action = QCheckBox(self.tr('Action'))
        self.cb_action.setStyleSheet(self.checkbox_style)
        self.cb_action.setCheckState(
            main_controller.list_group_settings[GroupType().action][0])
        self.cb_action.stateChanged.connect(self.cb_clicked)
        self.btn_cancel = QPushButton(self.tr('Cancel'))
        self.btn_cancel.setStyleSheet(Style.StyleSheet.button_style2)
        self.btn_cancel.setFixedWidth(100)
        self.btn_cancel.setFixedHeight(40)
        # self.btn_cancel.setStyleSheet('background-color: #0D64E8; color: white;')
        self.btn_cancel.clicked.connect(self.btn_cancel_clicked)
        self.btn_apply = QPushButton(self.tr('Apply'))
        self.btn_apply.setStyleSheet(Style.StyleSheet.button_style1)
        self.btn_apply.setFixedWidth(100)
        self.btn_apply.setFixedHeight(40)
        self.btn_apply.clicked.connect(self.btn_apply_clicked)
        self.layout1.addWidget(self.cb_all)
        # self.layout2.addWidget(self.cb_id)
        self.layout2.addWidget(self.cb_group_name)
        self.layout3.addWidget(self.cb_sub_group)
        self.layout3.addWidget(self.cb_number_cameras)
        self.layout4.addWidget(self.cb_description)
        self.layout4.addWidget(self.cb_aiflows)
        self.layout5.addWidget(self.cb_action)
        self.layout_button_save_cancel.addWidget(QWidget())
        self.layout_button_save_cancel.addWidget(self.btn_cancel)
        self.layout_button_save_cancel.addWidget(self.btn_apply)
        self.layout_dialog.addWidget(self.title)
        self.layout_dialog.addWidget(self.search_widget)
        self.layout_dialog.addLayout(self.layout1)
        self.layout_dialog.addLayout(self.layout2)
        self.layout_dialog.addLayout(self.layout3)
        self.layout_dialog.addLayout(self.layout4)
        self.layout_dialog.addLayout(self.layout5)

        self.layout_main = QVBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.setSpacing(0)
        self.layout_main.addWidget(self.title_bar_widget, 5)
        self.layout_main.addLayout(self.layout_dialog, 80)
        self.layout_main.addLayout(self.layout_button_save_cancel, 5)
        self.setLayout(self.layout_main)
        self.setStyleSheet("background-color: white;")

    def create_title_bar(self):
        # layout
        self.title_bar_widget = QWidget()
        self.title_bar_widget.setObjectName("title_bar")
        # set background Style.PrimaryColor.primary
        self.title_bar_widget.setStyleSheet(
            f"background-color: {Style.PrimaryColor.primary}; border-top-left-radius: 10px; border-top-right-radius: 10px;")

        self.title_bar_layout = QHBoxLayout()
        # event name
        self.title_name_label = QLabel(
            self.tr("Set the table's display fields"))
        self.title_name_label.setStyleSheet(
            f"color: {Style.PrimaryColor.white}; font-size: {Style.Size.body}px")
        close_icon = QIcon(Style.PrimaryImage.close_tab)
        self.close_button = QPushButton(close_icon, "")
        self.close_button.setIconSize(QSize(30, 30))
        self.close_button.setFixedSize(30, 30)
        self.close_button.setStyleSheet(Style.StyleSheet.close_button_dialog_style)
        self.close_button.clicked.connect(self.close)
        # add widget
        self.title_bar_layout.addWidget(self.title_name_label, 90)
        self.title_bar_layout.addWidget(self.close_button, 10)
        self.title_bar_widget.setLayout(self.title_bar_layout)

    def cb_clicked(self, state):
        self.is_cb_all = False
        if state == 2:
            pass
        else:
            # logger.debug('cb_unclicked')
            self.cb_all.setCheckState(Qt.CheckState.Unchecked)
        self.is_cb_all = True

    def cb_all_clicked(self, state):
        if self.is_cb_all:
            if state == 2:
                # logger.debug('cb_all_clicked')
                # self.cb_id.setCheckState(Qt.CheckState.Checked)
                self.cb_group_name.setCheckState(Qt.CheckState.Checked)
                self.cb_sub_group.setCheckState(Qt.CheckState.Checked)
                self.cb_number_cameras.setCheckState(Qt.CheckState.Checked)
                self.cb_description.setCheckState(Qt.CheckState.Checked)
                self.cb_action.setCheckState(Qt.CheckState.Checked)
            else:
                # logger.debug('cb_all_unclicked')
                # self.cb_id.setCheckState(Qt.CheckState.Unchecked)
                self.cb_group_name.setCheckState(Qt.CheckState.Unchecked)
                self.cb_sub_group.setCheckState(Qt.CheckState.Unchecked)
                self.cb_number_cameras.setCheckState(Qt.CheckState.Unchecked)
                self.cb_description.setCheckState(Qt.CheckState.Unchecked)
                self.cb_action.setCheckState(Qt.CheckState.Unchecked)

    def btn_cancel_clicked(self):
        self.close()

    def btn_apply_clicked(self):
        main_controller.list_state_button_group.clear()
        # main_controller.list_group_settings[GroupType().id][0] = Qt.CheckState.Checked if self.cb_id.checkState(
        # ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_group_settings[GroupType().group_name][0] = Qt.CheckState.Checked if self.cb_group_name.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_group_settings[GroupType().sub_group][0] = Qt.CheckState.Checked if self.cb_sub_group.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_group_settings[GroupType().number_cameras][0] = Qt.CheckState.Checked if self.cb_number_cameras.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_group_settings[GroupType().description][0] = Qt.CheckState.Checked if self.cb_description.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_group_settings[GroupType().ai_flows][0] = Qt.CheckState.Checked if self.cb_aiflows.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_group_settings[GroupType().action][0] = Qt.CheckState.Checked if self.cb_action.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.list_group_settings[GroupType().all][0] = Qt.CheckState.Checked if self.cb_all.checkState(
        ) == Qt.CheckState.Checked else Qt.CheckState.Unchecked
        main_controller.update_group_table()
        self.device_management.set_header_group_table_data()
        # self.device_management.camera_group_table.custom_header.update_header_data()
        self.device_management.set_camera_group_data()
        self.device_management.camera_group_table.model = QStandardItemModel(
            len(main_controller.device_group_data_filtered), main_controller.group_columns)
        self.device_management.camera_group_table.table.setModel(
            self.device_management.camera_group_table.model)
        self.device_management.camera_group_table.create_model()
        self.close()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setBrush(QColor(Style.PrimaryColor.white))
        painter.setPen(Qt.NoPen)
        painter.drawRoundedRect(self.rect(), 10, 10)

    def eventFilter(self, source, event):
        if event.type() == QEvent.MouseButtonPress:
            # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

            global_pos = event.globalPosition().toPoint()
            if not self.geometry().contains(global_pos):
                QApplication.instance().removeEventFilter(self)
                self.close()
        return super().eventFilter(source, event)


class GroupConfig(QWidget):
    signal_change_camera_in_group = Signal(tuple)

    def __init__(self, parent=None, data=None, label_line_input=None, text_placeholder=None, camera_place_holder=None):
        super().__init__(parent)
        self.data = data
        self.label_line_input = label_line_input
        self.group_list = group_model_manager.get_group_list(server_ip = main_controller.current_controller.server.data.server_ip)
        self.camera_list = camera_model_manager.get_camera_list(server_ip = main_controller.current_controller.server.data.server_ip)
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setSpacing(10)
        self.layout_dialog.setContentsMargins(0, 0, 0, 0)
        self.layout_dialog.setAlignment(Qt.AlignTop)
        self.setLayout(self.layout_dialog)

        self.camera_group = InputWithDataCallback(title=self.label_line_input, text_placeholder=text_placeholder, key='camera_group')
        if self.data != None:
            self.camera_group.line_edit.setText(self.data.get_property('name'))

        text_camera = self.get_text_camera(self.data)

        self.combobox_camera = InputWithDataCallback(title=self.tr('Camera (*)'), is_read_only=True, model=self.data,
                                                     text_placeholder=camera_place_holder,
                                                     list_camera=self.camera_list, tree_view_type=TreeViewType.CAMERA,
                                                     enable_checkbox=True,
                                                     group_type=CameraGroupType.CAMERA_GROUP, text=text_camera,
                                                     show_arrow=True)

        self.combobox_camera.signal_change_data.connect(self.change_camera_in_group)

        self.layout_dialog.addWidget(self.camera_group)
        self.layout_dialog.addWidget(self.combobox_camera)
        self.setup_stylesheet()

    def change_camera_in_group(self, data):
        self.signal_change_camera_in_group.emit(data)

    def get_text_camera(self, group: GroupModel):
        text = ''
        if group is not None:
            if group.get_property("cameraIds") is not None:
                camera_list = camera_model_manager.get_camera_list(server_ip = self.data.get_property('server_ip'))
                # group_list = group_model_manager.group_list
                for id in group.get_property("cameraIds"):
                    for item in camera_list.values():
                        if item.get_property("cameraidIds") == id:
                            if id == group.get_property("cameraIds")[-1]:
                                text = text + item.get_property('name')
                                break
                            else:
                                text = text + item.get_property('name') + ','
        return text

    def setup_stylesheet(self):
        self.setStyleSheet(f'background-color: {Style.PrimaryColor.on_background}')

class AddCameraGroupDialog(NewBaseDialog):
    list_group_signal = Signal(list)
    signal_create_clicked = Signal()

    def __init__(self, parent=None):
        # logger.debug("Init AddCameraGroupDialog")
        self.load_ui()

        title = self.tr("ADD CAMERA GROUPS")
        self.widget_main = QWidget()
        self.widget_main.setFixedWidth(Config.WIDTH_DIALOG_MINI)
        self.widget_main.setLayout(self.layout_dialog)
        super().__init__(parent, title=title, content_widget=self.widget_main, width_dialog=Config.WIDTH_DIALOG_MINI, min_height_dialog=280)
        self.setObjectName(AddCameraGroupDialog)
        self.save_update_signal.connect(self.save_clicked)

    def load_ui(self):
        self.layout_dialog = QVBoxLayout()
        self.group_config = GroupConfig(label_line_input=self.tr('Group Name (*)'), text_placeholder=self.tr("Enter group name"), camera_place_holder=self.tr('Choose camera'))
        self.layout_dialog.addWidget(self.group_config)

    def update_style(self):
        # self.setStyleSheet(
        #     f'''
        #             QDialog {{
        #                 background-color: #efefef;
        #                 color: {Style.PrimaryColor.background};
        #             }}
        #             QWidget {{
        #                 background-color: #efefef;
        #                 color: {Style.PrimaryColor.background};
        #             }}
        #             QLabel {{
                        
        #                 color: #5C687F;
        #                 }}
        #             QWidget {{
        #                 background-color: white;
        #                 color: {Style.PrimaryColor.background};
        #             }}
        #             '''
        # )
        self.setStyleSheet(
            f'''
                    QWidget {{
                        background-color: white;
                        color: {Style.PrimaryColor.background};
                    }}
                    '''
        )

    def save_clicked(self):
        if self.group_config.camera_group.line_edit.text() != '':
            group = Group(name=self.group_config.camera_group.line_edit.text().strip())
            if self.group_config.combobox_camera.line_edit.text() != '':
                group.cameraIds = self.group_config.combobox_camera.list_id
            main_controller.current_controller.create_camera_group(parent=main_controller.list_parent['DeviceScreen'], data=group)
            self.close()
        else:
            Notifications(parent=main_controller.list_parent['DeviceScreen'],
                          title=self.tr('Please Enter Complete Information'), icon=Style.PrimaryImage.info_result)

class PresetNameDialog(QDialog):
    def __init__(self, title='', parent=None, warn=None):
        super().__init__(parent)

        # Set dialog properties
        self.setWindowTitle(title)
        # self.setWindowModality(Qt.ApplicationModal)
        self.setMinimumWidth(300)
        self.setMinimumHeight(100)
        if warn == None:
            warn = Style.Text.are_u_sure_delete
        # Set layout
        self.layout = QVBoxLayout(self)
        self.warning = QLabel()
        self.warning.setText(warn)
        self.warning.setAlignment(Qt.AlignCenter)
        self.layout.addWidget(self.warning)
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.layout.addWidget(self.button_box)


class VolumeDialog(QDialog):
    def __init__(self, parent=None, callback=None, current_volume=50):
        super().__init__(parent, Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TransparentForMouseEvents)
        self.setFixedHeight(120)
        self.setFixedWidth(70)
        self.callback = callback
        self.current_volume = current_volume
        self.layout_dialog = QVBoxLayout()
        self.label = QLabel(f'{self.current_volume}%')
        self.slider = QSlider(self)
        self.slider.setOrientation(Qt.Vertical)
        self.slider.setMinimum(0)
        self.slider.setMaximum(100)
        # self.slider.setFixedHeight(100)
        self.slider.setValue(self.current_volume)
        self.slider.setTickPosition(QSlider.TicksBelow)
        self.slider.setTickInterval(10)
        self.slider.valueChanged.connect(self.on_value_changed)
        self.layout_dialog.addWidget(self.label)
        self.layout_dialog.addWidget(self.slider)
        self.setLayout(self.layout_dialog)
        self.setModal(False)
        QApplication.instance().installEventFilter(self)

    def on_value_changed(self, value):
        if self.callback != None:
            self.callback(value)
            self.label.setText(str(value) + '%')
        else:
            self.label.setText(str(value) + '%')

    def eventFilter(self, source, event):
        if event.type() == QEvent.MouseButtonPress:
            # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

            global_pos = event.globalPos()
            if not self.geometry().contains(global_pos):
                QApplication.instance().removeEventFilter(self)
                self.close()
        return super().eventFilter(source, event)

class SetupPatrolDialog(QDialog):
    def __init__(self, title='', parent=None, warn=None):
        super().__init__(parent)

        # Set dialog properties
        self.setWindowTitle(title)
        # self.setWindowModality(Qt.ApplicationModal)
        self.setMinimumWidth(300)
        self.setMinimumHeight(200)

        self.main_layout = QVBoxLayout(self)
        self.table = QTableView()
        self.model = QStandardItemModel()
        self.table.setModel(self.model)
        self.update_model()
        self.main_layout.addWidget(self.table)
        self.button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        self.button_box.accepted.connect(self.accept)
        self.button_box.rejected.connect(self.reject)
        self.main_layout.addWidget(self.button_box)

    def update_model(self):
        self.table = QTableView()
        self.model = QStandardItemModel()
        self.table.setModel(self.model)
        self.update_model()
        data = [
            ['John', 'Doe', 25],
            ['Jane', 'Smith', 30],
            ['Bob', 'Johnson', 42]
        ]
        for row in data:
            items = [QStandardItem(str(item)) for item in row]
            self.model.appendRow(items)

class ExpressWidget(QWidget):
    def __init__(self, parent=None, callback_delete=None, callback_checkbox=None, index=None):
        super().__init__(parent)
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.widget_index = index
        self.callback_delete = callback_delete
        self.callback_checkbox = callback_checkbox
        self.is_load_ui = False
        self.widget = QWidget()
        self.setObjectName('ExpressWidget')
        self.main_layout.addWidget(self.widget)
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFixedHeight(1)
        # line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("background-color: #767676;")
        self.main_layout.addWidget(line)
        self.widget.setFixedHeight(55)
        self.widget_layout = QHBoxLayout(self.widget)
        self.widget_layout.setContentsMargins(0, 0, 0, 0)
        self.checkbox = QCheckBox()
        self.checkbox.setStyleSheet(f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:disabled {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            """)
        self.checkbox.stateChanged.connect(self.checkbox_clicked)
        # self.username = QLabel('Username')
        self.username = QLineEdit()
        self.username.setText('admin')
        self.username.setStyleSheet(Style.StyleSheet.line_edit_style)
        self.username.setFixedHeight(40)
        self.username.setPlaceholderText(self.tr('Enter desired username'))
        self.password = QLineEdit()
        self.password.setFixedHeight(40)
        self.password.setStyleSheet(Style.StyleSheet.line_edit_style)
        self.password.setPlaceholderText(self.tr('Enter desired password'))
        self.ip = QLineEdit()
        self.ip.setFixedHeight(40)
        self.ip.setStyleSheet(Style.StyleSheet.line_edit_style)
        self.ip.setPlaceholderText(self.tr('Enter desired Ip address'))
        self.port = QLineEdit()
        self.port.setFixedHeight(40)
        self.port.setPlaceholderText(self.tr('Enter desired Port'))
        self.port.setStyleSheet(Style.StyleSheet.line_edit_style)
        # self.delete = IconButton(icon= QIcon(Style.PrimaryImage.delete_camera_address),style=Style.StyleSheet.button_style4,tool_tip='Add camera address')
        self.delete = CustomIcon(
            icon=Style.PrimaryImage.delete_camera_address, style=Style.StyleSheet.button_style17)
        if callback_delete is not None:
            self.delete.icon_clicked = lambda: callback_delete(
                index=self.widget_index)
            # self.delete.button.clicked.connect(lambda:callback_delete(index=self.widget_index))
        # self.widget.setStyleSheet(f'''
        #                     Qwidget {{
        #                         background-color: #FFFFFF;
        #                         color: #000000;
        #                     }}''')
        # self.widget.setStyleSheet(f'''
        #                     Qwidget#ExpressWidget {{
        #                         background-color: #FFFFFF;
        #                         color: #000000;
        #                         border-bottom: 1px solid #767676;
        #                     }}
        #                 ''')

    def load_ui(self):
        self.is_load_ui = True
        self.widget_layout.addWidget(self.checkbox)
        self.widget_layout.addWidget(self.username)
        self.widget_layout.addWidget(self.password)
        self.widget_layout.addWidget(self.ip)
        self.widget_layout.addWidget(self.port)
        self.widget_layout.addWidget(self.delete)
        self.widget_layout.setStretch(0, 10)
        self.widget_layout.setStretch(1, 25)
        self.widget_layout.setStretch(2, 25)
        self.widget_layout.setStretch(3, 25)
        self.widget_layout.setStretch(4, 10)
        self.widget_layout.setStretch(5, 10)

    def checkbox_clicked(self, state):
        if state == 2:
            if self.username.text() != '':
                self.username.setStyleSheet(Style.StyleSheet.line_edit_style1)
            if self.password.text() != '':
                self.password.setStyleSheet(Style.StyleSheet.line_edit_style1)
            if self.ip.text() != '':
                self.ip.setStyleSheet(Style.StyleSheet.line_edit_style1)
            if self.port.text() != '':
                self.port.setStyleSheet(Style.StyleSheet.line_edit_style1)
        else:
            self.username.setStyleSheet(Style.StyleSheet.line_edit_style)
            self.password.setStyleSheet(Style.StyleSheet.line_edit_style)
            self.ip.setStyleSheet(Style.StyleSheet.line_edit_style)
            self.port.setStyleSheet(Style.StyleSheet.line_edit_style)
        if self.callback_checkbox is not None:
            self.callback_checkbox(state=state, index=self.widget_index)

class AddressRangeWidget(QWidget):
    def __init__(self, parent=None, callback_delete=None, callback_checkbox=None, index=None):
        super().__init__(parent)
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.widget_index = index
        self.callback_delete = callback_delete
        self.callback_checkbox = callback_checkbox
        self.is_load_ui = False
        self.widget = QWidget()
        self.main_layout.addWidget(self.widget)
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFixedHeight(1)
        # line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("background-color: #767676;")
        self.main_layout.addWidget(line)
        self.widget.setFixedHeight(55)
        self.widget_layout = QHBoxLayout(self.widget)
        self.widget_layout.setContentsMargins(0, 0, 0, 0)
        self.checkbox = QCheckBox()
        self.checkbox.setObjectName('checkbox')
        self.checkbox.setStyleSheet(f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:disabled {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            """)
        self.checkbox.stateChanged.connect(self.checkbox_clicked)
        # self.username = QLabel('Username')
        self.username = QLineEdit()
        self.username.setText('admin')
        self.username.setStyleSheet(Style.StyleSheet.line_edit_style)
        self.username.setFixedHeight(40)
        self.username.setPlaceholderText('Enter desired username')

        self.password = QLineEdit()
        self.password.setStyleSheet(Style.StyleSheet.line_edit_style)
        self.password.setFixedHeight(40)
        self.password.setPlaceholderText('Enter desired password')

        self.from_ip = QLineEdit()
        self.from_ip.setStyleSheet(Style.StyleSheet.line_edit_style)
        self.from_ip.setFixedHeight(40)
        self.from_ip.setPlaceholderText('Enter desired ip address')
        self.to_ip = QLineEdit()
        self.to_ip.setFixedHeight(40)
        self.to_ip.setStyleSheet(Style.StyleSheet.line_edit_style)
        self.to_ip.setPlaceholderText('Enter desired ip address')
        # self.delete = IconButton(icon= QIcon(Style.PrimaryImage.delete_camera_address),style=Style.StyleSheet.button_style4,tool_tip='Add camera address')
        self.delete = CustomIcon(
            icon=Style.PrimaryImage.delete_camera_address, style=Style.StyleSheet.button_style17)
        if callback_delete is not None:
            self.delete.icon_clicked = lambda: callback_delete(
                index=self.widget_index)
            # self.delete.button.clicked.connect(lambda:callback_delete(index=self.widget_index))
        self.widget.setStyleSheet('background-color: #FFFFFF;color: #000000;')

    def load_ui(self):
        self.is_load_ui = True
        self.widget_layout.addWidget(self.checkbox)
        self.widget_layout.addWidget(self.username)
        self.widget_layout.addWidget(self.password)
        self.widget_layout.addWidget(self.from_ip)
        self.widget_layout.addWidget(self.to_ip)
        self.widget_layout.addWidget(self.delete)
        self.widget_layout.setStretch(0, 10)
        self.widget_layout.setStretch(1, 20)
        self.widget_layout.setStretch(2, 20)
        self.widget_layout.setStretch(3, 20)
        self.widget_layout.setStretch(4, 20)
        self.widget_layout.setStretch(5, 10)

    def checkbox_clicked(self, state):
        if state == 2:
            if self.from_ip.text() != '':
                self.from_ip.setStyleSheet(Style.StyleSheet.line_edit_style1)
            if self.to_ip.text() != '':
                self.to_ip.setStyleSheet(Style.StyleSheet.line_edit_style1)
            if self.username.text() != '':
                self.username.setStyleSheet(Style.StyleSheet.line_edit_style1)
            if self.password.text() != '':
                self.password.setStyleSheet(Style.StyleSheet.line_edit_style1)
        else:
            self.from_ip.setStyleSheet(Style.StyleSheet.line_edit_style)
            self.to_ip.setStyleSheet(Style.StyleSheet.line_edit_style)
            self.username.setStyleSheet(Style.StyleSheet.line_edit_style)
            self.password.setStyleSheet(Style.StyleSheet.line_edit_style)

        if self.callback_checkbox is not None:
            self.callback_checkbox(state=state, index=self.widget_index)

class ExpressResultWidget(QWidget):
    def __init__(self, parent=None, callback_delete=None, callback_checkbox=None, index=None):
        super().__init__(parent)
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.widget_index = index
        self.data = None
        self.callback_delete = callback_delete
        self.callback_checkbox = callback_checkbox
        self.widget = QWidget()
        self.main_layout.addWidget(self.widget)
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFixedHeight(1)
        # line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("background-color: #767676;")
        self.main_layout.addWidget(line)
        self.widget.setFixedHeight(55)
        self.widget_layout = QHBoxLayout(self.widget)
        self.widget_layout.setContentsMargins(0, 0, 0, 0)
        # widget_layout.setSpacing(0)
        self.checkbox = QCheckBox()
        self.checkbox.setObjectName('checkbox')
        self.checkbox.setStyleSheet(f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:disabled {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            """)
        self.checkbox.stateChanged.connect(self.checkbox_clicked)
        self.ip = QLabel()
        self.ip.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.port = QLabel()
        self.port.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.name = QLabel()
        self.name.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # self.status = CustomIcon(icon= Style.PrimaryImage.sucess_state)
        self.edge = CustomIcon(icon=Style.PrimaryImage.show_hide,
                               icon_clicked=self.edge_clicked, style=Style.StyleSheet.button_style17)
        self.load_ui()
        self.widget.setStyleSheet('background-color: #FFFFFF;color: #000000;')

    def load_ui(self):
        self.widget_layout.addWidget(self.checkbox)
        self.widget_layout.addWidget(self.ip)
        self.widget_layout.addWidget(self.port)
        self.widget_layout.addWidget(self.name)
        # widget_layout.addWidget(self.status)
        self.widget_layout.addWidget(self.edge)
        self.widget_layout.setStretch(0, 10)
        self.widget_layout.setStretch(1, 35)
        self.widget_layout.setStretch(2, 10)
        self.widget_layout.setStretch(3, 35)
        # widget_layout.setStretch(4,10)
        self.widget_layout.setStretch(5, 10)

    def checkbox_clicked(self, state):
        if state == 2:
            pass
        else:
            pass

    def edge_clicked(self):
        if self.data is not None:
            ip = self.data['ip']
            rtsp = self.data['rtsp_list'][0]
            self.dialog = CameraStreamDialog(ip=ip, rtsp=rtsp)
            self.dialog.exec()

    def update_data(self, data=None):
        if data is not None:
            self.data = data
            self.ip.setText(str(data['ip']))
            self.port.setText(str(data['port']))
            self.name.setText(data['manufacturer'])

class AddressRangeResultWidget(QWidget):
    def __init__(self, parent=None, callback_delete=None, callback_checkbox=None, index=None):
        super().__init__(parent)
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.widget_index = index
        self.data = None
        self.callback_delete = callback_delete
        self.callback_checkbox = callback_checkbox
        self.widget = QWidget()
        self.main_layout.addWidget(self.widget)
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFixedHeight(1)
        # line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("background-color: #767676;")
        self.main_layout.addWidget(line)
        self.widget.setFixedHeight(55)
        self.widget_layout = QHBoxLayout(self.widget)
        self.widget_layout.setContentsMargins(0, 0, 0, 0)
        # widget_layout.setSpacing(0)
        self.checkbox = QCheckBox()
        self.checkbox.setObjectName('checkbox')
        self.checkbox.setStyleSheet(f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            QCheckBox::indicator:disabled {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                width: 20px;
                height: 20px;
                margin-left: 10px;
            }}
            """)
        self.checkbox.stateChanged.connect(self.checkbox_clicked)
        self.rtsp = QLabel()
        self.rtsp.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # self.status = CustomIcon(icon= Style.PrimaryImage.sucess_state)
        self.edge = CustomIcon(icon=Style.PrimaryImage.show_hide,
                               icon_clicked=self.edge_clicked, style=Style.StyleSheet.button_style17)
        self.load_ui()
        self.widget.setStyleSheet('background-color: #FFFFFF;color: #000000;')

    def load_ui(self):
        self.widget_layout.addWidget(self.checkbox)
        self.widget_layout.addWidget(self.rtsp)
        self.widget_layout.addWidget(self.edge)
        self.widget_layout.setStretch(0, 20)
        self.widget_layout.setStretch(1, 70)
        self.widget_layout.setStretch(2, 10)

    def checkbox_clicked(self, state):
        if state == 2:
            pass
        else:
            pass

    def edge_clicked(self):
        if self.data is not None:
            self.dialog = CameraStreamDialog(rtsp=self.data['rtsp_list'][0])
            self.dialog.exec()

    def update_data(self, data=None):
        if data is not None:
            self.data = data
            self.rtsp.setText(self.data['rtsp_list'][0])

class CameraStreamDialog(QDialog):
    def __init__(self, ip='', rtsp=None, parent=None):
        super().__init__(parent)
        self.parent1 = parent
        self.ip = ip
        self.setModal(False)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(0)
        # self.main_layout.setContentsMargins(0,0,0,0)
        self.setLayout(self.main_layout)
        self.create_top_widget()
        self.widget_camera_stream()
        self.main_layout.addWidget(self.widget)
        self.main_layout.addWidget(self.widget_camera)
        self.update_style()
        logger.debug(f'rtsp = {rtsp}')
        self.video_capture = VideoCapture(parent=self, rtsp=rtsp, camera_id=-1,
                                          stream_type=CommonEnum.StreamType.MAIN_STREAM, width=750, height=422, timeout=10)
        self.video_capture.set_send_mat_frame(True)
        self.video_capture.register_signal(self)
        self.video_capture.allow_share_frame = True
        self.video_capture.start_thread()

    def create_top_widget(self):
        self.widget = QWidget()
        layout = QHBoxLayout(self.widget)
        # layout.setContentsMargins(0,0,0,0)
        layout.setAlignment(Qt.AlignmentFlag.AlignBaseline)
        self.ip_camera = QLabel(self.ip)
        self.btn_close = IconButton(icon=QIcon(
            Style.PrimaryImage.close_stream), style=Style.StyleSheet.close_button_dialog_style, tool_tip='Close')
        self.btn_close.clicked.connect(self.btn_close_clicked)
        layout.addWidget(self.ip_camera)
        layout.addWidget(self.btn_close)

        self.widget.setStyleSheet(
            f"background-color: {Style.PrimaryColor.primary};color: #FFFFFF; border-top-left-radius: 10px; border-top-right-radius: 10px;")

    def widget_camera_stream(self):
        self.widget_camera = QWidget()
        layout = QHBoxLayout(self.widget_camera)
        # self.widget_camera.hide()
        self.widget_camera.setFixedSize(750, 422)
        self.stacked_layout = QStackedLayout()
        self.stacked_layout.setContentsMargins(20, 20, 20, 20)
        layout.addLayout(self.stacked_layout)
        self.preview_camera = CustomImage(scale_contents=False)
        self.camera_connecting = QPixmap(Style.PrimaryImage.ic_camera_connecting)
        self.preview_camera.setPixmap(self.camera_connecting)
        self.preview_camera.setStyleSheet(f'background-color: {Style.PrimaryColor.black};')
        self.stacked_layout.addWidget(self.preview_camera)
        self.widget_camera.setStyleSheet(
            f"border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: {Style.PrimaryColor.black};")

    def btn_close_clicked(self):
        self.close()

    def closeEvent(self, event):
        if self.video_capture is not None:
            self.video_capture.unregister_signal(self)
            self.video_capture.update_resize(750, 422, self.preview_camera.uuid, remove=True)

    def camera_state_signal(self, state):
        pass

    def share_frame_signal(self, data):
        # logger.debug(f"share_frame_signal = {data}")
        if data[0]:
            self.frame = CameraWidget.mat_resize(
                width=750, height=422, mat=data[1])
            pix = CameraWidget.mat_to_q_pixmap(mat=self.frame)
            try:
                self.preview_camera.set_scale_contents(True)
                self.preview_camera.setPixmap(pix)
            except RuntimeError:
                LogUtils.logger.debug("RuntimeError")

    def update_style(self):
        self.setStyleSheet(
            f'''
                    QWidget {{
                        background-color: #efefef;
                        color: {Style.PrimaryColor.background};
                    }}
                    '''
        )

class TreeViewDialog(TreeViewBase):
    camera_clicked_signal = Signal(int, str)
    stop_live_camera_signal = Signal(str)
    stop_live_group_signal = Signal(str)
    open_camera_position_signal = Signal(object)
    open_camera_in_tab_signal = Signal(object)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.tree_view.setStyleSheet(
            f'''
            QTreeView {{
                background-color: {Style.PrimaryColor.background};
                alternate-background-color: {Style.PrimaryColor.background};
                color: Style.PrimaryColor.white;
                border: None;
            }}
            QTreeView::item {{
                padding: 4px; 
                color: Style.PrimaryColor.white;
            }}
            QTreeView::item::selected {{
                background-color: {Style.PrimaryColor.on_background};
                color: {Style.PrimaryColor.white}; 
            }}
            QTreeView::branch:has-children:closed {{
                image:  url({Style.PrimaryImage.expand_item_treeview});
            }}
            QTreeView::branch:has-children:open {{
                image: url({Style.PrimaryImage.collapse_item_treeview});
            }}
            QHeaderView::section {{ 
                background-color: {Style.PrimaryColor.background}; 
                color: Style.PrimaryColor.white; 
            }}
            '''
        )

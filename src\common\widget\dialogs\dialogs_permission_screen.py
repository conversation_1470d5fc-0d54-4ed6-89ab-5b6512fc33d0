import copy
import re
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor

from PySide6.QtCore import Signal, Qt, QSize, QPropertyAnimation, QEasingCurve, QParallelAnimationGroup, QRect, QPoint
from PySide6.QtGui import <PERSON><PERSON><PERSON>Application, QPixmap, QIcon
from PySide6.QtWidgets import <PERSON>A<PERSON>lication, QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QStackedWidget, QLineEdit, \
    QSizePolicy, QDialog

from src.utils.theme_setting import theme_setting
from src.common.controller.controller_manager import Controller
from src.common.controller.main_controller import connect_slot, main_controller
from src.common.model.camera_model import camera_model_manager
from src.common.model.function_permission_model import function_menu_model_manager
from src.common.model.group_model import group_model_manager
from src.common.model.profile_group_model import profile_group_model_manager
from src.common.model.user_model import User<PERSON>ode<PERSON>, <PERSON>, User<PERSON><PERSON>
from src.common.model.user_role_model import RoleModel, Role, RoleCamera, RoleGroup, RoleMenu
from src.common.widget.dialogs.base_dialog import NewBaseDialog, FooterType
from src.common.widget.dialogs.child_widgets.widget_config_list_users import WidgetConfigListUsersRole
from src.common.widget.dialogs.child_widgets.widget_config_permissions import WidgetConfigPermissionRole
from src.common.widget.notifications.notify import Notifications
from src.common.widget.tree_view_widget import TreeViewType
from src.presentation.device_management_screen.widget.list_custom_widgets import PickImageArea, InputWithRequireField, \
    InputCallbackWithMessage, ComboBoxWithRequireField
from src.styles.style import Style
from src.utils.config import Config


class AddUserDialog(NewBaseDialog):
    signal_create_clicked = Signal()

    def __init__(self, parent=None, controller: Controller = None):
        # logger.debug("Init AddCameraGroupDialog")
        self.controller = controller
        self.is_username_exist = False
        self.is_email_exist = False
        self.load_ui()

        title = self.tr("ADD NEW USER")
        self.widget_main = QWidget()
        self.widget_main.setFixedWidth(Config.WIDTH_DIALOG_MEDIUM)
        self.widget_main.setLayout(self.layout_dialog)
        super().__init__(parent, title=title, content_widget=self.widget_main, width_dialog=Config.WIDTH_DIALOG_MEDIUM)
        self.setObjectName("AddUserDialog")
        self.save_update_signal.connect(self.save_clicked)
        self.update_style()
        self.connect_slot()

    def connect_slot(self):
        connect_slot(
            (main_controller.signal_check_username, self.result_check_information),)

    def load_ui(self):
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_dialog.setSpacing(0)
        # Avatar
        widget_avatar = QWidget()
        layout_avatar = QVBoxLayout()
        layout_avatar.setSpacing(6)
        avt_text = self.tr('Avatar')
        html_avt = f'<span style="color: {main_controller.get_theme_attribute("Color", "dialog_text")};">{avt_text} </span><span style="color: red;">*</span>'
        self.label_avatar = QLabel(html_avt)
        self.label_avatar.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')}")
        self.avatar_area = PickImageArea(controller=self.controller)

        layout_avatar.addWidget(self.label_avatar)
        layout_avatar.addWidget(self.avatar_area)

        # User Infor
        widget_user_infor = QWidget()
        layout_user_infor = QHBoxLayout()
        layout_user_infor.setAlignment(Qt.AlignmentFlag.AlignTop)
        username_text = self.tr('Username')
        full_name_text = self.tr('Full Name')
        system_user_group_text = self.tr('System User Group')
        html_system_user_group = f'<span style="color: {main_controller.get_theme_attribute("Color", "dialog_text")};">{system_user_group_text} </span><span style="color: red;">*</span>'
        self.username_input = InputWithRequireField(title=username_text, text_placeholder=self.tr('Enter Username'),
                                                    key='username_input', is_required_fields=True, limit_length=64, focus_out_callback=self.check_username)
        self.full_name_input = InputWithRequireField(title=full_name_text, text_placeholder=self.tr('Enter Full Name'),
                                                     key='full_name_input', is_required_fields=True, limit_length=50)

        self.system_user_group_input = InputCallbackWithMessage(title=html_system_user_group,
                                                                tree_view_type=TreeViewType.USER_ROLE,
                                                                text_placeholder=self.tr('Select Group'),
                                                                key='system_user_group_input', show_arrow=True,
                                                                is_read_only=True)

        layout_user_infor.addWidget(self.username_input)
        layout_user_infor.addWidget(self.full_name_input)
        layout_user_infor.addWidget(self.system_user_group_input)

        # Password status
        widget_password_status = QWidget()
        widget_password_status.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout_password_status = QHBoxLayout()
        layout_password_status.setAlignment(Qt.AlignmentFlag.AlignTop)
        subsystem_text = self.tr('Subsystem')
        password_text = self.tr('Password')
        status_text = self.tr('Status')
        message_password = self.tr(
            'The password must be a minimum of 6 characters and a maximum of 32 characters, including uppercase and lowercase letters, numbers, and special characters.')
        self.subsystem_input = ComboBoxWithRequireField(title=subsystem_text, text_placeholder=self.tr('Subsystem'),
                                                        key='subsystem_input', is_required_fields=True,
                                                        list_data={'EMS': 1, 'VMS': 2, 'APP': 3})
        self.subsystem_input.combo_box.setCurrentIndex(-1)
        self.password_input = InputWithRequireField(title=password_text, text_placeholder=self.tr('Enter Password'),
                                                    key='password_input', is_required_fields=True, limit_length=32,
                                                    is_password_line=True,
                                                    suggestion_message=message_password)
        self.password_input.label_suggestion.setStyleSheet(f'color: {Style.PrimaryColor.error}; font-weight: 400; font-style: italic; font-size: 11px')
        self.status_input = ComboBoxWithRequireField(title=status_text,
                                                     list_data={self.tr('Active'): 1, self.tr('In-active'): 0},
                                                     key='status_input')

        layout_password_status.addWidget(self.subsystem_input, 33)
        layout_password_status.addWidget(self.password_input, 33)
        layout_password_status.addWidget(self.status_input, 33)

        # Contact
        widget_contact = QWidget()
        widget_contact.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Fixed)
        layout_contact = QHBoxLayout()
        layout_contact.setAlignment(Qt.AlignmentFlag.AlignTop)
        phone_number_text = self.tr('Phone number')
        position_text = self.tr('Position')
        male_female_text = self.tr('Gender')
        self.phone_number_input = InputWithRequireField(title=phone_number_text,
                                                        text_placeholder=self.tr('Phone Number'),
                                                        key='phone_number_input', limit_length=10)
        self.position_input = InputWithRequireField(title=position_text, text_placeholder=self.tr('Position'),
                                                    key='position_input', is_required_fields=False, limit_length=255)
        self.gender_input = ComboBoxWithRequireField(title=male_female_text,
                                                     list_data={self.tr('Male'): 1, self.tr('Female'): 2},
                                                     key='gender_input')

        layout_contact.addWidget(self.phone_number_input, 33)
        layout_contact.addWidget(self.position_input, 33)
        layout_contact.addWidget(self.gender_input, 33)

        # Email
        widget_email = QWidget()
        layout_email = QHBoxLayout()
        layout_email.setAlignment(Qt.AlignmentFlag.AlignTop)
        email_text = self.tr('Email')
        self.email_input = InputWithRequireField(title=email_text, text_placeholder=self.tr('Email'),
                                                 key='email_input', limit_length=255, focus_out_callback=self.check_email)
        layout_email.addWidget(self.email_input, 33)
        layout_email.addWidget(QWidget(), 33)
        layout_email.addWidget(QWidget(), 33)

        widget_avatar.setLayout(layout_avatar)
        widget_user_infor.setLayout(layout_user_infor)
        widget_password_status.setLayout(layout_password_status)
        widget_contact.setLayout(layout_contact)
        widget_email.setLayout(layout_email)
        self.layout_dialog.addWidget(widget_avatar)
        self.layout_dialog.addWidget(widget_user_infor)
        self.layout_dialog.addWidget(widget_password_status)
        self.layout_dialog.addWidget(widget_contact)
        self.layout_dialog.addWidget(widget_email)

    def update_style(self):
        self.setStyleSheet(
            f'''
            QWidget {{
                background-color: {Style.PrimaryColor.on_background};
                color: {Style.PrimaryColor.white_2};
            }}
        ''')

    def check_username(self):
        self.username_input.line_edit.clearFocus()
        text = self.username_input.line_edit.text().strip()

        if text:
            self.controller.check_user_information(type_check='username', data=f'username={text}')
        else:
            self.username_input.label_suggestion.setText(self.tr('Please enter a valid username.'))

    def check_email(self):
        self.email_input.line_edit.clearFocus()
        text = self.email_input.line_edit.text().strip()
        email_pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
        if not re.match(email_pattern, text) or not text:
            self.email_input.label_suggestion.setText(self.tr('Please enter a valid email address.\n '))
        else:
            self.controller.check_user_information(type_check='email', data=f'email={text}')

    def result_check_information(self, data):
        type_check, message = data
        if type_check == 'username':
            if message == 'Success':
                self.is_username_exist = False
                self.username_input.label_suggestion.setVisible(False)
            else:
                self.is_username_exist = True
                self.username_input.label_suggestion.setVisible(True)
                self.username_input.label_suggestion.setText(self.tr('Username is existing.'))
        elif type_check == 'email':
            if message == 'Success':
                self.is_email_exist = False
                self.email_input.label_suggestion.setVisible(False)
            else:
                self.is_email_exist = True
                self.email_input.label_suggestion.setVisible(True)
                self.email_input.label_suggestion.setText(self.tr('Email is existing.'))
        else:
            pass

    def save_clicked(self):
        self.hide_all_suggestion()
        if self.username_input.line_edit.text() == '' or self.is_username_exist:
            self.username_input.label_suggestion.setText(self.tr('Please enter a valid username.'))
            return
        elif self.full_name_input.line_edit.text() == '':
            self.full_name_input.label_suggestion.setText(self.tr('Please enter the full name.'))
            return
        elif self.system_user_group_input.line_edit.text() == '':
            self.system_user_group_input.label_suggestion.setText(self.tr('Please choose an user group.'))
            return
        elif self.subsystem_input.combo_box.currentIndex() == -1:
            self.subsystem_input.label_suggestion.setText(self.tr('Please choose a subsystem.'))
            return
        elif self.password_input.line_edit.text() == '':
            self.password_input.label_suggestion.setText(self.tr('Please enter a valid password.'))
            return
        elif self.is_email_exist:
            self.email_input.label_suggestion.setText(self.tr('Email is existing.'))
            return
        # avatar
        if self.controller.pick_file_model is not None:
            avt_url = self.controller.pick_file_model.url
        else:
            avt_url = ''
            self.avatar_area.label_suggestion_avatar.setText(self.tr('Please select an avatar image.'))
            return
            # Notifications(parent=self.controller.list_parent['DeviceScreen'],
            #               title=self.tr('Please Enter Complete Information'), icon=Style.PrimaryImage.info_result)

        username = self.username_input.line_edit.text().strip()
        full_name = self.full_name_input.line_edit.text().strip()
        user_group = self.system_user_group_input.line_edit.text().strip()
        client_id = self.subsystem_input.combo_box.currentData()
        password = self.password_input.line_edit.text().strip()
        gender = self.gender_input.combo_box.currentData()
        position = self.position_input.line_edit.text()
        phone_number = self.phone_number_input.line_edit.text()
        status = self.status_input.combo_box.currentData()
        email = self.email_input.line_edit.text().strip()

        user_roles = []
        for item in self.system_user_group_input.list_role_ids:
            role = UserRole(roleId=item['roleId'])
            user_roles.append(role)

        user = Users(username=username, fullName=full_name, rolesName=user_group,
                     password=password, clientId=client_id, avatar=avt_url, gender=gender,
                     userRoles=user_roles, status=status, position=position, phone=phone_number, email=email)

        self.controller.create_new_user(parent=self, user_data=user)
        self.close()

    def hide_all_suggestion(self):
        self.username_input.label_suggestion.setText(' \n ')
        self.full_name_input.label_suggestion.setText(' \n ')
        self.system_user_group_input.label_suggestion.setText(' \n ')
        self.subsystem_input.label_suggestion.setText(' \n ')
        self.password_input.label_suggestion.setText(' \n ')
        self.avatar_area.label_suggestion_avatar.setText(' \n ')

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if self.username_input.line_edit.hasFocus():
                self.username_input.line_edit.clearFocus()
            if self.full_name_input.line_edit.hasFocus():
                self.full_name_input.line_edit.clearFocus()
            if self.password_input.line_edit.hasFocus():
                self.password_input.line_edit.clearFocus()
            if self.position_input.line_edit.hasFocus():
                self.position_input.line_edit.clearFocus()
            if self.phone_number_input.line_edit.hasFocus():
                self.phone_number_input.line_edit.clearFocus()
            if self.email_input.line_edit.hasFocus():
                self.email_input.line_edit.clearFocus()
        super().mousePressEvent(event)

    def fade_in(self):
        self.setWindowOpacity(0)

        # Define the starting and ending geometries for the zoom effect
        full_geometry = self.geometry()
        end_geometry = QRect(full_geometry.x(), full_geometry.y() - full_geometry.height() // 2, full_geometry.width(), full_geometry.height())
        center = full_geometry.center()

        small_size = QSize(int(full_geometry.width()), int(full_geometry.height() * 0.4))
        small_bottom_left = QPoint(center.x() - small_size.width() // 2 + 2, full_geometry.bottom() - small_size.height())
        small_geometry = QRect(small_bottom_left, small_size)

        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(self.animation_duration)
        self.opacity_animation.setStartValue(0)
        self.opacity_animation.setEndValue(1)
        self.opacity_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.zoom_animation = QPropertyAnimation(self, b"geometry")
        self.zoom_animation.setDuration(self.animation_duration)
        self.zoom_animation.setStartValue(small_geometry)
        self.zoom_animation.setEndValue(end_geometry)
        self.zoom_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.animation_group = QParallelAnimationGroup()
        self.animation_group.addAnimation(self.opacity_animation)
        self.animation_group.addAnimation(self.zoom_animation)
        self.animation_group.start()


class UserInformationDialog(NewBaseDialog):
    signal_create_clicked = Signal()

    def __init__(self, parent=None, just_view_infor=False, user_model=None, controller: Controller = None):
        model_temp = copy.deepcopy(user_model.data)
        self.user_model = UserModel(user=model_temp)
        self.just_view_infor = just_view_infor
        self.controller = controller
        self.load_ui()
        title = self.tr("USER DETAIL")
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_MEDIUM)
        widget_main.setLayout(self.layout_dialog)
        if just_view_infor:
            footer_type = FooterType.CLOSE
        else:
            footer_type = FooterType.UPDATE_CANCEL
        super().__init__(parent, title=title, content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_MEDIUM,
                         footer_type=footer_type)
        self.setObjectName("UserInformationDialog")
        self.save_update_signal.connect(self.save_clicked)
        self.update_style()
        self.load_data()

    def load_ui(self):
        self.layout_dialog = QVBoxLayout()
        self.layout_dialog.setSpacing(0)
        # Avatar
        widget_avatar = QWidget()
        layout_avatar = QVBoxLayout()
        layout_avatar.setSpacing(6)
        avt_text = self.tr('Avatar')
        if self.just_view_infor:
            html_avt = avt_text
        else:
            html_avt = f'<span style="color: {main_controller.get_theme_attribute("Color", "dialog_text")};">{avt_text} </span><span style="color: red;">*</span>'
        self.label_avatar = QLabel(html_avt)
        self.label_avatar.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')}")
        self.avatar_area = PickImageArea(just_view_image=self.just_view_infor, data_available=True,
                                         controller=self.controller)
        self.avatar_area.view_pixmap_signal.connect(self.view_avatar)
        layout_avatar.addWidget(self.label_avatar)
        layout_avatar.addWidget(self.avatar_area)

        # User Infor
        widget_user_infor = QWidget()
        layout_user_infor = QHBoxLayout()
        layout_user_infor.setAlignment(Qt.AlignmentFlag.AlignTop)
        username_text = self.tr('Username')
        full_name_text = self.tr('Full Name')
        system_user_group_text = self.tr('System User Group')
        if self.just_view_infor:
            html_system_user_group = system_user_group_text
        else:
            html_system_user_group = f'<span style="color: {main_controller.get_theme_attribute("Color", "dialog_text")};">{system_user_group_text} </span><span style="color: red;">*</span>'
        self.username_input = InputWithRequireField(title=username_text, text_placeholder=self.tr('Enter Username'),
                                                    key='username_input', is_required_fields=True, limit_length=64,
                                                    just_view_infor=True)
        self.full_name_input = InputWithRequireField(title=full_name_text, text_placeholder=self.tr('Enter Full Name'),
                                                     key='full_name_input', is_required_fields=True, limit_length=50,
                                                     just_view_infor=self.just_view_infor)

        self.system_user_group_input = InputCallbackWithMessage(title=html_system_user_group,
                                                                tree_view_type=TreeViewType.USER_ROLE,
                                                                text_placeholder=self.tr('Select Group'),
                                                                key='system_user_group_input', show_arrow=True,
                                                                is_read_only=True, just_view_infor=self.just_view_infor,
                                                                model=self.user_model)
        self.system_user_group_input.line_edit.setReadOnly(True)

        layout_user_infor.addWidget(self.username_input)
        layout_user_infor.addWidget(self.full_name_input)
        layout_user_infor.addWidget(self.system_user_group_input)

        # Password status
        widget_subsystem_status = QWidget()
        layout_subsystem_status = QHBoxLayout()
        layout_subsystem_status.setAlignment(Qt.AlignmentFlag.AlignTop)
        subsystem_text = self.tr('Subsystem')
        phone_number_text = self.tr('Phone number')
        status_text = self.tr('Status')
        self.subsystem_input = ComboBoxWithRequireField(title=subsystem_text, text_placeholder=self.tr('Subsystem'),
                                                        key='subsystem_input', is_required_fields=True,
                                                        list_data={'EMS': 1, 'VMS': 2, 'APP': 3},
                                                        just_view_infor=self.just_view_infor)
        self.status_input = ComboBoxWithRequireField(title=status_text,
                                                     list_data={self.tr('Active'): 1, self.tr('In-active'): 0},
                                                     key='status_input', just_view_infor=self.just_view_infor)
        self.phone_number_input = InputWithRequireField(title=phone_number_text,
                                                        text_placeholder=self.tr('Phone Number'),
                                                        key='phone_number_input', limit_length=10,
                                                        just_view_infor=self.just_view_infor)

        layout_subsystem_status.addWidget(self.subsystem_input, 33)
        layout_subsystem_status.addWidget(self.status_input, 33)
        layout_subsystem_status.addWidget(self.phone_number_input, 33)

        # Contact
        widget_position_email = QWidget()
        layout_position_email = QHBoxLayout()
        layout_position_email.setAlignment(Qt.AlignmentFlag.AlignTop)

        position_text = self.tr('Position')
        male_female_text = self.tr('Gender')
        email_text = self.tr('Email')
        self.email_input = InputWithRequireField(title=email_text, text_placeholder=self.tr('Email'),
                                                 key='email_input', limit_length=255,
                                                 just_view_infor=self.just_view_infor, focus_out_callback=self.check_email)
        self.position_input = InputWithRequireField(title=position_text, text_placeholder=self.tr('Position'),
                                                    key='position_input', is_required_fields=False, limit_length=255,
                                                    just_view_infor=self.just_view_infor)
        self.gender_input = ComboBoxWithRequireField(title=male_female_text,
                                                     list_data={self.tr('Male'): 1, self.tr('Female'): 2},
                                                     key='gender_input', just_view_infor=self.just_view_infor)

        layout_position_email.addWidget(self.position_input, 33)
        layout_position_email.addWidget(self.gender_input, 33)
        layout_position_email.addWidget(self.email_input, 33)

        widget_avatar.setLayout(layout_avatar)
        widget_user_infor.setLayout(layout_user_infor)
        widget_subsystem_status.setLayout(layout_subsystem_status)
        widget_position_email.setLayout(layout_position_email)
        self.layout_dialog.addWidget(widget_avatar)
        self.layout_dialog.addWidget(widget_user_infor)
        self.layout_dialog.addWidget(widget_subsystem_status)
        self.layout_dialog.addWidget(widget_position_email)

    def load_data(self):
        if self.user_model is not None:
            # Avatar
            if self.user_model.data.avatar:
                self.avatar_area.load_image(self.user_model.data.avatar)
            # Username
            if self.user_model.data.username:
                self.username_input.setText(self.user_model.data.username)
            # Fullname
            if self.user_model.data.fullName:
                self.full_name_input.setText(self.user_model.data.fullName)
            # System user group
            if self.user_model.data.rolesName:
                self.system_user_group_input.line_edit.setText(self.user_model.data.rolesName)
            # Subsystem
            if self.user_model.data.clientId:
                self.subsystem_input.setValue(self.user_model.data.clientId)
            # Status
            if self.user_model.data.status:
                self.status_input.setValue(self.user_model.data.status)
            # Phone number
            if self.user_model.data.phone:
                self.phone_number_input.setText(self.user_model.data.phone)
            # Position
            if self.user_model.data.position:
                self.position_input.setText(self.user_model.data.position)
            # Gender
            if self.user_model.data.gender:
                self.gender_input.setValue(self.user_model.data.gender)
            # Email
            if self.user_model.data.email:
                self.email_input.setText(self.user_model.data.email)

    def update_style(self):
        self.setStyleSheet(
            f'''
            QWidget {{
                background-color: {Style.PrimaryColor.on_background};
                color: {Style.PrimaryColor.white_2};
            }}
        ''')

    def save_clicked(self):
        # self.hide_all_suggestion()

        if self.full_name_input.line_edit.text() == '':
            self.full_name_input.label_suggestion.setText(self.tr('Please enter the full name.'))
            return
        elif self.system_user_group_input.line_edit.text() == '':
            self.system_user_group_input.label_suggestion.setText(self.tr('Please choose an user group.'))
            return
        elif self.subsystem_input.combo_box.currentIndex() == -1:
            self.subsystem_input.label_suggestion.setText(self.tr('Please choose a subsystem.'))
            return
        elif self.is_email_exist:
            self.email_input.label_suggestion.setText(self.tr('Email is existing.'))
            return
        # avatar
        if self.controller.pick_file_model is not None:
            avt_url = self.controller.pick_file_model.url
        else:
            avt_url = ''
            self.avatar_area.label_suggestion_avatar.setText(self.tr('Please select an avatar image.'))
            return
        self.user_model.data.avatar = avt_url
        self.user_model.data.fullName = self.full_name_input.line_edit.text().strip()
        self.user_model.data.rolesName = self.system_user_group_input.line_edit.text().strip()
        self.user_model.data.clientId = self.subsystem_input.combo_box.currentData()
        self.user_model.data.gender = self.gender_input.combo_box.currentData()
        self.user_model.data.position = self.position_input.line_edit.text()
        self.user_model.data.phone = self.phone_number_input.line_edit.text()
        self.user_model.data.status = self.status_input.combo_box.currentData()
        self.user_model.data.email = self.email_input.line_edit.text()

        user_roles = []
        for item in self.system_user_group_input.list_role_ids:
            role = UserRole(roleId=item['roleId'])
            user_roles.append(role)
        self.user_model.data.userRoles = user_roles

        self.controller.update_user_information_by_put(parent=self, user_data=self.user_model.data)
        self.close()

    def view_avatar(self, pixmap):
        dialog = ImageAvatarDialog(pixmap=pixmap)
        dialog.exec()

    def check_email(self):
        self.email_input.line_edit.clearFocus()
        text = self.email_input.line_edit.text().strip()
        email_pattern = r"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"
        if not re.match(email_pattern, text) or not text:
            self.email_input.label_suggestion.setText(self.tr('Please enter a valid email address.\n '))
        else:
            self.controller.check_user_information(type_check='email', data=f'email={text}')

    def result_check_information(self, data):
        type_check, message = data
        if type_check == 'username':
            if message == 'Success':
                self.is_username_exist = False
                self.username_input.label_suggestion.setVisible(False)
            else:
                self.is_username_exist = True
                self.username_input.label_suggestion.setVisible(True)
                self.username_input.label_suggestion.setText(self.tr('Username is existing.'))
        elif type_check == 'email':
            if message == 'Success':
                self.is_email_exist = False
                self.email_input.label_suggestion.setVisible(False)
            else:
                self.is_email_exist = True
                self.email_input.label_suggestion.setVisible(True)
                self.email_input.label_suggestion.setText(self.tr('Email is existing.'))
        else:
            pass

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if self.username_input.line_edit.hasFocus():
                self.username_input.line_edit.clearFocus()
            if self.full_name_input.line_edit.hasFocus():
                self.full_name_input.line_edit.clearFocus()
            if self.position_input.line_edit.hasFocus():
                self.position_input.line_edit.clearFocus()
            if self.phone_number_input.line_edit.hasFocus():
                self.phone_number_input.line_edit.clearFocus()
            if self.email_input.line_edit.hasFocus():
                self.email_input.line_edit.clearFocus()
        super().mousePressEvent(event)

    def fade_in(self):
        self.setWindowOpacity(0)

        # Define the starting and ending geometries for the zoom effect
        full_geometry = self.geometry()
        end_geometry = QRect(full_geometry.x(), full_geometry.y() - full_geometry.height() // 2, full_geometry.width(), full_geometry.height())
        center = full_geometry.center()

        small_size = QSize(int(full_geometry.width()), int(full_geometry.height() * 0.4))
        small_bottom_left = QPoint(center.x() - small_size.width() // 2 + 2, full_geometry.bottom() - small_size.height())
        small_geometry = QRect(small_bottom_left, small_size)

        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(self.animation_duration)
        self.opacity_animation.setStartValue(0)
        self.opacity_animation.setEndValue(1)
        self.opacity_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.zoom_animation = QPropertyAnimation(self, b"geometry")
        self.zoom_animation.setDuration(self.animation_duration)
        self.zoom_animation.setStartValue(small_geometry)
        self.zoom_animation.setEndValue(end_geometry)
        self.zoom_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.animation_group = QParallelAnimationGroup()
        self.animation_group.addAnimation(self.opacity_animation)
        self.animation_group.addAnimation(self.zoom_animation)
        self.animation_group.start()


class ImageAvatarDialog(NewBaseDialog):

    def __init__(self, parent=None, pixmap: QPixmap = None):
        self.pixmap = pixmap
        self.load_ui()

        title = self.tr("AVATAR")
        self.widget_main = QWidget()
        self.widget_main.setFixedWidth(Config.WIDTH_DIALOG_MINI)
        self.widget_main.setLayout(self.layout_dialog)
        super().__init__(parent, title=title, content_widget=self.widget_main, width_dialog=Config.WIDTH_DIALOG_MINI,
                         footer_type=FooterType.NONE_TYPE)
        self.setObjectName("ImageAvatarDialog")
        self.widget_main.setStyleSheet(f"""
            border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_header_background")};
        """)

    def load_ui(self):
        self.layout_dialog = QVBoxLayout()
        self.label_avatar = QLabel()
        self.label_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_avatar.setPixmap(
            self.pixmap.scaled(Config.WIDTH_DIALOG_MINI, Config.WIDTH_DIALOG_MINI, Qt.AspectRatioMode.KeepAspectRatio,
                               Qt.TransformationMode.SmoothTransformation))
        # Optionally, you can remove any scaling behavior
        self.label_avatar.setScaledContents(False)

        self.label_avatar.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout_dialog.addWidget(self.label_avatar)

class AddRoleDialog(NewBaseDialog):
    def __init__(self, parent=None, controller: Controller = None):
        screen = QGuiApplication.primaryScreen()
        self.controller = controller
        self.desktop_screen_size = screen.availableGeometry()
        self.load_ui()
        title = self.tr('ADD NEW ROLE')
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_LARGE)
        widget_main.setLayout(self.layout_main)

        super().__init__(parent, title=title, content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_LARGE,
                         min_height_dialog=640)
        self.setObjectName("AddRoleDialog")
        self.save_update_signal.connect(self.update_clicked)
        self.widget_stacked_info.addWidget(self.tab_system_permissions)
        self.widget_stacked_info.addWidget(self.tab_list_users)
        self.update_style()

    def load_ui(self):
        self.layout_main = QVBoxLayout()
        self.layout_main.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.widget_stacked_info = QStackedWidget()
        self.widget_stacked_info.setStyleSheet(
            f'''
            QStackedWidget {{
                background-color: transparent;
            }}
        '''
        )
        self.init_widget_button_config()
        self.init_widget_system_permissions()
        self.init_widget_list_of_users()

        self.layout_main.addWidget(self.widget_button_tab)
        self.layout_main.addWidget(self.widget_stacked_info)

    def init_widget_button_config(self):
        self.widget_button_tab = QWidget()
        self.widget_button_tab.setStyleSheet(f'border-bottom: 1px solid {Style.PrimaryColor.button_second_background}')
        self.layout_button_tab = QHBoxLayout()
        self.layout_button_tab.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.layout_button_tab.setContentsMargins(0, 0, 0, 0)
        self.btn_system_permissions = QPushButton(self.tr('System Permissions'))
        self.btn_system_permissions.clicked.connect(self.btn_permissions_clicked)
        self.btn_list_of_users = QPushButton(self.tr('List Of Users'))
        self.btn_list_of_users.clicked.connect(self.btn_list_users_clicked)
        self.layout_button_tab.addWidget(self.btn_system_permissions)
        self.layout_button_tab.addWidget(self.btn_list_of_users)
        self.widget_button_tab.setLayout(self.layout_button_tab)
        self.update_stylesheet_button()

    def init_widget_system_permissions(self):
        list_permission = function_menu_model_manager.get_function_menu_list(self.controller.server.data.server_ip)
        list_profile_group = profile_group_model_manager.get_profile_group_list(self.controller.server.data.server_ip)
        group_list = group_model_manager.get_group_list(server_ip=self.controller.server.data.server_ip)
        camera_list = camera_model_manager.get_camera_list(server_ip=self.controller.server.data.server_ip)
        camera_non_group_list = []
        for camera_id, camera_model in camera_list.items():
            if camera_model.get_property("cameraGroupIds") is None or len(camera_model.get_property("cameraGroupIds")) == 0:
                camera_non_group_list.append(camera_model)

        self.tab_system_permissions = WidgetConfigPermissionRole(controller=self.controller,
                                                                 data_permission=list_permission,
                                                                 data_profile_group=list_profile_group)

    def init_widget_list_of_users(self):
        self.tab_list_users = WidgetConfigListUsersRole(controller=self.controller)

    # Slot
    def btn_permissions_clicked(self):
        self.btn_system_permissions.setStyleSheet(self.style_sheet_active_button)
        self.btn_list_of_users.setStyleSheet(self.style_sheet_inactive_button)
        self.widget_stacked_info.setCurrentIndex(0)

    def btn_list_users_clicked(self):
        self.btn_list_of_users.setStyleSheet(self.style_sheet_active_button)
        self.btn_system_permissions.setStyleSheet(self.style_sheet_inactive_button)
        self.widget_stacked_info.setCurrentIndex(1)

    def update_clicked(self):
        if self.tab_system_permissions.name_group_input.text() == '':
            self.tab_system_permissions.name_group_input.label_suggestion.setText(self.tr('Please enter the group name.'))
            return

        """name: str = None
        roleCameras: List[RoleCamera] = None
        roleGroups: List[RoleGroup] = None
        roleMenus: List[RoleMenu] = None
        status: int = None
        userRoles: List[UserRole] = None"""
        role_cameras = []
        role_groups = []
        role_menus = []
        user_roles = []
        role_name = self.tab_system_permissions.name_group_input.text().strip()
        description = self.tab_system_permissions.description_input.text().strip()
        status = self.tab_system_permissions.status_input.combo_box.currentData()
        role_camera_ids = self.tab_system_permissions.treeview_camera_management.get_id_item_checked()
        role_menus_ids = self.tab_system_permissions.treeview_system_permission.get_id_item_checked()
        role_group_ids = self.tab_system_permissions.treeview_case_management.get_id_item_checked()
        user_ids = self.tab_list_users.list_user_id

        def create_role_objects(cls, ids, attr):
            return [cls(**{attr: idx}) for idx in ids] if ids else []

        with ThreadPoolExecutor() as executor:
            futures = []
            if role_camera_ids:
                futures.append(executor.submit(create_role_objects, RoleCamera, role_camera_ids, 'cameraId'))
            if role_menus_ids:
                futures.append(executor.submit(create_role_objects, RoleMenu, role_menus_ids, 'menuId'))
            if role_group_ids:
                futures.append(executor.submit(create_role_objects, RoleGroup, role_group_ids, 'groupId'))
            if user_ids:
                futures.append(executor.submit(create_role_objects, UserRole, user_ids, 'userId'))

            # Collect results only from non-empty lists
            for future in futures:
                result = future.result()
                if isinstance(result[0], RoleCamera):
                    role_cameras = result
                elif isinstance(result[0], RoleMenu):
                    role_menus = result
                elif isinstance(result[0], RoleGroup):
                    role_groups = result
                elif isinstance(result[0], UserRole):
                    user_roles = result

        new_role = Role(name=role_name, description=description, status=status, roleCameras=role_cameras,
                        roleGroups=role_groups, roleMenus=role_menus, userRoles=user_roles)

        self.controller.create_new_role(new_role)
        self.close()

    # Style sheet
    def update_stylesheet_button(self):
        self.style_sheet_active_button = Style.PrimaryStyleSheet.get_dialog_tabbutton_active_style(theme_instance=main_controller)
        self.style_sheet_inactive_button = Style.PrimaryStyleSheet.get_dialog_tabbutton_inactive_style(theme_instance=main_controller)

        self.btn_system_permissions.setStyleSheet(self.style_sheet_active_button)
        self.btn_list_of_users.setStyleSheet(self.style_sheet_inactive_button)

    def update_style(self):
        self.setStyleSheet(
            f'''
            QWidget {{
                background-color: transparent;
                color: {Style.PrimaryColor.white_2};
            }}
            '''
        )

class RoleInfoDialog(NewBaseDialog):
    def __init__(self, parent=None, controller: Controller = None, just_view_info=False, role_model=None):
        screen = QGuiApplication.primaryScreen()
        self.just_view_info = just_view_info
        model_temp = copy.deepcopy(role_model.data)
        self.role_model = RoleModel(role=model_temp)

        self.controller = controller
        self.desktop_screen_size = screen.availableGeometry()
        self.load_ui()
        title = self.tr('ROLE INFORMATION')
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_LARGE)
        widget_main.setLayout(self.layout_main)

        super().__init__(parent, title=title, content_widget=widget_main, width_dialog=Config.WIDTH_DIALOG_LARGE,
                         min_height_dialog=640)
        self.setObjectName("RoleInfoDialog")
        self.save_update_signal.connect(self.update_clicked)
        self.widget_stacked_info.addWidget(self.tab_system_permissions)
        self.widget_stacked_info.addWidget(self.tab_list_users)
        self.update_style()

    def load_ui(self):
        self.layout_main = QVBoxLayout()
        self.layout_main.setAlignment(Qt.AlignmentFlag.AlignTop)
        # self.scroll = QScrollArea()
        # self.scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        # self.scroll.setWidgetResizable(True)
        # self.scroll.resize(self.desktop_screen_size.width() * 0.6,
        #                    self.desktop_screen_size.height() * 0.8)
        # self.scroll.setStyleSheet(Style.StyleSheet.scrollbar_ver_style)
        # self.widget_content = QWidget()
        # self.layout_content = QVBoxLayout()
        # self.layout_content.setAlignment(Qt.AlignmentFlag.AlignTop)
        # self.widget_content.setLayout(self.layout_content)
        # self.layout.addWidget(self.scroll)
        # self.scroll.setWidget(self.widget)

        self.widget_stacked_info = QStackedWidget()
        self.widget_stacked_info.setStyleSheet(
            f'''
                        QStackedWidget {{
                            background-color: transparent;
                        }}
                    '''
        )

        self.init_widget_button_config()
        self.init_widget_system_permissions()
        self.init_widget_list_of_users()

        self.layout_main.addWidget(self.widget_button_tab)
        self.layout_main.addWidget(self.widget_stacked_info)

    def init_widget_button_config(self):
        self.widget_button_tab = QWidget()
        self.widget_button_tab.setStyleSheet(f'border-bottom: 1px solid {Style.PrimaryColor.button_second_background}')
        self.layout_button_tab = QHBoxLayout()
        self.layout_button_tab.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.layout_button_tab.setContentsMargins(0, 0, 0, 0)
        self.btn_system_permissions = QPushButton(self.tr('System Permissions'))
        self.btn_system_permissions.clicked.connect(self.btn_permissions_clicked)
        self.btn_list_of_users = QPushButton(self.tr('List Of Users'))
        self.btn_list_of_users.clicked.connect(self.btn_list_users_clicked)
        self.layout_button_tab.addWidget(self.btn_system_permissions)
        self.layout_button_tab.addWidget(self.btn_list_of_users)
        self.widget_button_tab.setLayout(self.layout_button_tab)
        self.update_stylesheet_button()

    def init_widget_system_permissions(self):
        list_permission = function_menu_model_manager.get_function_menu_list(self.controller.server.data.server_ip)
        list_profile_group = profile_group_model_manager.get_profile_group_list(self.controller.server.data.server_ip)
        group_list = group_model_manager.get_group_list(server_ip=self.controller.server.data.server_ip)
        camera_list = camera_model_manager.get_camera_list(server_ip=self.controller.server.data.server_ip)
        camera_non_group_list = []
        for camera_id, camera_model in camera_list.items():
            if camera_model.get_property("cameraGroupIds")is None or len(camera_model.get_property("cameraGroupIds")) == 0:
                camera_non_group_list.append(camera_model)

        self.tab_system_permissions = WidgetConfigPermissionRole(controller=self.controller,
                                                                 data_permission=list_permission,
                                                                 data_profile_group=list_profile_group,
                                                                 is_view_only=self.just_view_info,
                                                                 role_model=self.role_model)

    def init_widget_list_of_users(self):
        self.tab_list_users = WidgetConfigListUsersRole(controller=self.controller, is_view_only=self.just_view_info,
                                                        role_model=self.role_model)

    # Slot
    def btn_permissions_clicked(self):
        self.btn_system_permissions.setStyleSheet(self.style_sheet_active_button)
        self.btn_list_of_users.setStyleSheet(self.style_sheet_inactive_button)
        self.widget_stacked_info.setCurrentIndex(0)

    def btn_list_users_clicked(self):
        self.btn_list_of_users.setStyleSheet(self.style_sheet_active_button)
        self.btn_system_permissions.setStyleSheet(self.style_sheet_inactive_button)
        self.widget_stacked_info.setCurrentIndex(1)

    def update_clicked(self):
        if self.tab_system_permissions.name_group_input.text() == '':
            self.tab_system_permissions.name_group_input.label_suggestion.setText(self.tr('Please enter the group name.'))
            return
        role_cameras = []
        role_groups = []
        role_menus = []
        user_roles = []
        self.role_model.data.name = self.tab_system_permissions.name_group_input.text().strip()
        self.role_model.data.description = self.tab_system_permissions.description_input.text().strip()
        self.role_model.data.status = self.tab_system_permissions.status_input.combo_box.currentData()

        role_camera_ids = self.tab_system_permissions.treeview_camera_management.get_id_item_checked()
        role_menus_ids = self.tab_system_permissions.treeview_system_permission.get_id_item_checked()
        role_group_ids = self.tab_system_permissions.treeview_case_management.get_id_item_checked()
        user_ids = self.tab_list_users.list_user_id

        def create_role_objects(cls, ids, attr):
            return [cls(**{attr: idx}) for idx in ids] if ids else []

        with ThreadPoolExecutor() as executor:
            futures = []
            if role_camera_ids:
                futures.append(executor.submit(create_role_objects, RoleCamera, role_camera_ids, 'cameraId'))
            if role_menus_ids:
                futures.append(executor.submit(create_role_objects, RoleMenu, role_menus_ids, 'menuId'))
            if role_group_ids:
                futures.append(executor.submit(create_role_objects, RoleGroup, role_group_ids, 'groupId'))
            if user_ids:
                futures.append(executor.submit(create_role_objects, UserRole, user_ids, 'userId'))

            # Collect results only from non-empty lists
            for future in futures:
                result = future.result()
                if isinstance(result[0], RoleCamera):
                    role_cameras = result
                elif isinstance(result[0], RoleMenu):
                    role_menus = result
                elif isinstance(result[0], RoleGroup):
                    role_groups = result
                elif isinstance(result[0], UserRole):
                    user_roles = result

        self.role_model.data.roleCameras = role_cameras
        self.role_model.data.roleGroups = role_groups
        self.role_model.data.roleMenus = role_menus
        self.role_model.data.userRoles = user_roles

        self.controller.update_role_information_by_put(data=self.role_model.data)
        self.close()

    # Style sheet
    def update_stylesheet_button(self):
        self.style_sheet_active_button = f'''
                                    QPushButton{{
                                        background-color: {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                                        padding: 4px 16px 4px 16px;
                                        color: {Style.PrimaryColor.white_3};
                                        border: None;
                                        border-top-left-radius: 4px;  /* Set top-left border radius */
                                        border-top-right-radius: 4px;
                                        font-size: 14px
                                    }}
                                '''
        self.style_sheet_inactive_button = f'''
                                    QPushButton{{
                                        background-color: transparent;
                                        padding: 4px 16px 4px 16px;
                                        color: {main_controller.get_theme_attribute("Color", "subtabbar_text_normal")};
                                        border: None;
                                        font-size: 14px
                                    }}
                                '''

        self.btn_system_permissions.setStyleSheet(self.style_sheet_active_button)
        self.btn_list_of_users.setStyleSheet(self.style_sheet_inactive_button)

    def update_style(self):
        self.setStyleSheet(
            f'''
            QWidget {{
                background-color: transparent;
                color: {Style.PrimaryColor.white_2};
            }}
            '''
        )

import logging

from src.utils.theme_setting import theme_setting

logger = logging.getLogger(__name__)
import datetime
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QDialog
from PySide6.QtGui import QPixmap,QFont
from PySide6.QtCore import Qt
from src.styles.style import Style
from src.common.widget.event.event_combobox import EventComboB<PERSON>
from src.common.widget.event.calendar_combobox import CalendarComboBox
from src.common.controller.main_controller import main_controller
from src.common.model.camera_model import Camera, CameraModel, camera_model_manager
from src.common.model.group_model import Group, GroupModel, GroupModelManager
from src.common.widget.event.filter_mode import FilterMode
class FilterEventDialog(QDialog):
    def __init__(self):
        super().__init__()
        self.setModal(False)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        # self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        GroupModelManager.get_instance().add_group_list_signal.connect(self.update_group_list)
        camera_model_manager.add_camera_list_signal.connect(self.update_camera_list)

        self.event_bar = main_controller.list_parent['EventBar']
        
        self.filter_selected = {}
        self.create_filter_selected()
        self.main_layout = QVBoxLayout()
        self.font = QFont()
        # self.setFixedHeight(500)

        self.combobox_style = f'''
             QComboBox{{
                border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                border-radius: 4px;
                background: transparent;
                padding: 4px 16px;
            }}
            QComboBox::disabled{{
                border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                color: {Style.PrimaryColor.text_disable};
            }}

            QComboBox QAbstractItemView {{
                 border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                 color: {Style.PrimaryColor.white_2};
                 selection-background-color: {Style.PrimaryColor.on_background};
                 background-color: {Style.PrimaryColor.background};
                 border-radius: 4px;
            }}

            QComboBox QAbstractItemView::indicator:checked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}

            QComboBox QAbstractItemView::indicator:unchecked {{
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}

            QComboBox::drop-down {{
                url({Style.PrimaryImage.down_arrow_linedit});
             }}
            '''
        self.create_status_combobox()

        self.create_group_combobox()
        
        self.create_ai_combobox()
        
        self.create_camera_combobox()

        # self.create_calendar()
        self.calendar_combobox = CalendarComboBox()
        self.calendar_combobox.setObjectName(FilterMode.TimeRange)
        self.calendar_combobox.data_changed.connect(self.data_changed_signal)
        self.main_layout.addWidget(self.calendar_combobox)
        self.setMaximumSize(100, 100)

        self.setLayout(self.main_layout)
        self.setup_dynamic_style_sheet()
        self.setObjectName("FilterEventDialog")

    def create_status_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel(self.tr('Status'))
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px; font-weight: bold')
        self.event_combobox = EventComboBox()
        self.event_combobox.setObjectName(FilterMode.Status)
        self.event_combobox.data_changed.connect(self.data_changed_signal)
        self.event_combobox.setFont(self.font)
        self.event_combobox.setFixedWidth(300)
        self.event_combobox.addItem(self.tr('All'))
        self.event_combobox.addItem(self.tr('Check-In'))
        self.event_combobox.addItem(self.tr('Check-Out'))
        self.event_combobox.addItem(self.tr('Appear'))
        layout.addWidget(title)
        layout.addWidget(self.event_combobox)
        self.main_layout.addWidget(widget)

    def create_group_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel(self.tr('Group'))
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px; font-weight: bold')
        self.group_combobox = EventComboBox()
        self.group_combobox.setObjectName(FilterMode.Group)
        self.group_combobox.data_changed.connect(self.data_changed_signal)
        self.group_combobox.setFont(self.font)
        self.group_combobox.setFixedWidth(300)
        self.group_combobox.addItem(self.tr('All'))
        layout.addWidget(title)
        layout.addWidget(self.group_combobox)
        self.main_layout.addWidget(widget)

    def create_ai_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel(self.tr('AI Type'))
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px; font-weight: bold')
        self.ai_combobox = EventComboBox()
        self.ai_combobox.setObjectName(FilterMode.AI)
        self.ai_combobox.data_changed.connect(self.data_changed_signal)
        self.ai_combobox.setFont(self.font)
        self.ai_combobox.setFixedWidth(300)
        self.ai_combobox.addItem(self.tr('All'))
        self.ai_combobox.addItem(self.tr('Human'))
        self.ai_combobox.addItem(self.tr('Vehicle'))
        self.ai_combobox.addItem(self.tr('Crowd'))
        layout.addWidget(title)
        layout.addWidget(self.ai_combobox)
        self.main_layout.addWidget(widget)

    def create_camera_combobox(self):
        widget = QWidget()
        layout = QVBoxLayout(widget)
        title = QLabel(self.tr('Camera'))
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px; font-weight: bold')
        self.camera_combobox = EventComboBox()
        self.camera_combobox.setObjectName(FilterMode.Camera)
        self.camera_combobox.data_changed.connect(self.data_changed_signal)
        self.camera_combobox.setFont(self.font)
        self.camera_combobox.setFixedWidth(300)
        self.camera_combobox.addItem(self.tr('All'))
        layout.addWidget(title)
        layout.addWidget(self.camera_combobox)
        self.main_layout.addWidget(widget)
        
    def create_filter_selected(self):
        self.filter_selected[FilterMode.Status] = [self.tr('All')]
        self.filter_selected[FilterMode.Group] = [self.tr('All')]
        self.filter_selected[FilterMode.AI] = [self.tr('All')]
        self.filter_selected[FilterMode.Camera] = [self.tr('All')]
        start_date_converted = datetime.date.today().strftime("%Y-%m-%d 00:00:00")
        end_date_converted = datetime.date.today().strftime("%Y-%m-%d 23:59:59")
        self.filter_selected[FilterMode.TimeRange] = {'start_time': start_date_converted,'end_time':end_date_converted}

    def update_camera_list(self):
        camera_list = camera_model_manager.get_camera_list()
        for camera_model in camera_list:
            self.camera_combobox.addItem(camera_model.get_property('name'))

    def update_group_list(self):
        group_list = GroupModelManager.get_instance().get_group_list()
        for group_model in group_list:
            self.group_combobox.addItem(group_model.get_property('name'))

    def data_changed_signal(self,data):
        logger.debug(f'data_changed_signal = {data}')
        key, value = data
        self.filter_selected[key] = value
        self.event_bar.filter_mode.emit(self.filter_selected)    


    def setup_dynamic_style_sheet(self):
        self.setStyleSheet(f'''
            background-color: {main_controller.get_theme_attribute('Color', 'main_background')}; 
            color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')};
            ''')
        self.event_combobox.setStyleSheet(self.combobox_style)
        self.group_combobox.setStyleSheet(self.combobox_style)
        self.ai_combobox.setStyleSheet(self.combobox_style)
        self.camera_combobox.setStyleSheet(self.combobox_style)

        self.calendar_combobox.time_widget.setStyleSheet(f'''
                        QWidget#CalendarComboBox {{
                            border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                            border-radius: 4px;
                        }}
                        QWidget#CalendarComboBox:hover {{
                            border: 1px solid {Style.PrimaryColor.primary};
                            border-radius: 4px;
                        }}                  
                    ''')
        self.calendar_combobox.time_text.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; font-size: 12px;")


    def showAt(self, position):
        # Get the screen geometry
        # screen_geometry = QApplication.primaryScreen().availableGeometry()

        # # Calculate the new x and y position, ensuring they are within the bounds of the screen
        # x = max(0, min(position.x() - 100, screen_geometry.width() - self.width()))
        # y = max(0, min(position.y(), screen_geometry.height() - self.height()))

        # Move and show the dialog
        self.move(position.x() - 350, position.y())
        self.setModal(False)
        self.show()


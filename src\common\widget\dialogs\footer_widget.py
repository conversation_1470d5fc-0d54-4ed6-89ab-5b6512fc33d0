import enum
from PySide6.QtWidgets import QWidget, QHBoxLayout
from PySide6.QtGui import QIcon
from PySide6.QtCore import QSize, Signal
from src.common.widget.button.base_button import BaseButton, ButtonType
from src.styles.style import Style

class FooterType(enum.Enum):
    SAVE_CANCEL = 1
    SAVE_CANCEL_DELETE = 2
    SAVE = 3
    CANCEL = 4
    DELETE = 5
    UPDATE_CANCEL = 6
    UPDATE_CANCEL_DELETE = 7

class FooterInDialog(QWidget):
    positive_button_signal = Signal()
    negative_button_signal = Signal()
    delete_button_signal = Signal()
    def __init__(self, parent = None, footer_type=FooterType.SAVE_CANCEL):
        super().__init__(parent)
        self.footer_type = footer_type
        self._setup_ui()

    def _setup_ui(self):
        layout = QHBoxLayout()
        
        positive_button = BaseButton(self.tr("Save"), button_type=ButtonType.POSITIVE, size=QSize(100, 40))
        positive_button.clicked.connect(self.positive_button_signal.emit)
        negative_button = BaseButton(self.tr("Cancel"), button_type=ButtonType.NEGATIVE, size=QSize(100, 40))
        negative_button.clicked.connect(self.negative_button_signal.emit)
        delete_button = BaseButton(button_type=ButtonType.NEUTRAL, icon=QIcon(Style.PrimaryImage.trash_mini), icon_size=QSize(40, 40))
        delete_button.clicked.connect(self.delete_button_signal.emit)

        if self.footer_type == FooterType.SAVE_CANCEL:
            layout.addWidget(positive_button)
            layout.addWidget(negative_button)
        elif self.footer_type == FooterType.SAVE_CANCEL_DELETE:
            layout.addWidget(positive_button)
            layout.addWidget(negative_button)
            layout.addWidget(delete_button)
        elif self.footer_type == FooterType.SAVE:
            layout.addWidget(positive_button)
        elif self.footer_type == FooterType.CANCEL:
            layout.addWidget(negative_button)
        elif self.footer_type == FooterType.DELETE:
            layout.addWidget(delete_button)
        elif self.footer_type == FooterType.UPDATE_CANCEL:
            positive_button.setText(self.tr("Update"))
            layout.addWidget(positive_button)
            layout.addWidget(negative_button)
        elif self.footer_type == FooterType.UPDATE_CANCEL_DELETE:
            positive_button.setText(self.tr("Update"))
            layout.addWidget(positive_button)
            layout.addWidget(negative_button)
            layout.addWidget(delete_button)
        self.setLayout(layout)
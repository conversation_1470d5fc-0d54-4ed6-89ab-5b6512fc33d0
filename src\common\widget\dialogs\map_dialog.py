from PySide6.QtCore import QUrl
from PySide6.QtQuickWidgets import QQuickWidget
from PySide6.QtGui import QGuiApplication

from VMS import main_controller
from src.utils.config import Config 
from src.common.widget.dialogs.base_dialog import NewBaseDialog

class MapDialog(NewBaseDialog):
    def __init__(self, parent=None):
        title = self.tr("MAP")
        self.widget_main = QQuickWidget()
        # widget_main.engine().rootContext().setContextProperty('device_controller', self.device_controller)
        self.widget_main.setSource(QUrl("qrc:src/common/qml/map/MapPicker.qml"))
        self.widget_main.setResizeMode(QQuickWidget.SizeRootObjectToView)

        super().__init__(parent=parent, title=title, content_widget=self.widget_main, width_dialog=Config.WIDTH_DIALOG_LARGE, min_height_dialog=800)

        # align center
        window_geometry = main_controller.mainwindow_geometry
        x = window_geometry.x() + (window_geometry.width() - self.width()) // 2
        y = window_geometry.y() + (window_geometry.height() - self.height()) // 2
        self.move(x, y)

    def set_property(self, key, value):
        self.widget_main.engine().rootContext().setContextProperty(key, value)
    
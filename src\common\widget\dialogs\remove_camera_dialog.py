from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QLabel,
    QCheckBox,
    QSpacerItem,
    QSizePolicy
)
from PySide6.QtCore import Qt
from src.utils.config import Config
from src.utils.dialog_settings import dialog_settings
from src.common.controller.main_controller import main_controller
from src.common.widget.dialogs.base_dialog import NewBaseDialog, FooterType

class RemoveCameraDialog(NewBaseDialog):
    def __init__(self, parent=None, title='Delete Camera', ok_title='Delete', description='Are you sure you want to delete this camera?'):
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(10)  # Tăng khoảng cách giữa các widget

        # Label mô tả
        label_description = QLabel(description)
        label_description.setStyleSheet(f"""color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                                       background-color: transparent;
                                       font-weight: 500;""")

        # Thêm checkbox "Không hiển thị lại"
        self.dont_show_again_checkbox = QCheckBox(self.tr("Don't show again"))
        self.dont_show_again_checkbox.setStyleSheet(f"""
            color: {main_controller.get_theme_attribute("Color", "dialog_text")};
            background-color: transparent;
        """)

        # Thêm spacer để tạo khoảng cách
        spacer = QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Fixed)

        # Thêm các widget vào layout
        self.main_layout.addWidget(label_description)
        self.main_layout.addItem(spacer)
        self.main_layout.addWidget(self.dont_show_again_checkbox)

        footer_type = FooterType.OK_CANCEL
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_VERTICAL)
        widget_main.setLayout(self.main_layout)

        super().__init__(parent, title=title, content_widget=widget_main,
                         width_dialog=Config.WIDTH_DIALOG_VERTICAL, max_height_dialog=150, footer_type=footer_type)

        self.footer.button_save_update.setText(ok_title)
        self.save_update_signal.connect(self.accept)

    def accept(self):
        # Lưu trạng thái checkbox vào settings nếu được chọn
        if self.dont_show_again_checkbox.isChecked():
            dialog_settings.set_dont_show_delete_camera_dialog(True)
        super().accept()

    @staticmethod
    def shouldShow():
        # Kiểm tra xem có nên hiển thị dialog hay không
        return not dialog_settings.get_dont_show_delete_camera_dialog()

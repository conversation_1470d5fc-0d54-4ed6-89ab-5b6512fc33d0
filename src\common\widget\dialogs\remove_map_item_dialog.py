from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QLabel
)
from src.utils.config import Config
from src.common.controller.main_controller import main_controller
from src.common.widget.dialogs.base_dialog import NewBaseDialog, FooterType

class RemoveMapItemDialog(NewBaseDialog):
    def __init__(self, parent=None, title='Delete Floor', ok_title='Confirm', description='' ):
        self.main_layout = QVBoxLayout()
        self.main_layout.setSpacing(0)

        label_description = QLabel(description)
        label_description.setStyleSheet(f"""color: {main_controller.get_theme_attribute("Color", "dialog_text")}; 
                                       background-color: transparent;
                                       font-weight: 500;""")
        
        self.main_layout.addWidget(label_description)
        footer_type = FooterType.SAVE_CANCEL
        widget_main = QWidget()
        widget_main.setFixedWidth(Config.WIDTH_DIALOG_VERTICAL)
        widget_main.setLayout(self.main_layout)

        super().__init__(parent, title=title, content_widget=widget_main,
                         width_dialog=Config.WIDTH_DIALOG_VERTICAL, max_height_dialog=120, footer_type=footer_type)
        
        self.footer.button_save_update.setText(ok_title)
        self.save_update_signal.connect(self.accept)
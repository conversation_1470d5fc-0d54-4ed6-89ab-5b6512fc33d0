

import logging
logger = logging.getLogger(__name__)
import datetime
from PySide6.QtWidgets import <PERSON>Widget, QVBoxLayout, QHBoxLayout, QPushButton, QDialog, QCheckBox, QApplication, QGraphicsDropShadowEffect, QLabel
from PySide6.QtGui import QPainter, QColor, QPaintEvent, QMouseEvent, QCursor, QIcon
from PySide6.QtCore import Qt, QPoint, QTimer
from src.common.widget.custom_calendar import CalendarPickerWidget
from src.presentation.device_management_screen.widget.list_custom_widgets import IconButton
from src.common.controller.main_controller import main_controller

from src.styles.style import Style


class TimeFilterEventDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | 
            Qt.WindowType.Dialog |
            Qt.WindowType.WindowStaysOnTopHint
        )
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, False)
        
        # Track mouse press position
        self.mouse_press_pos = None
        self.mouse_move_pos = None
        
        # Add flag to prevent immediate close
        self.just_opened = False
        
        # Set a fixed size for the dialog
        self.setFixedSize(400, 450)  # Adjust size as needed
        
        main_layout = QVBoxLayout()
        main_layout.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        
        # Create header with close button
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(8, 8, 8, 0)
        
        # Title
        title_label = QLabel(self.tr("Calendar Filter"))
        title_label.setStyleSheet(f'''
            QLabel {{
                color: {Style.PrimaryColor.white};
                font-size: 14px;
                font-weight: bold;
            }}
        ''')
        header_layout.addWidget(title_label)
        
        # Add stretch to push close button to right
        header_layout.addStretch()
        
        # Close button
        self.close_button = IconButton(
            icon=QIcon(main_controller.get_theme_attribute("Image", "dialog_close")),
            style=f'''
                IconButton {{
                    border: none;
                    padding: 4px;
                    background: transparent;
                }}
                IconButton:hover {{
                    background-color: {main_controller.get_theme_attribute("Color", "dialog_close_hover_bg")};
                    border-radius: 3px;
                }}
            ''',
            tool_tip=self.tr("Close")
        )
        self.close_button.setFixedSize(24, 24)
        self.close_button.clicked.connect(self.close)
        header_layout.addWidget(self.close_button)
        
        # Add header to main layout
        main_layout.addLayout(header_layout)
        
        # Add calendar widget
        self.calendar_picker_widget = CalendarPickerWidget()
        self.calendar_picker_widget.setSizeIncrement(350, 350)
        main_layout.addWidget(self.calendar_picker_widget)
        # create filter button
        self.filter_button = QPushButton(self.tr("Filter"))
        self.filter_button.setObjectName("filter_button")
        self.filter_button.setFixedSize(100, 30)
        self.filter_button.setCursor(Qt.PointingHandCursor)
        self.filter_button.setToolTip(self.tr("Apply filter"))
        self.filter_button.setStyleSheet(
            f'''
            QPushButton#filter_button {{
                background-color: {Style.PrimaryColor.on_hover_button};
                color: {Style.PrimaryColor.white};
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton#filter_button:hover {{
                background-color: {Style.PrimaryColor.on_hover_secondary};
                color: {Style.PrimaryColor.white};
                border-radius: 5px;
            }}
            QPushButton#filter_button:pressed {{
                background-color: {Style.PrimaryColor.on_background};
                color: {Style.PrimaryColor.white};
                border-radius: 5px;
            }}
            '''
        )
        # Create a horizontal layout, add spacers and the button
        button_layout = QHBoxLayout()
        button_layout.setAlignment(Qt.AlignmentFlag.AlignHCenter)
        button_layout.addWidget(self.filter_button)
        main_layout.addLayout(button_layout)
        main_widget = QWidget()
        main_widget.setLayout(main_layout)
        main_widget.setObjectName('time_filter_event_dialog')
        main_widget.setStyleSheet(
            f'''
                QWidget#time_filter_event_dialog {{
                    background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                    border-radius: 4px;
                    border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_border")};
                    color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                }}
            '''
        )
        
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        layout.addWidget(main_widget)
        self.setLayout(layout)
        self.setObjectName("TimeFilterEventDialog")
        
        # Add flag to track if we're interacting with the calendar
        self.calendar_interaction = False
        
        # Connect to calendar signals - use clicked instead of pressed/released
        self.calendar_picker_widget.calendar.activated.connect(self._on_calendar_interaction)
        self.calendar_picker_widget.calendar.clicked.connect(self._on_calendar_interaction)
        
        # Also track mouse events on the calendar widget
        self.calendar_picker_widget.calendar.installEventFilter(self)

    def eventFilter(self, obj, event):
        """Handle events from calendar widget"""
        if obj == self.calendar_picker_widget.calendar:
            if event.type() in [event.Type.MouseButtonPress, event.Type.MouseButtonDblClick]:
                self.calendar_interaction = True
                QTimer.singleShot(500, self._reset_calendar_interaction)
        return super().eventFilter(obj, event)

    def _on_calendar_interaction(self):
        """Track when user interacts with calendar"""
        self.calendar_interaction = True
        QTimer.singleShot(500, self._reset_calendar_interaction)

    def _reset_calendar_interaction(self):
        """Reset the calendar interaction flag"""
        self.calendar_interaction = False

    def paintEvent(self, event: QPaintEvent) -> None:
        # draw rounded rect with theme colors
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(Qt.NoPen)
        painter.setBrush(QColor(main_controller.get_theme_attribute("Color", "dialog_body_background")))
        painter.drawRoundedRect(self.rect(), 10, 10)

    def showAt(self, position, center_on_screen=True):
        """Show dialog either centered or at specific position"""
        if center_on_screen:
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            
            x = (screen_geometry.width() - self.width()) // 2
            y = (screen_geometry.height() - self.height()) // 2
        else:
            x = position.x() + 30
            y = position.y()
        
        # Set the just_opened flag
        self.just_opened = True
        
        # Move and show the dialog
        self.move(x, y)
        self.show()
        self.raise_()
        self.activateWindow()
        
        # Reset the just_opened flag after a short delay
        QTimer.singleShot(200, self._reset_just_opened)

    def _reset_just_opened(self):
        """Reset the just_opened flag"""
        self.just_opened = False
        self.activateWindow()
        self.setFocus()

    def mousePressEvent(self, event: QMouseEvent):
        """Handle mouse press events"""
        if self.just_opened:
            return
            
        # Check if click is inside the dialog
        if self.rect().contains(event.position().toPoint()):
            super().mousePressEvent(event)
        else:
            # Only close if we're not interacting with the calendar
            if not self.calendar_interaction:
                self.close()

    def mouseMoveEvent(self, event: QMouseEvent):
        """Handle mouse move events for dragging"""
        if self.mouse_press_pos is not None:
            # Calculate the difference
            curr_pos = event.globalPosition().toPoint()
            diff = curr_pos - self.mouse_move_pos
            # Update the dialog position
            new_pos = self.pos() + diff
            self.move(new_pos)
            # Remember the new position
            self.mouse_move_pos = curr_pos

    def mouseReleaseEvent(self, event: QMouseEvent):
        """Handle mouse release events"""
        self.mouse_press_pos = None
        self.mouse_move_pos = None
        super().mouseReleaseEvent(event)

    def focusOutEvent(self, event):
        """Close dialog when focus is lost, but not during calendar interaction"""
        if not self.just_opened and not self.calendar_interaction:
            # Only close if we're not interacting with the calendar
            if not self.rect().contains(self.mapFromGlobal(QCursor.pos())):
                self.close()
        super().focusOutEvent(event)

    def closeEvent(self, event):
        """Handle close events"""
        # Add any cleanup or confirmation here if needed
        event.accept()  # Allow the dialog to close


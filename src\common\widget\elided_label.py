from PySide6.QtWidgets import QLabel
from PySide6.QtGui import QPainter, QFontMetrics, QTextDocument
from PySide6.QtCore import Qt

class ElidedLabel(QLabel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

    def paintEvent(self, event):
        painter = QPainter(self)
        metrics = QFontMetrics(self.font())
        text = self.text()
        elided_text = metrics.elidedText(text, Qt.TextElideMode.ElideRight, self.width())  
        painter.drawText(self.rect(), self.alignment(), elided_text)

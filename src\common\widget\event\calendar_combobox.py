import src.utils.log_utils as LogUtils
import logging

from src.utils.theme_setting import theme_setting

logger = logging.getLogger(__name__)
from PySide6.QtCore import Qt,QEvent,Signal
from PySide6.QtGui import  QStandardItem, QFontMetrics,QPixmap
from PySide6.QtWidgets import QStyledItemDelegate, QComboBox,QWidget,QVBoxLayout,QLabel,QHBoxLayout
from src.styles.style import Style
from src.common.widget.dialogs.time_filter_event_dialog import TimeFilterEventDialog
from src.common.controller.main_controller import main_controller
import datetime

class CalendarComboBox(QWidget):
    data_changed = Signal(tuple)
    def __init__(self, parent=None):
        super().__init__(parent)
        self.main_layout = QVBoxLayout()
        title = QLabel(self.tr('Time'))
        title.setStyleSheet(f'color: {Style.PrimaryColor.primary};font-size: 14px;')
        self.time_widget = QWidget()
        self.time_widget.setObjectName('CalendarComboBox')

        time_layout = QHBoxLayout(self.time_widget)
        # time_layout.setContentsMargins(40,10,40,10)
        self.time_text = QLabel()
        calendar = QLabel()
        pixmap_calendar = QPixmap(main_controller.get_theme_attribute('Image', 'calendar'))
        calendar.setPixmap(pixmap_calendar)
        time_layout.addWidget(self.time_text,95)
        time_layout.addWidget(calendar,5)
        self.main_layout.addWidget(title)
        self.main_layout.addWidget(self.time_widget)
        self.time_widget.mousePressEvent = lambda event: self.show_time_filter_dialog(event)
        self.setLayout(self.main_layout)
        self.time_filter_dialog = TimeFilterEventDialog()
        self.time_filter_dialog.filter_button.clicked.connect(
            self.filter_event_time_range)
        self.create_first_time()
        
    def create_first_time(self):
        self.start_date_converted = datetime.date.today().strftime("%Y-%m-%d 00:00:00")
        self.end_date_converted = datetime.date.today().strftime("%Y-%m-%d 23:59:59")
        self.time_text.setText(f'{self.start_date_converted} : {self.end_date_converted}')
        # self.data_changed.emit((self.objectName(),{'start_time':self.start_date_converted,'end_time':self.end_date_converted}))

    def filter_event_time_range(self):
        start_time = self.time_filter_dialog.calendar_picker_widget.getStartValue()
        end_time = self.time_filter_dialog.calendar_picker_widget.getEndValue()
        # logger.debug(f"filter_event_time_range = {start_time} {end_time}")
        # convert QDateTime to datetime
        self.start_date_converted = start_time.toString('yyyy-MM-dd hh:mm:ss')
        self.end_date_converted = end_time.toString('yyyy-MM-dd hh:mm:ss')
        # logger.debug(f'start_date_converted = {self.start_date_converted}')
        # logger.debug(f'end_date_converted = {self.end_date_converted}')
        self.time_text.setText(f'{self.start_date_converted} : {self.end_date_converted}')
        self.data_changed.emit((self.objectName(),{'start_time':self.start_date_converted,'end_time':self.end_date_converted}))
        self.time_filter_dialog.close()

    def show_time_filter_dialog(self, event):
        # logger.debug('mousePressEvent')
        # position = self.geometry().bottomLeft()
        position = self.mapToGlobal(
            self.rect().bottomLeft())
        self.time_filter_dialog.showAt(position)
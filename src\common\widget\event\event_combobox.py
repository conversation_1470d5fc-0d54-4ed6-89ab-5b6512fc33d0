import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
from PySide6.QtCore import Qt,QEvent,Signal
from PySide6.QtGui import  QStandardItem, QFontMetrics
from PySide6.QtWidgets import QStyledItemDelegate, QComboBox

class EventComboBox(QComboBox):
    data_changed = Signal(tuple)
    # Subclass Delegate to increase item height
    class Delegate(QStyledItemDelegate):
        def sizeHint(self, option, index):
            size = super().sizeHint(option, index)
            size.setHeight(40)
            return size

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make the combo editable to set a custom text, but readonly
        self.list_item_selected = []
        self.popup_status = True
        self.setEditable(True)
        self.lineEdit().setReadOnly(True)
        # Make the lineedit the same color as QPushButton
        # palette = qApp.palette()
        # palette.setBrush(QPalette.Base, palette.button())
        # self.lineEdit().setPalette(palette)

        # Use custom delegate
        self.setItemDelegate(EventComboBox.Delegate())

        # Update the text when an item is toggled
        self.model().dataChanged.connect(self.updateText)

        # Hide and show popup when clicking the line edit
        self.lineEdit().installEventFilter(self)
        self.closeOnLineEditClick = False

        # Prevent popup from closing when clicking on an item
        self.view().viewport().installEventFilter(self)
        self.setFixedHeight(40)

    def resizeEvent(self, event):
        # Recompute text to elide as needed
        self.updateText()
        super().resizeEvent(event)

    def setDisabled(self, state):
        self.popup_status = False if state == True else True

    def eventFilter(self, object, event):
        if self.popup_status:
            if object == self.lineEdit():
                if event.type() == QEvent.MouseButtonRelease:
                    if self.closeOnLineEditClick:
                        self.hidePopup()
                    else:
                        self.showPopup()
                    return True
                return False

            if object == self.view().viewport():
                if event.type() == QEvent.MouseButtonRelease:
                    index = self.view().indexAt(event.pos())
                    
                    item = self.model().item(index.row())
                    item_data = item.data(Qt.UserRole)
                    logger.debug(f'eventFilter = {item.text()}')
                    if item.text() == 'All' and item.checkState() == Qt.Unchecked:
                        item.setCheckState(Qt.Checked)
                        self.all_clicked()
                    elif item.checkState() == Qt.Checked:
                        item.setCheckState(Qt.Unchecked)
                        
                    else:
                        item.setCheckState(Qt.Checked)
                        self.all_unclicked()
                    return True
        return False

    def showPopup(self):
        super().showPopup()
        # When the popup is displayed, a click on the lineedit should close it
        self.closeOnLineEditClick = True

    def hidePopup(self):
        super().hidePopup()
        # Used to prevent immediate reopening when clicking on the lineEdit
        self.startTimer(100)
        # Refresh the display text when closing
        self.updateText()
        logger.debug('hidePopup')
        self.data_changed.emit((self.objectName(),self.list_item_selected))

    def timerEvent(self, event):
        # After timeout, kill timer, and reenable click on line edit
        self.killTimer(event.timerId())
        self.closeOnLineEditClick = False

    def all_clicked(self):
        for i in range(self.model().rowCount()):
            if self.model().item(i).text() != 'All' and self.model().item(i).checkState() == Qt.Checked:
                self.model().item(i).setCheckState(Qt.Unchecked)

    def all_unclicked(self):
        for i in range(self.model().rowCount()):
            if self.model().item(i).text() == 'All' and self.model().item(i).checkState() == Qt.Checked:
                self.model().item(i).setCheckState(Qt.Unchecked)
                break

    def updateText(self):
        texts = []
        self.list_item_selected = []
        for i in range(self.model().rowCount()):
            if self.model().item(i).checkState() == Qt.Checked:
                logger.debug(f'text = {i} {self.model().item(i).text()}')
                self.list_item_selected.append(self.model().item(i).text())
                texts.append(self.model().item(i).text())
        text = ", ".join(texts)

        # Compute elided text (with "...")
        metrics = QFontMetrics(self.lineEdit().font())
        elidedText = metrics.elidedText(text, Qt.ElideRight, self.lineEdit().width())
        self.lineEdit().setText(elidedText)
        # logger.debug('updateText')
        # self.data_changed.emit((self.objectName(),self.list_item_selected))

    def uncheck_state(self):
        for i in range(self.model().rowCount()):
            if self.model().item(i).checkState() == Qt.Checked:
                self.model().item(i).setCheckState(Qt.CheckState.Unchecked)

    def update_check_state(self,list_index=[]):
        for i in range(self.model().rowCount()):
            if i in list_index:
               self.model().item(i).setCheckState(Qt.CheckState.Checked) 
               
    def addItem(self, text, data=None):
        item = QStandardItem()
        item.setText(text)
        if data is None:
            item.setData(text)
        else:
            item.setData(data)
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsUserCheckable)
        if text == 'All':
            item.setData(Qt.Checked, Qt.CheckStateRole)
        else:
            item.setData(Qt.Unchecked, Qt.CheckStateRole)
        self.model().appendRow(item)

    def addItems(self, texts, datalist=None):
        for i, text in enumerate(texts):
            try:
                data = datalist[i]
            except (TypeError, IndexError):
                data = None
            self.addItem(text, data)

    def currentData(self):
        # Return the list of selected items data
        res = []
        for i in range(self.model().rowCount()):
            if self.model().item(i).checkState() == Qt.Checked:
                res.append(self.model().item(i).data())
        logger.debug(f'res = {res}')
        return res

from typing import List
from dataclasses import dataclass
from src.common.model.base_model import BaseModel
from src.common.model.zone_model import ZoneModel
from src.common.model.group_model import Group
from src.common.model.aiflows_model import AiFlow
from src.common.onvif_api.camera_model import Resolution
from dataclasses import dataclass, asdict, fields
from PySide6.QtCore import QObject, Signal
from PySide6.QtWidgets import QWidget
from src.utils.camera_qsettings import Camera_Qsettings

@dataclass
class EventModel:
    name: str = None
    type: str = None

    @classmethod
    def from_dict(cls, data_dict):
        field_names = {field.name for field in fields(cls)}
        filtered_dict = {key: value for key, value in data_dict.items() if key in field_names}
        return cls(**filtered_dict)

    # def to_dict(self):
    #     return asdict(self)
    def to_dict(self):
        # only return the fields that are not None
        return {k: v for k, v in self.__dict__.items() if v is not None}
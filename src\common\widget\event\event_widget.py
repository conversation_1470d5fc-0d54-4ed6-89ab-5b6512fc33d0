import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
from concurrent.futures import ThreadPoolExecutor
import datetime
from enum import Enum
import json
import threading
from typing import List
from PySide6.QtWidgets import QApplication, QPushButton, QHBoxLayout, QDialog, QLabel,QMenu, QWidget, QVBoxLayout, QFrame, QGraphicsView, QGraphicsScene, QGraphicsPixmapItem, QGraphicsTextItem,QSizePolicy
from PySide6.QtCore import Qt, QSize, QRect,QEvent, Slot,QMimeData,Signal
from PySide6.QtGui import QIcon, QPainter, QColor, QPixmap, QPainterPath, QGuiApplication, QResizeEvent, QDrag, QFont, \
    QCursor, QAction, QFontMetrics
from src.common.controller.main_controller import main_controller
from src.common.model.event_data_model import CropItemObject<PERSON>rowd, EventAI, MetadataAccessControl, MetadataCrowed, MetadataHuman, MetadataTraffic
from src.styles.style import Style
from src.common.widget.image_widget import ImageLoader, ImageWidget
from src.utils.auth_qsettings import AuthQSettings
from src.presentation.camera_screen.stream_object_base_widget import StreamObjectBaseWidget
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.widget.responsive_label import ResponsiveLabel
import pickle
class EventAIType(Enum):
    HUMAN = 'HUMAN'
    VEHICLE = 'VEHICLE'
    ACCESS_CONTROL = 'ACCESS_CONTROL'
    CROWD = 'CROWD'

# {"pedestrian", "motor", "bicycle", "car", "bus", "van", "truck", "container", "bagac", "cyclo", "ambulance", "firetruck", "wheelchair", "trashcar", "tanktruck", "mixertruck", "crane", "roller", "excavator", "streetvendor", "unknown"}
class TypeTraffic(Enum):
    PEDESTRIAN = 'pedestrian'
    MOTOR = 'motor'
    BICYCLE = 'bicycle'
    CAR = 'car'
    BUS = 'bus'
    VAN = 'van'
    TRUCK = 'truck'
    CONTAINER = 'container'
    BAGAC = 'bagac'
    CYCLO = 'cyclo'
    AMBULANCE = 'ambulance'
    FIRETRUCK = 'firetruck'
    WHEELCHAIR = 'wheelchair'
    TRASHCAR = 'trashcar'
    TANKTRUCK = 'tanktruck'
    MIXERTRUCK = 'mixertruck'
    CRANE = 'crane'
    ROLLER = 'roller'
    EXCAVATOR = 'excavator'
    STREETVENDOR = 'streetvendor'
    UNKNOWN = 'unknown'

class ColorTraffic(Enum):
    RED = 'red'
    BLUE = 'blue'
    GREEN = 'green'
    YELLOW = 'yellow'
    BLACK = 'black'
    WHITE = 'white'
    ORANGE = 'orange'

class LicensePlateType(Enum):
    RED = 'BIEN_DO'
    BLUE = 'BIEN_XANH'
    YELLOW = 'BIEN_VANG'
    NG = 'NGOAI_GIAO'

class EventWidget(StreamObjectBaseWidget):
    ENABLE_HEATMAP_CROWD = False

    def __init__(self, parent=None, show_warning_info=False, event_model: EventAI = None,
                 width=None,
                 height=None, row=None, col=None, tab_model=None, is_virtual_window=False, is_demo=False,
                 stack_item=None):
        super().__init__(parent, tab_model=tab_model,
                         is_virtual_window=is_virtual_window, is_demo=is_demo, stack_item=stack_item)

        self.setObjectName("main_event_widget")
        self.setFocusPolicy(Qt.StrongFocus)
        self.setMouseTracking(True)
        self.stack_item = stack_item
        self.tab_model = tab_model
        self.drag = None
        self.row = row
        self.col = col
        self.root_width = width
        self.root_height = height
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
        # change color window title
        self.event_item = event_model
        self.show_warning_info = show_warning_info
        self.left_widget = None
        self.crop_widget = None
        self.setup_ui()
        self.setup_event_widget_stylesheet()

    def setup_ui(self):
        temp_layout = QVBoxLayout()
        temp_layout.setContentsMargins(0, 0, 0, 0)
        self.origin_image = None
        self.crop_image = None
        self.license_plate_image = None
        # horizontal layout
        self.image_result_widget = self.create_image_result_widget()

        self.crop_image_widget = self.create_crop_image_widget()

        self.event_info_widget = self.create_event_info()
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        # self.resize(self.root_width * 0.7, self.root_height * 0.8)
        # header
        self.create_header_top_widget_for_camera()
        if self.header_top_widget is not None:
            self.header_top_widget.setGeometry(0, 0, self.root_width, self.HEIGHT_HEADER)
            self.header_top_widget.setVisible(True)

        infor_layout = QHBoxLayout()
        infor_layout.setContentsMargins(0, 0, 0, 0)
        infor_layout.addWidget(self.crop_image_widget, 40)
        infor_layout.addWidget(self.event_info_widget, 60)

        temp_layout.addWidget(self.image_result_widget, 85)
        temp_layout.addLayout(infor_layout, 15)
        

        self.temp_wigget = QWidget()
        self.temp_wigget.setObjectName("temp_wigget")
        self.temp_wigget.setLayout(temp_layout)

        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.main_layout.addWidget(self.temp_wigget)
        self.setLayout(self.main_layout)

    def create_header_top_widget_for_camera(self):
        self.header_top_widget = self.create_header_top_widget_base()

    def create_image_result_widget(self):
        widget = QWidget()
        image_result_layout = QHBoxLayout()
        image_result_layout.setAlignment(Qt.AlignCenter)
        image_result_layout.setContentsMargins(0, 0, 0, 0)
        image_result_layout.setSpacing(2)
        widget.setLayout(image_result_layout)
        # logger.debug(f'self.event_item = {self.event_item.pixmap}')
        # logger.debug(f'self.event_item = {self.event_item.pixmapFullUrl,}')
        if self.event_item.pixmapFullUrl is not None:
            self.origin_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.95,
                                            height=self.root_height * 0.60, pixmapFullUrl=self.event_item.pixmapFullUrl)
            self.origin_image.update_resize(width=self.root_width * 0.95, height=self.root_height)
        else:
            self.origin_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.95,
                                            height=self.root_height * 0.60)
            # self.origin_image.load_from_url(self.event_item.imageUrl)

        if self.event_item.pixmapUrl is not None:
            self.crop_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                          height=self.root_height * 0.60, pixmapFullUrl=self.event_item.pixmapUrl,
                                          image_type=1)
            self.crop_image.update_resize(width=self.root_width * 0.3, height=self.root_height)
        else:
            self.crop_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                          height=self.root_height * 0.60, image_type=1)

        if self.event_item.pixmapLicensePlateUrl is not None:
            self.license_plate_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                                   height=self.root_height * 0.32,
                                                   pixmapFullUrl=self.event_item.pixmapLicensePlateUrl, image_type=2)
            self.license_plate_image.update_resize(width=self.root_width * 0.3, height=self.root_height)
        else:
            self.license_plate_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                                   height=self.root_height * 0.32, image_type=2)
        self.left_widget = QWidget()
        self.left_widget.setObjectName("left_widget")
        # origin_widget.setStyleSheet("background-color: transparent; border: 1px solid #000000;border-radius: 2px;")
        left_layout = QHBoxLayout(self.left_widget)
        left_layout.setContentsMargins(1, 1, 1, 1)
        # origin_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        left_layout.addWidget(self.origin_image)

        right_widget = QWidget()
        right_widget.setObjectName("right_widget")
        image_result_layout.addWidget(self.left_widget, 65)
        # image_result_layout.addWidget(right_widget,35)
        self.left_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        return widget

    def create_crop_image_widget(self):
        widget = QWidget()
        image_result_layout = QHBoxLayout()
        image_result_layout.setAlignment(Qt.AlignCenter)
        image_result_layout.setContentsMargins(0, 0, 0, 0)
        image_result_layout.setSpacing(2)
        widget.setLayout(image_result_layout)

        if self.event_item.pixmapUrl is not None:
            self.crop_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                          height=self.root_height * 0.60, pixmapFullUrl=self.event_item.pixmapUrl,
                                          image_type=1)
            self.crop_image.update_resize(width=self.root_width * 0.3, height=self.root_height)
        else:
            self.crop_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                          height=self.root_height * 0.60, image_type=1)

        if self.event_item.pixmapLicensePlateUrl is not None:
            self.license_plate_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                                   height=self.root_height * 0.32,
                                                   pixmapFullUrl=self.event_item.pixmapLicensePlateUrl, image_type=2)
            self.license_plate_image.update_resize(width=self.root_width * 0.3, height=self.root_height)
        else:
            self.license_plate_image = ImageWidget(event_ai=self.event_item, width=self.root_width * 0.3,
                                                   height=self.root_height * 0.32, image_type=2)

        right_widget = QWidget()
        right_widget.setObjectName("right_widget")
        # origin_widget.setStyleSheet("background-color: transparent; border: 1px solid #000000;border-radius: 2px;")
        right_layout = QHBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        if self.event_item.imageUrl is not None:
            self.crop_widget = QWidget()
            self.crop_widget.setObjectName("crop_widget")

            crop_layout = QHBoxLayout(self.crop_widget)
            crop_layout.setContentsMargins(0, 0, 0, 0)
            # crop_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
            crop_layout.addWidget(self.crop_image)
            right_layout.addWidget(self.crop_widget)
            self.crop_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        if self.event_item.imageLicensePlateUrl is not None and self.event_item.imageLicensePlateUrl != '':
            license_plate_widget = QWidget()
            license_plate_widget.setObjectName("license_plate_widget")

            license_plate_layout = QHBoxLayout(license_plate_widget)
            license_plate_layout.setContentsMargins(0, 1, 0, 1)
            # crop_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
            license_plate_layout.addWidget(self.license_plate_image)
            right_layout.addWidget(license_plate_widget)
            license_plate_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        image_result_layout.addWidget(right_widget)
        right_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        return widget

    def create_event_info(self):
        widget = QWidget()
        widget.setObjectName("event_info_widget")

        left_right_layout = QVBoxLayout(widget)
        left_right_layout.setContentsMargins(4, 2, 4, 2)
        left_right_layout.setSpacing(0)
        # left_right_layout.setContentsMargins(0,0,0,0)
        # left_right_layout.addStretch(15)
        # left_right_layout.addWidget(self.left_info_widget)
        # left_right_layout.addWidget(self.right_info_widget)
        # left_right_layout.addStretch(15)
        # left_right_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # AI Flows
        ai_flow_layout = QHBoxLayout()
        ai_flow_label = AutoResizeLabel()
        ai_flow_label.setText(self.tr("AI Flows: "))

        ai_flow_content = AutoResizeLabel()
        ai_flow_content.setText(self.event_item.type)
        ai_flow_layout.addWidget(ai_flow_label, 30)
        ai_flow_layout.addWidget(ai_flow_content, 70)

        event_name_layout = QHBoxLayout()
        event_name = AutoResizeLabel(self.tr("Name: "))

        event_name_content = AutoResizeLabel(self.event_item.name)
        event_name_layout.addWidget(event_name, 30)
        event_name_layout.addWidget(event_name_content, 70)

        # self.left_info.addLayout(ai_flow_layout)
        # self.left_info.addLayout(event_name_layout)
        # self.left_info.addWidget(QWidget())
        left_right_layout.addLayout(ai_flow_layout)
        left_right_layout.addLayout(event_name_layout)

        status_layout = QHBoxLayout()
        status_label = AutoResizeLabel(self.tr("Status: "))
        # status_label.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Ignored)
        # status_label.setWordWrap(True)

        status_content = AutoResizeLabel(self.event_item.status)
        # status_content.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Ignored)

        # status_content.setWordWrap(True)
        status_layout.addWidget(status_label, 30)
        status_layout.addWidget(status_content, 70)

        camera_name_layout = QHBoxLayout()
        camera_name_label = AutoResizeLabel(self.tr("Camera: "))
        # camera_name_label.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Ignored)

        # camera_name_label.setWordWrap(True)

        camera_name_content = AutoResizeLabel(self.event_item.cameraName)
        # camera_name_content.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Ignored)

        # camera_name_content.setWordWrap(True)
        camera_name_layout.addWidget(camera_name_label, 30)
        camera_name_layout.addWidget(camera_name_content, 70)

        # # GET TIME
        # # get time from UTC
        # 2024-05-30T13:41:05.478202+07:00 - convert to 2024-05-30 13:41:05
        try:
            # Handle ISO format datetime with timezone
            event_time = datetime.datetime.fromisoformat(self.event_item.createdAtLocalDate).strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            try:
                # Fallback to parsing without timezone
                event_time = datetime.datetime.strptime(self.event_item.createdAtLocalDate, "%Y-%m-%d %H:%M:%S.%f").strftime("%Y-%m-%d %H:%M:%S")
            except ValueError:
                # If all parsing fails, use current time
                event_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                logger.error(f"Failed to parse datetime: {self.event_item.createdAtLocalDate}")

        time_layout = QHBoxLayout()
        time_label = AutoResizeLabel(self.tr("Time: "))

        time_content = AutoResizeLabel(event_time)
        time_layout.addWidget(time_label, 30)
        time_layout.addWidget(time_content, 70)

        left_right_layout.addLayout(status_layout)
        left_right_layout.addLayout(camera_name_layout)
        left_right_layout.addLayout(time_layout)
        # self.right_info.addLayout(status_layout)
        # self.right_info.addLayout(camera_name_layout)
        # self.right_info.addLayout(time_layout)

        return widget

    def enterEvent(self, event):
        super().enterEvent(event)

    def leaveEvent(self, event):
        super().leaveEvent(event)

    def callback_keyPressEvent(self, event):
        # logger.debug(f'callback_keyPressEvent')
        current_tab = main_controller.current_tab
        if grid_item_selected.is_tab_index(current_tab):
            if grid_item_selected.data['tab_index'] is None:
                pass
            else:
                widget = grid_item_selected.data['widget']
                if widget == self:
                    if event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
                        # logger.debug(f'event.key() == Qt.Key.Key_Return')
                        self.btn_full_screen_clicked()
                    elif event.key() == Qt.Key.Key_Delete:
                        self.btn_close_clicked()

    # draw round corner for window
    def update_resize(self, width, height):
        # logger.debug(f"update_resize: new_size: {width} : {height} : {self.root_width} : {self.root_height}")
        self.on_resize_change(QResizeEvent(QSize(width, height), QSize(self.root_width, self.root_height)))

    def on_resize_change(self, event):
        # logger.debug(f'event = {event}')
        new_size = event.size()
        self.root_width = new_size.width()
        self.root_height = new_size.height()

        self.origin_image.update_resize(self.root_width*0.95,self.root_height*0.65)
        # self.crop_image.update_resize(self.root_width*0.3,self.root_height*0.65)
        if self.event_item.imageUrl is not None and self.event_item.imageLicensePlateUrl == '':
            self.crop_image.update_resize(self.root_width*0.5,self.root_height*0.34)
        elif self.event_item.imageUrl is not None and self.event_item.imageLicensePlateUrl != '':
            self.crop_image.update_resize(self.root_width*0.25,self.root_height*0.34)
            self.license_plate_image.update_resize(self.root_width*0.25,self.root_height*0.32)

        self.image_result_widget.setGeometry(0, 10, self.root_width, self.root_height*0.65 - 10)
        self.image_result_widget.setVisible(True)
        # self.image_result_widget.raise_()

        # self.crop_image_widget.setFixedHeight(self.root_height * 0.34 - 10)
        # print(f"HanhLT: self.root_height * 0.34 - 10 = {self.root_height * 0.34 - 10}")
        self.crop_image_widget.setVisible(True)
        # self.crop_image_widget.raise_()

        # self.event_info_widget.setSizeIncrement(self.root_width - self.root_width * 0.5 - 10, self.root_height * 0.34 - 10)
        self.event_info_widget.setVisible(True)
        # self.event_info_widget.raise_()
        # if self.header_top_widget is not None:
        #     # print(f'on_resize_change is not None - width: {new_size.width()} - height: {self.HEIGHT_HEADER}')
        #     self.header_top_widget.setSizeIncrement(new_size.width(), self.HEIGHT_HEADER)
        #     self.header_top_widget.raise_()
        if self.header_top_widget is not None:
            # print(f'on_resize_change is not None - width: {new_size.width()} - height: {self.HEIGHT_HEADER}')
            self.header_top_widget.setGeometry(0, 0, new_size.width(), self.HEIGHT_HEADER)
            # self.header_top_widget.raise_()

    def setup_event_widget_stylesheet(self):
        self.setStyleSheet(f'''
            QWidget#temp_wigget{{
                background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")};
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")}; 
            }}

            QWidget#event_info_widget {{
                        border: 1px solid {main_controller.get_theme_attribute("Color", "divider")};
                        color: black; 
                    }}
            QWidget#crop_widget {{
                background-color: transparent;
                border: 1px solid {main_controller.get_theme_attribute("Color", "divider")};
                border-radius: 2px;
            }}
            QWidget#event_info_widget {{
                background-color: transparent;
                border: 1px solid {main_controller.get_theme_attribute("Color", "divider")};
                border-radius: 2px;
            }}
            QWidget#left_widget {{
                        background-color: transparent;
                        border: 1px solid {main_controller.get_theme_attribute("Color", "divider")};
                        border-radius: 2px;
                    }}
            
            QWidget#license_plate_widget {{
                    background-color: transparent;
                    border: 1px solid {main_controller.get_theme_attribute("Color", "divider")};
                    border-radius: 2px;
                }}
            QLabel {{
                color: {main_controller.get_theme_attribute("Color", "text_color_all_app")}; 
            }}
        ''')

    def create_group_list_layout(self, event_item: EventAI):
        group_list: List[str] = []
        if event_item.profile_list:
            for item in event_item.profile_list:
                group_list.append(item.name)
        else:
            group_list.append(self.tr("No group"))

        self.group_list_layout.setContentsMargins(5, 5, 5, 5)
        self.group_list_layout.setSpacing(5)

        for group in group_list:
            group_label = QLabel(group)
            # group_label.setObjectName(f'{group}')
            text_style = "font-family: Inter; font-size: 12px; font-weight: 400; line-height: 11px; letter-spacing: -0.015em; text-align: left;"
            if event_item.profile_list:
                group_label.setStyleSheet(
                    f"""
                    QLabel {{
                        background-color: {Style.PrimaryColor.primary};
                        color: {Style.PrimaryColor.white};
                        border-radius: 5px;
                        padding-left: 5px;
                        padding-right: 5px;
                    }}
                    """
                )
            else:
                group_label.setStyleSheet(
                    f"""
                    QLabel {{
                        background-color: {Style.PrimaryColor.text_unselected};
                        color: {Style.PrimaryColor.white};
                        border-radius: 5px;
                        padding-left: 5px;
                        padding-right: 5px;
                    }}
                    """
                )
            group_label.setFixedHeight(30)
            group_layout = QHBoxLayout()
            group_layout.addWidget(group_label, alignment=Qt.AlignLeft)
            self.group_list_layout.addLayout(group_layout)

    def convert_traffic_type_name(self, metadata: MetadataTraffic) -> str:
        if metadata.vehicle_name == TypeTraffic.CAR.value:
            return self.tr('Car')
        elif metadata.vehicle_name == TypeTraffic.MOTOR.value:
            return self.tr('Motor')
        elif metadata.vehicle_name == TypeTraffic.BICYCLE.value:
            return self.tr('Bicycle')
        elif metadata.vehicle_name == TypeTraffic.PEDESTRIAN.value:
            return self.tr('Pedestrian')
        elif metadata.vehicle_name == TypeTraffic.TRUCK.value:
            return self.tr('Truck')
        elif metadata.vehicle_name == TypeTraffic.BUS.value:
            return self.tr('Bus')
        elif metadata.vehicle_name == TypeTraffic.VAN.value:
            return self.tr('Van')
        elif metadata.vehicle_name == TypeTraffic.CONTAINER.value:
            return self.tr('Container truck')
        elif metadata.vehicle_name == TypeTraffic.BAGAC.value:
            return self.tr('Delivery tricycles')
        elif metadata.vehicle_name == TypeTraffic.CYCLO.value:
            return self.tr('Cyclo')
        elif metadata.vehicle_name == TypeTraffic.AMBULANCE.value:
            return self.tr('Ambulance')
        elif metadata.vehicle_name == TypeTraffic.FIRETRUCK.value:
            return self.tr('Fire truck')
        elif metadata.vehicle_name == TypeTraffic.WHEELCHAIR.value:
            return self.tr('Wheelchair')
        elif metadata.vehicle_name == TypeTraffic.TRASHCAR.value:
            return self.tr('Trash car')
        elif metadata.vehicle_name == TypeTraffic.TANKTRUCK.value:
            return self.tr('Tank truck')
        elif metadata.vehicle_name == TypeTraffic.MIXERTRUCK.value:
            return self.tr('Mixer truck')
        elif metadata.vehicle_name == TypeTraffic.CRANE.value:
            return self.tr('Crane')
        elif metadata.vehicle_name == TypeTraffic.ROLLER.value:
            return self.tr('Roller')
        elif metadata.vehicle_name == TypeTraffic.EXCAVATOR.value:
            return self.tr('Excavator')
        elif metadata.vehicle_name == TypeTraffic.STREETVENDOR.value:
            return self.tr('Street Vendor')
        else:
            return self.tr('Undefined')

    def convert_traffic_color(self, metadata: MetadataTraffic) -> str:
        if metadata.vehicle_color == ColorTraffic.BLACK.value:
            return self.tr('Black')
        elif metadata.vehicle_color == ColorTraffic.WHITE.value:
            return self.tr('White')
        elif metadata.vehicle_color == ColorTraffic.RED.value:
            return self.tr('Red')
        elif metadata.vehicle_color == ColorTraffic.BLUE.value:
            return self.tr('Blue')
        elif metadata.vehicle_color == ColorTraffic.GREEN.value:
            return self.tr('Green')
        elif metadata.vehicle_color == ColorTraffic.YELLOW.value:
            return self.tr('Yellow')
        elif metadata.vehicle_color == ColorTraffic.ORANGE.value:
            return self.tr('Orange')
        else:
            return self.tr('Undefined')

    def convert_traffic_brand(self, metadata: MetadataTraffic) -> str:
        # convert String to Capitalize only first letter
        if metadata.vehicle_brand:
            return metadata.vehicle_brand.capitalize()

    def eventFilter(self, source, event):
        if event.type() == QEvent.MouseButtonPress:
            # Kiểm tra xem vị trí của sự kiện chuột có nằm trong vùng của cửa sổ cha hay không

            global_pos = event.globalPos()
            if not self.geometry().contains(global_pos):
                QApplication.instance().removeEventFilter(self)
                self.close()
        return super().eventFilter(source, event)

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            row = self.stack_item.position.x()
            col = self.stack_item.position.y()
            item_grid = self.childAt(event.position().toPoint())
            position = (row, col)
            data_bytes = pickle.dumps(position)
            if isinstance(item_grid, QWidget):
                # Start the drag operation
                self.drag = QDrag(self)
                mime_data = QMimeData()
                mime_data.setObjectName("swap_item")
                mime_data.setText(self.event_item.name)
                mime_data.setData("application/position", data_bytes)
                self.drag.setMimeData(mime_data)

        return super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton:
            if self.drag is not None:
                # Execute the drag operation
                self.drag.exec()
        return super().mouseMoveEvent(event)

    def closeEvent(self, event):
        # logger.debug(f'closeEvent EventWidget = {self.origin_image.image_loader.pixmap}')
        self.origin_image.close()
        self.crop_image.close()

class ImageScrollWidget(QWidget):
    def __init__(self):
        super().__init__()

        self.initUI()

    def initUI(self):
        self.layout = QHBoxLayout()
        self.scroll_area = QGraphicsView(self)
        self.x_position = 0  # Initialize the x-coordinate position
        self.scene = QGraphicsScene()
        self.scroll_area.setScene(self.scene)
        # hide scrollbars and stroke
        self.scroll_area.setStyleSheet("border: 0px; background: transparent;")
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        self.layout.addWidget(self.scroll_area)
        self.setLayout(self.layout)

        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def add_image(self, pixmap, id):
        logger.debug(f'height: {self.height()}')

        item = CircularImageItem(pixmap, id)
        item.setPos(self.x_position, 0)  # Set the position of the item
        self.scene.addItem(item)
        item_text = QGraphicsTextItem(str(id))
        item_text.setPos(self.x_position, 80)
        self.scene.addItem(item_text)
        self.x_position += 80  # Adjust this value to match the spacing

    def add_list(self, image_list: List[CropItemObjectCrowd]):
        self.x_position = 0  # Initialize the x-coordinate position
        self.thread_pool = ThreadPoolExecutor(max_workers=8)
        for item in image_list:
            self.image_loader = ImageLoader(url=item.image, height=70, id=item.id)
            self.image_loader.custom_finished_data.connect(self._on_image_loaded)
            if self.thread_pool:
                self.thread_pool.submit(self.image_loader.load_image)
            else:
                thread = threading.Thread(target=self.image_loader.load_image)
                thread.start()

    @Slot(QPixmap)
    def _on_image_loaded(self, pixmap, id):
        logger.debug('add_list: loaded')
        self.add_image(pixmap, id)

class CircularImageItem(QGraphicsPixmapItem):
    def __init__(self, pixmap, id=''):
        super().__init__()
        self.pixmap = pixmap
        self.id = id
        self.radius = min(self.pixmap.width(), self.pixmap.height()) / 2

    def boundingRect(self):
        return self.pixmap.rect()

    def paint(self, painter: QPainter, option, widget):
        clip_path = QPainterPath()
        clip_path.addEllipse(self.boundingRect())

        # Save the current painter state
        painter.save()
        painter.setClipPath(clip_path)

        # Draw the clipped image
        painter.drawPixmap(self.boundingRect(), self.pixmap)

        # Restore the painter state
        painter.restore()

class AutoResizeLabel(QLabel):
    def __init__(self, text="", parent=None):
        super().__init__(parent)
        # self.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self._full_text = text
        self._elide_mode = Qt.TextElideMode.ElideMiddle
        self._original_font_size = self.font().pointSize()  # can set to 20
        self.update_elided_text()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        self.update_elided_text()

    def setText(self, text):
        self._full_text = text
        self.update_elided_text()

    def setElideMode(self, mode):
        self._elide_mode = mode
        self.update_elided_text()

    def update_elided_text(self):
        self.adjust_font_size()
        metrics = QFontMetrics(self.font())
        elided_text = metrics.elidedText(self._full_text, self._elide_mode, self.width())
        super().setText(elided_text)

    def adjust_font_size(self):
        current_font = self.font()
        current_font_size = self._original_font_size
        # current_font.setPointSize(current_font_size)
        self.setFont(current_font)

        # Binary search for optimal font size
        min_size = 1
        max_size = self._original_font_size
        step = (max_size - min_size) // 2

        while step > 1:
            rect = self.fontMetrics().boundingRect(self._full_text)
            if rect.width() <= self.width() and rect.height() <= self.height():
                min_size += step
            else:
                max_size -= step
            step = (max_size - min_size) // 2
            # current_font.setPointSize(min_size)
            self.setFont(current_font)

        # Adjust to final optimal size
        for size in range(min_size, max_size + 1):
            # current_font.setPointSize(size)
            self.setFont(current_font)
            rect = self.fontMetrics().boundingRect(self._full_text)
            if rect.width() > self.width() or rect.height() > self.height():
                # current_font.setPointSize(size - 1)
                self.setFont(current_font)
                break

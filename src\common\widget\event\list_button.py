from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QFrame
from PySide6.QtCore import Signal
from typing import List
from src.styles.style import Style
from src.common.controller.main_controller import main_controller


class CustomButtonEventType(QWidget):
    clicked = Signal()
    def __init__(self, parent=None,feature = None):
        super().__init__(parent)
        self.feature = feature
        self.number = None
        self.object_name = feature.get('object_name', None)
        self.hide_text_state = False
        self.icon_on = feature.get('icon_on',None)
        self.icon_off = feature.get('icon_off',None)
        title = feature.get('title','')

        self.main_layout = QHBoxLayout()
        self.main_layout.setContentsMargins(0,0,0,0)
        self.main_layout.setSpacing(0)
        self.button_widget = QWidget()
        self.button_widget.setObjectName('CustomButton')
        layout = QHBoxLayout(self.button_widget)

        self.icon_label = QLabel()
        self.pixmap_on = QPixmap(self.icon_on)
        self.pixmap_off = QPixmap(self.icon_off)
        self.icon_label.setPixmap(self.pixmap_off)
        self.title_label = QLabel(title)
        self.title_label.hide()
        self.title_label.setStyleSheet(f"color: {Style.PrimaryColor.primary}; font-size: 14px;")
        layout.addWidget(self.icon_label)
        layout.addWidget(self.title_label)
        self.main_layout.addWidget(self.button_widget)
        self.setLayout(self.main_layout)

        self.number_widget = QLabel(self)
        self.number_widget.setStyleSheet('color:#efefef; font-size: 14px;')
        self.number_widget.setVisible(False)
        self.number_widget.setGeometry(18, 5, 50, 20)

    def hide_text(self, flag):
        self.hide_text_state = flag
        self.title_label.setVisible(not flag)
        self.icon_label.setPixmap(self.pixmap_off if flag else self.pixmap_on)

        # Determine border direction based on the object name
        border_side = "right" if self.object_name == 'alert' else "left"

        # Generate the stylesheet
        self.button_widget.setStyleSheet(self.generate_stylesheet(border_side))

    def generate_stylesheet(self, border_side):
        """
        Helper method to generate a stylesheet based on the border side.
        """
        main_bg = main_controller.get_theme_attribute('Color', 'main_background')
        hover_bg = main_controller.get_theme_attribute('Color', 'hover_button')
        divider = main_controller.get_theme_attribute('Color', 'divider')
        return f'''
            QWidget#CustomButton {{
                background-color: transparent;
                border-{border_side}: 1px solid {divider}; /* Border width and color */
            }} 
            QWidget#CustomButton:hover {{
                background-color: transparent;
                border-{border_side}: 1px solid {divider}; /* Border width and color */
            }}
        '''

    def restyle_button(self, icon_on, icon_off):
        self.icon_on = icon_on
        self.icon_off = icon_off

        self.pixmap_on = QPixmap(icon_on)
        self.pixmap_off = QPixmap(icon_off)
        if self.hide_text_state:
            self.icon_label.setPixmap(self.pixmap_off)
        else:
            self.icon_label.setPixmap(self.pixmap_on)

        # Determine border direction based on the object name
        border_side = "right" if self.object_name == 'alert' else "left"

        # Generate the stylesheet
        self.button_widget.setStyleSheet(self.generate_stylesheet(border_side))

    def set_number(self, number):
        self.number_widget.setVisible(True)
        self.number_widget.setText(str(number))

    def mousePressEvent(self, event):
        self.number_widget.setVisible(False)
        self.clicked.emit()

    def enterEvent(self, event):
        pass

    def leaveEvent(self, event):
        pass

class ListButton(QWidget):
    changed = Signal(int)
    def __init__(self, parent=None,list_item:List = None):
        super().__init__(parent)
        self.index = 0
        self.list_item = list_item
        self.main_layout = QHBoxLayout()
        self.main_layout.setContentsMargins(0,0,0,0)
        self.main_layout.setSpacing(0)
        self.list_action = {}
        if self.list_item is not None:
            self.add_items(self.list_item)
        self.setLayout(self.main_layout)

    def add_items(self,list_item:List = []):
        for index,item in enumerate(list_item):
            button = CustomButtonEventType(feature=item)
            if index == 0:
                button.hide_text(False)
            else:
                button.hide_text(True)
            button.clicked.connect(lambda checked = True, index=index: self.button_clicked(checked,index))
            self.main_layout.addWidget(button)
            self.list_action[index] = {'feature':item,'widget':button}

    def add_item(self,item = None):
        button = CustomButtonEventType(feature=item)
        index = len(self.list_action)
        if index == 0:
            button.hide_text(False)
        else:
            button.hide_text(True)
        button.clicked.connect(lambda checked = True, index=index: self.button_clicked(checked,index))
        self.main_layout.addWidget(button)
        self.list_action[index] = {'feature':item,'widget':button}

    def button_clicked(self,checked,index):
        for key, item in self.list_action.items():
            item['widget'].hide_text(True)
        data = self.list_action.get(index,None)
        if data is not None:
            button = data['widget']
            button.hide_text(False)
            # button.setStyleSheet("color: 1px solid #5B5B9F")
        self.index = index
        self.changed.emit(index)

    def current_index(self):
        return self.index

    def set_number(self,index,number):
        data = self.list_action.get(index,None)
        if data is not None:
            button = data['widget']
            button.set_number(number)
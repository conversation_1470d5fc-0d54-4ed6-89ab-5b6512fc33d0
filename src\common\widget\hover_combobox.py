import sys

from PySide6.QtCore import Signal, QEvent
from PySide6.QtWidgets import QComboBox, QApplication

from src.styles.style import Style
from src.common.controller.main_controller import main_controller


class ComboBoxScreensWithHover(QComboBox):
    signal_hover_screen = Signal(tuple)

    def __init__(self, parent=None):
        super().__init__(parent)
        # Check the platform
        self.platform = sys.platform
        self.view().viewport().installEventFilter(self)
        self.setStyleSheet(f'''
            QComboBox{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                border-radius: 4px;
                background: transparent;
            }}
            QComboBox::disabled{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                border-radius: 4px;
                background: transparent;
            }}

            QComboBox QAbstractItemView {{
                 border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};;
                 color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                 selection-background-color: {Style.PrimaryColor.primary};
                 background-color: {main_controller.get_theme_attribute("Color", "main_background")};
                 border-radius: 4px;
                 padding: 4px;
            }}

            QComboBox QAbstractItemView::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: transparent;
                padding: 4px;
            }}

            QComboBox QAbstractItemView::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: transparent;
                padding: 4px;
            }}
            
            QComboBox::item:selected {{
                background-color: {main_controller.get_theme_attribute("Color", "combobox_item_active_background")};
                color: #FFFFFF;
                border-radius: 2px;
                border: 1px solid #2E2E2E;
            }}
            QComboBox::down-arrow {{
                image: url({Style.PrimaryImage.down_arrow_linedit});
            }}
            QComboBox::drop-down {{
              border-left: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
              }}
        ''')

    def eventFilter(self, obj, event):
        if obj == self.view().viewport() and event.type() == QEvent.MouseMove:
            index = self.view().indexAt(event.pos())
            if index.isValid():
                screen_name = index.data()
                screens = QApplication.screens()
                for screen in screens:
                    if screen.name() == screen_name:
                        screen_index = QApplication.screens().index(screen)
                        data = (screen_index, screen)
                        # Check the platform
                        if self.platform == "darwin":
                            return
                        self.signal_hover_screen.emit(data)

        return super().eventFilter(obj, event)

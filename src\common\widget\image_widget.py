import src.utils.log_utils as LogUtils
import asyncio
import logging
from concurrent.futures import ThreadPoolExecutor
import aiohttp
logger = logging.getLogger(__name__)
import threading
from PySide6.QtCore import Qt, QObject, Signal, Slot, QRect, QThreadPool, QRunnable,QSize
import requests
from PySide6.QtCore import Qt, QObject, Signal, Slot, QRect
from PySide6.QtGui import QPixmap, QGuiApplication
from PySide6.QtWidgets import <PERSON><PERSON>abel, QVBoxLayout, QWidget, QDialog, QApplication
from src.styles.style import Style
from src.common.model.event_data_model import EventAI
class ImageLoader(QObject):
    DEBUG = False
    finished = Signal(tuple)
    custom_finished_data = Signal(QPixmap, str)
    error = Signal(str)

    def __init__(self, url, crop_rect = None, width: int = 0, height: int = 0, id: str = ''):
        super().__init__()
        self.crop_rect = crop_rect
        self.width = width
        self.height = height
        self.url = url
        self.is_running = True
        self.id = id
        self.max_retry = 10
        self.pixmap = None
        
    def stop(self):
        self.is_running = False
        self.pixmap = None
    @Slot()
    def load_image(self):
        asyncio.run(self._load_image_async())
        pass

    async def fetch_image(self, session: aiohttp.ClientSession, url):
        async with session.get(url) as response:
            if response.status == 200:
                return await response.read()
            else:
                raise aiohttp.ClientResponseError(response.request_info, response.history, status=response.status)
    
    async def _load_image_async(self, retry_count=0):
        # logger.debug(f'_load_image_async')
        if retry_count > self.max_retry:
            self.error.emit(f"Max retry count reached for loading image: {self.url}")
            return

        try:
            timeout = aiohttp.ClientTimeout(total=1)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                data = await self.fetch_image(session, self.url)
                # print(f"------ImageLoader: Image fetched from {self.url}")
                pixmap = QPixmap()
                pixmap.loadFromData(data)
                # self.caculate_pixmap_size(pixmap,width=self.width,height=self.height)
                # logger.debug(f'ImageLoader: {self.pixmap.size()} {self.width} {self.height}')
                if self.DEBUG:
                    logger.debug(f'ImageLoader: {pixmap.size()} {self.width} {self.height}')
                # Crop the image if a crop rect is specified
                if self.crop_rect:
                    pixmap = pixmap.copy(self.crop_rect)
                    if self.DEBUG:
                        logger.debug(f'ImageLoader: crop_rect {pixmap.size()}')
                pixmap_scale = self.caculate_pixmap_size(pixmap,width=self.width,height=self.height)

                if not self.is_running:
                    self.pixmap = None
                else:
                    self.finished.emit((pixmap,pixmap_scale))
                    self.custom_finished_data.emit(pixmap, self.id) 
        except Exception as e:
            # logger.error(f"Error loading image: {e}. Retrying ({retry_count + 1}/{self.max_retry})")
            await self._load_image_async(retry_count + 1)

    def __del__(self):
        self.stop()

    def caculate_pixmap_size(self, pixmap:QPixmap, width = 0, height = 0):
        origin_width = pixmap.size().width()
        origin_height = pixmap.size().height()
        # logger.debug(f"caculate_pixmap_size = {origin_width, origin_height}")
        if width !=0 and height !=0:
            origin_ratio = origin_width / origin_height
            # logger.debug(f"caculate_pixmap_size = {origin_ratio}")
            new_ratio = width / height
            # logger.debug(f"caculate_pixmap_size1 = {new_ratio}")
            if origin_ratio < new_ratio:
                pixmap = pixmap.scaled(origin_ratio * height, height, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation) 
            else:
                pixmap = pixmap.scaled(width, width / origin_ratio, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation) 
        elif width !=0:
            pixmap = pixmap.scaledToWidth(width)
        elif height !=0:
            pixmap = pixmap.scaledToHeight(height)
        return pixmap
    
    def update_resize(self,width,height):
        self.width = width
        self.height = height

class ImageLoaderWorker(QRunnable):
    def __init__(self, loader):
            super().__init__()
            self.loader: ImageLoader = loader
            
    @Slot()
    def run(self):
        self.loader.load_image()

class ImageWidget(QWidget):
    def __init__(self, parent=None,event_ai:EventAI = None, crop_rect: QRect = None, width: int = 0, height: int = 0, allow_click_to_show_origin: bool = True, thread_pool:ThreadPoolExecutor = None,width_image:int = 0,height_image:int = 0,pixmapFullUrl = None,image_type = 0,is_event_bar = False):
        super().__init__(parent)
        self.thread_pool = thread_pool
        self.event_ai = event_ai
        self.image_type = image_type
        self.allow_click_to_show_origin = allow_click_to_show_origin
        self.crop_rect = crop_rect
        self.width_image = width
        self.height_height = height
        self.width_image_event = width_image
        self.height_image = height_image
        self.is_event_bar = is_event_bar
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        self.label_loading = QLabel(self)
        self.label_loading.setText(self.tr('Loading...'))
        self.label_loading.setStyleSheet(f'''
                QLabel {{
                    color: {Style.PrimaryColor.white};
                }}
                ''')
        self.label_loading.setAlignment(Qt.AlignCenter)
        # self.label.setStyleSheet('border: 1px solid #FFFFFF')
        # self.label.setFixedWidth(100)
        self.layout.addWidget(self.label_loading)
        # self.progress_bar = QProgressBar(self)
        # self.progress_bar.setRange(0, 0)  # Indeterminate progress bar
        # self.layout.addWidget(self.progress_bar)

        self.image_loader = None
        self.pixmap = pixmapFullUrl
        # register event when last window closed
        app = QApplication.instance()
        def on_app_quit():
            if self.image_loader:
                self.image_loader.stop()
        app.aboutToQuit.connect(on_app_quit)
        if self.pixmap is None:
            self.load_from_url()

    def closeEvent(self, event):
        # logger.debug(f"closeEvent = {self.image_loader.pixmap}")
        if self.image_loader is not None:
            # if self.image_loader.pixmap is not None:
            self.image_loader.stop()
        self.pixmap = None
        self.label_loading.clear()

    def load_from_url(self, url = None):
        # check if url is empty or url not have 'http/https' then return
        # logger.debug(f"load_from_url = {self.image_type,self.event_ai.imageFullUrl,self.event_ai.imageUrl}")
        if self.image_type == 0:
            self.url = self.event_ai.imageFullUrl
        elif self.image_type == 1:
            self.url = self.event_ai.imageUrl
        else:
            self.url = self.event_ai.imageLicensePlateUrl
        # self.progress_bar.setValue(0)
        # self.progress_bar.show()

        if self.image_loader is not None:
            return
        self.image_loader = ImageLoader(url=self.url, crop_rect=self.crop_rect, width=self.width_image, height=self.height_height)
        self.image_loader.finished.connect(self._on_image_loaded)
        self.image_loader.error.connect(self._on_image_load_error)

        # if self.thread_pool:
        #     # worker = ImageLoaderWorker(self.image_loader)
        #     self.thread_pool.map(self.image_loader.load_image)
        # else:
        thread = threading.Thread(target=self.image_loader.load_image)
        thread.start()
    
    def run_in_thread(self):
        thread = threading.Thread(target=self.image_loader.load_image)
        thread.start()

    @Slot(QPixmap)
    def _on_image_loaded(self, data):
        pixmap, pixmap_scale = data
        # Nếu muốn lưu pixmap để sử dụng khi drop event này lên grid item mà không cần gửi request lên server thì mở comment dưới
        # Nếu mở comment thì gây vấn đề tăng Ram
        # if self.event_ai is not None:
        #     if self.image_type == 0:
        #         self.event_ai.pixmapFullUrl = self.image_loader.pixmap
        #     elif self.image_type == 1:
        #         self.event_ai.pixmapUrl = self.image_loader.pixmap
        #     else:
        #         self.event_ai.pixmapLicensePlateUrl = self.image_loader.pixmap

        # Trong trong trường hợp dùng ImageWidget để show pixmap trên event bar thì không cần sử dụng self.pixmap để tránh tăng Ram
        # Trong trong trường hợp dùng ImageWidget để show pixmap trên grid item thì cần sử dụng self.pixmap để lưu pixmap gốc phục vụ cho quá trình resize pixmap khi thay đổi grid.
        if not self.is_event_bar:
            self.pixmap = pixmap
        self.set_image(pixmap_scale)

    @Slot(str)
    def _on_image_load_error(self, error_message):
        # Handle error case, e.g., show an error message
        # logger.debug(f'ahihi = {error_message}')
        pass
    
    def update_resize(self,width,height):

        if self.pixmap is None:
            if self.image_loader is not None:
                self.pixmap = self.image_loader.pixmap
                self.image_loader.update_resize(width,height)
            else:
                pass

        if self.pixmap is not None:
            self.label_loading.clear()
            
            pixmap = self.pixmap.scaled(width*0.9,height*0.9,Qt.AspectRatioMode.KeepAspectRatio)
            # self.label.setFixedWidth(width)
            self.label_loading.setPixmap(pixmap)

    def caculate_pixmap_size(self, pixmap:QPixmap, width = 0, height = 0):
        origin_width = pixmap.size().width()
        origin_height = pixmap.size().height()
        # logger.debug(f"caculate_pixmap_size = {origin_width, origin_height}")
        if width !=0 and height !=0:
            origin_ratio = origin_width / origin_height
            # logger.debug(f"caculate_pixmap_size = {origin_ratio}")
            new_ratio = width / height
            # logger.debug(f"caculate_pixmap_size1 = {new_ratio}")
            if origin_ratio < new_ratio:
                pixmap = pixmap.scaled(origin_ratio * height, height)  
            else:
                pixmap = pixmap.scaled(width, width / origin_ratio)  
        elif width !=0:
            pixmap = pixmap.scaledToWidth(width)
        elif height !=0:
            pixmap = pixmap.scaledToHeight(height)
        return pixmap
    
    def set_image(self, pixmap):
        self.label_loading.clear()
        # if self.width_image_event != 0:
        #     self.label.setFixedWidth(self.width_image_event)
        self.label_loading.setPixmap(pixmap)
        # self.label.setStyleSheet("background-color: #303750; border: 1px solid #303750; border-radius: 2px")
        if self.image_loader is not None:
            self.image_loader.stop()

    def show_image(self):
        if self.label_loading.pixmap():
            #set geometry to center
            # get the size of the screen
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
            rect = screen.availableGeometry()
            # get the center point of screen
            center_point = desktop_screen_size.center()
            height_of_dialog = rect.height()/2
            #show in qdialog
            dialog = QDialog(self)
            image = ImageWidget(dialog, height=height_of_dialog, crop_rect=self.crop_rect, allow_click_to_show_origin=False)
            image.load_from_url(self.url)
            dialog.layout = QVBoxLayout(dialog)
            dialog.layout.addWidget(image)
            
            # set the center point of window
            dialog.move(center_point.x() - rect.width() / 2, center_point.y() - rect.height() / 2)
            dialog.exec()
            



# if __name__ == "__main__":
#     app = QApplication(sys.argv)

#     widget = ImageWidget()
#     widget.load_from_url("https://images.pexels.com/photos/842711/pexels-photo-842711.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1")  # Replace with your image URL
#     widget.show()

#     sys.exit(app.exec())

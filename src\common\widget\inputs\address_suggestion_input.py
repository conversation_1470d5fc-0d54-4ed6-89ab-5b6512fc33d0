from typing import List
from PySide6.QtWidgets import QCompleter
from PySide6.QtCore import QStringListModel, Qt, Slot, QTimer, Signal
from pydantic import <PERSON>son
import requests

from src.common.controller.main_controller import main_controller
from src.presentation.device_management_screen.widget.list_custom_widgets import InputWithDataCallback

class AddressSuggestionInput(InputWithDataCallback):
    update_completer_signal = Signal(requests.models.Response)
    def __init__(self, key=None, title=None, text_placeholder=None):
        super().__init__(key=key, title=title, text_placeholder=text_placeholder)
        
        self.can_fetch = True
        # Create debounce timer for smoother suggestions
        self.debounce_timer = QTimer()
        self.debounce_timer.setSingleShot(True)
        self.debounce_timer.setInterval(300)
        self.debounce_timer.timeout.connect(self.fetch_suggestions)
        
        # Timer for throttling
        self.delay_timer = QTimer()
        self.delay_timer.setSingleShot(True)
        self.delay_timer.setInterval(300)
        self.delay_timer.timeout.connect(self.enable_fetch)
        
        self.current_keyword = ""
        self.setup_ui()

    def setup_ui(self):
        self.completer = QCompleter()
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setCompletionMode(QCompleter.PopupCompletion)
        self.completer.setFilterMode(Qt.MatchContains)
        self.model = QStringListModel()
        self.completer.setModel(self.model)
        self.line_edit.setCompleter(self.completer)
        self.line_edit.textEdited.connect(self.on_keyword_changed)
        self.update_completer_signal.connect(self.handle_suggestions)

    @Slot()
    def enable_fetch(self):
        self.can_fetch = True

    @Slot(str)
    def on_keyword_changed(self, keyword):
        self.current_keyword = keyword
        if keyword == "":
            self.model.setStringList([])
            return
        
        # Reset and start debounce timer
        self.debounce_timer.stop()
        self.debounce_timer.start()

    def fetch_suggestions(self):
        if not self.current_keyword or not self.can_fetch:
            return
        
        self.can_fetch = False
        self.delay_timer.start()
        main_controller.current_controller.get_map_suggestions(
            self.current_keyword, 
            self.callback_on_keyword_changed
        )

    def callback_on_keyword_changed(self, response):
        self.update_completer_signal.emit(response)
    
    @Slot(str)
    def handle_suggestions(self, response):
        if response.status_code != 200:
            return
        
        suggestions_data = response.json()
        if not suggestions_data:
            return

        suggestions_name = []
        
        for data in suggestions_data:
            display_name = data["display_name"]
            # Only add if it contains the search term
            if self.current_keyword.lower() in display_name.lower():
                suggestions_name.append(display_name)
        
        # Update the model with filtered suggestions
        self.model.setStringList(suggestions_name)
        if suggestions_name:
            self.completer.complete()

import math
from enum import Enum
from functools import partial
from typing import List

from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QAction, QIcon
from PySide6.QtWidgets import QMenu, QWidget, QGridLayout, QPushButton, QWidgetAction, QVBoxLayout, QCheckBox, QLabel
from src.common.qml.models.common_enum import CommonEnum
from src.common.camera.video_capture import StreamCameraType
from src.common.model.aiflows_model import Ai<PERSON>low,AiFlowModel,aiflow_model_manager
from src.presentation.device_management_screen.widget.ai_state import AIFlowType, AIType
from src.common.model.item_grid_model import ItemGridModel
from src.common.model.main_tree_view_model import TreeType
from src.common.model.device_models import TabType
from src.common.widget.button_state import ButtonState
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from src.utils.camera_qsettings import Camera_Qsettings
# from src.common.widget.camera_widget import CameraWidget
from src.common.model.camera_model import Camera,CameraModel,camera_model_manager
from src.common.model.group_model import group_model_manager
from src.common.model.tab_model import TabModel,tab_model_manager,GridItem,ItemType
from src.common.model.event_data_model import  EventAI
from src.presentation.camera_screen.managers.grid_manager import GridModel, gridManager
import logging
from src.utils.theme_setting import theme_setting

logger = logging.getLogger(__name__)

class AiFlowMenuType(Enum):
    MENU_RESTREAM = 'MENU_RESTREAM'
    MENU_HUMAN_FLOW = 'MENU_HUMAN_FLOW'
    MENU_VEHICLE_FLOW = 'MENU_VEHICLE_FLOW'
    MENU_ACCESS_CONTROL_FLOW = 'MENU_ACCESS_CONTROL_FLOW'
    MENU_CROWDED_FLOW = 'MENU_CROWDED_FLOW'

class CustomSubMenu(QMenu):
    def __init__(self):
        super().__init__()
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))

class CustomMenuWithCheckbox(QMenu):
    signal_change_ai_flow_single_item = Signal(tuple)
    signal_change_ai_flow_multi_item = Signal(tuple)
    signal_change_video_stream = Signal(tuple)
    signal_turn_off_all_AI = Signal(list)
    action_aitype_signal = Signal(tuple)

    def __init__(self, item_data=None, list_type_ai_flow=None, list_type_video_stream=None, camera_widget=None,
                 selected_groups=None, selected_cameras=None, controller=None, item_model=None):
        super().__init__()
        # self.item = item
        self.controller = controller
        self.item_data = item_data
        self.selected_groups = selected_groups
        self.selected_cameras = selected_cameras
        self.list_type_ai_flow = list_type_ai_flow
        self.list_type_video_stream = list_type_video_stream
        self.item_model = item_model
        self.id_item_model = self.item_model.get_property('id')
        self.camera_widget = camera_widget
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        if item_data == TreeType.Camera:
            self.model = camera_model_manager.get_camera_model(id=self.item_model.get_property('id'))
        elif item_data == TreeType.Group:
            self.model = group_model_manager.get_group_model(id=self.item_model.get_property('id'))
        if self.model is not None:
            self.ai_flow_ids = self.model.get_property("aiFlowIds")
        self.load_ui()

    def load_ui(self):
        if self.list_type_ai_flow is not None:
            for key, action_ai in self.list_type_ai_flow.items():
                action = QAction(action_ai, self)
                action.setData(action_ai)
                action.setCheckable(True)
                icon = self.get_icon(key)
                if key in self.item_model.get_property("features"):
                    # action.setChecked(True)
                    action.setIcon(icon)
                elif key == "Turn off AI":
                    pass
                else:
                    icon.addPixmap(icon.pixmap(16, 16, QIcon.Mode.Disabled)) 
                    action.setIcon(icon)
                action.triggered.connect(partial(self.action_triggered,key))
                self.addAction(action)

            if len(self.item_model.get_property("features")) == 0:
                first_action = self.actions()[0]
                first_action.setChecked(True)

        elif self.list_type_video_stream is not None:
            self.restream_menu = SubMenuAIFlow(type_menu=AiFlowMenuType.MENU_RESTREAM)
            self.restream_menu.action_restream_signal.connect(self.action_restream_slot)
            self.add_aiflow_stream_action_to_menu(self.ai_flow_ids)
            # ["Main", "Sub", "AI"]
            for action_stream in self.list_type_video_stream:
                if action_stream == "Main":
                    self.main_stream_action = QAction(self.tr("Main"), self)
                    self.main_stream_action.setData(("Main", self.item_model.get_property('id')))
                    self.main_stream_action.setCheckable(True)
                    self.addAction(self.main_stream_action)
                elif action_stream == "Sub":
                    self.sub_stream_action = QAction(self.tr("Sub"), self)
                    self.sub_stream_action.setData(("Sub", self.item_model.get_property('id')))
                    self.sub_stream_action.setCheckable(True)
                    self.addAction(self.sub_stream_action)
                elif action_stream == "AI":
                    self.ai_stream_action = QAction(self.tr("AI"), self)
                    self.ai_stream_action.setData(("AI", self.item_model.get_property('id')))
                    self.ai_stream_action.setCheckable(True)
                    self.ai_stream_action.setMenu(self.restream_menu)
                    self.addAction(self.ai_stream_action)
            list_action_stream = self.actions()
            for i, actions in enumerate(list_action_stream):
                actions.triggered.connect(lambda data=actions.data(), index=i: self.action_video_stream_triggered(data, index))
            self.update_status_video_stream()

    def add_aiflow_stream_action_to_menu(self, aiFlowIds):  # HIEN THI LUONG RESTREAM
        if len(aiFlowIds) == 0:
            return
        list_ai_in_camera = self.get_list_ai_flow(aiFlowIds)
        for aiFlowId, aiflow_model in list_ai_in_camera.items():
            logger.debug(f'aiflow_model: {aiflow_model} - {type(aiflow_model)} - {aiFlowId}')
            aiflow = aiflow_model.data
            logger.debug(f'aiflow: {aiflow.to_dict()}')
            if (aiflow.is_recognition_and_protection() or aiflow.is_risk_identification()) and aiflow.is_apply():
                # Add each AI type as an action to the restream_menu (submenu for ai_stream_action)
                logger.debug(f'aiflow.type: {aiflow.type} - {type(aiflow)}')
                self.restream_menu.add_action(aiflow.type)
                

    def get_icon(self,key):
        if key == AIFlowType.RECOGNITION or key == AIFlowType.PROTECTION or key == AIFlowType.FREQUENCY or key == AIFlowType.ACCESS or key == AIFlowType.MOTION or key == AIFlowType.TRAFFIC:
            return QIcon(Style.PrimaryImage.ic_recognition_security)
        else:
            return QIcon(Style.PrimaryImage.ic_risk_identification)
        
    def update_status_video_stream(self):
        if self.camera_widget is None:
            return
        if self.camera_widget.video_capture.stream_type == CommonEnum.StreamType.MAIN_STREAM:
            self.main_stream_action.setChecked(True)
        elif self.camera_widget.video_capture.stream_type == CommonEnum.StreamType.SUB_STREAM:
            self.sub_stream_action.setChecked(True)
        else:
            self.ai_stream_action.setChecked(True)
        list_action = self.actions()
        for i in list_action:
            if i.data()[0] == "Sub":
                i.setDisabled(False)
            if i.data()[0] == "AI":
                # Enable AI stream action if restream_menu has actions
                if self.restream_menu.actions():
                    i.setDisabled(False)
                else:
                    i.setDisabled(True)

    def get_list_ai_flow(self, aiFlowIds: List = []):
        list_ai_flow = {}
        for aiFlowId in aiFlowIds:
            aiflow = aiflow_model_manager.get_aiflow_model(id = aiFlowId)
            if aiflow is not None:
                list_ai_flow[aiFlowId] = aiflow
        return list_ai_flow

    def get_ai_activate(self, aiFlowIds: List = []):
        list_ai_activate = {}
        for aiFlowId in aiFlowIds:
            aiflow:AiFlowModel = aiflow_model_manager.get_aiflow_model(id = aiFlowId)
            if aiflow is not None and aiflow.data.is_apply():
                list_ai_activate[aiFlowId] = aiflow
        return list_ai_activate
    
    def action_triggered(self, ai_type):
        data = (ai_type, self.item_model)
        self.action_aitype_signal.emit(data)

    def action_turn_off_AI(self, list_ai_ids):
        data = (list_ai_ids, self.item_model)
        self.signal_turn_off_all_AI.emit(data)
    
    def action_ai_triggered(self, data_emitted):
        index, ai_flow_id = data_emitted
        data = (index, ai_flow_id, self.item_model)
        self.signal_change_ai_flow_single_item.emit(data)

    def action_video_stream_triggered(self, type_action, index):
        # ["Main", "Sub", "AI"]
        button_feature = ButtonState.ButtonFeature.STREAM_FLOW
        is_use_camera_name = True
        data = (index, None, self.item_model.get_property('id'))
        self.signal_change_video_stream.emit(data)

    def action_restream_slot(self, data):
        index, action_data = data
        logger.debug(f'action_restream_slot: {data}')
        ai_flow_id = action_data
        index = 2
        data_emit = (index, ai_flow_id, self.item_model.get_property('id'))
        self.signal_change_video_stream.emit(data_emit)

class SubMenuAIFlow(QMenu):
    action_aiflow_signal = Signal(tuple)
    action_restream_signal = Signal(tuple)
    checkbox_signal = Signal(tuple)

    def __init__(self, item_data=None, list_ai_flow=None, style_image=None, type_menu=None, is_show_checkbox_all=False, item_model=None):
        super().__init__()
        # type_menu = MENU_RESTREAM, HUMAN_FLOW, VEHICLE_FLOW, ACCESS_CONTROL_FLOW, CROWDED_FLOW
        self.type_menu = type_menu
        self.item_data = item_data
        self.item_model = item_model
        self.list_action_ai_flow = list_ai_flow
        self.style_image = style_image
        self.is_show_checkbox_all = is_show_checkbox_all
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.load_ui()

    def load_ui(self):
        if self.is_show_checkbox_all:
            # Create a QWidgetAction
            widget_action = QWidgetAction(self)
            # Create a widget to hold the checkbox
            widget = QWidget()
            layout = QVBoxLayout()
            layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
            layout.setContentsMargins(0, 4, 0, 4)
            self.checkbox_apply_all_ai = QCheckBox("Enable all AI scenarios")
            self.checkbox_apply_all_ai.setStyleSheet(f"""
                QCheckBox {{
                    color: white;
                }}
                QCheckBox::indicator:checked {{
                    border: none;
                    image: url({Style.PrimaryImage.checkbox_checked});
                    width: 20px;
                    height: 20px;
                    margin-left: 10px;
                }}
                QCheckBox::indicator:unchecked {{
                    border: none;
                    image: url({Style.PrimaryImage.checkbox_checked});
                    width: 20px;
                    height: 20px;
                    margin-left: 10px;
                }}
                QCheckBox::indicator:disabled {{
                    border: none;
                    image: url({Style.PrimaryImage.checkbox_unchecked});
                    width: 20px;
                    height: 20px;
                    margin-left: 10px;
                }}
            """)
            self.checkbox_apply_all_ai.stateChanged.connect(self.checkbox_toggled)
            layout.addWidget(self.checkbox_apply_all_ai)
            widget.setLayout(layout)
            # Set the widget to the QWidgetAction
            widget_action.setDefaultWidget(widget)

            self.addAction(widget_action)
            self.addSeparator()

        if self.list_action_ai_flow is not None:
            for aiflow in self.list_action_ai_flow:
                self.add_action(key=aiflow.type)

    def checkbox_toggled(self, state):
        # list_ai_flow: dict[int, str] = {}
        list_ai_flow = []
        for index, action in enumerate(self.actions()):
            if action.data() is not None:
                list_ai_flow.append(action.data())
        data_emit = (state, list_ai_flow)
        self.checkbox_signal.emit(data_emit)
        self.close()

    def add_action(self, key, model = None):
        name, icon = self.get_text(key)
        action = QAction(name, self)
        action.setIcon(icon)
        action.setCheckable(True)
        self.addAction(action)
        action.setData(key)
        action.triggered.connect(self.action_triggered)

    def get_text(self, key):
        logger.debug(f'get_text key: {key}')
        if key == AIFlowType.RECOGNITION.__str__():
            return (self.tr("Recognition"),QIcon(Style.PrimaryImage.ic_recognition_security))
        elif key == AIFlowType.PROTECTION.__str__():
            return (self.tr("Protection"),QIcon(Style.PrimaryImage.ic_recognition_security))
        elif key == AIFlowType.FREQUENCY.__str__():
            return (self.tr("Frequency"),QIcon(Style.PrimaryImage.ic_recognition_security))
        elif key == AIFlowType.ACCESS.__str__():
            return (self.tr("Access"),QIcon(Style.PrimaryImage.ic_recognition_security))
        elif key == AIFlowType.MOTION.__str__():
            return (self.tr("Motion"),QIcon(Style.PrimaryImage.ic_recognition_security))
        elif key == AIFlowType.TRAFFIC.__str__():
            return (self.tr("Traffic"),QIcon(Style.PrimaryImage.ic_recognition_security))
        elif key == AIFlowType.WEAPON.__str__():
            return (self.tr("Weapon"),QIcon(Style.PrimaryImage.ic_risk_identification))
        elif key == AIFlowType.UFO.__str__():
            return (self.tr("UFO"),QIcon(Style.PrimaryImage.ic_risk_identification))
        return None

    def action_triggered(self):
        # get current action triggered
        action = self.sender()
        index = self.actions().index(action)
        action_data = action.data()
        combined_arg = (index, action_data)
        if self.type_menu == AiFlowMenuType.MENU_RESTREAM:
            logger.debug(f'action_triggered: {combined_arg} - type_menu: {self.type_menu}')
            self.action_restream_signal.emit(combined_arg)
        # else:
        #     self.action_aiflow_signal.emit(combined_arg)

class CustomMenuForEventRightClick(QMenu):
    # input is list item name of tab and first item is 'new tab'
    open_in_tab_signal = Signal(tuple)
    open_new_tab_signal = Signal(tuple)
    open_group_in_view_signal = Signal(object)
    def __init__(self,event_ai:EventAI = None,show_grid_position = True):
        super().__init__()
        self.event_ai = event_ai
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        # self.init_action_and_menu()
        # self.addActionToMenu()

        new_tab = QAction(self.tr("New View"), self)
        # data = (camera_name, 'New View', [0, 0], self.item_standard, self.list_camera_selection)
        new_view_data = ("New View",self.event_ai.id)
        new_tab.triggered.connect(lambda: (self.open_new_tab_signal.emit(new_view_data)))
        self.addAction(new_tab)

        # new Saved View
        new_saved_view = QAction(self.tr("New Saved View"), self)
        new_savedview_data = ("New Saved View",self.event_ai.id)
        new_saved_view.triggered.connect(lambda: (self.open_new_tab_signal.emit(new_savedview_data)))
        self.addAction(new_saved_view)

        # new Virtual Window
        new_virtual_window = QAction(self.tr("New Virtual Window"), self)
        new_virtual_data = ("New Virtual Window",self.event_ai.id)
        new_virtual_window.triggered.connect(lambda: (self.open_new_tab_signal.emit(new_virtual_data)))
        self.addAction(new_virtual_window)
        self.addSeparator()

        # add tab available use MenuPosition not QAction
        list_tab_name_available = []
        list_tab_grid_model: List[ItemGridModel] = []

        # tab_name = new_custom_tab_widget.tab_widget.tab_bar.tabText(
        #     index)
        # tab_name = tab_name.replace("*", "") if tab_name[:-1] == "*" else tab_name

        for tab_name, tab_model in tab_model_manager.tab_model_list.items():
            tab_type = tab_model.data.type
            if tab_type != 'MapView' and tab_model.data.isShow:
                list_tab_name_available.append(tab_model.get_property('name'))
                list_tab_grid_model.append(ItemGridModel.from_dict(tab_model.data.currentGrid))

        if not show_grid_position:
            for idx in range(len(list_tab_name_available)):
                menu_action = QAction(f"{list_tab_name_available[idx]}", self)
                # lambda data=actions.data(), index=i: self.action_video_stream_triggered(data, index)
                menu_action.triggered.connect(lambda data_text=menu_action.text(), index=idx: self.open_group_in_view(data_text, index))
                self.addAction(menu_action)
        else:
            for i in range(len(list_tab_name_available)):
                tab = MenuGridPosition(current_item_grid_model=list_tab_grid_model[i],tab_name=list_tab_name_available[i])
                tab.row_col_signal.connect(self.open_in_tab)
                menu_action = tab.menuAction()
                # set text for menu action
                menu_action.setText(f"{list_tab_name_available[i]}")
                self.addAction(menu_action)

    def open_in_tab(self, data):
        tab_name,row,col = data
        self.open_in_tab_signal.emit((self.event_ai.id,tab_name,row,col))

class MenuGridPosition(QMenu):
    row_col_signal = Signal(tuple)

    def __init__(self,current_item_grid_model = None,tab_name=None):
        super().__init__()
        self.tab_index = None
        self.tab_name = tab_name
        self.item_grid_model = main_controller.current_grid_model if current_item_grid_model is None else current_item_grid_model
        custom_widget = QWidget(self)
        grid_layout = QGridLayout()
        grid_layout.setSpacing(1)
        grid_layout.setContentsMargins(2, 2, 2, 2)
        current_tab_idx = main_controller.current_tab
        # print(f'self.item_grid_model: {self.item_grid_model.divisions_type} - {self.item_grid_model.row} - {self.item_grid_model.column}')
        if self.item_grid_model.divisions_type == ButtonState.DivisionType.STANDARD_DIVISIONS:
            index = 1
            for row in range(self.item_grid_model.row):
                for col in range(self.item_grid_model.column):
                    button = QPushButton(f"{index}")
                    button.setStyleSheet(
                        f"""
                        QPushButton {{
                            background-color: {Style.PrimaryColor.on_background}; /* Green background */
                            color: white; /* White text */
                            border-radius: 1px; /* Rounded corners */
                            padding: 10px 20px 10px 20px; /* Padding inside the button */
                            font-size: 16px; /* Font size */
                        }}

                        QPushButton:hover {{
                            background-color: {Style.PrimaryColor.primary_hover}; /* Darker green on hover */
                        }}

                        QPushButton:pressed {{
                            background-color: {Style.PrimaryColor.primary_pressed}; /* Dark blue on press */
                        }}
                        """
                    )
                    button.clicked.connect(partial(self.button_clicked, row=row, col=col))
                    grid_layout.addWidget(button, row, col)
                    index += 1
        else:
            index = 0
            size = int(math.sqrt(self.item_grid_model.total_grid_count))
            ex_list = self.item_grid_model.data
            if ex_list is not None:
                new_data = []
                for item in ex_list:
                    list_to_tuple = set()
                    if isinstance(item, list):
                        for list_item in item:
                            list_to_tuple.add(tuple(list_item))
                        new_data.append(set(list_to_tuple))
                    else:
                        new_data.append(set(item))
                all_excluded_positions = set.union(*new_data)
                for list_tuple in new_data:
                    min_row = min(row for row, _ in list_tuple)
                    min_col = min(col for _, col in list_tuple)
                    max_row = max(row for row, _ in list_tuple)
                    max_col = max(col for _, col in list_tuple)

                    row_span = max_row - min_row + 1
                    col_span = max_col - min_col + 1
                    width_large = col_span * 50 + (row_span - 1)
                    height_large = row_span * 30 + (col_span - 1)

                    button_large = QPushButton(f"{index}")
                    button_large.setStyleSheet(
                        f"""
                        QPushButton {{
                            background-color: {Style.PrimaryColor.on_background}; /* Green background */
                            color: white; /* White text */
                            border-radius: 1px; /* Rounded corners */
                            padding: 2px 2px 2px 2px; /* Padding inside the button */
                            font-size: 16px; /* Font size */
                        }}

                        QPushButton:hover {{
                            background-color: {Style.PrimaryColor.primary_hover}; /* Darker green on hover */
                        }}

                        QPushButton:pressed {{
                            background-color: {Style.PrimaryColor.primary_pressed}; /* Dark blue on press */
                        }}
                        """
                    )
                    button_large.setFixedSize(width_large, height_large)
                    button_large.clicked.connect(partial(self.button_clicked, row=min_row, col=min_col))
                    grid_layout.addWidget(button_large, min_row, min_col, row_span, col_span)

                # Create items surrounding the largest_item
                for row in range(size):
                    for col in range(size):
                        if (row, col) in all_excluded_positions:
                            continue  # Skip the specified positions
                        button_small = QPushButton(f"{index}")
                        button_small.setFixedSize(50, 30)
                        button_small.setStyleSheet(
                            f"""
                            QPushButton {{
                                background-color: {Style.PrimaryColor.on_background}; /* Green background */
                                color: white; /* White text */
                                border-radius: 1px; /* Rounded corners */
                                padding: 2px 2px 2px 2px; /* Padding inside the button */
                                font-size: 16px; /* Font size */
                            }}

                            QPushButton:hover {{
                                background-color: {Style.PrimaryColor.primary_hover}; /* Darker green on hover */
                            }}

                            QPushButton:pressed {{
                                background-color: {Style.PrimaryColor.primary_pressed}; /* Dark blue on press */
                            }}
                            """
                        )
                        button_small.clicked.connect(partial(self.button_clicked, row=row, col=col))
                        grid_layout.addWidget(button_small, row, col)

                # Create a list to store widgets and their positions
                widget_positions = []
                for index in range(grid_layout.count()):
                    item = grid_layout.itemAt(index)
                    re_row, re_col, re_row_span, re_col_span = grid_layout.getItemPosition(index)
                    widget_positions.append((item.widget(), re_row, re_col, re_row_span, re_col_span))

                # Sort the list based on positions
                widget_positions.sort(key=lambda x: (x[1], x[2]))

                # Rearrange widgets in the layout
                for new_index, (widget, re_row, re_col, re_row_span, re_col_span) in enumerate(widget_positions):
                    widget.setText(f"{new_index}")
                    grid_layout.addWidget(widget, re_row, re_col, re_row_span, re_col_span)
                    # Optionally, set row and column stretch to manage spacing
                    grid_layout.setRowStretch(re_row, 1)
                    grid_layout.setColumnStretch(re_col, 1)
                # Update the layout
                grid_layout.update()

        custom_widget.setLayout(grid_layout)

        # Create a QWidgetAction to add the custom widget to the QMenu
        custom_action = QWidgetAction(self)
        custom_action.setDefaultWidget(custom_widget)
        self.addAction(custom_action)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))

    def button_clicked(self, row, col):
        print(f'button_clicked {row,col}')
        # value = (self.camera_name, self.tab_name, [row, col])
        self.row_col_signal.emit((self.tab_name,row, col))


class SubMenuOpenCameraInTab(QMenu):
    # input is list item name of tab and first item is 'new tab'
    open_in_tab_signal = Signal(tuple)
    open_new_tab_signal = Signal(tuple)
    open_group_in_view_signal = Signal(object)

    def __init__(self, model = None, item_data=None, camera_name: str = None, show_grid_position=True, list_camera_selection=None):
        super().__init__()
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.camera_name = camera_name
        self.model = model
        self.item_standard = item_data
        self.list_camera_selection = list_camera_selection
        
        # add new tab
        new_tab = QAction(self.tr("New View"), self)
        data_new_tab = (self.model, 'New View', [0, 0], self.item_standard, self.list_camera_selection)
        new_tab.triggered.connect(lambda: (self.open_new_tab_signal.emit(data_new_tab)))
        self.addAction(new_tab)

        # new Saved View
        new_saved_view = QAction(self.tr("New Saved View"), self)
        data_saved_view = (self.model, 'New Saved View', [0, 0], self.item_standard, self.list_camera_selection)
        new_saved_view.triggered.connect(lambda: (self.open_new_tab_signal.emit(data_saved_view)))
        self.addAction(new_saved_view)

        # new Virtual Window
        new_virtual_window = QAction(self.tr("New Virtual Window"), self)
        data_virtual_window = (self.model, 'New Virtual Window', [0, 0], self.item_standard, self.list_camera_selection)
        new_virtual_window.triggered.connect(lambda: (self.open_new_tab_signal.emit(data_virtual_window)))
        self.addAction(new_virtual_window)
        self.addSeparator()

        # add tab available use MenuPosition not QAction
        list_tab_model_available = []
        list_tab_grid_model: List[ItemGridModel] = []

        # for tab_name, tab_model in tab_model_manager.tab_model_list.items():
        #     tab_type = tab_model.data.type
        #     if tab_type != 'MapView' and tab_model.data.isShow:
        #         list_tab_model_available.append(tab_model)
        #         list_tab_grid_model.append(ItemGridModel.from_dict(tab_model.data.currentGrid))

        for id, gridModel in gridManager.data.items():
            tab_type = gridModel.get_property("type",None)
            if tab_type != TabType.MapView and tab_type != TabType.FloorView and gridModel.get_property("isShow"):
                list_tab_model_available.append(gridModel)
                # list_tab_grid_model.append(ItemGridModel.from_dict(gridModel.get_property("currentGrid")))

        if not show_grid_position:
            
            for idx in range(len(list_tab_model_available)):
                data = (self.model, list_tab_model_available[idx], [0, 0], self.item_standard, self.list_camera_selection)
                menu_action = QAction(f"{list_tab_model_available[idx].get_property('name')}", self)
                menu_action.triggered.connect(partial(self.open_group_in_view, data))
                self.addAction(menu_action)
        else:
            for i,gridModel in enumerate(list_tab_model_available):
                tab = MenuPosition(model=self.model, gridModel=gridModel, is_from_map=True,
                                   current_item_grid_model=None)
                tab.row_col_signal.connect(self.open_in_tab)
                menu_action = tab.menuAction()
                # set text for menu action
                menu_action.setText(f"{gridModel.get_property('name')}")
                self.addAction(menu_action)

    def open_in_tab(self, data):
        model, gridModel, [row, col] = data
        data_emit = (model, gridModel, [row, col], self.item_standard, self.list_camera_selection)
        self.open_in_tab_signal.emit(data_emit)

    def open_group_in_view(self, data):
        self.open_group_in_view_signal.emit(data)

class SubMenuOpenFloorInGrid(QMenu):
    # input is list item name of tab and first item is 'new tab'
    open_in_tab_signal = Signal(tuple)

    def __init__(self, item_data=None, model=None, item=None):
        super().__init__()
        self.model = model
        self.server_ip = item.server_ip
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.item_standard = item_data
    
        # add tab available use MenuPosition not QAction
        list_tab_name_available = []
        list_tab_grid_model: List[ItemGridModel] = []

        # for tab_name, tab_model in tab_model_manager.tab_model_list.items():
        #     tab_type = tab_model.data.type
        #     if tab_type != TabType.MapView and tab_type != TabType.FloorView and tab_model.data.isShow:
        #         list_tab_name_available.append(tab_model.get_property("name"))
        #         list_tab_grid_model.append(ItemGridModel.from_dict(tab_model.data.currentGrid))

        for i in range(len(list_tab_name_available)):
            tab = MenuPosition(model=self.model, tab_name=list_tab_name_available[i], is_from_map=True,
                                current_item_grid_model=list_tab_grid_model[i])
            tab.row_col_signal.connect(self.open_in_tab)
            menu_action = tab.menuAction()
            # set text for menu action
            menu_action.setText(f"{list_tab_name_available[i]}")
            self.addAction(menu_action)

    def open_in_tab(self, data):
        model, tab_name, [row, col] = data
        data_emit = (model, tab_name, [row, col], self.item_standard, self.server_ip)
        self.open_in_tab_signal.emit(data_emit)

class SubMenuOpenMapInGrid(QMenu):
    # input is list item name of tab and first item is 'new tab'
    open_in_tab_signal = Signal(tuple)

    def __init__(self, item_data=None, list_camera_selection=None, model=None, item=None):
        super().__init__()
        self.model = model
        self.server_ip = item.server_ip
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.item_standard = item_data
        self.list_camera_selection = list_camera_selection
    
        # add tab available use MenuPosition not QAction
        list_tab_name_available = []
        list_tab_grid_model: List[ItemGridModel] = []

        # for id, gridModel in gridManager.data.items():
        #     tab_type = gridModel.get_property("type",None)
        #     if tab_type != TabType.MapView and tab_type != TabType.FloorView and gridModel.get_property("isShow"):
        #         list_tab_name_available.append(gridModel.get_property("name"))
        #         list_tab_grid_model.append(ItemGridModel.from_dict(gridModel.get_property("currentGrid")))

        for i in range(len(list_tab_name_available)):
            tab = MenuPosition(model=self.model, tab_name=list_tab_name_available[i], is_from_map=True,
                                current_item_grid_model=list_tab_grid_model[i])
            tab.row_col_signal.connect(self.open_in_tab)
            menu_action = tab.menuAction()
            # set text for menu action
            menu_action.setText(f"{list_tab_name_available[i]}")
            self.addAction(menu_action)

    def open_in_tab(self, data):
        model, tab_name, [row, col] = data
        data_emit = (model, tab_name, [row, col], self.item_standard, self.list_camera_selection, self.server_ip)
        self.open_in_tab_signal.emit(data_emit)


class SubMenuCreateObject(QMenu):
    create_camera_signal = Signal()
    create_building_signal = Signal()
    create_floor_signal = Signal()

    def __init__(self, item_data=None, model=None):
        super().__init__()
        self.model = model
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.item_standard = item_data

        new_tab = QAction(self.tr("Camera"), self)
        # data = (self.model, 'New View', [0, 0], self.item_standard, self.list_camera_selection)
        new_tab.triggered.connect(lambda: (self.create_camera_signal.emit()))
        self.addAction(new_tab)

        new_tab = QAction(self.tr("Building"), self)
        # data = (self.model, 'New View', [0, 0], self.item_standard, self.list_camera_selection)
        new_tab.triggered.connect(lambda: (self.create_building_signal.emit()))
        self.addAction(new_tab)

        new_tab = QAction(self.tr("Floor"), self)
        # data = (self.model, 'New View', [0, 0], self.item_standard, self.list_camera_selection)
        new_tab.triggered.connect(lambda: (self.create_floor_signal.emit()))
        self.addAction(new_tab)

    def open_in_tab(self, data):
        model, tab_name, [row, col] = data

class MenuPosition(QMenu):
    row_col_signal = Signal(list)

    def __init__(self, model, gridModel = None, current_item_grid_model = None, is_from_map = False, is_zones = False):
        super().__init__()
        self.gridModel = gridModel
        self.tab_index = None
        self.item_grid_model = main_controller.current_grid_model if current_item_grid_model is None else current_item_grid_model
        self.model = model
        custom_widget = QWidget(self)
        grid_layout = QGridLayout()
        grid_layout.setSpacing(1)
        grid_layout.setContentsMargins(2, 2, 2, 2)
        current_tab_idx = main_controller.current_tab

        if not is_from_map and not is_zones:
            for item in Camera_Qsettings.get_instance().json_status_tab:
                if current_tab_idx == item['tab_index']:
                    main_controller.current_grid_model = ItemGridModel.from_dict(item['grid'])
        # print(f'self.item_grid_model: {self.item_grid_model.divisions_type} - {self.item_grid_model.row} - {self.item_grid_model.column}')
        # if self.item_grid_model.divisions_type == ButtonState.DivisionType.STANDARD_DIVISIONS:
        #     index = 1
        #     for row in range(self.item_grid_model.row):
        #         for col in range(self.item_grid_model.column):
        #             button = QPushButton(f"{index}")
        #             button.setStyleSheet(
        #                 f"""
        #                 QPushButton {{
        #                     background-color: {main_controller.get_theme_attribute('Color', 'camera_widget_background')}; /* Green background */
        #                     color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; /* White text */
        #                     border-radius: 1px; /* Rounded corners */
        #                     padding: 10px 20px 10px 20px; /* Padding inside the button */
        #                 }}

        #                 QPushButton:hover {{
        #                     background-color: {Style.PrimaryColor.primary}; /* Darker green on hover */
        #                 }}

        #                 QPushButton:pressed {{
        #                     background-color: {Style.PrimaryColor.primary_pressed}; /* Dark blue on press */
        #                 }}
        #                 """
        #             )
        #             button.clicked.connect(partial(self.button_clicked, row=row, col=col))
        #             grid_layout.addWidget(button, row, col)
        #             index += 1
        # else:
        #     index = 0
        #     size = int(math.sqrt(self.item_grid_model.total_grid_count))
        #     ex_list = self.item_grid_model.data
        #     if ex_list is not None:
        #         new_data = []
        #         for item in ex_list:
        #             list_to_tuple = set()
        #             if isinstance(item, list):
        #                 for list_item in item:
        #                     list_to_tuple.add(tuple(list_item))
        #                 new_data.append(set(list_to_tuple))
        #             else:
        #                 new_data.append(set(item))
        #         all_excluded_positions = set.union(*new_data)
        #         for list_tuple in new_data:
        #             min_row = min(row for row, _ in list_tuple)
        #             min_col = min(col for _, col in list_tuple)
        #             max_row = max(row for row, _ in list_tuple)
        #             max_col = max(col for _, col in list_tuple)

        #             row_span = max_row - min_row + 1
        #             col_span = max_col - min_col + 1
        #             width_large = col_span * 50 + (row_span - 1)
        #             height_large = row_span * 30 + (col_span - 1)

        #             button_large = QPushButton(f"{index}")
        #             button_large.setStyleSheet(
        #                 f"""
        #                 QPushButton {{
        #                     background-color: {Style.PrimaryColor.on_background}; /* Green background */
        #                     color: white; /* White text */
        #                     border-radius: 1px; /* Rounded corners */
        #                     padding: 2px 2px 2px 2px; /* Padding inside the button */
        #                     font-size: 16px; /* Font size */
        #                 }}

        #                 QPushButton:hover {{
        #                     background-color: {Style.PrimaryColor.primary_hover}; /* Darker green on hover */
        #                 }}

        #                 QPushButton:pressed {{
        #                     background-color: {Style.PrimaryColor.primary_pressed}; /* Dark blue on press */
        #                 }}
        #                 """
        #             )
        #             button_large.setFixedSize(width_large, height_large)
        #             button_large.clicked.connect(partial(self.button_clicked, row=min_row, col=min_col))
        #             grid_layout.addWidget(button_large, min_row, min_col, row_span, col_span)

        #         # Create items surrounding the largest_item
        #         for row in range(size):
        #             for col in range(size):
        #                 if (row, col) in all_excluded_positions:
        #                     continue  # Skip the specified positions
        #                 button_small = QPushButton(f"{index}")
        #                 button_small.setFixedSize(50, 30)
        #                 button_small.setStyleSheet(
        #                     f"""
        #                     QPushButton {{
        #                         background-color: {Style.PrimaryColor.on_background}; /* Green background */
        #                         color: white; /* White text */
        #                         border-radius: 1px; /* Rounded corners */
        #                         padding: 2px 2px 2px 2px; /* Padding inside the button */
        #                         font-size: 16px; /* Font size */
        #                     }}

        #                     QPushButton:hover {{
        #                         background-color: {Style.PrimaryColor.primary_hover}; /* Darker green on hover */
        #                     }}

        #                     QPushButton:pressed {{
        #                         background-color: {Style.PrimaryColor.primary_pressed}; /* Dark blue on press */
        #                     }}
        #                     """
        #                 )
        #                 button_small.clicked.connect(partial(self.button_clicked, row=row, col=col))
        #                 grid_layout.addWidget(button_small, row, col)

        #         # Create a list to store widgets and their positions
        #         widget_positions = []
        #         for index in range(grid_layout.count()):
        #             item = grid_layout.itemAt(index)
        #             re_row, re_col, re_row_span, re_col_span = grid_layout.getItemPosition(index)
        #             widget_positions.append((item.widget(), re_row, re_col, re_row_span, re_col_span))

        #         # Sort the list based on positions
        #         widget_positions.sort(key=lambda x: (x[1], x[2]))

        #         # Rearrange widgets in the layout
        #         for new_index, (widget, re_row, re_col, re_row_span, re_col_span) in enumerate(widget_positions):
        #             widget.setText(f"{new_index}")
        #             grid_layout.addWidget(widget, re_row, re_col, re_row_span, re_col_span)
        #             # Optionally, set row and column stretch to manage spacing
        #             grid_layout.setRowStretch(re_row, 1)
        #             grid_layout.setColumnStretch(re_col, 1)
        #         # Update the layout
        #         grid_layout.update()

        custom_widget.setLayout(grid_layout)

        # Create a QWidgetAction to add the custom widget to the QMenu
        custom_action = QWidgetAction(self)
        custom_action.setDefaultWidget(custom_widget)
        self.addAction(custom_action)
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))

    def button_clicked(self, row, col):
        value = (self.model, self.gridModel, [row, col])
        self.row_col_signal.emit(value)


class SubMenuOpenInTab(QMenu):
    open_in_tab_signal = Signal(int)
    open_new_tab_signal = Signal(str)

    def __init__(self):
        super().__init__()
        self.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))

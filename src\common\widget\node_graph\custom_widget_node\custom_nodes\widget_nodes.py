from src.common.model.camera_model import CameraModelManager
from src.common.widget.node_graph.nodegraph_library import BaseNode
from src.common.controller.main_controller import main_controller


class DropdownMenuNode(BaseNode):
    """
    An example node with a embedded added QCombobox menu.
    """

    # unique node identifier.
    __identifier__ = 'nodes.widget'

    # initial default node name.
    NODE_NAME = 'Camera'

    def __init__(self, list_data=None):
        super(DropdownMenuNode, self).__init__()
        # create input & output ports
        self.add_input('IN', multi_input=True)
        self.add_output('OUT', multi_output=True)
        # create the QComboBox menu.
        if len(list_data) > 0:
            self.add_combo_menu('camera', 'Camera List', items=list_data)


class TextInputNode(BaseNode):
    """
    An example of a node with a embedded QLineEdit.
    """

    # unique node identifier.
    __identifier__ = 'nodes.widget'

    # initial default node name.
    NODE_NAME = 'Camera'

    def __init__(self, value=None, node_id=None):
        super(TextInputNode, self).__init__()
        self.node_id = node_id
        # create input & output ports
        self.add_input('IN', multi_input=True)
        self.add_output('OUT', multi_output=True)

        # create QLineEdit text input widget.
        self.add_text_input('camera_input', 'Camera name:', tab='widgets_input_camera')
        for idx, widget_child in self.widgets().items():
            line_edit = widget_child.get_custom_widget()
            line_edit.setText(value)
            line_edit.setReadOnly(True)

    def set_node_id(self, new_id):
        self.node_id = new_id

class CheckboxNode(BaseNode):
    """
    An example of a node with 2 embedded QCheckBox widgets.
    """

    # set a unique node identifier.
    __identifier__ = 'nodes.widget'

    # set the initial default node name.
    NODE_NAME = 'checkbox'

    def __init__(self):
        super(CheckboxNode, self).__init__()

        # create the checkboxes.
        self.add_checkbox('cb_1', '', 'Checkbox 1', True)
        self.add_checkbox('cb_2', '', 'Checkbox 2', False)

        # create input and output port.
        self.add_input('in', color=(200, 100, 0))
        self.add_output('out', color=(0, 100, 200))

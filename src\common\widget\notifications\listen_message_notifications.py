from PySide6.QtCore import Signal, QObject

from src.api.api_client import TypeRequestVMS, LogErrorMessage

from src.styles.style import Style


class ListenShowNotification(QObject):
    listen_API_success_message_signal = Signal(tuple)
    listen_API_fail_message_signal = Signal(tuple)
    listen_local_message = Signal(object)
    __instance = None

    def __init__(self):
        super().__init__()
        self.main_parent = None
        self.listen_API_success_message_signal.connect(self.success_message_API_notification)
        self.listen_API_fail_message_signal.connect(self.failed_message_API_notification)
        self.listen_local_message.connect(self.local_message_notification)

    @staticmethod
    def get_instance():
        if ListenShowNotification.__instance is None:
            ListenShowNotification.__instance = ListenShowNotification()
        return ListenShowNotification.__instance

    def register_parent(self, widget_parent):
        self.main_parent = widget_parent

    def show_local_message(self, message=None, is_failed=False):
        if message is None:
            return
        from .notify import Notifications
        icon = Style.PrimaryImage.fail_result if is_failed else Style.PrimaryImage.sucess_result
        Notifications(parent=self.main_parent, title=message, icon=icon)

    def success_message_API_notification(self, data):
        from .notify import Notifications
        response, type_request, request_method = data
        
        # Constants for request methods - keep original constants
        add_express_status = "AddExpressStatus"
        add_address_range_status = "AddAddressRangeStatus"
        import_file_status = "ImportFileStatus"
 
        # Success message mappings by request type
        SUCCESS_MESSAGES = {
            TypeRequestVMS.CREATE_CAMERA: self.tr('Add Camera Successfully Using RTSP'),
            TypeRequestVMS.UPDATE_CAMERA: self.tr('Update Camera to Server Successfully'),
            TypeRequestVMS.DELETE_CAMERA: self.tr('Successfully deleted Camera on Server'),
            TypeRequestVMS.CREATE_CAMERA_GROUP: self.tr('Added Camera Group to Server Successfully'),
            TypeRequestVMS.UPDATE_CAMERA_GROUP: self.tr('Update Camera Group to Server Successfully'),
            TypeRequestVMS.DELETE_CAMERA_GROUP: self.tr('Successfully deleted Camera Group on Server'),
            TypeRequestVMS.CREATE_AIFLOWS: self.tr('Added AIFlow to Server Successfully'),
            TypeRequestVMS.UPDATE_AIFLOWS: self.tr('Update AIFlow to Server Successfully'),
            TypeRequestVMS.WARNING_AIFLOWS: self.tr('There is no AI problem'),
            TypeRequestVMS.DELETE_AIFLOWS: self.tr('Successfully deleted AIFlow on Server'),
            TypeRequestVMS.CREATE_SHORTCUTID: self.tr('Added ShortcutID to Server Successfully'),
            TypeRequestVMS.UPDATE_SHORTCUTID: self.tr('Update ShortcutID to Server Successfully'),
            TypeRequestVMS.DELETE_SHORTCUTID: self.tr('Successfully deleted ShortcutID on Server'),
            TypeRequestVMS.UPDATE_WARNING_ALERT_SETTING: self.tr('Update Successfully'),
            TypeRequestVMS.APPLY_AIFLOW: self.tr('Successfully completed apply AI')
        }

        if type_request == TypeRequestVMS.CREATE_CAMERAS:
            # Handle CREATE_CAMERAS with original logic for request_method
            if request_method == import_file_status:
                message = self.tr('Successfully Added Camera List')
            elif request_method == add_express_status:
                message = self.tr('Successfully Added Camera List')
            elif request_method == add_address_range_status:
                message = self.tr('Successfully Added Camera List')
            else:
                message = self.tr('Add Camera Successfully Using Onvif')
            icon = Style.PrimaryImage.sucess_result
        elif type_request in SUCCESS_MESSAGES:
            # Regular success cases
            message = SUCCESS_MESSAGES[type_request]
            icon = Style.PrimaryImage.sucess_result
        elif response is not None:
            # Handle error cases with response
            try:
                type_error = response.json()['data'][0]
                message = self._get_error_message(type_error)
                icon = Style.PrimaryImage.fail_result
            except (AttributeError, KeyError, IndexError):
                message = self.tr("An error occurred")
                icon = Style.PrimaryImage.fail_result
        else:
            # No response received
            message = self.tr("No response received")
            icon = Style.PrimaryImage.fail_result
            
        # Show notification
        Notifications(parent=self.main_parent, title=message, icon=icon)

    def failed_message_API_notification(self, data):
        from .notify import Notifications
        response, type_request, error_data = data
        
        if error_data is not None:
            # Use error_data directly if provided
            message = self._get_error_message(error_data)
        else:
            # Extract error from response
            try:
                error_message = LogErrorMessage(response=response)
                if hasattr(response, 'json'):
                    response_json = response.json()
                    if 'exception' in response_json:
                        # Extract error from exception field
                        exception_message = response_json['exception']
                        message = self.tr(exception_message)
                    elif 'data' in response_json and response_json['data'] is not None:
                        # Fall back to data field if available and not None
                        type_error = response_json['data'][0]
                        message = self._get_error_message(type_error)
                    else:
                        # Use message field if available
                        message = response_json.get('message', self.tr("An error occurred"))
                else:
                    message = self.tr("An error occurred")
            except (AttributeError, KeyError, IndexError, TypeError):
                message = self.tr("An error occurred")
                
        Notifications(parent=self.main_parent, title=message, icon=Style.PrimaryImage.fail_result)

    def _get_error_message(self, error_code):
        """Return translated error message for error code"""
        ERROR_MESSAGES = {
            'UNKNOWN': self.tr("UNKNOWN"),
            'INTERNAL_SERVER_ERROR': self.tr('An internal server error occurred.'),
            'CAMERA_NOT_FOUND': self.tr('Camera not found.'),
            'CAMERA_INVALID': self.tr('The camera is invalid.'),
            'CAMERA_NAME_NULL': self.tr('The camera name cannot be null.'),
            'CAMERA_NAME_EXISTS': self.tr('This camera name already exists.'),
            'CAMERA_URL_NULL': self.tr('The camera URL cannot be null.'),
            'CAMERA_URL_EXISTS': self.tr('This camera URL already exists.'),
            'CAMERA_URL_MAINSTREAM_INVALID': self.tr('The camera mainstream URL is invalid.'),
            'CAMERA_URL_SUBSTREAM_INVALID': self.tr('The camera substream URL is invalid.'),
            'CAMERA_SUPPORTED_MAIN_RESOLUTION_INVALID': self.tr('The camera\'s supported main resolution is invalid.'),
            'CAMERA_SUPPORTED_MAIN_FPS_INVALID': self.tr('The camera\'s supported main FPS is invalid.'),
            'CAMERA_SUPPORTED_SUB_RESOLUTION_INVALID': self.tr('The camera\'s supported sub resolution is invalid.'),
            'CAMERA_SUPPORTED_SUB_FPS_INVALID': self.tr('The camera\'s supported sub FPS is invalid.'),
            'CAMERA_STATUS_NULL': self.tr('The camera status cannot be null.'),
            'CAMERA_STATE_NULL': self.tr('The camera state cannot be null.'),
            'CAMERA_GROUP_NOT_FOUND': self.tr('Camera group not found.'),
            'CAMERA_GROUP_INVALID': self.tr('The camera group is invalid.'),
            'CAMERA_GROUP_INVALID_PARENT_CHILD': self.tr('Invalid parent-child relationship in camera group.'),
            'CAMERA_GROUP_NAME_NULL': self.tr('The camera group name cannot be null.'),
            'CAMERA_GROUP_NAME_EXISTS': self.tr('This camera group name already exists.'),
            'CAMERA_GROUP_PARENT_ID_NOT_EXISTS': self.tr('The camera group parent ID does not exist.'),
            'CAMERA_GROUP_CHILD_ID_NOT_EXISTS': self.tr('The camera group child ID does not exist.'),
            'EVENT_NOT_FOUND': self.tr('Event not found.'),
            'EVENT_INVALID': self.tr('The event is invalid.'),
            'EVENT_PROFILE_ID_NULL': self.tr('The event profile ID cannot be null.'),
            'EVENT_CREATE_AT_NULL': self.tr('The event creation time cannot be null.'),
            'EVENT_IMAGE_URL_NULL': self.tr('The event image URL cannot be null.'),
            'EVENT_VIDEO_URL_NULL': self.tr('The event video URL cannot be null.'),
            'META_DATA_NULL': self.tr('Metadata cannot be null.'),
            'META_DATA_INVALID': self.tr('The metadata is invalid.'),
            'PROFILE_NOT_FOUND': self.tr('Profile not found.'),
            'PROFILE_NAME_NULL': self.tr('The profile name cannot be null.'),
            'PROFILE_INVALID': self.tr('The profile is invalid.'),
            'PROFILE_NAME_EXISTS': self.tr('This profile name already exists.'),
            'PROFILE_UUID_EXISTS': self.tr('This profile UUID already exists.'),
            'AI_FLOW_NOT_FOUND': self.tr('AI flow not found, please press button to create AI and set zone for this AI'),
            'AI_FLOW_NAME_NULL': self.tr('The AI flow name cannot be null.'),
            'AI_FLOW_TYPE_NULL': self.tr('The AI flow type cannot be null.'),
            'AI_FLOW_APPLY_NULL': self.tr('The AI flow apply field cannot be null.'),
            'AI_FLOW_INVALID': self.tr('The AI flow is invalid.'),
            'FORMAT_DATE_INVALID': self.tr('The date format is invalid.'),
            'USER_USERNAME_NULL': self.tr('The username cannot be null.'),
            'USER_USERNAME_EXISTS': self.tr('This username already exists.'),
            'USER_PASSWORD_NULL': self.tr('The password cannot be null.'),
            'USER_INVALID': self.tr('The user is invalid.'),
            'INVALID_TOKEN': self.tr('The token is invalid.'),
            'USER_NOT_FOUND': self.tr('User not found.'),
            'INVALID_PASSWORD': self.tr('The password is invalid.'),
            'API_KEY_NOT_FOUND': self.tr('API key not found.'),
            'CAMERA_ID_NULL': self.tr('The camera ID cannot be null.'),
            'CAMERA_GROUP_ID_NULL': self.tr('The camera group ID cannot be null.'),
            'AI_FLOW_NOT_APPLIED_WITHOUT_ZONE': self.tr('AI flow is not applied, please set zone for this AI flow'),
            'SERVER_DISCONNECTED': self.tr('Unable to connect to server.')
        }
        if error_code is None:
            error_code = self.tr("An error occurred")
        return ERROR_MESSAGES.get(error_code, error_code)

    def local_message_notification(self, data):
        from .notify import Notifications
        title, icon = data
        Notifications(parent=self.main_parent, title=title, icon=icon)


listen_show_notification = ListenShowNotification.get_instance()
import logging
from PySide6.QtWidgets import Q<PERSON>essageBox, QA<PERSON>lication, QVBoxLayout, QHBoxLayout, QLabel, QWidget, QSizePolicy
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve, QRect, QPoint, QSize
from PySide6.QtGui import QPixmap
from src.presentation.device_management_screen.widget.list_custom_widgets import CustomIcon

logger = logging.getLogger(__name__)


class Notifications(QWidget):
    def __init__(self, title=None, icon=Style.PrimaryImage.sucess_result, time=2000, parent=None, controller=None):
        super(Notifications, self).__init__(parent)
        self.parent = parent
        self.controller = controller
        self.is_show = True
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        
        # Set size policy to allow expansion
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.setMinimumWidth(200)
        self.setMaximumWidth(400)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        self.widget = QWidget()
        self.widget.setObjectName('widget_main')
        self.widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.widget.setStyleSheet(f"""
            QWidget#widget_main {{
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_2")};
                border-radius: 8px;
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
            }}
        """)

        layout = QVBoxLayout(self.widget)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(0)

        # Content row
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(8)
        
        # Icon container to ensure proper spacing
        icon_container = QWidget()
        icon_container.setFixedSize(24, 24)
        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(0, 0, 0, 0)
        icon_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.icon = QLabel()
        self.icon.setPixmap(QPixmap(icon).scaled(20, 20, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation))
        icon_layout.addWidget(self.icon)
        
        self.label = QLabel(title)
        self.label.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        self.label.setStyleSheet(f'''
            font-size: 13px;
            color: {main_controller.get_theme_attribute("Color", "label_title_1")};
            padding: 0px;
            margin: 0px;
        ''')
        self.label.setWordWrap(False)
        self.label.setMinimumWidth(150)
        
        content_layout.addWidget(icon_container)
        content_layout.addWidget(self.label)
        content_layout.addStretch()

        layout.addLayout(content_layout)

        main_layout.addWidget(self.widget)
        
        # Timer setup
        timer = QTimer(self)
        timer.setInterval(time)
        timer.setSingleShot(True)
        timer.timeout.connect(self.start_animation)
        timer.start()
        
        # Animation setup
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(300)
        self.animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # Adjust size based on content
        self.adjustSize()
        self.start_animation()

    def start_animation(self):
        if self.is_show:
            self.is_show = False
            self.show()
            
            # Calculate center position
            center_x = (self.parent.width() - self.width()) // 2
            start_y = -self.height()
            end_y = 20  # Distance from top
            
            self.animation.setStartValue(QRect(center_x, start_y, self.width(), self.height()))
            self.animation.setEndValue(QRect(center_x, end_y, self.width(), self.height()))
            self.animation.start()
        else:
            self.is_show = True
            center_x = (self.parent.width() - self.width()) // 2
            current_y = self.y()
            
            self.animation.setStartValue(QRect(center_x, current_y, self.width(), self.height()))
            self.animation.setEndValue(QRect(center_x, -self.height(), self.width(), self.height()))
            self.animation.finished.connect(self.close)
            self.animation.start()

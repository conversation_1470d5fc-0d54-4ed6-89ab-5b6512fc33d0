import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
from PySide6.QtWidgets import QWidget, QHBoxLayout, QListView,QLineEdit,QStyledItemDelegate, QPushButton
from PySide6.QtGui import QStandardItemModel, QStandardItem, QPainter
from PySide6.QtCore import Qt, QModelIndex
from PySide6.QtWidgets import QStyleOptionViewItem, QStyle
from typing import List
class PresetListView(QListView):
    def __init__(self, parent=None,list_icon: List[dict] = []):
        super(PresetListView, self).__init__(parent)
        self.setContentsMargins(50,0,50,0)
        self.list_icon = list_icon
        self.setup_ui()

    def setup_ui(self):
        self.setItemDelegate(EventItemDelegate())
        self.list_view_model = QStandardItemModel()
        self.setModel(self.list_view_model)
        self.setSpacing(0)
        
    def remove_preset(self):
        self.list_view_model.clear()
        self.reset()

    def update_preset_below(self,name,index):
        item = PresetItem(name = name,index = index,list_icon=self.list_icon)
        self.list_view_model.appendRow(item)
        self.setIndexWidget(self.list_view_model.index(self.list_view_model.rowCount() - 1, 0), item.main_widget)

class EventItemDelegate(QStyledItemDelegate):
    def paint(self, painter: QPainter, option: QStyleOptionViewItem, index: QModelIndex) -> None:
        # Set the background color for the item
        painter.save()
        if option.state & QStyle.State_Selected:
            painter.fillRect(option.rect, option.palette.highlight())
        painter.restore()
        # Call the base class paint method
        super().paint(painter, option, index)
class PresetItem(QStandardItem):
    def __init__(self, parent=None,name = None,index = None,list_icon: List[dict] = []):
        super(PresetItem, self).__init__(parent)  
        self.name = name
        self.preset_index = index
        self.list_icon = list_icon
        self.callback_delete = None
        self.setup_ui() 

    def setup_ui(self):
        self.main_widget = QWidget()  # Create a main widget for the item
        self.main_layout = QHBoxLayout(self.main_widget)
        self.label_name = QLineEdit(self.name)
        self.label_name.setFixedWidth(110)
        self.main_layout.addWidget(self.label_name,50)
        count = len(self.list_icon)
        list_direction = []
        for index,icon in enumerate(self.list_icon):
            list_direction.append(icon['direction'])
            button = QPushButton()
            #button.setStyleSheet(Style.StyleSheet.button_style10)
            button.setStyleSheet(f'''
                                QPushButton {{border: None; image: url({icon['icon'][0]});}}
                                QPushButton:hover {{image: url({icon['icon'][1]});}}
                                ''')
            self.main_layout.addWidget(button,int(50/count))
            button.hide()
            #button.setIcon(icon['icon'])
            if icon['direction'] == 'call_preset':
                button.setFixedWidth(30)
                callback_call = icon['callback']
                button.clicked.connect(lambda: callback_call('call_preset',self.label_name.text(),self.preset_index))
            elif icon['direction'] == 'setting_preset':
                button.setFixedWidth(30)
                callback_setting = icon['callback']
                button.clicked.connect(lambda: callback_setting('setting_preset',self.label_name.text(),self.preset_index))
            elif icon['direction'] == 'delete_preset':
                button.setFixedWidth(30)
                self.callback_delete = icon['callback']
                button.clicked.connect(self.delete_preset_clicked)
            elif icon['direction'] == 'play_patrol':
                button.setFixedWidth(20)
                callback_play_patrol = icon['callback']
                button.clicked.connect(lambda: callback_play_patrol('play_patrol',self.label_name.text(),self.preset_index))
            elif icon['direction'] == 'stop_patrol':
                button.setFixedWidth(20)
                callback_stop_patrol = icon['callback']
                button.clicked.connect(lambda: callback_stop_patrol('stop_patrol',self.label_name.text(),self.preset_index))
            elif icon['direction'] == 'setting_patrol':
                button.setFixedWidth(20)
                callback_setting_patrol= icon['callback']
                button.clicked.connect(lambda: callback_setting_patrol('setting_patrol',self.label_name.text(),self.preset_index))
            elif icon['direction'] == 'delete_patrol':
                button.setFixedWidth(20)
                callback_delete_patrol= icon['callback']
                button.clicked.connect(lambda: callback_delete_patrol('delete_patrol',self.label_name.text(),self.preset_index))
            elif icon['direction'] == 'add_preset':
                button.setFixedWidth(20)
                callback_add_preset = icon['callback']
                button.clicked.connect(lambda: callback_add_preset('add_preset',self.label_name.text(),self.preset_index))
                button.show()
            elif icon['direction'] == 'delete_presets':
                button.setFixedWidth(20)
                callback_delete_preset = icon['callback']
                button.clicked.connect(lambda: callback_delete_preset('delete_presets',self.label_name.text(),self.preset_index))
                button.show()
            elif icon['direction'] == 'down_preset':
                button.setFixedWidth(20)
                callback_down_preset= icon['callback']
                button.clicked.connect(lambda: callback_down_preset('up_preset',self.label_name.text(),self.preset_index))
                button.show()
            elif icon['direction'] == 'up_preset':
                button.setFixedWidth(20)
                callback_up_preset= icon['callback']
                button.clicked.connect(lambda: callback_up_preset('down_preset',self.label_name.text(),self.preset_index))
                button.show()

            
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.setSizeHint(self.main_widget.sizeHint())

    def show_control(self):
        for i in range(self.main_layout.count()):
            item = self.main_layout.itemAt(i)
            button = item.widget()
            if item.widget() and isinstance(item.widget(),QPushButton):
                button.show()

    def hide_control(self):
        for i in range(self.main_layout.count()):
            item = self.main_layout.itemAt(i)
            button = item.widget()
            if item.widget() and isinstance(item.widget(),QPushButton):
                button.hide()
    def delete_preset_clicked(self):
        logger.debug(f'delete_preset_clicked')
        if self.callback_delete is not None:
            name = self.callback_delete('delete_preset',self.label_name.text(), index = self.preset_index)
            self.label_name.setText(str(name))
            

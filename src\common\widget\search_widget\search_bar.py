from PySide6.QtWidgets import QWidget, QLineEdit, QVBoxLayout, QHBoxLayout,QLabel
from PySide6.QtCore import Qt, Signal, QCoreApplication
from PySide6.QtGui import QIcon, QPixmap, Q<PERSON>eyEvent
from src.styles.style import Style
from src.common.controller.main_controller import main_controller

class SearchBar(QWidget):
    search_items_signal = Signal(str)

    def __init__(self, parent =None,title = None):
        super().__init__(parent)
        self.camera_screen = parent
        
        layout = QVBoxLayout()
        layout.setContentsMargins(4, 0, 4, 0)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.title = title
        self.label_title = None
        if title is not None:
            self.label_title = QLabel(title)
            layout.addWidget(self.label_title)
        self.search_bar = QLineEdit()
        # custom clear button
        self.search_bar.setPlaceholderText(self.tr("Search"))
        self.search_bar.setFixedHeight(32)
        self.search_bar.setFocusPolicy(Qt.FocusPolicy.ClickFocus)
        icon = QIcon(main_controller.get_theme_attribute('Image', 'search'))
        self.search_bar.addAction(icon, QLineEdit.LeadingPosition)
        self.search_bar.setClearButtonEnabled(True)
        self.search_bar.textChanged.connect(self.search_items)

        layout.addWidget(self.search_bar)
        self.setLayout(layout)
        self.set_dynamic_stylesheet()

    def search_items(self, text):
        self.search_items_signal.emit(text)

    def set_stylesheet_for_search_bar(self, style):
        self.search_bar.setStyleSheet(style)
    
    def keyPressEvent(self, event: QKeyEvent) -> None:
        if event.key() == Qt.Key_Escape:
            self.search_bar.clearFocus()

    def retranslateUi_searchbar(self):
        self.search_bar.setPlaceholderText(QCoreApplication.translate("SearchBar", u"Search", None))

    def set_dynamic_stylesheet(self):
        self.search_bar.actions()[0].setIcon(QIcon(main_controller.get_theme_attribute('Image', 'search')))
        self.search_bar.setStyleSheet(
        f"""
            QLineEdit {{
                background-color: transparent;
                color: {main_controller.get_theme_attribute('Color', 'label_title_1')};
                border-radius: 4px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "main_border")};
                padding-left: 10px;
                padding-right: 10px;
            }}
            QLineEdit:focus {{
                border: 1px solid {main_controller.get_theme_attribute("Color", "add_server_lineedit_focused")};
                color: {main_controller.get_theme_attribute('Color', 'label_title_1')}
            }}
            QLineEdit::clear-button {{
                image: url({main_controller.get_theme_attribute("Image", "close_tab")});
            }}
            """)
        if self.label_title is not None:
            self.label_title.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'label_title_1')};")


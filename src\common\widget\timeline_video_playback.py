#!/usr/bin/python3
# -*- coding: utf-8 -*-
from src.common.widget.circule_button import CircularButton
import src.utils.log_utils as LogUtils
import logging

from src.styles.style import Style
logger = logging.getLogger(__name__)
import datetime
import tempfile
from base64 import b64encode

from PySide6 import QtWidgets, QtGui, QtCore
from PySide6.QtCore import Qt, QPoint, QLine, QRect, QRectF, Signal, QDateTime, QTimer
from PySide6.QtGui import QPainter, QColor, QFont, QBrush, QPalette, QPen, QPolygon, QPainterPath, QPixmap
from PySide6.QtWidgets import QWidget, QFrame, QScrollArea, QVBoxLayout, QPushButton
import sys
import os
from PySide6.QtWidgets import QApplication

from numpy import load

__textColor__ = QColor(Style.PrimaryColor.text_unselected)
__backgroudColor__ = QColor(60, 63, 65)
__font__ = QFont('Decorative', 12)


# create enum for timeline mode: 1 minute, 5 minute, 10 minute, 30 minute 1 hour
class TimelineMode:
    ONE_MINUTE = 60  # second
    FIVE_MINUTE = 300
    TEN_MINUTE = 600
    THIRTY_MINUTE = 1800
    ONE_HOUR = 3600
    TWO_HOUR = 7200


# class VideoSample:

#     def __init__(self, start_time, duration = 86400, color=Qt.darkYellow, picture=None, audio=None):
#         self.duration = duration
#         self.color = color  # Floating color
#         self.defColor = color  # DefaultColor
#         if picture is not None:
#             self.picture = picture.scaledToHeight(45)
#         else:
#             self.picture = None
#         self.startPos = 0  # Inicial position
#         self.endPos = self.duration  # End position


class QTimeLine(QWidget):
    DEBUG = False
    positionChanged = Signal(int)
    on_clicked_progress = Signal(QDateTime)
    # selectionChanged = Signal(VideoSample)
    current_timeline_mode = TimelineMode.FIVE_MINUTE
    previous_timeline_mode = TimelineMode.FIVE_MINUTE

    def __init__(self,  total_time=86400):
        super(QTimeLine, self).__init__()
        self.total_time = total_time
        self.current_time = datetime.datetime(1, 1, 1, 0, 0, 0)
        self.start_time = datetime.datetime(1, 1, 1, 0, 0, 0)
        self.end_time = datetime.datetime(1, 1, 1, 0, 0, 0)
        self.start_time_scroll = datetime.datetime(1, 1, 1, 0, 0, 0)
        self.end_time_scroll = datetime.datetime(1, 1, 1, 0, 0, 0)

        self.end_time = self.current_time + \
            datetime.timedelta(seconds=self.total_time)
        # logger.debug('self.start_time_max: ', self.start_time_max)
        # logger.debug('self.height(): ', self.height())

        # Set variables
        self.backgroundColor = __backgroudColor__
        self.textColor = __textColor__
        # self.font = __font__
        self.pos = None
        self.pointerPos = None
        self.pointerTimePos = None
        self.selectedSample = None
        self.clicking_wheel = False  # Check if mouse left button is being pressed
        self.clicking_progress = False  # Check if mouse left button is being pressed
        self.is_in = False  # check if user is in the widget
        # self.videoSamples = []  # List of videos samples
        self.progress_video = 50
        # create circle button + and -
        # + button is increase detail time example between each line 1s to 0.1s
        # - button is decrease detail time example between each line 0.1s to 1s
        self.plusButton = CircularButton("+", parent=self, width=25, height=25, border_color=QColor(Style.PrimaryColor.text_unselected), background_color=QColor(Style.PrimaryColor.text_unselected), text_color=QColor(Style.PrimaryColor.background))
        self.plusButton.setObjectName("plusButton")
        self.minusButton = CircularButton("-", parent=self, width=25, height=25, border_color=QColor(Style.PrimaryColor.text_unselected), background_color=QColor(Style.PrimaryColor.text_unselected), text_color=QColor(Style.PrimaryColor.background))
        self.minusButton.setObjectName("minusButton")

        # connect button signal to respective slots
        self.plusButton.clicked.connect(self.increaseDetailTime)
        self.minusButton.clicked.connect(self.decreaseDetailTime)

        self.setMouseTracking(True)  # Mouse events

        # Set the background color for the widget
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(Style.PrimaryColor.on_background))  # Red background color
        self.setPalette(palette)
        self.setAutoFillBackground(True)

    def paintEvent(self, event):

        self.height_of_wheel = self.height() * 0.5
        self.size_button_change_time_duration = 25
        self.height_of_progress = self.height() * 0.55
        self.time_margin = 50
        # logger.debug('w - h:', self.width(), '-', self.height())
        

        qp = QPainter()
        qp.begin(self)
        qp.setPen(self.textColor)
        # qp.setFont(self.font)
        qp.setRenderHint(QPainter.Antialiasing)
        w = 0

        # Draw time
        # Left and right margin for the time text
        number_line = self.calculateNumberDashLine()
        # if self.DEBUG:
        # logger.debug('number_line: ', number_line)

        # draw time only number_line text
        for i in range(0, number_line):
            time = self.start_time_scroll + \
                datetime.timedelta(seconds=self.current_timeline_mode * i)
            # set font size
            font = QFont()
            font.setPixelSize(Style.Size.caption)
            qp.setFont(font)
            qp.drawText(self.time_margin / 2 + w,
                        self.height_of_wheel-20, time.strftime("%H:%M:%S"))
            w += (self.width() - 2 * self.time_margin) / (number_line-1)
            # check if i is last line
            if i == number_line-1:
                self.end_time_scroll = time
            if i == 0:
                self.start_time_scroll = time

        # Draw down line
        # qp.setPen(QPen(Qt.darkCyan, 5, Qt.SolidLine))

        qp.drawLine(self.time_margin, self.height_of_wheel,
                    self.width() - self.time_margin, self.height_of_wheel)

        # Draw dash lines
        # draw dash line only number_line line
        point = 0
        for i in range(0, number_line):
            # logger.debug('line pos: ', (self.time_margin + point))
            qp.drawLine(self.time_margin + point,
                        self.height_of_wheel - 10, self.time_margin + point, self.height_of_wheel)
            point += (self.width() - 2 * self.time_margin) / (number_line-1)

        if self.pos is not None and self.is_in:
            qp.drawLine(self.pos.x(), 0, self.pos.x(), 40)

        # if self.pointerPos is not None:
        #     line = QLine(QPoint(self.pointerTimePos/self.getScale(), 40),
        #                  QPoint(self.pointerTimePos/self.getScale(), self.height()))
        #     poly = QPolygon([QPoint(self.pointerTimePos/self.getScale() - 10, 20),
        #                      QPoint(self.pointerTimePos/self.getScale() + 10, 20),
        #                      QPoint(self.pointerTimePos/self.getScale(), 40)])
        # else:
        #     line = QLine(QPoint(0, 0), QPoint(0, self.height()))
        #     poly = QPolygon([QPoint(-10, 20), QPoint(10, 20), QPoint(0, 40)])

        # Draw progress bar

        qp.setPen(Qt.NoPen)
        qp.setBrush(QColor(Style.PrimaryColor.primary))  # Change the color as needed
        x1 = self.time_margin
        y1 = self.height_of_wheel - 2 # 2 is margin
        w = (self.width() - 2 * self.time_margin) * \
            self.progress_video / 100
        # logger.debug('when draw: self.progress_video: ',self.progress_video)

        # logger.debug('progress w position: ', w)
        h = 4
        qp.drawRoundedRect(x1, y1, w, h, 2, 2)

        # set button position to left top
        self.plusButton.setGeometry(self.width()/2 + self.size_button_change_time_duration * 1.2, self.height_of_progress + h*2, self.size_button_change_time_duration, self.size_button_change_time_duration)
        self.minusButton.setGeometry(self.width()/2 - self.size_button_change_time_duration * 1.2, self.height_of_progress + h*2, self.size_button_change_time_duration, self.size_button_change_time_duration)

        # Clear clip path
        path = QPainterPath()
        path.addRect(self.rect().x(), self.rect().y(),
                     self.rect().width(), self.rect().height())
        qp.setClipPath(path)

        # Draw pointer
        qp.setPen(Qt.darkCyan)
        qp.setBrush(QBrush(Qt.darkCyan))

        qp.end()
        pass

    # Mouse movement
    def mouseMoveEvent(self, e):
        self.pos = e.pos()

        # if mouse is being pressed, update pointer
        if self.clicking_wheel:
            x = self.pos.x()
            y = self.pos.y()
            # logger.debug('Dragging')
            if y < self.height_of_wheel:
                # move timebar to left
                if x < self.pointerPos:
                    self.moveTimeBarRight()
                    if self.DEBUG:
                        logger.debug("drag right to left")

                # move timebar to right
                elif x > self.pointerPos:
                    self.moveTimeBarLeft()
                    if self.DEBUG:
                        logger.debug("drag left to right")
            self.pointerPos = x
            self.update()
        elif self.clicking_progress:
            x = self.pos.x()
            y = self.pos.y()
            logger.debug('click progress')

            self.positionChanged.emit(x)
            self.checkSelection(x)
            pointerTimePos = self.pointerPos*self.getScale()
            progress_video = (self.pointerPos - self.time_margin) / (self.width() - 2 * self.time_margin)  * 100

            if progress_video < 0 or progress_video > 100:
                return
            self.pointerTimePos = pointerTimePos
            self.progress_video = progress_video
            logger.debug(f'when move: self.progress_video: {self.progress_video}')
            self.pointerPos = x
            self.update()

    # Mouse pressed
    def mousePressEvent(self, e):
        if e.button() == Qt.LeftButton:
            x = e.pos().x()
            y = e.pos().y()

            if y > self.height_of_wheel:
                self.pointerPos = x
                self.positionChanged.emit(x)
                self.pointerTimePos = self.pointerPos * self.getScale()
                self.progress_video = (self.pointerPos - self.time_margin) / (self.width() - 2 * self.time_margin)  * 100
                self.update()
                logger.debug('click progress')
                self.clicking_progress = True  # Set clicking check to true
                # get current_time
                self.current_time = (self.progress_video/100) * (self.end_time_scroll - self.start_time_scroll) + self.start_time_scroll
                logger.debug(f'self.current_time: {self.current_time}')
            else:
                self.pointerPos = x
                self.positionChanged.emit(x)
                self.pointerTimePos = self.pointerPos * self.getScale()
                self.update()
                self.clicking_wheel = True  # Set clicking check to true

    # Mouse release
    def mouseReleaseEvent(self, e):
        if e.button() == Qt.LeftButton:
            self.clicking_wheel = False  # Set clicking check to false
            # wait 500ms then set clicking check to false in a function
            if self.clicking_progress:
                if self.DEBUG:
                    logger.debug(f'self.current_time: {self.current_time}')
                self.on_clicked_progress.emit(self.current_time)
                self.clicking_progress = False

            # def set_clicking_wheel_false():
            #     self.clicking_progress = False
            # QTimer.singleShot(500, set_clicking_wheel_false)
            

    # Enter
    def enterEvent(self, e):
        self.is_in = True

    # Leave
    def leaveEvent(self, e):
        self.is_in = False
        self.update()

    # check selection
    def checkSelection(self, x):
        # Check if user clicked in video sample
        # for sample in self.videoSamples:
        #     if sample.startPos < x < sample.endPos:
        #         sample.color = Qt.darkCyan
        #         if self.selectedSample is not sample:
        #             self.selectedSample = sample
        #             self.selectionChanged.emit(sample)
        #     else:
        #         sample.color = sample.defColor
        pass

    # Get time string from seconds
    def get_time_string(self, seconds):
        m, s = divmod(seconds, 60)
        h, m = divmod(m, 60)
        return "%02d:%02d:%02d" % (h, m, s)

    def getScale(self):
        return float(self.total_time)/float(self.width())

    # Get duration
    def getDuration(self):
        return self.total_time

    # Get selected sample
    def getSelectedSample(self):
        return self.selectedSample

    # Set background color
    def setBackgroundColor(self, color):
        self.backgroundColor = color

    # Set text color
    def setTextColor(self, color):
        self.textColor = color

    # Set Font
    def setTextFont(self, font):
        self.font = font

    # Slot for moving time bar duration to the left
    def moveTimeBarLeft(self):
        # decrease follow timeline mode
        if self.start_time_scroll > self.start_time:
            self.start_time_scroll = self.start_time_scroll - \
                datetime.timedelta(seconds=self.current_timeline_mode)
            if self.DEBUG:
                logger.debug(f'moveTimeBarLeft: self.start_time = {self.start_time}')


    # Slot for moving time bar duration to the right
    def moveTimeBarRight(self):
        # increase 1 minute
        self.start_time_scroll = self.start_time_scroll + \
            datetime.timedelta(seconds=self.current_timeline_mode)
        if self.DEBUG:
            logger.debug(f'moveTimeBarRight: self.start_time = {self.start_time}')

        if self.start_time_scroll > self.end_time:
            self.start_time_scroll = self.end_time

    # Set duration
    def setDuration(self, duration):
        pass

    # change second_each_pointer, min 1 minute, 5 minute, 10 minute, 30 minute, 1 hour, max 2 hours

    def increaseDetailTime(self):
        current_timeline_mode = self.previous_timeline_mode
        # logger.debug('before increaseDetailTime: self.timeline_mode = ',
        #       self.current_timeline_mode)
        if current_timeline_mode == TimelineMode.ONE_MINUTE:
            current_timeline_mode = TimelineMode.FIVE_MINUTE
        elif current_timeline_mode == TimelineMode.FIVE_MINUTE:
            current_timeline_mode = TimelineMode.TEN_MINUTE
        elif current_timeline_mode == TimelineMode.TEN_MINUTE:
            current_timeline_mode = TimelineMode.THIRTY_MINUTE
        elif current_timeline_mode == TimelineMode.THIRTY_MINUTE:
            current_timeline_mode = TimelineMode.ONE_HOUR
        elif current_timeline_mode == TimelineMode.ONE_HOUR:
            current_timeline_mode = TimelineMode.TWO_HOUR
        if current_timeline_mode > self.total_time:
            current_timeline_mode = self.previous_timeline_mode
        if current_timeline_mode == self.previous_timeline_mode:
            return
        # logger.debug('after increaseDetailTime: self.timeline_mode = ',
        #       self.current_timeline_mode)
        self.current_timeline_mode = current_timeline_mode
        self.update()
        self.previous_timeline_mode = self.current_timeline_mode

    def decreaseDetailTime(self):
        # logger.debug('before decreaseDetailTime: self.timeline_mode = ',
        #       self.current_timeline_mode)
        current_timeline_mode = self.previous_timeline_mode
        if current_timeline_mode == TimelineMode.TWO_HOUR:
            current_timeline_mode = TimelineMode.ONE_HOUR
        elif current_timeline_mode == TimelineMode.ONE_HOUR:
            current_timeline_mode = TimelineMode.THIRTY_MINUTE
        elif current_timeline_mode == TimelineMode.THIRTY_MINUTE:
            current_timeline_mode = TimelineMode.TEN_MINUTE
        elif current_timeline_mode == TimelineMode.TEN_MINUTE:
            current_timeline_mode = TimelineMode.FIVE_MINUTE
        elif current_timeline_mode == TimelineMode.FIVE_MINUTE:
            current_timeline_mode = TimelineMode.ONE_MINUTE
        if current_timeline_mode == self.previous_timeline_mode:
            return
        # logger.debug('after decreaseDetailTime: self.timeline_mode = ', self.current_timeline_mode)
        self.current_timeline_mode = current_timeline_mode
        self.update()
        self.previous_timeline_mode = self.current_timeline_mode

    def calculateNumberDashLine(self):
        number_dash_line = (
            int(float(self.total_time) / float(self.current_timeline_mode)))
        # logger.debug('self.current_timeline_mode = ', self.current_timeline_mode, ' - number_dash_line: ',number_dash_line)
        if number_dash_line > 12:
            return 12
        if number_dash_line == 1 or number_dash_line == 0:
            return 2
        return number_dash_line + 1

    def update_timeline(self, start_time: datetime.datetime, end_time: datetime.datetime):
        self.total_time: int = (end_time - start_time).total_seconds()
        # convert QDateTime to datetime
        self.start_time: datetime.datetime = start_time
        self.end_time: datetime.datetime = end_time
        self.current_time: datetime.datetime = self.start_time
        self.start_time_scroll: datetime.datetime = self.start_time
        self.end_time_scroll: datetime.datetime = self.end_time
        self.update()


    # Progressbar video matching with first video
    def onProgressChanged(self, duration: int, start_time: QDateTime):
        if self.clicking_progress:
            # logger.debug('onProgressChanged: self.clicking_progress = ', self.clicking_progress)
            return
        self.current_time = start_time.toPython() + \
            datetime.timedelta(milliseconds=duration)
        # if self.DEBUG:
        # logger.debug('onProgressChanged: self.current_time = ', self.current_time)

        # update self.progress_video 0.0 - 1.0, use start_time and current_time, end_time
        current_time = self.current_time.timestamp()

        start_time_scroll = self.start_time_scroll.timestamp()
        end_time_scroll = self.end_time_scroll.timestamp()

        # get progress_video from current_time and self.width()
        self.progress_video = (float(current_time - start_time_scroll) /
                               float(end_time_scroll - start_time_scroll)) * 100
        # logger.debug('onProgressChanged: self.progress_video = ', self.progress_video)
        if current_time < start_time_scroll:
            self.progress_video = 0.0
        if current_time > end_time_scroll:
            self.progress_video = 1.0
        if self.DEBUG:
            logger.debug(f'current_time - start_time_scroll: {current_time - start_time_scroll}')
            logger.debug(f'end_time_scroll - start_time_scroll: {end_time_scroll - start_time_scroll}')
            logger.debug(f'onProgressChanged: progress: {self.progress_video}')
        self.update()

import logging
logger = logging.getLogger(__name__)
from PySide6.QtCore import Qt, QUrl, QDateTime
from PySide6.QtGui import QIcon, QContextMenuEvent, QAction
from PySide6.QtMultimedia import QMediaPlayer, QAudioOutput
from PySide6.QtMultimediaWidgets import QVideoWidget
from PySide6.QtWidgets import QFileDialog, QHBoxLayout, QLabel, QPushButton, QSlider, QVBoxLayout, QWidget, QMenu
from src.styles.style import Style
from PySide6.QtCore import Signal
from src.common.model.camera_record_result_model import CameraRecord


class VideoPlayerWidget(QWidget):
    DEBUG = False
    # create a signal durationChanged: duration: int, start_time: QDateTime
    positionChanged = Signal(int, QDateTime)
    playbackStateChanged = Signal(int)

    def __init__(self, enable_browse_btn=False, play_url=None, enable_control_bar=False, enable_camera_name_label=False, label_name=''):
        super().__init__()
        self.is_click_progress = False
        self.camera_id = None
        self.current_url_index = 0
        self.list_camera_record = []
        
        self.audio_output = QAudioOutput()
        # config audio output volume = system volume
        self.audio_output.setVolume(1.0)

        # Initialize the media player and its components
        self.media_player = QMediaPlayer()
        if play_url is not None:
            logger.debug(f'play_url: {play_url}')

            self.media_player.setSource(QUrl(play_url))
        self.media_player.setAudioOutput(self.audio_output)

        self.play_button = QPushButton(icon=QIcon(Style.PrimaryImage.play_video))
        self.rewind_button_previous = QPushButton(
            icon=QIcon(Style.PrimaryImage.fast_backward))
        self.rewind_button_next = QPushButton(
            icon=QIcon(Style.PrimaryImage.fast_forward))
        self.slider = QSlider(Qt.Orientation.Horizontal)
        self.slider.setRange(0, 0)
        self.position_label = QLabel('--:--')
        self.duration_label = QLabel('--:--')
        self.sound_button = QPushButton(icon=QIcon(Style.PrimaryImage.sound_video))
        self.interval_button = QPushButton(icon=QIcon(Style.PrimaryImage.speed_video))
        self.fullscren = QPushButton(icon=QIcon(Style.PrimaryImage.fullscreen_video))

        # Connect media player signals to slots
        # listen playbackState
        self.media_player.playbackStateChanged.connect(
            self.playback_state_changed)
        self.media_player.durationChanged.connect(self.duration_changed)
        self.media_player.positionChanged.connect(self.position_changed)
        self.play_button.clicked.connect(self.play)
        self.slider.sliderMoved.connect(self.set_position)
        self.rewind_button_previous.clicked.connect(self.rewind_previous)
        self.rewind_button_next.clicked.connect(self.rewind_next)
        self.sound_button.clicked.connect(self.mute_sound)
        self.interval_button.clicked.connect(self.change_interval)
        self.fullscren.clicked.connect(self.fullscreen)

        # Set up the layout
        self.vlayout = QVBoxLayout()

        # create video widget
        self.video_widget = QVideoWidget()
        self.video_widget.hide()

        self.video_widget_place_holder = QWidget()
        self.video_widget_place_holder.setStyleSheet(f"background-color: {Style.PrimaryColor.on_background}; border: 1px solid {Style.PrimaryColor.on_background};")

        # horizontal layout
        control_bar_layout = QHBoxLayout()
        control_bar_layout.addWidget(self.rewind_button_previous)
        control_bar_layout.addWidget(self.play_button)
        control_bar_layout.addWidget(self.rewind_button_next)
        control_bar_layout.addWidget(self.position_label)
        control_bar_layout.addWidget(self.slider)
        control_bar_layout.addWidget(self.duration_label)
        control_bar_layout.addWidget(self.sound_button)
        control_bar_layout.addWidget(self.interval_button)
        control_bar_layout.addWidget(self.fullscren)

        # create label camera name
        label_name_layout = QHBoxLayout()
        self.camera_name_label = QLabel(label_name)
        self.camera_name_label.setObjectName("camera_name_label_video_player")
        self.camera_name_label.setStyleSheet(
            'QLabel#camera_name_label_video_player {background-color: #BEBEBE; border-top-left-radius: 4px; border-top-right-radius: 4px; border-left: 1px solid #DDDDDD; border-right: 1px solid #DDDDDD; border-top: 1px solid #DDDDDD;}')
        label_name_layout.addWidget(self.camera_name_label)

        # add video widget to layout
        if enable_camera_name_label:
            self.vlayout.addLayout(label_name_layout, 10)
        self.vlayout.addWidget(self.video_widget)
        self.vlayout.addWidget(self.video_widget_place_holder)
        if enable_control_bar:
            self.vlayout.addLayout(control_bar_layout, 10)

        self.vlayout.setSpacing(0)
        self.vlayout.setContentsMargins(1, 1, 1, 1)
        self.setLayout(self.vlayout)
        # self.setStyleSheet("border: 1px solid black")

        if enable_browse_btn:
            # create browse button
            self.browse_button = QPushButton('Browse')
            self.browse_button.clicked.connect(self.browse_file)
            # add browse button to layout
            self.vlayout.addWidget(self.browse_button)

        self.media_player.setVideoOutput(self.video_widget)
        self.media_player.play()

    def browse_file(self):
        # Open a file dialog to select a media file to play
        file_name, _ = QFileDialog.getOpenFileName(
            self, 'Open Media File', '', 'Video Files (*.flv *.mp4 *.mkv)')

        if file_name != '':
            # logger.debug('file_name - ', file_name)
            # Set the media content and start playing
            self.media_player.setSource(QUrl.fromLocalFile(file_name))
            # get size of video output
            # logger.debug('video size - ', self.media_player.videoOutput())

            self.media_player.setVideoOutput(self.video_widget)
            self.media_player.play()

    def duration_changed(self, duration):
        # Update the slider range and duration label when the media duration changes
        self.slider.setRange(0, duration)
        self.duration_label.setText(self.format_time(duration))

    def position_changed(self, position):
        # Update the slider position and position label when the media position changes
        self.slider.setValue(position)
        self.position_label.setText(self.format_time(position))
        # logger.debug('position_changed - ', position)
        format = Qt.DateFormat.ISODate
        # format_string = "yyyy-MM-dd HH:mm:ss.zzzzzz"
        if self.list_camera_record and self.current_url_index < len(self.list_camera_record):
            start_time = QDateTime.fromString(self.list_camera_record[self.current_url_index].startTime, format)
            self.positionChanged.emit(position, start_time)

    def set_position(self, position):
        if self.media_player:
            # Seek to the selected position when the slider is moved
            self.media_player.setPosition(position)

    def play(self):
        if self.media_player:
            # logger.debug('playbackState - ', self.media_player.playbackState)
            # Toggle the play/pause state of the media player
            if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                # pause video
                self.media_player.pause()
                # logger.debug('pause')
            else:
                if self.video_widget.isHidden():
                    self.video_widget.show()
                    self.video_widget_place_holder.hide()
                # play video
                try:
                    self.media_player.play()
                except Exception as e:
                    logger.debug('E play: ',e)
                # logger.debug('play')

    def stop(self):
        # Stop the media player
        self.media_player.stop()

    def stop_and_hide_video_widget(self):
        self.stop()
        self.video_widget.hide()
        self.video_widget_place_holder.show()

    def show_video_widget(self):
        self.video_widget.show()
        self.video_widget_place_holder.hide()

    def playback_state_changed(self):
        # Update the play/pause button icon when the media player state changes
        if self.media_player:
            if self.media_player.playbackState() == QMediaPlayer.PlaybackState.PlayingState:
                self.play_button.setIcon(QIcon(Style.PrimaryImage.stop_video))
            else:
                self.play_button.setIcon(QIcon(Style.PrimaryImage.play_video))

    def format_time(self, milliseconds):
        # Convert milliseconds to a formatted string (hh:mm:ss)
        seconds = int(milliseconds / 1000)
        minutes = int(seconds / 60)
        seconds -= minutes * 60
        hours = int(minutes / 60)
        minutes -= hours * 60

        if hours > 0:
            return '{:02d}:{:02d}:{:02d}'.format(hours, minutes, seconds)
        else:
            return '{:02d}:{:02d}'.format(minutes, seconds)

    # rewind 10s previous
    def rewind_previous(self):
        if self.media_player:
            self.media_player.setPosition(self.media_player.position() - 10000)

    # rewind 10s next
    def rewind_next(self):
        if self.media_player:
            self.media_player.setPosition(self.media_player.position() + 10000)

    def mute_sound(self):
        if self.media_player:
            if self.media_player.audioOutput().isMuted():
                self.media_player.audioOutput().setMuted(False)
                self.sound_button.setIcon(QIcon(Style.PrimaryImage.sound_video))
            else:
                self.media_player.audioOutput().setMuted(True)
                self.sound_button.setIcon(QIcon(Style.PrimaryImage.mute_sound_video))

    def change_interval(self):
        if self.media_player:
            if self.media_player.playbackRate() == 1.0:
                self.media_player.setPlaybackRate(2.0)
                self.interval_button.setIcon(QIcon(Style.PrimaryImage.speed_video))
            else:
                self.media_player.setPlaybackRate(1.0)
                self.interval_button.setIcon(QIcon(Style.PrimaryImage.speed_video))

    def fullscreen(self):
        if self.media_player:
            if self.isFullScreen():
                self.showNormal()
                self.fullscren.setIcon(QIcon(Style.PrimaryImage.fullscreen_video))
            else:
                self.showFullScreen()
                self.fullscren.setIcon(QIcon(Style.PrimaryImage.minimize_video))

    # def resizeEvent(self, event):
    #     # Calculate the new height based on the width and the 16:9 aspect ratio
    #     height = self.width() * 9 // 16

    #     # Set the new height for the widget
    #     self.setFixedHeight(height)

    def set_camera_name(self, camera_name: str):
        self.camera_name_label.setText(camera_name)

    def set_url(self, url: str, callback=None):
        # play video online by url
        self.media_player.setSource(QUrl(url))
        
        # listen to the end of the video
        self.media_player.mediaStatusChanged.connect(self.media_status_changed)
        # listen to callback
        if callback:
            self.media_player.mediaStatusChanged.connect(callback)
        
        self.play()

    def media_status_changed(self, status):
        if self.DEBUG:
            logger.debug(f'media_status_changed - {status}')
            logger.debug(f'media_status_changed: self.media_player.duration(): {self.media_player.duration()}')

        # if video end, play next video if exist
        if status == QMediaPlayer.EndOfMedia:
            self.play_next_video()

    # play a list url video
    def set_camera_record(self, camera_record: CameraRecord):
        self.list_camera_record = camera_record.urlList
        self.camera_id = camera_record.cameraId
        self.current_url_index = 0
        if self.list_camera_record and self.current_url_index < len(self.list_camera_record):
            self.set_url(self.list_camera_record[self.current_url_index].url)

    # play next video in after current video end
    def play_next_video(self):
        if self.list_camera_record:
            index:int = self.current_url_index + 1
            if index >= len(self.list_camera_record):
                self.current_url_index = 0
            self.current_url_index = index
            # check self.current_url_index < len(self.list_camera_record)
            if self.current_url_index < len(self.list_camera_record):
                self.set_url(self.list_camera_record[self.current_url_index].url)

    def contextMenuEvent(self, event: QContextMenuEvent):
        # Create a context menu
        menu = QMenu(self)
        # create menu actions icon and text largger
        menu.setStyleSheet(
            "QMenu{font-size: 18px}")

        # Add some actions to the menu
        # stop live view
        stop_live_view_action = QAction(
            QIcon(Style.PrimaryImage.stop_live), "  Dừng phát trực tiếp", self)
        menu.addAction(stop_live_view_action)
        # capture
        capture_action = QAction(QIcon(Style.PrimaryImage.screenshot), "  Chụp", self)
        menu.addAction(capture_action)
        # record
        record_action = QAction(
            QIcon(Style.PrimaryImage.record), "  Bắt đầu quay", self)
        menu.addAction(record_action)
        # ptz
        ptz_action = QAction(QIcon(Style.PrimaryImage.ptz), "  PTZ", self)
        menu.addAction(ptz_action)
        # open digital zoom
        open_digital_zoom_action = QAction(
            QIcon(Style.PrimaryImage.zoom_digital), "  Mở Zoom số", self)
        menu.addAction(open_digital_zoom_action)
        # switch to instant playback
        switch_to_instant_playback_action = QAction(QIcon(Style.PrimaryImage.playback),
                                                    "  Chuyển sang xem lại", self)
        menu.addAction(switch_to_instant_playback_action)
        # enable audio
        enable_audio_action = QAction(
            QIcon(Style.PrimaryImage.audio), "  Mở âm thanh", self)
        menu.addAction(enable_audio_action)
        # camera status
        camera_status_action = QAction(
            QIcon(Style.PrimaryImage.info), "  Trạng thái Camera", self)
        menu.addAction(camera_status_action)
        # full screen
        full_screen_action = QAction(
            QIcon(Style.PrimaryImage.full_screen), "  Toàn màn hình", self)
        menu.addAction(full_screen_action)

        # Show the menu at the current mouse position
        menu.exec_(self.mapToGlobal(event.pos()))


    def on_clicked_progress(self, current_time: QDateTime):
        if current_time is None:
            return
        logger.debug(f'on_clicked_progress - {current_time.toPython()}')

        self.is_click_progress = True
        # search video by time in list
        if self.list_camera_record:
            # find camera record by current_time
            for index, camera_record in enumerate(self.list_camera_record):
                # convert string to QDateTime
                format = Qt.DateFormat.ISODate
                # format_string = "yyyy-MM-dd HH:mm:ss.zzzzzz"
                start_time = QDateTime.fromString(camera_record.startTime, format)
                end_time = QDateTime.fromString(camera_record.endTime, format)
                
                logger.debug(f'before: start_time - {start_time.toPython()}')
                logger.debug(f'before: end_time - {end_time.toPython()}')
                logger.debug(f'current_time - {current_time.toPython()}')


                if current_time >= start_time and current_time <= end_time:
                    logger.debug(f'camera_record.startTime - {camera_record.startTime}')
                    logger.debug(f'camera_record.endTime - {camera_record.endTime}')
                    logger.debug(f'start_time - {start_time.toPython()}')
                    logger.debug(f'end_time - {end_time.toPython()}')
                    logger.debug(f'current_time - {current_time.toPython()}')
                    
                    if self.current_url_index != index:
                        self.current_url_index = index
                        def callback(status):
                            if self.DEBUG:
                                logger.debug(f'callback: self.media_player.duration(): {self.media_player.duration()} - state: {status}')
                            if status == QMediaPlayer.MediaStatus.BufferedMedia:
                                duration = current_time.toMSecsSinceEpoch() - start_time.toMSecsSinceEpoch()
                                self.on_position_video_changed(duration)
                        self.set_url(camera_record.url, callback)
                        break # break for loop
                    # calculate progress via current_time and camera_record.startTime and camera_record.endTime

                    logger.debug(f'current_url_index - {self.current_url_index}')
                    logger.debug(f'on_clicked_progress: current_time.toSecsSinceEpoch() - {current_time.toMSecsSinceEpoch()}')
                    logger.debug(f'on_clicked_progress: start_time.toSecsSinceEpoch() - {start_time.toMSecsSinceEpoch()}')
                    logger.debug(f'on_clicked_progress: end_time.toSecsSinceEpoch() - {end_time.toMSecsSinceEpoch()}')
                    logger.debug(f'total time of current video: {end_time.toMSecsSinceEpoch() - start_time.toMSecsSinceEpoch()}')

                    duration = current_time.toMSecsSinceEpoch() - start_time.toMSecsSinceEpoch()
                    
                    self.on_position_video_changed(duration)
                    # then set position video
                    break
        self.is_click_progress = False

    

    def on_position_video_changed(self,duration:int = 0):
        if self.DEBUG:
            logger.debug(f'on_position_video_changed: duration - {duration}')

        # set position video
        self.media_player.setPosition(int(duration))

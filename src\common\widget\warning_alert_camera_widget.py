import sys
import logging
logger = logging.getLogger(__name__)
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QWidget, QLabel, QPushButton, QVBoxLayout, QHBoxLayout, QSizePolicy
from PySide6.QtCore import Qt, Signal, QSize, QCoreApplication
from PySide6.QtGui import QPixmap, QIcon, QMouseEvent, QMovie
from src.common.widget.dialog_ai_event_widget import EventDialog
from src.styles.style import Style
import resources_rc
from src.utils.setting_screen_qsetting import SettingScreenQSettings


class WarningAlertCameraWidget(QWidget):
    close_signal = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_event_ai = None

        self.layout_alert_message = QHBoxLayout()
        self.layout_alert_message.setContentsMargins(4, 4, 4, 4)
        self.layout_alert_message.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.svg_widget = QSvgWidget()
        self.svg_widget.setFixedSize(16, 16)
        self.svg_widget.load(Style.PrimaryImage.alarm_alert_icon)

        self.message_label = QLabel("Warning here!!!!")

        self.layout_alert_message.addWidget(self.svg_widget)
        self.layout_alert_message.addWidget(self.message_label)

        main_widget = QWidget()
        main_widget.setLayout(self.layout_alert_message)
        main_widget.setStyleSheet(
            f'''
                background-color: {Style.PrimaryColor.primary};
                border-radius: 2px;
                color: white;
            '''
        )
        main_widget.mousePressEvent = self.show_event_ai_detail

        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(4, 4, 0, 0)
        self.layout.addWidget(main_widget, alignment=(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft))
        self.setLayout(self.layout)
        self.setVisible(False)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def close_alert_widget(self):
        if self.isVisible():
            self.setVisible(False)
        self.stop_sound_alert()
        self.close()

    def set_current_event_ai(self, event_ai):
        self.current_event_ai = event_ai
        pass

    def show_event_ai_detail(self, event: QMouseEvent):
        self.dialog = EventDialog(event=self.current_event_ai, show_warning_info=True)
        self.dialog.exec()
        pass

    def play_sound_alert(self):
        pass

    def stop_sound_alert(self):
        pass

    def show_warning_alert(self, event_ai):
        self.set_current_event_ai(event_ai)
        # self.gif_warning.start()
        # self.event_type = event_ai.event_type
        self.event_type = event_ai.type

        if self.event_type == 'ANPR':
            self.status = QCoreApplication.translate("EventItem", "Traffic Vehicle Detection Warning", None)
        elif self.event_type == 'CROWD':
            self.status = QCoreApplication.translate("EventItem", "Crowd Detected Warning", None)
        elif self.event_type == 'ACCESS_CONTROL' or self.event_type == 'HUMAN':
            self.status = QCoreApplication.translate("EventItem", "Intruder Detected Warning", None)
        self.message_label.setText(self.status)
        self.setVisible(True)
        data = SettingScreenQSettings.get_instance().get_current_alert_channel()
        if data is not None and len(data) > 0:
            if 'ALARM_SOUND' in data:
                self.play_sound_alert()

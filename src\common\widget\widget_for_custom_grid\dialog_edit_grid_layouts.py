from PySide6.QtGui import QIcon
from PySide6.QtWidgets import QVBox<PERSON>ayout, QPushButton, QHBoxLayout, QDialog, QW<PERSON>t, QLabel, QSpinBox
from PySide6.QtCore import Qt, QSize, Signal

from src.common.widget.dialogs.base_dialog import ButtonCloseDialog
from src.common.model.item_grid_model import ItemGridModel
from src.common.widget.button_state import ButtonState
from src.common.widget.widget_for_custom_grid.grid_editable_widget import GridEditableWidget
from src.common.widget.widget_for_custom_grid.list_item_grid_custom import ListGridCustom, ItemGridCustom
from src.common.controller.main_controller import main_controller
from src.presentation.device_management_screen.widget.list_custom_widgets import SpinBoxNewStyle
from src.styles.style import Style
from src.utils.camera_qsettings import Camera_Qsettings
from src.utils.theme_setting import theme_setting


class DialogEditGridLayouts(QDialog):
    signal_save_trigger = Signal(object)
    def __init__(self, parent=None, list_divisions=None):
        super().__init__(parent)
        self.is_restore_default = False
        self.setModal(False)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        print(f'DialogEditGridLayouts: list_divisions: {list_divisions}')
        self.list_divisions = list_divisions
        self.create_title_bar()
        self.create_footer()
        self.load_ui_listview_and_grid_custom()
        self.load_ui_top_title()
        self.load_ui()
        self.setObjectName("DialogEditGridLayouts")
        print("DialogEditGridLayouts")

    def load_ui(self):
        # create layout
        self.layout = QVBoxLayout()
        self.layout.setSpacing(0)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setAlignment(Qt.AlignTop)

        self.body_widget = QWidget()
        self.body_widget.setObjectName("body_widget")
        self.body_layout = QVBoxLayout(self.body_widget)

        self.body_layout.addLayout(self.layout_top)
        self.body_layout.addWidget(self.widget_body_content)

        self.layout.addWidget(self.title_bar_widget)
        self.layout.addWidget(self.body_widget)
        self.layout.addWidget(self.footer_widget)
        self.setStyleSheet(f'''
            QWidget#body_widget {{
                background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
            }}
            QLabel {{
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
            }}
            
        ''')
        self.setLayout(self.layout)

    def create_title_bar(self):
        # layout
        self.title_bar_widget = QWidget()
        self.title_bar_widget.setObjectName("title_bar")
        # set background Style.PrimaryColor.primary
        self.title_bar_widget.setStyleSheet(f'''
                        QWidget#title_bar {{
                            background-color: {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                            border-bottom: 1px solid {main_controller.get_theme_attribute("Color", "common_border")};
                        }}
                    ''')

        self.title_bar_layout = QHBoxLayout()
        # event name
        self.title_name_label = QLabel(self.tr("EDIT GRID LAYOUTS"))
        self.title_name_label.setStyleSheet(
            f"color: {Style.PrimaryColor.white_2}; font-size: {Style.Size.body_strong}px; font-weight: 600;")
        close_icon = QIcon(main_controller.get_theme_attribute("Image", "dialog_close"))
        self.close_button = ButtonCloseDialog()
        self.close_button.setIconSize(QSize(24, 24))
        self.close_button.setFixedSize(24, 24)
        self.close_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {main_controller.get_theme_attribute("Color", "dialog_header_background")}; 
                border: None
                }}
        """)
        self.close_button.clicked.connect(self.close)
        # add widget
        self.title_bar_layout.addWidget(self.title_name_label, 90)
        self.title_bar_layout.addWidget(self.close_button, 10)
        self.title_bar_widget.setLayout(self.title_bar_layout)

    def create_footer(self):
        self.footer_widget = QWidget()
        self.footer_widget.setObjectName("footer_widget")
        # set background
        self.footer_widget.setStyleSheet(f'''
                        QWidget#footer_widget {{
                            background-color: {main_controller.get_theme_attribute("Color", "dialog_body_background")};
                            border-top: 1px solid {main_controller.get_theme_attribute("Color", "common_border")};
                        }}
                    ''')

        self.footer_layout = QHBoxLayout()
        self.footer_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.button_close = QPushButton(self.tr('Cancel'))
        self.button_close.setStyleSheet(f'''
                    QPushButton {{
                        background-color: {main_controller.get_theme_attribute("Color", "button_background_normal")};
                        color: {Style.PrimaryColor.white_2};
                        border-radius: {Style.Size.border_radius}px;
                        padding: {Style.Size.common_button_padding};

                    }}
                ''')
        self.button_close.clicked.connect(self.close)
        self.button_save_update = QPushButton(self.tr('Save'))
        self.button_save_update.setStyleSheet(f'''
                    QPushButton {{
                        background-color: {Style.PrimaryColor.button_primary_background};
                        color: {Style.PrimaryColor.text_on_primary};
                        border-radius: {Style.Size.border_radius}px;
                        padding: {Style.Size.common_button_padding};

                    }}
                ''')
        self.button_save_update.clicked.connect(self.on_handle_save_data)

        # add widget
        self.footer_layout.addWidget(self.button_close)
        self.footer_layout.addWidget(self.button_save_update)
        self.footer_widget.setLayout(self.footer_layout)

    def load_ui_top_title(self):
        self.layout_top = QHBoxLayout()
        self.layout_top.setContentsMargins(0, 0, 0, 0)

        label_columns = QLabel(self.tr("Columns: "))
        label_rows = QLabel(self.tr("Rows: "))

        self.columns_spinbox = SpinBoxNewStyle()
        self.columns_spinbox.setMinimum(3)
        self.columns_spinbox.setMaximum(8)
        self.columns_spinbox.setValue(self.current_model.column)
        self.columns_spinbox.valueChanged.connect(self.handle_value_column_change)
        self.rows_spinbox = SpinBoxNewStyle()
        self.rows_spinbox.setMinimum(3)
        self.rows_spinbox.setMaximum(8)
        self.rows_spinbox.setValue(self.current_model.row)
        self.rows_spinbox.valueChanged.connect(self.handle_value_row_change)

        layout_columns_spinbox = QHBoxLayout()
        layout_columns_spinbox.setSpacing(4)
        layout_columns_spinbox.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_columns_spinbox.addWidget(label_columns)
        layout_columns_spinbox.addWidget(self.columns_spinbox)

        layout_rows_spinbox = QHBoxLayout()
        layout_rows_spinbox.setSpacing(4)
        layout_rows_spinbox.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_rows_spinbox.addWidget(label_rows)
        layout_rows_spinbox.addWidget(self.rows_spinbox)

        self.layout_spinbox = QHBoxLayout()
        self.layout_spinbox.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout_spinbox.setSpacing(40)
        self.layout_spinbox.addLayout(layout_columns_spinbox)
        self.layout_spinbox.addLayout(layout_rows_spinbox)

        self.rows, self.cols = self.rows_spinbox.value(), self.columns_spinbox.value()

        title_layouts = QLabel(self.tr("Layouts:"))
        title_layouts.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')};")
        layout_title = QHBoxLayout()
        layout_title.addWidget(title_layouts)
        self.layout_top.addLayout(layout_title, 20)
        self.layout_top.addLayout(self.layout_spinbox, 80)

    def load_ui_listview_and_grid_custom(self):
        self.widget_body_content = QWidget()
        self.widget_body_content.setStyleSheet(f'color: {Style.PrimaryColor.white_2};')
        self.layout_body_content = QHBoxLayout(self.widget_body_content)
        self.layout_body_content.setContentsMargins(0, 0, 0, 0)
        self.layout_body_content.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignCenter)

        widget_list_and_reset_button = QWidget()
        widget_list_and_reset_button.setObjectName('widget_list_and_reset_buttonn')
        widget_list_and_reset_button.setStyleSheet(f'''
            QWidget#widget_list_and_reset_buttonn {{
                color: {main_controller.get_theme_attribute('Color', 'dialog_text')};
                background-color: {main_controller.get_theme_attribute('Color', 'dialog_body_background')};
                border-radius: 4px;
                border: 1px solid {main_controller.get_theme_attribute('Color', 'common_border')};
            }}
        ''')
        self.layout_list_and_reset_button = QVBoxLayout(widget_list_and_reset_button)
        self.layout_list_and_reset_button.setContentsMargins(0, 0, 0, 10)
        self.layout_list_and_reset_button.setSpacing(0)
        self.layout_list_and_reset_button.setAlignment(Qt.AlignmentFlag.AlignCenter)
        print(f'self.list_divisions: {self.list_divisions}')
        self.list_item_grid = ListGridCustom(divisions_list=self.list_divisions)
        self.list_item_grid.signal_item_click.connect(self.connect_data_item_click)

        layout_restore = QHBoxLayout()
        layout_restore.setContentsMargins(0, 0, 0, 20)
        layout_restore.setSpacing(0)
        layout_restore.setAlignment(Qt.AlignmentFlag.AlignCenter)
        restore_button = QPushButton(self.tr("Restore to Default"))
        restore_button.setFixedWidth(160)
        restore_button.setStyleSheet(f'''
            QPushButton {{
                background-color: {main_controller.get_theme_attribute("Color", "button_background_normal")};
                color: {Style.PrimaryColor.white_2};
                border-radius: {Style.Size.border_radius}px;
                padding: {Style.Size.common_button_padding};

            }}
        ''')
        restore_button.clicked.connect(self.on_handle_store_to_default)
        layout_restore.addWidget(restore_button)
        self.layout_list_and_reset_button.addWidget(self.list_item_grid)
        self.layout_list_and_reset_button.addLayout(layout_restore)

        self.current_model = self.get_current_item_model()
        self.drawing_widget = GridEditableWidget(data_model=self.current_model)
        self.drawing_widget.signal_update_data.connect(self.connect_update_data_model)

        self.layout_body_content.addWidget(widget_list_and_reset_button, 30)
        self.layout_body_content.addWidget(self.drawing_widget, 70)

    def connect_data_item_click(self, data: ItemGridModel):

        self.drawing_widget.merged_frame = self.drawing_widget.convert_array_to_set(data.data)
        self.drawing_widget.update()

        self.columns_spinbox.setValue(data.column)
        self.rows_spinbox.setValue(data.row)

    def connect_update_data_model(self, data):
        merged_frame, remain_item, row, column = data
        self.update_current_model(data=merged_frame, divisions=remain_item, new_row=row, new_column=column, total_grid_count=row*column)

    def handle_value_row_change(self, new_value):
        self.rows = new_value
        self.drawing_widget.set_row_count(new_value)
        remain_item = self.drawing_widget.calculate_remaining_items()
        self.update_current_model(new_row=new_value, divisions=remain_item, total_grid_count=self.rows*self.cols)
        self.drawing_widget.update()

    def handle_value_column_change(self, new_value):
        self.cols = new_value
        self.drawing_widget.set_column_count(new_value)
        remain_item = self.drawing_widget.calculate_remaining_items()
        self.update_current_model(new_column=new_value, divisions=remain_item, total_grid_count=self.rows*self.cols)
        self.drawing_widget.update()

    def update_current_model(self, new_row=None, new_column=None, data=None, divisions=None, total_grid_count=None):
        current_model = self.get_current_item_model()
        if current_model:
            current_model.update_data(row=new_row, column=new_column, data=data, divisions=divisions, total_grid_count=total_grid_count)
            if divisions is not None:
                self.update_name_grid(f'{current_model.divisions}'+self.tr(" Divisions"))

    def update_name_grid(self, name):
        # Get the current selected index
        current_item_list_idx = self.list_item_grid.currentIndex()
        # Get the item from the model at the selected index
        item: ItemGridCustom = self.list_item_grid.list_view_model.itemFromIndex(current_item_list_idx)
        item.label_name_grid.setText(name)

    def on_handle_save_data(self):
        self.signal_save_trigger.emit(self.list_item_grid.divisions_list)
        self.close()

    def on_handle_cancel_change(self):
        print("on_handle_cancel_change")
        self.is_restore_default = False
        self.close()

    def on_handle_store_to_default(self):
        # Kịch bản reset to default có cần lưu thì mới là oke reset không hay là cứ reset là mất hết luôn?
        self.is_restore_default = True
        list_data_grid = [
            ItemGridModel(name_grid='6 ' + self.tr(f"Divisions"), data=[{(0, 1), (1, 0), (1, 1), (0, 0)}], row=3,
                          column=3,
                          total_grid_count=9,
                          image_url=f"{main_controller.get_theme_attribute('Image', 'custom_6_grid')}",
                          grid_type=ButtonState.GridType.GRID_6_CUSTOM, divisions=6,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path='custom_6_grid'),

            ItemGridModel(name_grid='8 ' + self.tr(f"Divisions"),
                          data=[{(0, 1), (1, 2), (2, 1), (0, 0), (1, 1), (2, 0), (0, 2), (2, 2), (1, 0)}],
                          row=4, column=4, total_grid_count=16, image_url=f"{main_controller.get_theme_attribute('Image', 'custom_8_grid')}",
                          grid_type=ButtonState.GridType.GRID_8_CUSTOM, divisions=8,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path='custom_8_grid'),

            ItemGridModel(name_grid='10 ' + self.tr(f"Divisions"),
                          data=[{(0, 1), (1, 0), (1, 1), (0, 0)}, {(1, 2), (0, 2), (0, 3), (1, 3)}],
                          row=4, column=4, total_grid_count=16,
                          image_url=f"{main_controller.get_theme_attribute('Image', 'custom_10_grid')}",
                          grid_type=ButtonState.GridType.GRID_10_CUSTOM, divisions=10,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path='custom_10_grid'),

            ItemGridModel(name_grid='13 ' + self.tr(f"Divisions"), data=[{(1, 1), (1, 2), (2, 1), (2, 2)}],
                          row=4, column=4, total_grid_count=16,
                          image_url=f"{main_controller.get_theme_attribute('Image', 'custom_13_grid')}",
                          grid_type=ButtonState.GridType.GRID_13_CUSTOM, divisions=13,
                          divisions_type=ButtonState.DivisionType.CUSTOM_DIVISIONS,
                          grid_path='custom_13_grid')
        ]
        self.list_item_grid.update_grid_items(list_data_grid)
        self.current_model = self.get_current_item_model()
        self.drawing_widget.update_data_model(self.current_model)
        self.columns_spinbox.setValue(self.current_model.column)
        self.rows_spinbox.setValue(self.current_model.row)

    def get_current_item_model(self):
        # Get the current selected index
        current_item_list_idx = self.list_item_grid.currentIndex()
        # Check if there is a selected index
        if current_item_list_idx.isValid():
            # Get the item from the model at the selected index
            item = self.list_item_grid.list_view_model.itemFromIndex(current_item_list_idx)
            # Assuming ItemGridModel is the type of model used in your code
            model_current_item: ItemGridModel = item.model
            return model_current_item
        else:
            return None  # Return None if there is no selected item


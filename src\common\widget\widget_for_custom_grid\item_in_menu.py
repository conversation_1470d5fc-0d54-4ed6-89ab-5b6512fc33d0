from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QEnterEvent, QPixmap
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton

from src.common.model.item_grid_model import ItemGridModel
from src.styles.style import Style
from src.common.controller.main_controller import main_controller

class ItemGridMenu(QWidget):
    emit_size_signal = Signal(object)

    def __init__(self, title=None, image_path=None, type_division="GRID_STANDARD", model_grid: ItemGridModel = None):
        super().__init__()
        self.title = title
        self.image_path = image_path
        self.type_division = type_division
        self.model_grid = model_grid
        self.load_ui()
        self.setup_dynamic_stylesheet()

    def load_ui(self):
        # create layout
        self.layout = QVBoxLayout()
        self.layout.setSpacing(0)
        self.layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout.setContentsMargins(4, 0, 4, 0)
        self.label_image = QLabel()
        self.label_image.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pixmap = QPixmap(self.model_grid.image_url)
        self.label_image.setPixmap(pixmap)

        self.label_title = QLabel(str(self.model_grid.divisions))
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout.addWidget(self.label_image)
        self.layout.addWidget(self.label_title)
        self.setObjectName("item_grid_menu")

        self.background = QWidget()
        self.background.setObjectName("item_grid_menu")
        self.background.setLayout(self.layout)

        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        self.main_layout.addWidget(self.background)
        
        self.setLayout(self.main_layout)

        # self.setLayout(self.layout)

        # register event
        self.installEventFilter(self)

    def setup_dynamic_stylesheet(self, pixmap=None):
        if pixmap is not None:
            self.label_image.setPixmap(pixmap)
        self.label_title.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}; ")

    def mousePressEvent(self, event):
        value = self.model_grid
        self.emit_size_signal.emit(value)

    def enterEvent(self, event: QEnterEvent) -> None:
        self.setStyleSheet(
            f'''
                QWidget#item_grid_menu {{
                    background-color: {Style.PrimaryColor.primary};
                    border-radius: 4px;
                    border: 1px solid {Style.PrimaryColor.primary};
                }}
            '''
        )
    
    def leaveEvent(self, event: QEnterEvent) -> None:
        self.setStyleSheet(
            f'''
                QWidget#item_grid_menu {{
                    background-color: transparent;
                    border-radius: 4px;
                    border: 1px solid transparent;
                }}
            '''
        )

    def update_ui(self):
        self.title = self.model_grid.name_grid
        self.image_path = self.model_grid.image_url
        self.label_title.setText(self.title)
        pixmap = QPixmap(self.image_path)
        self.label_image.setPixmap(pixmap)


class ButtonEditLayout(QWidget):

    button_click_signal = Signal()

    def __init__(self, title=None, image_path=None):
        super().__init__()
        self.title = title
        self.image_path = image_path
        self.load_ui()
        self.setup_dynamic_stylesheet()

    def load_ui(self):
        # create layout
        self.layout = QVBoxLayout()
        self.layout.setSpacing(0)
        self.layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout.setContentsMargins(4, 0, 4, 0)
        self.label_image = QLabel()
        self.label_image.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pixmap = QPixmap(self.image_path)
        self.label_image.setPixmap(pixmap)

        self.label_title = QLabel(self.title)
        self.label_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_title.setStyleSheet(f"font-weight: bold; color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}")
        self.layout.addWidget(self.label_image)
        self.layout.addWidget(self.label_title)
        widget = QWidget()
        widget.setObjectName("widget_test")
        widget.setLayout(self.layout)
        layout = QVBoxLayout(self)
        layout.addWidget(widget)
        layout.setSpacing(0)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.setContentsMargins(0, 0, 0, 0)

    def setup_dynamic_stylesheet(self, image_path=None):
        if image_path is not None:
            self.image_path = image_path
            pixmap = QPixmap(image_path)
            self.label_image.setPixmap(pixmap)
        self.label_title.setStyleSheet(
            f"font-weight: bold; color: {main_controller.get_theme_attribute('Color', 'text_color_all_app')}")


    def mousePressEvent(self, event):
        print("ButtonEditLayout mousePressEvent")
        self.button_click_signal.emit()

    def enterEvent(self, event):
        self.setStyleSheet(f'''
            QWidget#widget_test {{
                background-color: {Style.PrimaryColor.primary};
                border-radius: 4px
            }}
        ''')

    def leaveEvent(self, event):
        self.setStyleSheet(f'''
                    QWidget#widget_test {{
                        background-color: transparent;
                        border-radius: 4px
                    }}
                ''')
        self.setStyleSheet(f"background-color: transparent")

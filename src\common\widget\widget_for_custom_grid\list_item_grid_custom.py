from typing import List

from PySide6.QtCore import Qt, QItemSelectionModel, Signal, QObject
from PySide6.QtGui import QStandardItemModel, QStandardItem, QPixmap
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>iew, QWidget, QHBoxLayout, QVBoxLayout, QLabel

from VMS import main_controller
from src.common.model.item_grid_model import ItemGridModel
from src.styles.style import Style


class ListGridCustom(QListView):
    signal_item_click = Signal(object)
    def __init__(self, parent=None, divisions_list=None):
        super().__init__(parent)
        print(f'INIT: divisions_list: {divisions_list}')
        self.divisions_list = divisions_list
        self.today_index = None
        self.setFixedWidth(180)
        self.list_grid_custom: List[ItemGridModel] = []
        self.load_ui()

    def load_ui(self):
        self.setContentsMargins(0, 0, 0, 0)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.clicked.connect(self.item_clicked)
        self.list_view_model = QStandardItemModel()
        self.setModel(self.list_view_model)
        self.populate_grid_item()
        self.set_style_sheet()

    def item_clicked(self, index):
        clicked_item: ItemGridCustom = self.list_view_model.itemFromIndex(index)
        self.signal_item_click.emit(clicked_item.model)
        # Handle visibility of state_choose for each item in the list
        for row in range(self.list_view_model.rowCount()):
            item = self.list_view_model.item(row, 0)
            item_widget = self.indexWidget(self.list_view_model.indexFromItem(item))
            if item == clicked_item:
                item.set_clicked_style()
            else:
                item.set_unclicked_style()
            if item_widget:
                if item == clicked_item:
                    item.state_choose.setVisible(True)
                else:
                    item.state_choose.setVisible(False)

    def set_style_sheet(self):
        self.setStyleSheet(f'''
            QListView{{
                border-radius: 4px;
                background: transparent;
                border-left: 1px solid {main_controller.get_theme_attribute("Color", "common_border")};
                border-right: 1px solid {main_controller.get_theme_attribute("Color", "common_border")};
            }}
            QListView::item{{
                background: transparent;
            }}
            QListView::item:selected{{
                background: {Style.PrimaryColor.primary};
                border-radius: 4px;
            }}
            ''')

    def populate_grid_item(self):
        self.today_index = self.list_view_model.rowCount()
        for i in self.divisions_list:
            self.list_grid_custom.append(i)

        if self.list_grid_custom:
            self.add_items_to_list(self.list_grid_custom)

    def add_newest_item_grid(self, model: ItemGridModel):
        item_widget = ItemGridCustom(model=model)
        self.list_view_model.insertRow(self.today_index, item_widget)

        select_index = self.list_view_model.index(self.today_index, 0)
        selection_model = self.selectionModel()
        if selection_model is not None:
            selection_model.setCurrentIndex(select_index, QItemSelectionModel.SelectionFlag(QItemSelectionModel.ClearAndSelect | QItemSelectionModel.Rows))

        self.setIndexWidget(self.list_view_model.indexFromItem(item_widget), item_widget.main_widget)
        self.today_index += 1

    def add_items_to_list(self, list_items):
        for item_model in list_items:
            item = ItemGridCustom(model=item_model)
            self.list_view_model.appendRow(item)
            self.setIndexWidget(self.list_view_model.indexFromItem(item), item.main_widget)

        if self.list_view_model.rowCount() > 0:
            index_to_select = self.list_view_model.index(0, 0)
            self.setCurrentIndex(index_to_select)
            current_item = self.list_view_model.itemFromIndex(index_to_select)
            if current_item:
                current_item_widget = self.indexWidget(index_to_select)
                if current_item_widget:
                    current_item.state_choose.setVisible(True)
                    current_item.set_clicked_style()

    def update_grid_items(self, new_divisions_list):
        # Clear existing items
        self.list_view_model.clear()
        self.list_grid_custom.clear()
        # Update data
        self.list_grid_custom = new_divisions_list
        self.divisions_list = new_divisions_list
        self.add_items_to_list(self.divisions_list)


class ItemGridCustom(QStandardItem, QObject):
    def __init__(self, model: ItemGridModel = None):
        super().__init__()
        self.model = model
        self.load_ui()

    def load_ui(self):
        self.main_widget = QWidget()
        self.main_layout = QHBoxLayout(self.main_widget)
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_layout.setSpacing(12)
        self.widget_state_choose = QWidget()
        self.layout_state_choose = QVBoxLayout(self.widget_state_choose)
        self.state_choose = QSvgWidget()
        self.state_choose.load(Style.PrimaryImage.caret_right_grid)
        self.state_choose.setFixedSize(10, 10)
        self.state_choose.setVisible(False)
        self.layout_state_choose.addWidget(self.state_choose)

        self.layout_content = QHBoxLayout()
        self.image_grid = QLabel()
        self.image_grid.setFixedSize(40, 40)
        self.image_grid.setAlignment(Qt.AlignmentFlag.AlignCenter)
        pixmap = QPixmap(self.model.image_url)  # Replace with the actual image file path
        self.image_grid.setPixmap(pixmap)
        str_divisions = self.tr("Divisions")
        self.label_name_grid = QLabel(f"{self.model.divisions} {str_divisions}")
        self.label_name_grid.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')};")
        self.label_name_grid.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.layout_content.addWidget(self.image_grid)
        self.layout_content.addWidget(self.label_name_grid)

        self.main_layout.addLayout(self.layout_content)
        self.main_layout.addWidget(self.widget_state_choose)

        self.setSizeHint(self.main_widget.sizeHint())
        self.setData(self.main_widget, Qt.UserRole)

    def set_clicked_style(self):
        self.label_name_grid.setStyleSheet(f"color: {Style.PrimaryColor.white};")

    def set_unclicked_style(self):
        self.label_name_grid.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'dialog_text')};")
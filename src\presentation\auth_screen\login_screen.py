from src.common.widget.custom_titlebar.custom_component.login_title_bar import LoginTitleBar
from src.common.widget.custom_titlebar.custom_titlebar_with_tab import CustomTitleBarWithTab
from src.utils.auth_qsettings import AuthQSettings
from src.common.scan_network.scan_network import NetworkScanner
from src.utils.config import Config
from src.styles.style import Style
from src.common.widget.clickable_qlabel import ClickableQLabel
from src.common.controller.main_controller import main_controller
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QRect, QCoreApplication, QEvent
from PySide6.QtGui import QPixmap, QGuiApplication, QPainter, QIcon, QAction, QPalette, QBrush, QScreen
from PySide6.QtWidgets import QStackedWidget, QWidget, QVBoxLayout, QLabel, QLineEdit, QPushButton, \
    QHBoxLayout, QSpacerItem, QSizePolicy, QApplication
import os
import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)


class LoginForm(QWidget):

    login_signal = Signal(str)
    connect_server_signal = Signal(str)
    scan_ip_signal = Signal(tuple)

    def __init__(self, login_success, change_server=False):
        super().__init__()
        self.shake_animation = None
        self.server_error_label = None
        self.connect_server_button = None
        self.login_button = None
        self.sso_label = None
        self.forgot_label = None
        self.showPassAction = None
        self.error_label = None
        self.password_edit = None
        self.username_edit = None
        self.username_data = None
        self.password_data = None
        self.login_success = login_success
        self.auth_qsettings = AuthQSettings.get_instance()
        screen = QGuiApplication.primaryScreen()
        desktop_screen_size = screen.availableGeometry()
        self.setGeometry(desktop_screen_size)

        full_geometry = screen.geometry()
        title_bar_height = full_geometry.height() - desktop_screen_size.height()
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(layout)

        self.central_widget = QWidget()
        self.central_widget.setObjectName("central_widget")
        self.central_layout = QVBoxLayout()
        self.central_layout.setContentsMargins(0, 8, 0, 8)
        self.central_layout.setAlignment(
            Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignHCenter)
        self.central_widget.setLayout(self.central_layout)

        pixmap = QPixmap(Style.PrimaryImage.logo_login)
        self.label_image = QLabel()
        self.label_image.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_image.setPixmap(pixmap)
        self.label_image.resize(pixmap.width(), pixmap.height())

        self.intro_label = QLabel(self.tr("Intelligent Surveillance Platform"))
        self.intro_label.setStyleSheet('''
            QLabel
                {
                    font-size: 20px;
                    font-weight: 100;
                }
        ''')
        self.intro_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.stack_form_widget = QStackedWidget()
        self.central_layout.addWidget(self.stack_form_widget)

        layout.addWidget(self.central_widget)

        # styleName: Body text;
        # font-family: Roboto;
        # font-size: 14px;
        # font-weight: 400;
        # line-height: 22px;
        # letter-spacing: 0em;
        # text-align: left;
        style_test = f'''
            QLabel
                {{
                    font-weight: 400;
                    letter-spacing: 0em;
                    text-align: left;
                }}
        '''

        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                color: {Style.PrimaryColor.black};}}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;
                color: {Style.PrimaryColor.white_2};
                font-weight: 200;}}
            /*-----QPushButton-----*/
            QPushButton{{
                background-color: {Style.PrimaryColor.primary};
                color: {Style.PrimaryColor.white};
                font-weight: bold;
                border: none;
                border-radius: 4px;
                padding: 5px;}}
            QPushButton::disabled{{
                background-color: #5c5c5c;}}
            QPushButton::pressed{{
                background-color: {Style.PrimaryColor.button_color};}}
            /*-----QLineEdit-----*/
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {Style.PrimaryColor.text_unselected};
                font-weight: 400;
                letter-spacing: 0em;
                text-align: left;
                border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                padding: 5px 15px;}}
            QLineEdit::focus{{
                border: 1px solid {Style.PrimaryColor.primary};
                color: {Style.PrimaryColor.white} }}
            QLineEdit:!focus{{
                color: {Style.PrimaryColor.border_line_edit};}}
            QLineEdit::placeholder{{
                color: {Style.PrimaryColor.text_disable};}}
        
        ''')

        # self.find_ip_server(change_server=change_server)
        # self.setup_ui_login()
        self.setup_ui_server()

    def setup_ui_login(self):
        self.username_edit = QLineEdit()
        self.password_edit = QLineEdit()
        if Config.ENABLE_FORCE_LOGIN:
            self.username_edit.setText('admin_vms')
            self.password_edit.setText('admin@#123')
        else:
            user_name = self.auth_qsettings.load_username()
            self.username_edit.setText(user_name)
        self.error_label = QLabel("")
        self.error_label.setStyleSheet(
            f"color: {Style.PrimaryColor.primary}; font-weight: 400")
        self.error_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        self.username_edit.setFixedSize(360, 34)
        self.username_edit.setPlaceholderText(self.tr("Username"))
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)

        self.password_edit.setFixedSize(360, 34)
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.showPassAction = QAction(
            QIcon(Style.PrimaryImage.eye_close), self.tr("Show password"), self)
        self.password_edit.addAction(
            self.showPassAction, QLineEdit.ActionPosition.TrailingPosition)
        self.showPassAction.setCheckable(True)
        self.showPassAction.toggled.connect(self.togglePasswordVisibility)
        self.password_edit.setPlaceholderText(self.tr("Password"))
        self.password_edit.returnPressed.connect(self.click_to_login)

        self.hBoxlayout = QHBoxLayout()
        self.hBoxlayout.setContentsMargins(0, 0, 0, 0)
        self.h_widget = QWidget()
        self.h_widget.setFixedWidth(360)
        self.h_widget.setLayout(self.hBoxlayout)
        self.forgot_label = ClickableQLabel(self.tr("Forgot your password"))
        self.sso_label = ClickableQLabel(self.tr("Sign in with SSO"))
        self.hBoxlayout.addWidget(self.forgot_label)
        self.hBoxlayout.addStretch()
        self.hBoxlayout.addWidget(self.sso_label)

        self.login_button = QPushButton(self.tr("LOGIN"))
        self.login_button.setFixedSize(148, 34)
        self.login_button.clicked.connect(self.click_to_login)

        self.login_form_layout = QVBoxLayout()
        self.login_form_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.login_form_layout.addStretch(4)
        self.login_form_layout.addWidget(self.label_image)
        self.login_form_layout.addStretch(1)
        self.login_form_layout.addWidget(self.intro_label)
        self.login_form_layout.addStretch(1)
        self.login_form_layout.addWidget(self.username_edit)
        self.login_form_layout.addWidget(self.password_edit)
        self.login_form_layout.addWidget(self.error_label)
        if Config.ENABLE_FORGOT_PASSWORD:
            self.login_form_layout.addWidget(self.h_widget)
        self.login_form_layout.addStretch(1)
        self.login_form_layout.addWidget(
            self.login_button, alignment=Qt.AlignmentFlag.AlignCenter)
        self.login_form_layout.addStretch(4)
        self.login_form_widget = QWidget()
        self.login_form_widget.setLayout(self.login_form_layout)
        # add to central layout
        self.stack_form_widget.addWidget(self.login_form_widget)

    def remove_ui_login(self):
        self.stack_form_widget.removeWidget(self.login_form_widget)

    def setup_ui_server(self):
        server_ip = self.auth_qsettings.load_ip_server()
        logger.debug(server_ip)
        server_port = self.auth_qsettings.load_port_server()
        logger.debug(server_port)
        event_port = self.auth_qsettings.load_port_websocket()
        logger.debug(event_port)
        self.server_ip_edit = QLineEdit(str(server_ip))
        self.server_port_edit = QLineEdit(str(server_port))
        self.event_port_edit = QLineEdit(str(event_port))
        self.server_error_label = QLabel("")
        self.server_error_label.setStyleSheet(
            f"color: {Style.PrimaryColor.primary}; font-weight: 400")
        self.server_error_label.setAlignment(Qt.AlignmentFlag.AlignRight)

        self.server_ip_edit.setFixedSize(360, 34)
        self.server_ip_edit.setPlaceholderText("Server IP: *************")
        self.server_ip_edit.returnPressed.connect(
            self.server_port_edit.setFocus)

        self.server_port_edit.setFixedSize(360, 34)
        self.server_port_edit.setPlaceholderText("Server Port")
        self.server_port_edit.returnPressed.connect(
            self.event_port_edit.setFocus)

        self.event_port_edit.setFixedSize(360, 34)
        self.event_port_edit.setPlaceholderText("Server Port")
        self.event_port_edit.returnPressed.connect(
            self.click_to_connect_server)

        self.connect_server_button = QPushButton(self.tr("CONNECT"))
        self.connect_server_button.setFixedSize(148, 34)
        self.connect_server_button.clicked.connect(
            self.click_to_connect_server)

        self.server_form_layout = QVBoxLayout()
        self.server_form_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.server_form_layout.addStretch(4)
        self.server_form_layout.addWidget(self.label_image)
        self.server_form_layout.addStretch(1)
        self.server_form_layout.addWidget(self.intro_label)
        self.server_form_layout.addStretch(1)
        self.server_form_layout.addWidget(self.server_ip_edit)
        self.server_form_layout.addWidget(self.server_port_edit)
        self.server_form_layout.addWidget(self.event_port_edit)
        self.server_form_layout.addWidget(self.server_error_label)
        self.server_form_layout.addStretch(1)
        self.server_form_layout.addWidget(
            self.connect_server_button, alignment=Qt.AlignmentFlag.AlignCenter)
        self.server_form_layout.addStretch(4)
        self.server_form_widget = QWidget()
        self.server_form_widget.setLayout(self.server_form_layout)
        # add to central layout
        self.stack_form_widget.addWidget(self.server_form_widget)

    def remove_ui_server(self):
        self.stack_form_widget.removeWidget(self.server_form_widget)

    def togglePasswordVisibility(self):
        if self.password_edit.echoMode() == QLineEdit.EchoMode.Password:
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Normal)
            self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye))
            self.showPassAction.setToolTip(self.tr("Hide password"))
        else:
            self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
            self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye_close))
            self.showPassAction.setToolTip(self.tr("Show password"))

    def click_to_login(self):
        # logger.debug(f'ahihi click_to_login')
        # check if self.shake_animation running -> return
        if self.shake_animation and self.shake_animation.state() == QPropertyAnimation.State.Running:
            return
        self.username_edit.clearFocus()
        self.password_edit.clearFocus()
        # Get the entered username and password
        self.username_data = self.username_edit.text().strip()
        self.password_data = self.password_edit.text().strip()

        if self.error_label:
            self.error_label.setText("")
            # add effect shake text
            self.shake_animation = QPropertyAnimation(
                self.error_label, b"geometry")
            self.shake_animation.setDuration(100)
            self.shake_animation.setLoopCount(5)
        if self.username_data == '':
            self.error_label.setText(self.tr("Please enter a username."))
            self.start_shake_animation(self.error_label)
        elif self.password_data == '':
            self.error_label.setText(self.tr("Please enter a password."))
            self.start_shake_animation(self.error_label)
        else:
            main_controller.message_log = ""
            # save username to qsettings
            self.auth_qsettings.set_username(self.username_data)
            main_controller.login(
                username=self.username_data, password=self.password_data)
            if main_controller.message_log == "Something Went Wrong" and self.error_label:
                self.error_label.setText(
                    self.tr("Username or password is incorrect!"))
                self.start_shake_animation(self.error_label)

    def click_to_connect_server(self):
        self.server_ip_edit.clearFocus()
        self.server_port_edit.clearFocus()
        self.event_port_edit.clearFocus()
        server_ip = self.server_ip_edit.text()
        port_server = self.server_port_edit.text()
        port_websocket = self.event_port_edit.text()

        if server_ip and port_server and port_websocket:
            scanner = NetworkScanner(
                port_server=port_server, port_websocket=port_websocket)
            # if scanner.ping_with_ip_address(server_ip, port_websocket=port_websocket, port_server=port_server):
            #     # save ip server to qsettings
            #     self.auth_qsettings.set_ip_server(server_ip)
            #     self.auth_qsettings.set_port_server(port_server)
            #     self.auth_qsettings.set_port_websocket(port_websocket)
            #     # set IP server
            #     main_controller.api_client.set_server_info(
            #         server_ip, port_server, port_websocket)
            #     self.server_error_label.setText("")
            #     self.connect_server_signal.emit("Connect Server")
            #     self.remove_ui_server()
            #     self.setup_ui_login()
            # else:
            #     self.server_error_label.setText(
            #         self.tr("Can't connect to server"))
            #     # add effect shake text
            #     self.shake_animation = QPropertyAnimation(
            #         self.server_error_label, b"geometry")
            #     self.shake_animation.setDuration(100)
            #     self.shake_animation.setLoopCount(5)
            #     self.start_shake_animation(self.server_error_label)
            self.auth_qsettings.set_ip_server(server_ip)
            self.auth_qsettings.set_port_server(port_server)
            self.auth_qsettings.set_port_websocket(port_websocket)
            # set IP server
            main_controller.api_client.set_server_info(
                server_ip, port_server, port_websocket)
            self.server_error_label.setText("")
            self.connect_server_signal.emit("Connect Server")
            self.remove_ui_server()
            self.setup_ui_login()

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Draw the background image
        image = QPixmap(Style.PrimaryImage.frame_login)
        painter.drawPixmap(self.rect(), image)

        # Call the base implementation of paintEvent
        super().paintEvent(event)
        painter.end()

    def find_ip_server(self, change_server=False):
        if change_server:
            self.load_form_ui(change_server=True)
        else:
            ip_server = Config.SERVER_IP_ADDRESS_DJANGO_DEFAULT if Config.ENABLE_FORCE_SERVER_CONFIG else self.auth_qsettings.load_ip_server()
            port_websocket = Config.SERVER_EVENT_PORT_DEFAULT if Config.ENABLE_FORCE_SERVER_CONFIG else self.auth_qsettings.load_port_websocket()
            port_server = Config.SERVER_VMS_PORT_DEFAULT if Config.ENABLE_FORCE_SERVER_CONFIG else self.auth_qsettings.load_port_server()
            scanner = NetworkScanner(
                port_websocket=port_websocket, port_server=port_server)
            # connect signal to self.load_form_ui pass ip_server
            self.scan_ip_signal.connect(self.load_form_ui_intermediate)
            logger.debug(f"find_ip_server: {ip_server}")
            # ip_server exist and ping success
            if ip_server is not None and port_websocket is not None and port_server is not None:
                logger.debug(
                    'find_ip_server: ip_server exist and ping success')
                self.scan_ip_signal.emit(ip_server)
            else:
                logger.debug(
                    "find_ip_server: ip_server not exist or ping fail")
                # ip_server not exist or ping fail -> scan ip server
                self.scan_ip_server(scanner)

    def scan_ip_server(self, scanner: NetworkScanner):
        logger.debug('scan_ip_server: start scan ip server')

        def callback_scan_ip(ip_address):
            logger.debug(f"Found IP SERVER: {ip_address}")
            self.scan_ip_signal.emit(ip_address)
        # Call the scan_network method to initiate the network scanning
        scanner.scan_network(callback=callback_scan_ip)

    def load_form_ui_intermediate(self, ip_server):
        self.load_form_ui(ip_server=ip_server)

    def load_form_ui(self, ip_server=Config.SERVER_IP_ADDRESS_DEFAULT, change_server=False):
        if change_server:
            self.setup_ui_server()
        else:
            port_websocket = self.auth_qsettings.load_port_websocket()
            port_server = self.auth_qsettings.load_port_server()
            scanner = NetworkScanner(
                port_websocket=port_websocket, port_server=port_server)
            if ip_server and scanner.ping_with_ip_address(ip_server):
                # save ip server to qsettings
                self.auth_qsettings.set_ip_server(ip_server)
                # set IP server
                main_controller.api_client.set_server_info(
                    ip_server, port_server, port_websocket)
                # get refresh token
                refresh_token = self.auth_qsettings.get_refresh_token()
                # validate refresh token
                token_valid = main_controller.api_client.validate_refresh_token(
                    refresh_token)
                if token_valid:
                    self.login_success()
                else:
                    self.setup_ui_login()
            else:
                self.setup_ui_server()

    def start_shake_animation(self, widget: QWidget):
        start_rect = widget.geometry()
        shake_rect = QRect(start_rect.x() - 5, start_rect.y(),
                           start_rect.width(), start_rect.height())
        self.shake_animation.setStartValue(start_rect)
        self.shake_animation.setEndValue(shake_rect)
        self.shake_animation.start()
        # set old geometry after animation end
        self.shake_animation.finished.connect(
            lambda: widget.setGeometry(start_rect))

    def retranslateUi_formlogin(self):
        if self.username_edit is not None:
            self.username_edit.setPlaceholderText(
                QCoreApplication.translate("LoginForm", u"Username", None))
        if self.showPassAction is not None:
            self.showPassAction.setText(QCoreApplication.translate(
                "LoginForm", u"Show password", None))
        if self.password_edit is not None:
            self.password_edit.setPlaceholderText(
                QCoreApplication.translate("LoginForm", u"Password", None))
            if self.password_edit.echoMode() == QLineEdit.EchoMode.Password:
                self.showPassAction.setToolTip(
                    QCoreApplication.translate("LoginForm", u"Hide password", None))
            else:
                self.showPassAction.setToolTip(
                    QCoreApplication.translate("LoginForm", u"Show password", None))
        if self.forgot_label is not None:
            self.forgot_label = ClickableQLabel(QCoreApplication.translate(
                "LoginForm", u"Forgot your password", None))
        if self.sso_label is not None:
            self.sso_label = ClickableQLabel(QCoreApplication.translate(
                "LoginForm", u"Sign in with SSO", None))
        if self.login_button is not None:
            self.login_button = QPushButton(
                QCoreApplication.translate("LoginForm", u"LOGIN", None))
        if self.connect_server_button is not None:
            self.connect_server_button = QPushButton(
                QCoreApplication.translate("LoginForm", u"CONNECT", None))

        if self.username_data == '':
            self.error_label.setText(QCoreApplication.translate(
                "LoginForm", u"Please enter a username.", None))
        elif self.password_data == '':
            self.error_label.setText(QCoreApplication.translate(
                "LoginForm", u"Please enter a password.", None))
        else:
            if main_controller.message_log == "Something Went Wrong" and self.login_form.error_label:
                self.error_label.setText(
                    QCoreApplication.translate("LoginForm", u"Username or password is incorrect!", None))
        if self.server_error_label is not None:
            self.server_error_label.setText(QCoreApplication.translate(
                "LoginForm", u"Can't connect to server", None))

        self.intro_label.setText(QCoreApplication.translate("LoginForm", u"Intelligent Surveillance Platform", None))


class LoginScreen(QWidget):
    def __init__(self, parent=None, login_success=False, change_server=False, window_parent=None):
        super().__init__()
        self.window_parent = window_parent
        # self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        layout = QHBoxLayout()
        self.setLayout(layout)
        # set background to qwidget
        self.setAutoFillBackground(True)
        p = self.palette()
        p.setBrush(QPalette.Window, QBrush(
            QPixmap(Style.PrimaryImage.login_background)))
        self.setPalette(p)
        # Create and center the login form widget
        self.login_form = LoginForm(login_success, change_server)
        self.stack_widget = QStackedWidget()
        # Add spacer item to push login form to the right
        spacer = QSpacerItem(0, 0, QSizePolicy.Expanding, QSizePolicy.Minimum)
        layout.addItem(spacer)
        layout.addStretch()
        layout.addWidget(self.login_form)
        layout.setContentsMargins(0, 0, 0, 0)

        self.title_bar = LoginTitleBar(parent=self, window_parent=self.window_parent)
        self.title_widget = QWidget(self)
        self.title_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        self.title_layout = QVBoxLayout(self.title_widget)
        self.title_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.title_layout.setContentsMargins(0, 0, 2, 0)
        self.title_layout.addWidget(self.title_bar)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def retranslateUi_login(self):
        self.login_form.retranslateUi_formlogin()

    def resizeEvent(self, event):
        frame_size = self.frameGeometry()
        self.title_widget.setGeometry(0, 0, frame_size.size().width(), 32)

    def window_state_changed(self, state):
        if state == Qt.WindowState.WindowFullScreen:
            self.title_bar.stacked_button.setCurrentIndex(1)
        else:
            self.title_bar.stacked_button.setCurrentIndex(0)

from PySide6.QtWidgets import QApplication, QWidget
from PySide6.QtGui import QScreen, QPainter, QColor, QPen, QBrush
from PySide6.QtCore import Qt

class BorderScreen(QWidget):
    def __init__(self,parent = None, screen = None):
        super().__init__(parent)

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setGeometry(screen.geometry())  # Thiết lập kích thước và vị trí của widget bằng màn hình

        # self.setAutoFillBackground(True)
        self.setAttribute(Qt.WA_TranslucentBackground)

    def paintEvent(self, event):
        painter = QPainter(self)

        pen = QPen(QColor(255, 0, 0))
        pen.setWidth(8)
        painter.setPen(pen)

        brush = QBrush(QColor(0, 0, 0, 0))
        painter.setBrush(brush)

        painter.drawRect(self.rect())
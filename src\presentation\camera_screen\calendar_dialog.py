import logging
from PySide6.QtWidgets import QVBoxLayout, QWidget
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
from PySide6.QtCore import Qt, QRect,QUrl
from PySide6.QtQuickWidgets import QQuickWidget
logger = logging.getLogger(__name__)


class CalendarDialog(QWidget):
    def __init__(self,parent=None, timeLineManager = None):
        super(CalendarDialog, self).__init__(parent)
        self.parent = parent
        self.timeLineManager = timeLineManager
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setGeometry(QRect(parent.width() - 410, parent.height() - 640, 400, 520))
        self.is_show = True
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 10, 10, 10)
        self.widget = QWidget()
        self.widget.setObjectName('widget_main')
        self.widget.setStyleSheet(f"""
                        QWidget#widget_main {{
                            background-color: {main_controller.get_theme_attribute("Color", "widget_background_2")};
                            border-radius: 4px;
                            color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                            border: 1px solid {Style.PrimaryColor.primary};
                        }}
                    """)

        layout = QVBoxLayout(self.widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.setContentsMargins(0, 0, 0, 0)
        qml_widget = QQuickWidget()
        # qml_widget.setMinimumSize(QSize(200,150))
        qml_widget.engine().rootContext().setContextProperty('timeLineManager', self.timeLineManager)
        qml_widget.setSource(QUrl("qrc:src/common/qml/videoplayback/Calendar.qml"))
        qml_widget.setResizeMode(QQuickWidget.SizeRootObjectToView)
        layout.addWidget(qml_widget)
        main_layout.addWidget(self.widget)

import QtQuick
import QtQuick.Controls

Menu {
    id: aiSolutionsMenu
    title: "Bài toán AI"

    // Apply theme style
    Component.onCompleted: {
        // Apply style to menu
        background.border.width = 1
    }

    property int cameraPosition: -1
    property var aiSolutions: gridModel.aiSolutions
    property bool isLoading: true
    property bool hasError: false
    property string errorMessage: ""

    // Khi menu được mở, gọi fetchAiSolutions để đảm bảo dữ liệu được tải
    onAboutToShow: {
        // Reset trạng thái
        isLoading = true
        hasError = false
        errorMessage = ""

        // Gọi fetchAiSolutions sẽ không tải lại nếu đã có dữ liệu
        gridModel.fetchAiSolutions()

        // Nếu đã có dữ liệu, không hiển thị loading
        if (aiSolutions && aiSolutions.length > 0) {
            isLoading = false
        }
    }

    // <PERSON><PERSON> danh sách AI solutions thay đổi, cập nhật menu
    Connections {
        target: gridModel
        function onAiSolutionsChanged() {
            // Dữ liệu đã được cập nhật tự động thông qua binding
            isLoading = false
        }

        function onAiSolutionsError(errorMsg) {
            isLoading = false
            hasError = true
            errorMessage = errorMsg
        }
    }

    // Loading indicator
    BusyIndicator {
        id: busyIndicator
        running: isLoading
        anchors.centerIn: parent
        width: 24
        height: 24
        visible: running
    }

    // Error message
    MenuItem {
        text: "Lỗi: " + errorMessage
        enabled: false
        visible: hasError
    }

    // No AI solutions message
    MenuItem {
        text: "Không có bài toán AI nào"
        enabled: false
        visible: !isLoading && !hasError && aiSolutions.length === 0
    }

    // AI solutions list
    Repeater {
        id: aiSolutionsRepeater
        model: aiSolutions || []

        MenuItem {
            // Sử dụng text an toàn để tránh crash
            text: {
                if (!modelData) return "Unknown";
                if (typeof modelData === "string") return modelData;
                return modelData.name || "Unknown";
            }

            // Ngăn chặn sự kiện wheel để tránh crash
            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.NoButton
                onWheel: function(wheel) {
                    // Chỉ chặn sự kiện wheel, không làm gì khác
                    wheel.accepted = true;
                }
            }

            onTriggered: {
                try {
                    if (!modelData) {
                        console.error("Invalid modelData");
                        return;
                    }

                    var id = typeof modelData === "string" ? modelData : modelData.id;
                    if (id) {
                        gridModel.applyAiSolution(cameraPosition, id);
                    }
                } catch (e) {
                    console.error("Error triggering AI solution: " + e);
                }
            }
        }
    }

    // Refresh button
    MenuSeparator {
        visible: !isLoading && (hasError || aiSolutions.length === 0)
    }

    MenuItem {
        text: "Làm mới"
        visible: !isLoading && (hasError || aiSolutions.length === 0)

        onTriggered: {
            isLoading = true
            hasError = false
            errorMessage = ""
            gridModel.fetchAiSolutions()
        }
    }
}
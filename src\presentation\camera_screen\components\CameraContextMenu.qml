import QtQuick 2.15
import QtQuick.Controls 2.15

Menu {
    id: contextMenu

    // Properties để truyền từ GridItem
    property int position: -1
    property var gridModel: null

    // Debug properties
    Component.onCompleted: {
        console.log("CameraContextMenu created - position:", position, "gridModel:", gridModel)
    }

    onPositionChanged: {
        console.log("CameraContextMenu position changed:", position)
    }

    onGridModelChanged: {
        console.log("CameraContextMenu gridModel changed:", gridModel)
    }

    // Camera opening submenu
    Menu {
        id: cameraOpeningMenu
        title: "Mở camera sang"

        MenuItem {
            text: "Màn hình mới"
            onTriggered: {
                console.log("Creating new screen with camera from position:", position)
                if (gridModel) gridModel.openCameraInNewScreen(position)
            }
        }

        MenuItem {
            text: "Màn hình đã lưu mới"
            onTriggered: {
                console.log("Creating new saved view with camera from position:", position)
                if (gridModel) gridModel.openCameraInNewSavedView(position)
            }
        }

        MenuItem {
            text: "Cửa sổ giả lập mới"
            onTriggered: {
                console.log("Creating new virtual window with camera from position:", position)
                if (gridModel) gridModel.openCameraInNewVirtualWindow(position)
            }
        }

        MenuSeparator {}

        // Saved view submenu - hover controlled
        Menu {
            id: savedViewMainMenu
            title: "Màn hình đã lưu cũ"
            // Temporarily enable always to test
            enabled: true
            // enabled: gridModel && gridModel.availableTabs && gridModel.availableTabs.some(function(tab) { return tab.type === "SavedView" })

            // Only update submenu when this specific menu is about to show
            onAboutToShow: {
                console.log("savedViewMainMenu onAboutToShow triggered")
                console.log("contextMenu.gridModel:", contextMenu.gridModel)
                console.log("contextMenu.position:", contextMenu.position)
                console.log("availableTabs:", contextMenu.gridModel ? contextMenu.gridModel.availableTabs : "null")
                if (contextMenu.gridModel) {
                    contextMenu.gridModel.refreshAvailableTabs()
                    updateSavedViewMenuItems()
                }
            }

            function updateSavedViewMenuItems() {
                var savedViews = contextMenu.gridModel ? contextMenu.gridModel.availableTabs.filter(function(tab) { return tab.type === "SavedView" }) : []
                console.log("Found saved views:", savedViews.length)

                // Use real data if available, otherwise use test data
                if (savedViews.length > 0) {
                    // Update first saved view menu
                    if (savedViews.length > 0) {
                        firstSavedViewMenu.title = savedViews[0].name
                        firstSavedViewMenu.visible = true
                        firstSavedViewMenu.tabData = savedViews[0]
                    } else {
                        firstSavedViewMenu.visible = false
                    }

                    // Update second saved view menu
                    if (savedViews.length > 1) {
                        secondSavedViewMenu.title = savedViews[1].name
                        secondSavedViewMenu.visible = true
                        secondSavedViewMenu.tabData = savedViews[1]
                    } else {
                        secondSavedViewMenu.visible = false
                    }

                    // Update third saved view menu
                    if (savedViews.length > 2) {
                        thirdSavedViewMenu.title = savedViews[2].name
                        thirdSavedViewMenu.visible = true
                        thirdSavedViewMenu.tabData = savedViews[2]
                    } else {
                        thirdSavedViewMenu.visible = false
                    }

                    console.log("Updated saved view menus with real data")
                } else {
                    // Force show test menus for debugging when no real data
                    firstSavedViewMenu.title = "Test Saved View 1"
                    firstSavedViewMenu.visible = true
                    firstSavedViewMenu.tabData = null

                    secondSavedViewMenu.title = "Test Saved View 2"
                    secondSavedViewMenu.visible = true
                    secondSavedViewMenu.tabData = null

                    thirdSavedViewMenu.title = "Test Saved View 3"
                    thirdSavedViewMenu.visible = true
                    thirdSavedViewMenu.tabData = null

                    console.log("Force updated saved view menus with test data")
                }
            }

            // Pre-built static menu items (Tầng 3) - EMPTY
            Menu {
                id: firstSavedViewMenu
                title: ""
                visible: false
                property var tabData: null
                // Test MenuItem để đảm bảo submenu hiện
                MenuItem {
                    text: "Test Saved View 1"
                    visible: true
                }
            }

            Menu {
                id: secondSavedViewMenu
                title: ""
                visible: false
                property var tabData: null
                // Test MenuItem để đảm bảo submenu hiện
                MenuItem {
                    text: "Test Saved View 2"
                    visible: true
                }
            }

            Menu {
                id: thirdSavedViewMenu
                title: ""
                visible: false
                property var tabData: null
                // Test MenuItem để đảm bảo submenu hiện
                MenuItem {
                    text: "Test Saved View 3"
                    visible: true
                }
            }
        }

        // Virtual window submenu - hover controlled
        Menu {
            id: virtualWindowMainMenu
            title: "Cửa sổ giả lập cũ"
            // Temporarily enable always to test
            enabled: true
            // enabled: gridModel && gridModel.availableTabs && gridModel.availableTabs.some(function(tab) { return tab.type === "VirtualWindow" })

            // Only update submenu when this specific menu is about to show
            onAboutToShow: {
                console.log("virtualWindowMainMenu onAboutToShow triggered")
                console.log("gridModel:", gridModel)
                console.log("availableTabs:", gridModel ? gridModel.availableTabs : "null")
                if (gridModel) {
                    gridModel.refreshAvailableTabs()
                    updateVirtualWindowMenuItems()
                }
            }

            function updateVirtualWindowMenuItems() {
                var virtualWindows = gridModel ? gridModel.availableTabs.filter(function(tab) { return tab.type === "VirtualWindow" }) : []
                console.log("Found virtual windows:", virtualWindows.length)

                // Force show test menus for debugging
                firstVirtualWindowMenu.title = "Test Virtual Window 1"
                firstVirtualWindowMenu.visible = true
                firstVirtualWindowMenu.tabData = null

                secondVirtualWindowMenu.title = "Test Virtual Window 2"
                secondVirtualWindowMenu.visible = true
                secondVirtualWindowMenu.tabData = null

                thirdVirtualWindowMenu.title = "Test Virtual Window 3"
                thirdVirtualWindowMenu.visible = true
                thirdVirtualWindowMenu.tabData = null

                console.log("Force updated virtual window menus for testing")
            }

            // Pre-built static menu items - EMPTY
            Menu {
                id: firstVirtualWindowMenu
                title: ""
                visible: false
                property var tabData: null
                // Test MenuItem để đảm bảo submenu hiện
                MenuItem {
                    text: "Test Virtual Window 1"
                    visible: true
                }
            }

            Menu {
                id: secondVirtualWindowMenu
                title: ""
                visible: false
                property var tabData: null
                // Test MenuItem để đảm bảo submenu hiện
                MenuItem {
                    text: "Test Virtual Window 2"
                    visible: true
                }
            }

            Menu {
                id: thirdVirtualWindowMenu
                title: ""
                visible: false
                property var tabData: null
                // Test MenuItem để đảm bảo submenu hiện
                MenuItem {
                    text: "Test Virtual Window 3"
                    visible: true
                }
            }
        }
    }

    MenuSeparator {}

    MenuItem {
        text: "Thông tin camera"
        onTriggered: {
            if (gridModel) gridModel.showCameraInfo(position)
        }
    }

    MenuItem {
        text: "Xóa camera đã chọn"
        onTriggered: {
            if (!gridModel) return;

            // Thu thập các camera được chọn
            var selectedItems = [];
            // Cần access parent của GridItem để lấy activeItems
            var gridParent = contextMenu.parent.parent;
            if (gridParent && gridParent.activeItems) {
                for (var pos in gridParent.activeItems) {
                    if (gridParent.activeItems[pos] && gridParent.activeItems[pos].isSelected) {
                        selectedItems.push(parseInt(pos));
                    }
                }
            }

            console.log("Selected items: " + JSON.stringify(selectedItems));

            if (selectedItems.length > 0) {
                console.log("Deleting " + selectedItems.length + " selected cameras");
                if (selectedItems.length === 1) {
                    // Nếu chỉ có một camera được chọn, sử dụng removeVideo
                    console.log("Calling removeVideo for position: " + selectedItems[0]);
                    try {
                        // Đặt isSave = false trước khi xóa camera
                        if (gridModel) {
                            gridModel.isSave = false
                        }
                        var result1 = gridModel.removeVideo(selectedItems[0]);
                        console.log("removeVideo result: " + result1);
                    } catch (e) {
                        console.error("Error calling removeVideo: " + e);
                    }
                } else {
                    // Nếu có nhiều camera được chọn, sử dụng removeMultipleVideos
                    console.log("Calling removeMultipleVideos for positions: " + JSON.stringify(selectedItems));
                    try {
                        // Chuyển đổi mảng JavaScript thành mảng QML/Qt để truyền sang Python
                        var qtArray = [];
                        for (var i = 0; i < selectedItems.length; i++) {
                            qtArray.push(selectedItems[i]);
                        }
                        var result2 = gridModel.removeMultipleVideos(qtArray);
                        console.log("removeMultipleVideos result: " + result2);
                    } catch (e) {
                        console.error("Error calling removeMultipleVideos: " + e);
                    }
                }
            } else {
                console.log("No cameras selected");
            }
        }
    }

    Menu {
        title: "Luồng video"
        MenuItem {
            text: "Luồng chính"
            onTriggered: gridModel ? gridModel.switchToMainStream(position) : {}
        }
        MenuItem {
            text: "Luồng phụ"
            onTriggered: gridModel ? gridModel.switchToSubStream(position) : {}
        }
        MenuItem {
            text: "Luồng AI"
            onTriggered: gridModel ? gridModel.switchToAiStream(position) : {}
        }
    }
}

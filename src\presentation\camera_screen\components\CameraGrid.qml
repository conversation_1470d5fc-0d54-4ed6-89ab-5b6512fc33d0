import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Dialogs
import QtMultimedia
import QtQuick.Effects  // For DropShadow
import Qt5Compat.GraphicalEffects  // For older Qt versions
import "qrc:/src/common/qml/dialogs"

/**
 * CameraGrid.qml - Container chính quản lý grid layout
 *
 * CHỨC NĂNG CHÍNH:
 * - Quản lý grid layout (tối đa 12x12 = 144 cells)
 * - Xử lý drag & drop camera từ tree view
 * - Tạo và quản lý GridItem instances
 * - Tính toán vị trí cell và kích thước
 * - Vẽ grid lines với Canvas cho hiệu suất tốt
 * - Theo dõi camera count và cập nhật UI
 */
Item {
    // 1. ID and basic properties
    id: cameraGrid
    focus: true // Đảm bảo Item nhận được focus để xử lý keyboard events

    // 2. Custom properties
    /**
     * THEME PROPERTIES: Màu sắc và theme với giá trị mặc định
     * Tác dụng: Lấy theme từ gridModel hoặc sử dụng giá trị mặc định
     */
    property bool isDarkTheme: gridModel ? gridModel.isDarkTheme : true                           // Dark/Light theme
    property color backgroundColor: gridModel ? gridModel.backgroundColor : "#1a1a1a"            // Màu nền chính
    property color foregroundColor: gridModel ? gridModel.foregroundColor : "#ffffff"            // Màu chữ chính
    property color borderColor: gridModel ? gridModel.borderColor : "#333333"                    // Màu viền
    property color headerColor: gridModel ? gridModel.headerColor : "#1f2937"                    // Màu header
    property color itemBackgroundColor: gridModel ? gridModel.itemBackgroundColor : "#2d2d2d"   // Màu nền item
    property color hoverColor: gridModel ? gridModel.hoverColor : "#3d3d3d"                      // Màu hover
    property color dragActiveColor: gridModel ? gridModel.dragActiveColor : "#4d4d4d"            // Màu khi drag
    property color videoBackgroundColor: gridModel ? gridModel.videoBackgroundColor : "#1f2937" // Màu nền video

    /**
     * GRID PROPERTIES: Kích thước grid và giới hạn
     */
    property int baseColumns: 1    // Số cột mặc định khi khởi tạo
    property int baseRows: 1       // Số hàng mặc định khi khởi tạo
    property int maxColumns: 12    // Giới hạn tối đa số cột (12x12 = 144 cells)
    property int maxRows: 12       // Giới hạn tối đa số hàng

    /**
     * STATUS PROPERTIES: Trạng thái và đếm số lượng
     */
    property int cameraCount: 0                // Đếm số camera hiện có trong grid
    property bool gridResizeHighlight: false   // Highlight khi resize grid

    // 3. Signals
    // No custom signals defined for this component

    // 4. States and transitions
    // No custom states defined for this component

    // 5. Functions
    /**
     * FUNCTION: Hiển thị grid resize highlight
     * Tác dụng: Bật highlight và restart timer để tự động ẩn
     */
    function showGridResizeHighlight() {
        gridResizeHighlight = true
        gridResizeTimer.restart()
    }

    /**
     * FUNCTION: Cập nhật số lượng camera trong grid
     * Tác dụng: Đếm số GridItem active và gửi thông tin đến grid_manager
     */
    function updateCameraCount() {
        var count = 0
        // Duyệt qua tất cả GridItem active và đếm những item có camera_id
        for (var pos in videoGrid.activeItems) {
            if (videoGrid.activeItems[pos] && videoGrid.activeItems[pos].camera_id) {
                count++
            }
        }
        cameraCount = count
        // console.log("Camera count updated: " + count)

        // Gửi camera count đến grid_manager để đồng bộ dữ liệu
        if (gridModel) {
            gridModel.updateCameraCount(count)
        }
    }

    /**
     * FUNCTION: Kiểm tra giới hạn camera trước khi thêm
     * Tác dụng: Đảm bảo không vượt quá 144 cells và hiển thị dialog cảnh báo
     */
    function checkCameraLimit(camerasToAdd) {
        // Tính đúng số cell bị chiếm thay vì chỉ đếm camera objects
        var currentOccupiedCells = 0
        var currentCameraCount = 0

        // Tính số cell thực sự bị chiếm (bao gồm camera đã resize)
        for (var pos in videoGrid.activeItems) {
            var item = videoGrid.activeItems[pos]
            if (item && item.camera_id) {
                currentCameraCount++

                // Lấy kích thước thực của camera từ gridModel
                if (gridModel) {
                    var dimensions = gridModel.getCellDimensions(pos)
                    if (dimensions) {
                        var cellsOccupied = dimensions.width * dimensions.height
                        currentOccupiedCells += cellsOccupied
                        console.log(`Camera at pos ${pos}: ${dimensions.width}x${dimensions.height} = ${cellsOccupied} cells`)
                    } else {
                        currentOccupiedCells += 1 // Default 1x1 nếu không có dimensions
                    }
                } else {
                    currentOccupiedCells += 1 // Default 1x1 nếu không có gridModel
                }
            }
        }

        var maxCells = 144 // Giới hạn tối đa 12x12 = 144 cells
        var totalCellsAfterAdd = currentOccupiedCells + camerasToAdd // Giả sử mỗi camera mới chiếm 1 cell
        var availableCells = maxCells - currentOccupiedCells

        console.log(`Cell limit check: occupied=${currentOccupiedCells}, adding=${camerasToAdd}, total=${totalCellsAfterAdd}, max=${maxCells}, available=${availableCells}`)

        if (totalCellsAfterAdd > maxCells) {
            // Hiển thị dialog cảnh báo với thông tin chính xác
            showGridFullDialog(currentCameraCount, currentOccupiedCells, camerasToAdd, maxCells)
            return false
        }

        return true
    }

    // Hiển thị dialog cảnh báo khi grid đầy
    function showGridFullDialog(currentCameraCount, currentOccupiedCells, addingCount, maxCells) {
        var availableCells = maxCells - currentOccupiedCells
        var message = qsTr("Grid is full! Cannot add %1 camera(s).\nCurrent: %2/%3 cameras\nOccupied cells: %4/%5\nAvailable space: %6 cell(s)")
                        .arg(addingCount)
                        .arg(currentCameraCount)
                        .arg(maxCells)
                        .arg(currentOccupiedCells)
                        .arg(maxCells)
                        .arg(availableCells)

        console.warn("Grid full:", message)

        // Hiển thị dialog mới
        gridFullDialog.show(message)
    }

    // Hàm tiện ích để kiểm tra xem một vị trí có sẵn sàng để thêm camera không
    function isPositionAvailable(position) {
        // Kiểm tra tham số đầu vào
        if (position === undefined || position < 0 || !gridModel) {
            console.warn("Invalid position or gridModel not available:", position)
            return false
        }

        // Kiểm tra xem grid có đang ở chế độ fullscreen không
        var isFullscreenMode = gridModel && gridModel.isMaximized

        // Sử dụng kích thước grid thích hợp
        var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns
        var effectiveRows = isFullscreenMode ? gridModel.previousRows : videoGrid.rows

        // Tính toán hàng và cột
        var row = Math.floor(position / effectiveColumns)
        var col = position % effectiveColumns

        // Kiểm tra xem vị trí có nằm trong lưới không
        if (row >= effectiveRows || col >= effectiveColumns) {
            return false
        }

        // Kiểm tra xem vị trí đã có camera chưa
        if (videoGrid.activeItems[position]) {
            return false
        }

        // Kiểm tra xem vị trí có thuộc về camera đã resize không
        for (var pos in videoGrid.activeItems) {
            if (!videoGrid.activeItems[pos]) continue

            // Lấy kích thước của camera
            var camDimensions = gridModel.getCellDimensions(parseInt(pos))
            var camWidth = camDimensions.width
            var camHeight = camDimensions.height

            // Tính toán vị trí của camera
            var camRow = Math.floor(parseInt(pos) / effectiveColumns)
            var camCol = parseInt(pos) % effectiveColumns

            // Kiểm tra xem vị trí hiện tại có nằm trong phạm vi của camera không
            if (row >= camRow && row < camRow + camHeight &&
                col >= camCol && col < camCol + camWidth) {
                return false
            }
        }

        return true
    }

    // Hàm tìm vị trí trống đầu tiên
    function findFirstAvailablePosition() {
        if (!gridModel) return 0

        // Kiểm tra xem grid có đang ở chế độ fullscreen không
        var isFullscreenMode = gridModel && gridModel.isMaximized

        // Sử dụng kích thước grid thích hợp
        var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns
        var effectiveRows = isFullscreenMode ? gridModel.previousRows : videoGrid.rows

        var maxPosition = effectiveRows * effectiveColumns
        console.log(`findFirstAvailablePosition: maxPosition=${maxPosition}, effectiveColumns=${effectiveColumns}, effectiveRows=${effectiveRows}, isFullscreenMode=${isFullscreenMode}`)

        // Debug: Log current active cameras and their dimensions
        console.log("DEBUG: Current active cameras:")
        for (var pos in videoGrid.activeItems) {
            var item = videoGrid.activeItems[pos]
            if (item && item.camera_id && gridModel) {
                var dimensions = gridModel.getCellDimensions(pos)
                console.log(`  Camera at pos ${pos}: ${dimensions.width}x${dimensions.height}`)
            }
        }

        for (var i = 0; i < maxPosition; i++) {
            var available = isPositionAvailable(i)
            console.log(`DEBUG: Position ${i} available: ${available}`)
            if (available) {
                console.log(`Found first available position: ${i}`)
                return i
            }
        }
        console.log(`No available position found, using nextPosition: ${gridModel.nextPosition}`)
        return gridModel.nextPosition // Sử dụng nextPosition từ gridModel nếu không tìm thấy
    }

    // Hàm tìm nhiều vị trí trống rời rạc - tính đúng các cell bị camera resize chiếm
    function findAvailablePositions(count) {
        if (!gridModel) return []

        // Kiểm tra xem grid có đang ở chế độ fullscreen không
        var isFullscreenMode = gridModel && gridModel.isMaximized

        // Sử dụng kích thước grid thích hợp
        var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns
        var effectiveRows = isFullscreenMode ? gridModel.previousRows : videoGrid.rows

        var maxPosition = effectiveRows * effectiveColumns
        console.log(`findAvailablePositions: count=${count}, maxPosition=${maxPosition}`)

        var availablePositions = []

        // Tìm các vị trí trống - tính đúng các cell bị camera resize chiếm
        for (var i = 0; i < maxPosition && availablePositions.length < count; i++) {
            if (isPositionAvailable(i)) {
                availablePositions.push(i)
            }
        }

        console.log(`Found ${availablePositions.length} available positions: ${JSON.stringify(availablePositions)}`)
        return availablePositions
    }

    // Hàm kiểm tra xem một dãy vị trí có sẵn sàng không
    function arePositionsAvailable(startPosition, count) {
        if (!gridModel) return false

        // Kiểm tra xem grid có đang ở chế độ fullscreen không
        var isFullscreenMode = gridModel && gridModel.isMaximized

        // Sử dụng kích thước grid thích hợp
        var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns

        for (var i = 0; i < count; i++) {
            // Tính toán vị trí thực tế trong grid
            var position = startPosition + i

            // Kiểm tra xem có cần nhảy sang hàng mới không
            if (i > 0 && (startPosition % effectiveColumns) + i >= effectiveColumns) {
                // Tính toán vị trí ở hàng tiếp theo
                var row = Math.floor(startPosition / effectiveColumns) + Math.floor((startPosition % effectiveColumns + i) / effectiveColumns)
                var col = (startPosition % effectiveColumns + i) % effectiveColumns
                position = row * effectiveColumns + col
            }

            if (!isPositionAvailable(position)) {
                return false
            }
        }
        return true
    }

    // Hàm mở rộng grid giống onwheel+ctrl cho group drop khi auto_resize_enabled = false
    function expandGridLikeOnWheelCtrl(targetColumns, targetRows) {
        if (!gridModel) return false

        try {
            // Đặt isSave = false trước khi thay đổi grid (giống onwheel+ctrl)
            gridModel.isSave = false

            var oldColumns = gridModel.columns
            var oldRows = gridModel.rows

            console.log(`expandGridLikeOnWheelCtrl: from ${oldColumns}x${oldRows} to ${targetColumns}x${targetRows}`)

            // Thu thập vị trí hiện tại của tất cả camera (giống onwheel+ctrl)
            var positions = {}
            for (var pos in videoGrid.activeItems) {
                positions[pos] = parseInt(pos)
            }

            // Hàm chuyển đổi vị trí (giống hệt onwheel+ctrl)
            function convertIndex(oldIndex, oldSize, newSize) {
                var row = Math.floor(oldIndex / oldSize)
                var col = oldIndex % oldSize
                return row * newSize + col
            }

            // Tính toán vị trí mới cho tất cả camera (giống onwheel+ctrl)
            var newPositions = {}
            for (var key in positions) {
                newPositions[key] = convertIndex(positions[key], oldColumns, targetColumns)
            }

            // ✅ REFACTORED: Use signal instead of direct call
            gridModel.setGridSizeRequested(targetColumns, targetRows)

            // Di chuyển các camera sang vị trí mới (giống hệt onwheel+ctrl)
            var sortedPositions = Object.keys(newPositions).sort((a, b) => parseInt(b) - parseInt(a))
            for (var oldPos of sortedPositions) {
                var newPos = newPositions[oldPos]
                var item = videoGrid.activeItems[oldPos]
                if (item) {
                    var currentDimensions = gridModel.getCellDimensions(parseInt(oldPos))

                    // Cập nhật position và vị trí hiển thị (giống onwheel+ctrl)
                    item.position = newPos

                    // Tính toán kích thước mới cho camera (giống hệt onwheel+ctrl)
                    item.width = (videoGrid.width / gridModel.columns) * currentDimensions.width
                    item.height = (videoGrid.height / gridModel.rows) * currentDimensions.height
                    item.x = (newPos % gridModel.columns) * (videoGrid.width / gridModel.columns)
                    item.y = Math.floor(newPos / gridModel.columns) * (videoGrid.height / gridModel.rows)

                    // Cập nhật trong activeItems (giống onwheel+ctrl)
                    delete videoGrid.activeItems[oldPos]
                    videoGrid.activeItems[newPos] = item

                    // Thông báo cho GridModel chỉ khi position thực sự thay đổi (giống onwheel+ctrl)
                    if (parseInt(oldPos) !== newPos) {
                        gridModel.updateVideoPosition(parseInt(oldPos), newPos)
                    }
                    // Cập nhật kích thước camera sau khi di chuyển (giống onwheel+ctrl)
                    gridModel.updateCellDimensions(newPos, currentDimensions.width, currentDimensions.height)
                }
            }

            console.log(`Grid expansion successful: ${oldColumns}x${oldRows} -> ${targetColumns}x${targetRows}`)
            return true

        } catch (error) {
            console.error(`Error expanding grid: ${error}`)
            return false
        }
    }

    // 6. Event handlers and connections
    /**
     * EVENT: Component initialization
     * Tác dụng: Thiết lập focus và khởi tạo camera count
     */
    Component.onCompleted: {
        // console.log("CameraGrid completed, requesting focus")
        forceActiveFocus() // Đảm bảo nhận được focus khi khởi tạo

        if (!gridModel) {
            console.warn("gridModel is not initialized")
            return
        }

        // Khởi tạo đếm số camera hiện có trong grid
        updateCameraCount()
    }

    // Xử lý phím tắt ở cấp độ cao nhất
    Keys.onPressed: function(event) {
        // Xử lý phím tắt Ctrl+A để chọn tất cả các camera
        if (event.key === Qt.Key_A && event.modifiers & Qt.ControlModifier) {
            console.log("Ctrl+A pressed at top level")

            // Tối ưu: Kiểm tra nhanh xem tất cả đã được chọn chưa
            var allSelected = true
            var anySelected = false
            var totalItems = 0

            for (var checkPos in videoGrid.activeItems) {
                totalItems++
                if (videoGrid.activeItems[checkPos].isSelected) {
                    anySelected = true
                } else {
                    allSelected = false
                }
            }

            // Chỉ cập nhật khi cần thiết
            if (totalItems > 0) {
                var newState = !allSelected
                console.log("Setting all cameras selected state to: " + newState)

                for (var pos in videoGrid.activeItems) {
                    videoGrid.activeItems[pos].isSelected = newState
                }
            }

            event.accepted = true
        }

        // Xử lý phím Shift+Delete để remove tất cả camera có cùng model
        else if (event.key === Qt.Key_Delete && event.modifiers) {
            console.log("Shift+Delete pressed - removing cameras by model")

            // Thu thập các camera được chọn
            var selectedItems = []
            for (var pos in videoGrid.activeItems) {
                if (videoGrid.activeItems[pos] && videoGrid.activeItems[pos].isSelected) {
                    selectedItems.push(parseInt(pos))
                }
            }

            if (selectedItems.length > 0) {
                // Sử dụng camera đầu tiên được chọn để xác định model
                var firstPosition = selectedItems[0]
                console.log("Removing all cameras with same model as position: " + firstPosition)

                try {
                    var result = gridModel.removeCamerasByModelFromPosition(firstPosition)
                    console.log("removeCamerasByModelFromPosition result: " + result)
                } catch (e) {
                    console.error("Error calling removeCamerasByModelFromPosition: " + e)
                }

                event.accepted = true
            } else {
                console.log("No cameras selected for model removal")
            }
        }
    }

    // Xử lý phím Delete ở cấp độ cao nhất
    Keys.onDeletePressed: {
        // console.log("Delete key pressed at top level")
        // Thu thập các camera được chọn
        var selectedItems = []
        for (var pos in videoGrid.activeItems) {
            if (videoGrid.activeItems[pos] && videoGrid.activeItems[pos].isSelected) {
                selectedItems.push(parseInt(pos))
            }
        }

        // console.log("Selected items (top level): " + JSON.stringify(selectedItems))

        if (selectedItems.length > 0) {
            // Emit signal to grid manager instead of direct handling
            if (gridModel) {
                // Đặt isSave = false trước khi xóa
                gridModel.isSave = false
                // Gửi yêu cầu xóa items đến grid manager
                gridModel.removeItemsRequested(selectedItems)
            }
            event.accepted = true
        } else {
            // console.log("No cameras selected (top level)")
        }
    }






    /**
     * CONNECTIONS: Xử lý thay đổi video info từ GridModel
     * Tác dụng: Tạo/xóa/cập nhật GridItem khi có thay đổi camera
     */
    Connections {
        target: gridModel

        /**
         * SIGNAL HANDLER: onVideoInfoChanged
         * Tác dụng: Xử lý thêm/xóa/cập nhật camera trong grid
         * Params: position, camera_model, isPlaying, supportsPTZ
         */
        function onVideoInfoChanged(position, camera_model, isPlaying, supportsPTZ) {
            // Use context property to determine if this is virtual grid or main grid
            var gridType = isVirtualGrid ? "[VIRTUAL]" : "[MAIN]"

            console.log(`${gridType} onVideoInfoChanged: position=${position}, camera_model=${camera_model ? camera_model.id : null}, isPlaying=${isPlaying}, supportsPTZ=${supportsPTZ}`)

            var item = videoGrid.activeItems[position]
            console.log(`${gridType} Current item at position ${position}: ${item ? "EXISTS" : "NULL"}`)

            // Xử lý xóa item khi camera_model = null
            if (camera_model === null) {
                console.log(`${gridType} REMOVE OPERATION: Removing item at position ${position}`)
                if (item) {
                    console.log(`${gridType} Item exists, stopping video and destroying...`)
                    if (item.isPlaying) {
                        item.isPlaying = false  // Dừng video trước khi xóa
                        console.log(`${gridType} Video stopped for position ${position}`)
                    }

                    // Sử dụng Qt.callLater để tránh conflict trong signal handling
                    Qt.callLater(function() {
                        if (item) {
                            console.log(`${gridType} Destroying item at position ${position}`)
                            item.destroy()                          // Destroy QML object
                            delete videoGrid.activeItems[position]  // Remove from dictionary
                            updateCameraCount()                     // Cập nhật camera count
                            console.log(`${gridType} Item destroyed and removed from activeItems`)
                        }
                    })
                } else {
                    console.log(`${gridType} No item found at position ${position} to remove`)
                }
                return
            }

            // Create GridItem if it doesn't exist
            if (!item) {
                console.log(`${gridType} CREATE OPERATION: Creating new GridItem for position ${position}`)
                console.log(`${gridType} Camera details: id=${camera_model.id}, name=${camera_model.name || 'Unknown'}`)
                item = videoGrid.createGridItem(position,camera_model)
                if (item) {
                    console.log(`${gridType} GridItem created successfully for position ${position}`)
                    item.camera_id = camera_model.id
                    item.isPlaying = isPlaying
                    item.supportsPTZ = supportsPTZ
                    console.log(`${gridType} Item properties set: camera_id=${item.camera_id}, isPlaying=${item.isPlaying}, supportsPTZ=${item.supportsPTZ}`)
                    // Update camera count after adding camera
                    updateCameraCount()
                    console.log(`${gridType} Camera count updated after creation`)
                } else {
                    console.log(`${gridType} FAILED to create GridItem for position ${position}`)
                }
            } else {
                // Update existing GridItem
                console.log(`${gridType} UPDATE OPERATION: Updating existing GridItem at position ${position}`)
                console.log(`${gridType} Current item: camera_id=${item.camera_id}, isPlaying=${item.isPlaying}, supportsPTZ=${item.supportsPTZ}`)
                console.log(`${gridType} New values: camera_id=${camera_model.id}, isPlaying=${isPlaying}, supportsPTZ=${supportsPTZ}`)

                // If camera_id is changing
                if (camera_model.id && item.camera_id !== camera_model.id) {
                    console.log(`${gridType} CAMERA SWAP: Changing camera from ${item.camera_id} to ${camera_model.id}`)
                    var wasPlaying = item.isPlaying
                    if (wasPlaying) {
                        item.isPlaying = false
                        console.log(`${gridType} Stopped previous camera video`)
                    }

                    item.camera_id = camera_model.id
                    console.log(`${gridType} Camera ID updated to: ${item.camera_id}`)

                    if (wasPlaying && isPlaying) {
                        item.isPlaying = true
                        console.log(`${gridType} Started new camera video (was playing before)`)
                    } else {
                        item.isPlaying = isPlaying
                        console.log(`${gridType} Set playing state to: ${item.isPlaying}`)
                    }
                    // Always update PTZ support
                    item.supportsPTZ = supportsPTZ
                    console.log(`${gridType} PTZ support updated to: ${item.supportsPTZ}`)
                    // Update camera count when camera_id changes
                    console.log(`${gridType} Camera swap completed at position ${position}`)
                } else {
                    // Just update playing state and PTZ support
                    console.log(`${gridType} PROPERTY UPDATE: Same camera, updating properties only`)
                    item.isPlaying = isPlaying
                    item.supportsPTZ = supportsPTZ
                    console.log(`${gridType} Updated properties: isPlaying=${item.isPlaying}, supportsPTZ=${item.supportsPTZ}`)
                }
            }
        }
        
        function onMapInfoChanged(position, mapData){
            var item = videoGrid.activeItems[position]
            // // console.log(`[DEBUG] item: ${item}`)
            console.log("onMapInfoChanged ",mapData)

            // Handle removing item if camera_id is empty
            if (mapData === null || mapData === undefined) {
                if (item) {
                    if (item.isPlaying) {
                        item.isPlaying = false
                    }

                    Qt.callLater(function() {
                        if (item) {
                            // console.log(`[DEBUG] Destroying item at position ${position}`)
                            item.destroy()
                            delete videoGrid.activeItems[position]
                            // Update camera count after removing camera
                            updateCameraCount()
                        }
                    })
                }
                return
            }

            // Create GridItem if it doesn't exist
            if (!item) {
                // console.log(`[DEBUG] Creating new GridItem for position ${position}`)
                item = videoGrid.createGridItem(position, mapData)
                if (item) {
                    // item.itemData = mapData.data
                    // // console.log(`[DEBUG] Updated GridItem at position ${position}: data=${JSON.stringify(item.itemData)}`)
                    // Update camera count after adding camera
                    updateCameraCount()
                } else {
                    // console.log(`[DEBUG] Failed to create GridItem for position ${position}`)
                }
            } else {
                // Update existing GridItem
                // console.log(`[DEBUG] Updating existing GridItem at position ${position}`)

                // If camera_id is changing
                if (item.camera_id) {
                    item.isPlaying = false
                    item.itemData = mapData.data
                    // Update camera count when camera_id changes
                    // // console.log(`[DEBUG] Updated GridItem at position ${position}: data=${JSON.stringify(item.data)}`)
                } else {
                    // Just update playing state
                    // console.log(`[DEBUG] Updated GridItem properties at position ${position}: type=${item.type}`)
                }
            }
        }
        function onRemoveAllItemChanged() {
            console.log("onRemoveAllItemChanged")
            // Xử lý clean các item ở đây
        }

    }

    // Handle layout changes
    Connections {
        target: gridModel
        function onLayoutChanged(activePositions) {
            // console.log("Layout changed. Active positions:", JSON.stringify(activePositions))

            // Cập nhật lại vị trí của các item
            for (var i = 0; i < activePositions.length; i++) {
                var position = activePositions[i]
                var item = videoGrid.activeItems[position]

                if (item) {
                    // Cập nhật vị trí của item nếu cần
                    var col = position % gridModel.columns
                    var row = Math.floor(position / gridModel.columns)

                    // Lấy kích thước hiện tại của camera từ GridModel
                    var dimensions = gridModel.getCellDimensions(position)
                    var widthCells = dimensions.width
                    var heightCells = dimensions.height

                    item.x = videoGrid.calculateX(position)
                    item.y = videoGrid.calculateY(position)
                    item.width = videoGrid.calculateItemWidth() * widthCells
                    item.height = videoGrid.calculateItemHeight() * heightCells
                }
            }

            // ✅ FIXED: More conservative approach to removing items
            // Only remove items that are definitely orphaned (not just missing from activePositions)
            // This prevents accidental removal during grid resize operations
            for (var pos in videoGrid.activeItems) {
                var shouldRemove = false

                // Only remove if the position is clearly invalid or the item is corrupted
                if (!videoGrid.activeItems[pos] ||
                    !videoGrid.activeItems[pos].itemData ||
                    parseInt(pos) < 0) {
                    shouldRemove = true
                }

                if (shouldRemove) {
                    if (videoGrid.activeItems[pos]) {
                        videoGrid.activeItems[pos].destroy()
                        delete videoGrid.activeItems[pos]
                    }
                }
            }
        }
    }

    // Handle action results from grid manager
    Connections {
        target: gridModel
        function onActionCompleted(actionType, success, message) {
            console.log("Action completed:", actionType, "Success:", success, "Message:", message)

            // Handle specific action results
            switch(actionType) {
                case "removeItems":
                    if (success) {
                        // Update camera count after successful removal
                        updateCameraCount()
                    }
                    break
                case "addCamera":
                case "addGroup":
                    if (success) {
                        // Camera count will be updated by videoInfoChanged signal
                    }
                    break
                case "clearGrid":
                    if (success) {
                        // Clear all items from UI
                        for (var pos in videoGrid.activeItems) {
                            if (videoGrid.activeItems[pos]) {
                                videoGrid.activeItems[pos].destroy()
                                delete videoGrid.activeItems[pos]
                            }
                        }
                        updateCameraCount()
                    }
                    break
                case "loadGrid":
                    if (success) {
                        // Grid items will be created by videoInfoChanged signals
                        console.log("Grid loaded successfully")
                    }
                    break
                case "toggleFullscreen":
                    if (success) {
                        console.log("Fullscreen toggled successfully")
                    }
                    break
                case "swapItems":
                    if (success) {
                        console.log("Items swapped successfully")
                    }
                    break
            }
        }

        // Handle grid state changes
        function onGridStateChanged(columns, rows, isMaximized) {
            console.log("Grid state changed - columns:", columns, "rows:", rows, "maximized:", isMaximized)
            // Grid will auto-reorganize via property bindings
        }
    }

    // 7. Child items
    /**
     * UI COMPONENT: Timer tự động ẩn grid resize highlight
     * Tác dụng: Ẩn highlight sau 1.5 giây để không làm phiền user
     */
    Timer {
        id: gridResizeTimer
        interval: 1500 // Ẩn sau 1.5 giây
        repeat: false
        onTriggered: {
            gridResizeHighlight = false
        }
    }

    // Main grid container
    Rectangle {
        id: gridContainer
        anchors.fill: parent
        clip: true
        color: backgroundColor

        // Camera count display
        Rectangle {
            id: cameraCountDisplay
            anchors.top: parent.top
            anchors.right: parent.right
            anchors.margins: 10
            width: cameraCountText.width + 20
            height: 30
            radius: 5
            color: isDarkTheme ? "transparent" : "transparent"
            border.color: isDarkTheme ? "#3d3d3d" : "#d1d5db"
            border.width: 1
            z: 10

            Row {
                anchors.centerIn: parent
                spacing: 5

                Text {
                    id: cameraCountText
                    anchors.verticalCenter: parent.verticalCenter
                    text: qsTr("Cameras: ") + cameraCount
                    color: isDarkTheme ? "#ffffff" : "#1f2937"
                    font.pixelSize: 12
                }
            }
        }

        // Grid lines - tối ưu hóa để cải thiện hiệu suất
        Grid {
            id: gridLines
            x: 10
            y: 10
            width: parent.width - 20
            height: parent.height - 20
            columns: gridModel ? gridModel.columns : baseColumns
            rows: gridModel ? gridModel.rows : baseRows
            z: 0

            // Thêm animation cho grid lines để đồng bộ với items
            Behavior on columns {
                NumberAnimation {
                    duration: 300
                    easing.type: Easing.OutCubic
                }
            }

            Behavior on rows {
                NumberAnimation {
                    duration: 300
                    easing.type: Easing.OutCubic
                }
            }

            // Loại bỏ các hiệu ứng không cần thiết
            layer.enabled: false

            // Tạo grid cells với hiệu suất tốt hơn - sử dụng một Rectangle duy nhất thay vì nhiều
            Rectangle {
                id: gridBackground
                width: parent.width
                height: parent.height
                x: 0
                y: 0
                color: "transparent"

                // Vẽ grid lines bằng Canvas thay vì nhiều Rectangle
                Canvas {
                    id: gridCanvas
                    width: parent.width
                    height: parent.height
                    x: 0
                    y: 0
                    visible: true

                    // Properties để animate grid size
                    property real animatedColumns: gridLines.columns
                    property real animatedRows: gridLines.rows
                    property bool isAnimating: false

                    // Timer để repaint canvas trong quá trình animation
                    Timer {
                        id: animationTimer
                        interval: 16 // 60fps
                        repeat: true
                        running: gridCanvas.isAnimating
                        onTriggered: gridCanvas.requestPaint()
                    }

                    // Animation cho columns - đồng bộ với GridItem animation
                    Behavior on animatedColumns {
                        NumberAnimation {
                            duration: 300
                            easing.type: Easing.InOutQuad
                            onRunningChanged: {
                                gridCanvas.isAnimating = running
                                if (!running) {
                                    gridCanvas.requestPaint()
                                }
                            }
                        }
                    }

                    // Animation cho rows - đồng bộ với GridItem animation
                    Behavior on animatedRows {
                        NumberAnimation {
                            duration: 300
                            easing.type: Easing.InOutQuad
                            onRunningChanged: {
                                gridCanvas.isAnimating = running
                                if (!running) {
                                    gridCanvas.requestPaint()
                                }
                            }
                        }
                    }

                    // Vẽ lại grid lines khi cần thiết
                    onPaint: {
                        var ctx = getContext("2d");
                        ctx.reset();

                        // Chỉ vẽ grid lines khi cần thiết hoặc khi grid resize highlight
                        if (!dropArea.containsDrag && !gridResizeHighlight) return;

                        var cellWidth = width / animatedColumns;
                        var cellHeight = height / animatedRows;

                        // Use neon colors when grid resize highlight is active
                        if (gridResizeHighlight) {
                            ctx.strokeStyle = isDarkTheme ? "#15ff00" : "#FF00FF"; // Cyan/Magenta neon
                            ctx.lineWidth = 1;
                        } else {
                            ctx.strokeStyle = "transparent";
                            ctx.lineWidth = 0;
                        }

                        // Vẽ các đường dọc
                        for (var i = 1; i < animatedColumns; i++) {
                            ctx.beginPath();
                            ctx.moveTo(i * cellWidth, 0);
                            ctx.lineTo(i * cellWidth, height);
                            ctx.stroke();
                        }

                        // Vẽ các đường ngang
                        for (var j = 1; j < animatedRows; j++) {
                            ctx.beginPath();
                            ctx.moveTo(0, j * cellHeight);
                            ctx.lineTo(width, j * cellHeight);
                            ctx.stroke();
                        }
                    }

                    // Cập nhật canvas khi grid thay đổi
                    Connections {
                        target: gridLines
                        function onColumnsChanged() { gridCanvas.requestPaint(); }
                        function onRowsChanged() { gridCanvas.requestPaint(); }
                    }

                    // Cập nhật canvas khi gridModel thay đổi
                    Connections {
                        target: gridModel
                        function onColumnsChanged() {
                            gridCanvas.animatedColumns = gridModel.columns;
                            gridCanvas.requestPaint();
                        }
                        function onRowsChanged() {
                            gridCanvas.animatedRows = gridModel.rows;
                            gridCanvas.requestPaint();
                        }
                    }

                    // Cập nhật canvas khi drag & drop
                    Connections {
                        target: dropArea
                        function onContainsDragChanged() { gridCanvas.requestPaint(); }
                    }

                    // Cập nhật canvas khi grid resize highlight changes
                    Connections {
                        target: cameraGrid
                        function onGridResizeHighlightChanged() { gridCanvas.requestPaint(); }
                    }
                }
            }
        }

        // Grid container (bottom layer)
        Item {
            id: videoGrid
            anchors.fill: parent
            anchors.margins: 10
            z: 4  // Above grid lines

            property var activeItems: ({})
            property int columns: {
                if (typeof gridModel === "undefined" || gridModel === null) {
                    console.log("videoGrid.columns: gridModel is undefined, using baseColumns:", baseColumns)
                    return baseColumns
                }
                console.log("videoGrid.columns: gridModel.columns =", gridModel.columns, "baseColumns =", baseColumns)
                return gridModel.columns || baseColumns
            }
            property int rows: {
                if (typeof gridModel === "undefined" || gridModel === null) {
                    console.log("videoGrid.rows: gridModel is undefined, using baseRows:", baseRows)
                    return baseRows
                }
                console.log("videoGrid.rows: gridModel.rows =", gridModel.rows, "baseRows =", baseRows)
                return gridModel.rows || baseRows
            }

            // Handle grid size changes
            onColumnsChanged: reorganizeGrid()
            onRowsChanged: reorganizeGrid()
            onWidthChanged: reorganizeGrid()
            onHeightChanged: reorganizeGrid()

            // Handle maximized state changes
            Connections {
                target: gridModel
                function onIsMaximizedChanged() {
                    videoGrid.reorganizeGrid()
                }
            }

            // Handle grid size changes from gridModel
            Connections {
                target: gridModel
                function onColumnsChanged() {
                    console.log("Grid columns changed to:", gridModel.columns)
                    videoGrid.reorganizeGrid()
                }
                function onRowsChanged() {
                    console.log("Grid rows changed to:", gridModel.rows)
                    videoGrid.reorganizeGrid()
                }
            }

            // Helper functions for grid calculations
            function calculateItemWidth() {
                return width / columns;
            }

            function calculateItemHeight() {
                return height / rows;
            }

            function calculateX(position) {
                return (position % columns) * calculateItemWidth();
            }

            function calculateY(position) {
                return Math.floor(position / columns) * calculateItemHeight();
            }

            // Function to reorganize grid
            function reorganizeGrid() {
                for (var pos in activeItems) {
                    var item = activeItems[pos]
                    if (item) {
                        if (gridModel && !gridModel.isMaximized) {
                            // Get stored dimensions from GridModel
                            var dimensions = gridModel.getCellDimensions(parseInt(pos))
                            var widthCells = dimensions.width
                            var heightCells = dimensions.height

                            // When restoring, force immediate update without binding
                            item.width = calculateItemWidth() * widthCells
                            item.height = calculateItemHeight() * heightCells
                            item.x = calculateX(parseInt(pos))
                            item.y = calculateY(parseInt(pos))
                        } else if (gridModel && pos === gridModel.activeItemPosition.toString()) {
                            // When maximizing, set to full size
                            item.width = width
                            item.height = height
                            item.x = 0
                            item.y = 0
                        }
                    }
                }
            }

            // Function to create grid item
            function createGridItem(position,model = null) {
                // if (model && model.toString().startsWith("FloorModel")){
                //     var component = Qt.createComponent("GridItemMap2D.qml")
                // }else {
                //     var component = Qt.createComponent("GridItem.qml")
                // }

                var modelType = "unknown"

                if (model) {
                    var str = model.toString()
                    if (str.startsWith("FloorModel")) {
                        modelType = "FloorModel"
                    } else if (str.startsWith("MapModel")) {
                        modelType = "MapModel"
                    } else {
                        modelType = "CameraModel"
                    }
                    // thêm các kiểu model khác nếu cần
                }

                var component
                switch (modelType) {
                    case "FloorModel":
                        component = Qt.createComponent("GridItemMap2D.qml")
                        if (component.status === Component.Error) {
                            console.error("Component load error:", component.errorString())
                        }
                        if (component.status === Component.Ready) {
                            var dimensions = gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}
                            var widthCells = dimensions.width
                            var heightCells = dimensions.height
                            var item = component.createObject(videoGrid, {
                                "width": calculateItemWidth() * widthCells,
                                "height": calculateItemHeight() * heightCells,
                                "position": position,
                                "isDarkTheme": cameraGrid.isDarkTheme,
                                "x": calculateX(position),
                                "y": calculateY(position),
                                "itemData": model

                            })
                            activeItems[position] = item
                            return item
                        } else {
                            if (component.errorString()) {
                                // // console.log(`[DEBUG] createGridItem: Error: ${component.errorString()}`)
                            }
                            return null
                        }
                        break
                    case "MapModel":
                        component = Qt.createComponent("GridItemDigitalMap.qml")
                        if (component.status === Component.Error) {
                            console.error("Component load error:", component.errorString())
                        }
                        if (component.status === Component.Ready) {
                            var dimensions = gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}
                            var widthCells = dimensions.width
                            var heightCells = dimensions.height
                            var item = component.createObject(videoGrid, {
                                "width": calculateItemWidth() * widthCells,
                                "height": calculateItemHeight() * heightCells,
                                "position": position,
                                "isDarkTheme": cameraGrid.isDarkTheme,
                                "x": calculateX(position),
                                "y": calculateY(position),
                                "itemData": model

                            })
                            activeItems[position] = item
                            return item
                        } else {
                            if (component.errorString()) {
                                // // console.log(`[DEBUG] createGridItem: Error: ${component.errorString()}`)
                            }
                            return null
                        }
                        break
                    default:
                        component = Qt.createComponent("GridItem.qml")
                        if (component.status === Component.Error) {
                            console.error("Component load error:", component.errorString())
                        }
                        if (component.status === Component.Ready) {
                            // Get stored dimensions from GridModel
                            var dimensions = gridModel ? gridModel.getCellDimensions(position) : {"width": 1, "height": 1}
                            var widthCells = dimensions.width
                            var heightCells = dimensions.height
                            // console.log("hello 123")
                            var item = component.createObject(videoGrid, {
                                "width": calculateItemWidth() * widthCells,
                                "height": calculateItemHeight() * heightCells,
                                "position": position,
                                "isDarkTheme": cameraGrid.isDarkTheme,
                                "x": calculateX(position),
                                "y": calculateY(position),
                                "itemData": model
                            })
                            // console.log("hello 123456")
                            activeItems[position] = item
                            return item
                        } else {
                            // // console.log(`[DEBUG] createGridItem: Component not ready for position ${position}, status: ${component.status}`)
                            if (component.errorString()) {
                                // // console.log(`[DEBUG] createGridItem: Error: ${component.errorString()}`)
                            }
                            return null
                        }
                        break
                }

                // console.log(`[DEBUG] createGridItem: modelType=${modelType}, component status=${component.status}`)

            }
        }

        // Drop area (middle layer) - tối ưu hóa để cải thiện hiệu suất
        DropArea {
            id: dropArea
            anchors.fill: parent
            z: 2  // Above grid container

            // Thêm thuộc tính để lưu trữ thông tin về camera đang kéo
            property int dragCameraWidth: 1
            property int dragCameraHeight: 1
            property int dragStartCol: -1
            property int dragStartRow: -1
            property bool processingDrag: false // Thêm flag để tránh xử lý quá nhiều
            property var lastDragPos: ({x: 0, y: 0}) // Lưu vị trí cuối cùng để tối ưu
            property bool highlightVisible: false // Theo dõi trạng thái hiển thị của highlight

            // Thêm Rectangle để highlight toàn bộ vùng thay vì highlight từng ô
            Rectangle {
                id: areaHighlight
                visible: dropArea.highlightVisible
                color: "#5022C55E"  // Xanh lá với 30% opacity
                border.color: "#22C55E"  // Xanh lá đậm cho viền
                border.width: 2  // Viền mỏng hơn để tối ưu hiệu suất
                radius: 4
                z: 10  // Đặt trên các ô grid nhưng dưới các camera

                // Loại bỏ animation để tối ưu hiệu suất
            }

            // Timer để tối ưu hiệu suất khi kéo thả - sử dụng repeat để cập nhật liên tục
            Timer {
                id: dragUpdateTimer
                interval: 16 // Cập nhật mỗi 16ms (khoảng 60fps) để đảm bảo mượt mà
                repeat: true // Lặp lại liên tục khi đang kéo thả

                onTriggered: {
                    if (dropArea.containsDrag) {
                        dropArea.updateDragPosition(dropArea.lastDragPos.x, dropArea.lastDragPos.y)
                    } else {
                        stop()
                        dropArea.processingDrag = false
                    }
                }
            }

            // Xử lý khi drag enter vào dropArea
            onEntered: function(drag) {
                // Thử lấy thông tin kích thước từ nhiều nguồn khác nhau
                if (drag.source && drag.source.hasOwnProperty("dragDimensions")) {
                    // Lấy thông tin kích thước từ camera đang kéo
                    dragCameraWidth = drag.source.dragDimensions.width
                    dragCameraHeight = drag.source.dragDimensions.height
                } else if (drag.hasText && drag.getDataAsString("camera/width") && drag.getDataAsString("camera/height")) {
                    // Nếu không có dragDimensions, thử lấy từ mimeData
                    dragCameraWidth = parseInt(drag.getDataAsString("camera/width"))
                    dragCameraHeight = parseInt(drag.getDataAsString("camera/height"))
                } else {
                    // Nếu không có thông tin kích thước, sử dụng giá trị mặc định
                    dragCameraWidth = 1
                    dragCameraHeight = 1
                }

                // Bắt đầu đánh dấu các ô ngay khi drag enter
                if (drag.x !== undefined && drag.y !== undefined) {
                    lastDragPos = {x: drag.x, y: drag.y}
                    highlightVisible = true // Hiển thị highlight ngay khi bắt đầu kéo
                    updateDragPosition(drag.x, drag.y)

                    // Bắt đầu timer để cập nhật liên tục
                    if (!dragUpdateTimer.running) {
                        dropArea.processingDrag = true
                        dragUpdateTimer.start()
                    }
                }
            }

            // Xử lý khi drag position thay đổi - cập nhật vị trí cuối cùng
            onPositionChanged: function(drag) {
                // Kiểm tra xem drag có hợp lệ không
                if (!drag || drag.x === undefined || drag.y === undefined) {
                    return
                }

                // Chỉ cập nhật vị trí cuối cùng, timer sẽ xử lý việc cập nhật UI
                lastDragPos = {x: drag.x, y: drag.y}
                updateDragPosition(drag.x, drag.y)
            }

            // Hàm cập nhật vị trí kéo thả - tách riêng để tái sử dụng
            function updateDragPosition(x, y) {
                // Kiểm tra xem grid có đang ở chế độ fullscreen không
                var isFullscreenMode = gridModel && gridModel.isMaximized

                // Sử dụng kích thước grid thích hợp
                var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns
                var effectiveRows = isFullscreenMode ? gridModel.previousRows : videoGrid.rows

                // console.log(`updateDragPosition: effectiveColumns=${effectiveColumns}, effectiveRows=${effectiveRows}, isFullscreenMode=${isFullscreenMode}`)

                // Tính toán vị trí ô bắt đầu dựa trên vị trí chuột
                var cellWidth = gridLines.width / effectiveColumns
                var cellHeight = gridLines.height / effectiveRows

                // Tính toán vị trí của chuột trong gridLines
                var mousePos = mapToItem(gridLines, x, y)

                // Tính toán vị trí góc trên bên trái của camera
                var col = Math.floor(mousePos.x / cellWidth)
                var row = Math.floor(mousePos.y / cellHeight)

                // console.log(`Mouse position in grid: row=${row}, col=${col}`)

                // Tính toán offset từ tâm của camera đến góc trên bên trái
                var offsetCol = Math.floor(dragCameraWidth / 2)
                var offsetRow = Math.floor(dragCameraHeight / 2)

                // Điều chỉnh vị trí để giữ tâm của camera
                var adjustedCol = Math.max(0, col - offsetCol)
                var adjustedRow = Math.max(0, row - offsetRow)

                // console.log(`Adjusted position before bounds check: row=${adjustedRow}, col=${adjustedCol}`)

                // Đảm bảo không vượt quá biên phải và biên dưới
                adjustedCol = Math.min(adjustedCol, effectiveColumns - dragCameraWidth)
                adjustedRow = Math.min(adjustedRow, effectiveRows - dragCameraHeight)

                // console.log(`Final adjusted position: row=${adjustedRow}, col=${adjustedCol}`)

                // Kiểm tra xem vị trí có thay đổi không
                if (adjustedCol === dragStartCol && adjustedRow === dragStartRow) {
                    // Vị trí không thay đổi, không cần cập nhật
                    return
                }

                // Lưu vị trí bắt đầu
                dragStartCol = adjustedCol
                dragStartRow = adjustedRow

                // Đánh dấu các ô sẽ bị chiếm
                updateOccupiedCells(adjustedCol, adjustedRow, dragCameraWidth, dragCameraHeight)
            }

            // Xử lý khi drag rời khỏi dropArea
            onExited: function() {
                // Xóa highlight ngay lập tức khi rời khỏi
                clearOccupiedCells()
                dropArea.processingDrag = false
                dragUpdateTimer.stop()
                highlightVisible = false
            }

            // Hàm đánh dấu các ô sẽ bị chiếm - tối ưu hóa để cải thiện hiệu suất
            function updateOccupiedCells(startCol, startRow, width, height) {
                // Đảm bảo kích thước hợp lệ
                width = Math.max(1, width)
                height = Math.max(1, height)

                // Kiểm tra xem có đủ không gian không
                var hasEnoughSpace = true

                // Kiểm tra xem grid có đang ở chế độ fullscreen không
                var isFullscreenMode = gridModel && gridModel.isMaximized

                // Sử dụng kích thước grid thích hợp
                var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns
                var effectiveRows = isFullscreenMode ? gridModel.previousRows : videoGrid.rows

                // console.log(`updateOccupiedCells: effectiveColumns=${effectiveColumns}, effectiveRows=${effectiveRows}, isFullscreenMode=${isFullscreenMode}`)

                // Kiểm tra xem có vượt quá biên không
                if (startCol + width > effectiveColumns || startRow + height > effectiveRows) {
                    hasEnoughSpace = false
                    // console.log(`Position out of bounds: startCol=${startCol}, startRow=${startRow}, width=${width}, height=${height}`)
                }

                // Kiểm tra xem có ô nào đã bị chiếm không
                if (hasEnoughSpace) {
                    // Nếu đang kéo từ tree vào, luôn cho phép màu xanh
                    if (dropArea.drag.source && dropArea.drag.source.parent &&
                        (dropArea.drag.source.parent.objectName === "cameraTree" ||
                         dropArea.drag.source.parent.objectName === "cameraList")) {
                        // Kéo từ tree vào, luôn cho phép màu xanh
                        hasEnoughSpace = true
                    } else {
                        // Luôn cho phép màu xanh khi kéo camera đã có trong lưới
                        // Lấy thông tin về camera đang kéo
                        var dragSource = dropArea.drag.source
                        var dragSourcePos = -1

                        // Kiểm tra xem có phải đang kéo camera từ lưới không
                        if (dragSource && typeof dragSource.position === 'number') {
                            // Đang kéo camera từ lưới
                            dragSourcePos = dragSource.position

                            // Kiểm tra xem grid có đang ở chế độ fullscreen không
                            var isFullscreenMode = gridModel && gridModel.isMaximized

                            // Sử dụng kích thước grid thích hợp
                            var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns

                            // Kiểm tra xem có đang kéo về vị trí ban đầu không
                            var originalRow = Math.floor(dragSourcePos / effectiveColumns)
                            var originalCol = dragSourcePos % effectiveColumns

                            // Nếu đang kéo về vị trí ban đầu hoặc gần đó, hiển thị màu xanh
                            if (Math.abs(startCol - originalCol) <= 1 && Math.abs(startRow - originalRow) <= 1) {
                                hasEnoughSpace = true

                                // Cập nhật highlight mà không cần kiểm tra thêm
                                updateHighlight(startCol, startRow, width, height, hasEnoughSpace)
                                return hasEnoughSpace;
                            }
                        }

                        // Tối ưu: Chỉ kiểm tra các camera có khả năng giao nhau
                        for (var pos in videoGrid.activeItems) {
                            var camera = videoGrid.activeItems[pos]
                            if (!camera) continue

                            // Bỏ qua camera đang kéo (cho phép thả tại chỗ)
                            if (parseInt(pos) === dragSourcePos) continue

                            // Kiểm tra xem grid có đang ở chế độ fullscreen không
                            var isFullscreenMode = gridModel && gridModel.isMaximized

                            // Sử dụng kích thước grid thích hợp
                            var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns

                            // Lấy vị trí và kích thước thực tế của camera
                            var camPos = parseInt(pos)
                            var camRow = Math.floor(camPos / effectiveColumns)
                            var camCol = camPos % effectiveColumns

                            // Kiểm tra nhanh xem camera có gần vùng kéo thả không
                            if (Math.abs(camRow - startRow) > height + 1 ||
                                Math.abs(camCol - startCol) > width + 1) {
                                continue; // Camera quá xa, không cần kiểm tra chi tiết
                            }

                            // Lấy kích thước thực tế của camera từ gridModel
                            var camDimensions = gridModel ? gridModel.getCellDimensions(camPos) : {"width": 1, "height": 1}
                            var camWidth = camDimensions.width
                            var camHeight = camDimensions.height

                            // Kiểm tra xem có giao nhau không
                            var hasOverlap = !(camCol + camWidth <= startCol ||
                                              camCol >= startCol + width ||
                                              camRow + camHeight <= startRow ||
                                              camRow >= startRow + height)

                            if (hasOverlap) {
                                hasEnoughSpace = false
                                break
                            }
                        }
                    }
                }

                // Cập nhật highlight
                updateHighlight(startCol, startRow, width, height, hasEnoughSpace)
                return hasEnoughSpace
            }

            // Tách riêng phần cập nhật highlight để tái sử dụng
            function updateHighlight(startCol, startRow, width, height, hasEnoughSpace) {
                // Kiểm tra xem grid có đang ở chế độ fullscreen không
                var isFullscreenMode = gridModel && gridModel.isMaximized

                // Sử dụng kích thước grid thích hợp
                var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns
                var effectiveRows = isFullscreenMode ? gridModel.previousRows : videoGrid.rows

                // console.log(`updateHighlight: effectiveColumns=${effectiveColumns}, effectiveRows=${effectiveRows}, isFullscreenMode=${isFullscreenMode}`)

                // Sử dụng Rectangle duy nhất để highlight toàn bộ vùng thay vì highlight từng ô
                var cellWidth = gridLines.width / effectiveColumns
                var cellHeight = gridLines.height / effectiveRows

                // Cập nhật vị trí và kích thước
                areaHighlight.x = gridLines.x + startCol * cellWidth
                areaHighlight.y = gridLines.y + startRow * cellHeight
                areaHighlight.width = width * cellWidth
                areaHighlight.height = height * cellHeight

                // Đổi màu nếu không đủ không gian - sử dụng màu nhẹ hơn để tối ưu hiệu suất
                if (hasEnoughSpace) {
                    // Màu xanh lá cho vùng hợp lệ
                    areaHighlight.color = "#5022C55E"  // Xanh lá với 30% opacity
                    areaHighlight.border.color = "#22C55E"  // Xanh lá đậm cho viền
                } else {
                    // Màu đỏ cho vùng không hợp lệ
                    areaHighlight.color = "#50FF4444"  // Đỏ với 30% opacity
                    areaHighlight.border.color = "#FF4444"  // Đỏ đậm cho viền
                }

                // Đảm bảo highlight luôn hiển thị khi đang kéo thả
                highlightVisible = true
            }

            // Hàm xóa đánh dấu - chỉ ẩn highlight khi thả hoặc rời khỏi dropArea
            function clearOccupiedCells() {
                // Ẩn highlight ngay lập tức
                highlightVisible = false
            }

            onDropped: function(drop) {
                // Get drop position
                var x = drop.x
                var y = drop.y

                // Kiểm tra xem grid có đang ở chế độ fullscreen không
                var isFullscreenMode = gridModel && gridModel.isMaximized

                // Sử dụng kích thước grid thích hợp
                var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns
                var effectiveRows = isFullscreenMode ? gridModel.previousRows : videoGrid.rows

                // console.log(`onDropped: isFullscreenMode=${isFullscreenMode}, effectiveColumns=${effectiveColumns}, effectiveRows=${effectiveRows}`)

                // Tính toán vị trí của chuột trong gridLines
                var cellWidth = gridLines.width / effectiveColumns
                var cellHeight = gridLines.height / effectiveRows
                var mousePos = mapToItem(gridLines, x, y)

                // Tính toán vị trí góc trên bên trái của camera
                var col = Math.floor(mousePos.x / cellWidth)
                var row = Math.floor(mousePos.y / cellHeight)

                // console.log(`Mouse position in grid: row=${row}, col=${col}`)

                // Tính toán offset từ tâm của camera đến góc trên bên trái
                var offsetCol = Math.floor(dragCameraWidth / 2)
                var offsetRow = Math.floor(dragCameraHeight / 2)

                // Điều chỉnh vị trí để giữ tâm của camera
                var adjustedCol = Math.max(0, col - offsetCol)
                var adjustedRow = Math.max(0, row - offsetRow)

                // console.log(`Adjusted position before bounds check: row=${adjustedRow}, col=${adjustedCol}`)

                // Đảm bảo không vượt quá biên phải và biên dưới
                adjustedCol = Math.min(adjustedCol, effectiveColumns - dragCameraWidth)
                adjustedRow = Math.min(adjustedRow, effectiveRows - dragCameraHeight)

                // console.log(`Final adjusted position: row=${adjustedRow}, col=${adjustedCol}`)

                // Tính vị trí trong grid
                const dropPosition = adjustedRow * effectiveColumns + adjustedCol
                // console.log(`Calculated drop position: ${dropPosition} (row=${adjustedRow}, col=${adjustedCol}, effectiveColumns=${effectiveColumns})`)

                // Kiểm tra xem vị trí có hợp lệ không
                var hasEnoughSpace = true

                // Kiểm tra xem có vượt quá biên không
                if (adjustedCol + dragCameraWidth > effectiveColumns || adjustedRow + dragCameraHeight > effectiveRows) {
                    hasEnoughSpace = false
                    // console.log(`Position out of bounds: adjustedCol=${adjustedCol}, adjustedRow=${adjustedRow}, dragCameraWidth=${dragCameraWidth}, dragCameraHeight=${dragCameraHeight}`)
                }

                // Kiểm tra xem có ô nào đã bị chiếm không
                if (hasEnoughSpace) {
                    // Kiểm tra xem vị trí này có camera nào không
                    for (var r = adjustedRow; r < adjustedRow + dragCameraHeight; r++) {
                        for (var c = adjustedCol; c < adjustedCol + dragCameraWidth; c++) {
                            var cellPos = r * effectiveColumns + c

                            // Kiểm tra xem ô này đã có camera chưa
                            if (videoGrid.activeItems[cellPos]) {
                                hasEnoughSpace = false
                                break
                            }

                            // Kiểm tra xem ô này có thuộc về camera đã resize không
                            for (var pos in videoGrid.activeItems) {
                                if (!videoGrid.activeItems[pos]) continue;

                                // Lấy kích thước của camera
                                var camDimensions = gridModel ? gridModel.getCellDimensions(parseInt(pos)) : {"width": 1, "height": 1};
                                var camWidth = camDimensions.width;
                                var camHeight = camDimensions.height;

                                // Kiểm tra xem grid có đang ở chế độ fullscreen không
                                var isFullscreenMode = gridModel && gridModel.isMaximized

                                // Sử dụng kích thước grid thích hợp
                                var effectiveColumns = isFullscreenMode ? gridModel.previousColumns : videoGrid.columns

                                // Tính toán vị trí của camera
                                var camRow = Math.floor(parseInt(pos) / effectiveColumns);
                                var camCol = parseInt(pos) % effectiveColumns;

                                // Kiểm tra xem ô hiện tại có nằm trong phạm vi của camera không
                                if (r >= camRow && r < camRow + camHeight &&
                                    c >= camCol && c < camCol + camWidth) {
                                    hasEnoughSpace = false;
                                    break;
                                }
                            }

                            if (!hasEnoughSpace) break;
                        }
                        if (!hasEnoughSpace) break
                    }
                }

                // Check formats directly from the array
                if (drop.formats.includes("application/camera")) {
                    gridModel.isSave = false
                    try {
                        // Parse camera data with error handling
                        var cameraData = JSON.parse(drop.getDataAsString("application/x-qabstractitemmodeldatalist"))
                        if (!cameraData) {
                            console.error("Failed to parse camera data")
                            return
                        }

                        var url = cameraData.data ? cameraData.data.urlMainstream : ""
                        var name = cameraData.data ? cameraData.data.name : "Camera"

                        // Kiểm tra giới hạn camera trước khi thêm
                        if (!checkCameraLimit(1)) {
                            isProcessingDrop = false
                            return
                        }

                        // Kiểm tra auto_resize_enabled trước khi xử lý single camera
                        var autoResizeEnabled = gridModel.autoResizeEnabled
                        console.log(`Single camera drop - Auto resize enabled: ${autoResizeEnabled}`)

                        // Sử dụng hàm tiện ích để kiểm tra vị trí
                        if (isPositionAvailable(dropPosition)) {
                            console.log(`Adding camera at position ${dropPosition}`)
                            // Emit signal to grid manager instead of direct handling
                            if (gridModel) {
                                gridModel.addCameraRequested(dropPosition, cameraData.data.id)
                                // Note: grid_manager.py will automatically set cell dimensions to 1x1
                                // We'll update dimensions after camera is added via signal
                                Qt.callLater(function() {
                                    gridModel.updateCellDimensions(dropPosition, dragCameraWidth, dragCameraHeight)
                                })
                            }
                        } else {
                            // Tìm vị trí trống đầu tiên
                            var availablePosition = findFirstAvailablePosition()
                            var effectiveColumns = gridModel.isMaximized ? gridModel.previousColumns : videoGrid.columns
                            var effectiveRows = gridModel.isMaximized ? gridModel.previousRows : videoGrid.rows
                            var currentMaxPosition = effectiveRows * effectiveColumns

                            console.log(`Using alternative position ${availablePosition} for camera (currentMaxPosition: ${currentMaxPosition})`)

                            // Kiểm tra xem có vị trí trống hợp lệ trong grid hiện tại không
                            if (availablePosition < currentMaxPosition && isPositionAvailable(availablePosition)) {
                                // Có vị trí trống hợp lệ trong grid hiện tại - không cần mở rộng
                                console.log(`Using available position ${availablePosition} for camera (within current grid)`)
                                if (gridModel) {
                                    gridModel.addCameraRequested(availablePosition, cameraData.data.id)
                                    Qt.callLater(function() {
                                        gridModel.updateCellDimensions(availablePosition, dragCameraWidth, dragCameraHeight)
                                    })
                                }
                            }
                            // ✅ FIX: Chỉ mở rộng khi thực sự không có vị trí trống nào
                            else if (autoResizeEnabled === false) {
                                console.log("Auto resize disabled and no available positions, expanding grid like onwheel+ctrl")

                                // Mở rộng grid giống onwheel+ctrl
                                var targetColumns = effectiveColumns
                                var targetRows = effectiveRows

                                // Mở rộng giống onwheel+ctrl: tăng cả columns và rows
                                if (targetColumns < maxColumns) {
                                    targetColumns += 1
                                }
                                var newRows = targetRows + 1

                                // Điều chỉnh để grid là hình vuông (giống onwheel+ctrl)
                                if (targetColumns !== newRows) {
                                    var maxDimension = Math.max(targetColumns, newRows)
                                    targetColumns = maxDimension
                                    targetRows = maxDimension
                                } else {
                                    targetRows = newRows
                                }

                                console.log(`Expanding grid from ${effectiveColumns}x${effectiveRows} to ${targetColumns}x${targetRows} for single camera`)

                                // Thực hiện mở rộng grid giống hệt onwheel+ctrl
                                var expansionSuccess = expandGridLikeOnWheelCtrl(targetColumns, targetRows)

                                if (expansionSuccess) {
                                    // Sau khi mở rộng, tìm vị trí trống để thêm camera
                                    var availablePositionAfterExpansion = findFirstAvailablePosition()
                                    console.log(`After expansion, adding single camera at position ${availablePositionAfterExpansion}`)
                                    if (gridModel) {
                                        gridModel.addCameraRequested(availablePositionAfterExpansion, cameraData.data.id)
                                        Qt.callLater(function() {
                                            gridModel.updateCellDimensions(availablePositionAfterExpansion, dragCameraWidth, dragCameraHeight)
                                        })
                                    }
                                } else {
                                    console.error("Grid expansion failed for single camera")
                                }
                            } else {
                                // Auto resize enabled - để grid tự động mở rộng
                                console.log("[DROP_DEBUG] Auto resize enabled, adding camera anyway (grid will auto-expand)")
                                if (gridModel) {
                                    gridModel.addCameraRequested(availablePosition, cameraData.data.id)
                                    Qt.callLater(function() {
                                        gridModel.updateCellDimensions(availablePosition, dragCameraWidth, dragCameraHeight)
                                    })
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`Error processing camera drop: ${error}`)
                    }
                }
                else if (drop.formats.includes("application/group")) {
                    gridModel.isSave = false
                    try {
                        // Parse group data with error handling
                        var groupData = JSON.parse(drop.getDataAsString("application/x-qabstractitemmodeldatalist"))
                        if (!groupData) {
                            console.error("Failed to parse group data")
                            return
                        }

                        // Lấy số lượng camera trong group để kiểm tra
                        var numCamerasInGroup = 0
                        if (groupData && groupData.data && groupData.data.cameraIds) {
                            numCamerasInGroup = groupData.data.cameraIds.length
                        }

                        // Log thông tin để debug
                        console.log(`Processing group with ${numCamerasInGroup} cameras at position ${dropPosition}`)

                        // Nếu không có camera nào trong group, không cần thêm
                        if (numCamerasInGroup === 0) {
                            console.warn("Group contains no cameras, skipping")
                            isProcessingDrop = false
                            return
                        }

                        // Kiểm tra giới hạn camera trước khi thêm group
                        if (!checkCameraLimit(numCamerasInGroup)) {
                            isProcessingDrop = false
                            return
                        }

                        // Kiểm tra auto_resize_enabled trước khi xử lý
                        var autoResizeEnabled = gridModel.autoResizeEnabled
                        console.log(`Auto resize enabled: ${autoResizeEnabled}`)

                        if (autoResizeEnabled === false) {
                            // Khi auto_resize_enabled = false, kiểm tra xem có đủ vị trí trống không
                            console.log("Auto resize disabled, checking available empty positions")

                            // Tìm tất cả vị trí trống hiện có trong grid
                            var currentMaxPosition = effectiveRows * effectiveColumns
                            var availableEmptyPositions = findAvailablePositions(currentMaxPosition)

                            console.log(`Current grid: ${effectiveColumns}x${effectiveRows} = ${currentMaxPosition} positions`)
                            console.log(`Available empty positions: ${availableEmptyPositions.length}`)
                            console.log(`Cameras to add: ${numCamerasInGroup}`)

                            if (availableEmptyPositions.length < numCamerasInGroup) {
                                // Không đủ vị trí trống, cần mở rộng grid
                                console.log("Not enough empty positions, need to expand grid")

                                // Tính toán kích thước grid cần thiết để chứa group
                                // Tính đúng số cell bị chiếm thay vì chỉ đếm camera objects
                                var currentMaxPosition = effectiveRows * effectiveColumns
                                var totalOccupiedCells = currentMaxPosition - findAvailablePositions(currentMaxPosition).length
                                var totalCamerasNeeded = totalOccupiedCells + numCamerasInGroup

                                console.log(`Current occupied: ${totalOccupiedCells}, adding: ${numCamerasInGroup}, total needed: ${totalCamerasNeeded}`)

                                // Gọi method từ grid_manager.py thay vì duplicate logic
                                var optimalSize = gridModel.calculateOptimalGridSize(totalCamerasNeeded)
                                var targetColumns = optimalSize.columns
                                var targetRows = optimalSize.rows

                                console.log(`Calculated target grid size: ${targetColumns}x${targetRows} for ${totalCamerasNeeded} total cameras`)

                                // Thực hiện mở rộng grid giống như onwheel+ctrl
                                console.log(`Expanding grid from ${effectiveColumns}x${effectiveRows} to ${targetColumns}x${targetRows}`)
                                var expansionSuccess = expandGridLikeOnWheelCtrl(targetColumns, targetRows)

                                if (expansionSuccess) {
                                    // Sau khi mở rộng, tìm lại vị trí trống
                                    var availablePositionsAfterExpansion = findAvailablePositions(numCamerasInGroup)
                                    console.log(`After expansion, found ${availablePositionsAfterExpansion.length} available positions`)

                                    if (availablePositionsAfterExpansion.length >= numCamerasInGroup) {
                                        console.log(`Adding group using available positions after expansion: ${JSON.stringify(availablePositionsAfterExpansion)}`)

                                        // Tạo một đối tượng mới để lưu trữ thông tin group và các vị trí trống
                                        var groupInfo = {
                                            groupData: groupData,
                                            positions: availablePositionsAfterExpansion.slice(0, numCamerasInGroup)
                                        }

                                        // Gọi hàm addGroupToPositions trong grid_manager.py
                                        gridModel.addGroupToPositions(JSON.stringify(groupInfo))
                                    } else {
                                        // Fallback: thêm group vào vị trí được chỉ định (có thể đè lên)
                                        console.log(`Fallback: Adding group at position ${dropPosition}`)
                                        if (gridModel) {
                                            gridModel.addGroupRequested(dropPosition, groupData.data.id)
                                        }
                                    }
                                } else {
                                    console.error("Failed to expand grid")
                                    // Fallback: thêm group vào vị trí được chỉ định
                                    if (gridModel) {
                                        gridModel.addGroupRequested(dropPosition, groupData.data.id)
                                    }
                                }
                            } else {
                                // Có đủ vị trí trống, thêm group vào các vị trí đó
                                console.log(`Adding group using available positions: ${JSON.stringify(availableEmptyPositions)}`)

                                // Tạo một đối tượng mới để lưu trữ thông tin group và các vị trí trống
                                var groupInfo = {
                                    id: groupData.data.id,
                                    positions: availableEmptyPositions.slice(0, numCamerasInGroup)
                                }

                                // Emit signal to grid manager for multi-position group
                                if (gridModel) {
                                    gridModel.addGroupToPositionsRequested(JSON.stringify(groupInfo))
                                }
                            }
                        } else {
                            // Logic cũ khi auto_resize_enabled = true
                            // Kiểm tra xem có đủ không gian cho group không
                            if (arePositionsAvailable(dropPosition, numCamerasInGroup)) {
                                console.log(`Adding group at position ${dropPosition}`)
                                // Emit signal to grid manager instead of direct handling
                                if (gridModel) {
                                    gridModel.addGroupRequested(dropPosition, groupData.data.id)
                                }
                            } else {
                                // Tìm các vị trí trống rời rạc trên lưới
                                var availablePositions = findAvailablePositions(numCamerasInGroup)
                                console.log(`Found ${availablePositions.length} available positions for group`)

                                if (availablePositions.length >= numCamerasInGroup) {
                                    // Nếu có đủ vị trí trống, thêm group vào các vị trí đó
                                    console.log(`Adding group using available positions: ${JSON.stringify(availablePositions)}`)

                                    // Tạo một đối tượng mới để lưu trữ thông tin group và các vị trí trống
                                    var groupInfo = {
                                        id: groupData.data.id,
                                        positions: availablePositions.slice(0, numCamerasInGroup)
                                    }

                                    // Emit signal to grid manager for multi-position group
                                    if (gridModel) {
                                        gridModel.addGroupToPositionsRequested(JSON.stringify(groupInfo))
                                    }
                                } else {
                                    // Kiểm tra xem có đủ không gian liên tiếp cho group không
                                    var allPositionsAvailable = arePositionsAvailable(dropPosition, numCamerasInGroup)

                                    // Nếu có đủ không gian cho tất cả camera trong group, thêm group vào vị trí đó
                                    if (allPositionsAvailable) {
                                        console.log(`Adding group at position ${dropPosition}`)
                                        if (gridModel) {
                                            gridModel.addGroupRequested(dropPosition, groupData.data.id)
                                        }
                                    } else {
                                        // Tìm vị trí trống đầu tiên có đủ không gian cho group
                                        var foundPosition = false
                                        var maxPosition = effectiveRows * effectiveColumns - numCamerasInGroup

                                        for (var i = 0; i < maxPosition; i++) {
                                            if (arePositionsAvailable(i, numCamerasInGroup)) {
                                                console.log(`Found alternative position ${i} for group`)
                                                // Emit signal to grid manager instead of direct handling
                                                if (gridModel) {
                                                    gridModel.addGroupRequested(i, groupData.data.id)
                                                }
                                                foundPosition = true
                                                break
                                            }
                                        }

                                        // Nếu không tìm thấy vị trí nào phù hợp, sử dụng vị trí mặc định
                                        if (!foundPosition) {
                                            var nextPosition = findFirstAvailablePosition()
                                            console.log(`Using default position ${nextPosition} for group`)
                                            // Emit signal to grid manager instead of direct handling
                                            if (gridModel) {
                                                gridModel.addGroupRequested(nextPosition, groupData.data.id)
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`Error processing group drop: ${error}`)
                    }
                }
                else if (drop.formats.includes("application/list_map")) {
                    gridModel.isSave = false
                    try {
                        // Parse map data with error handling
                        var mapData = JSON.parse(drop.getDataAsString("application/x-qabstractitemmodeldatalist"))
                        if (!mapData || !mapData.data || !mapData.data.name) {
                            console.error("Failed to parse map data or missing name")
                            return
                        }

                        var mapName = mapData.data.name
                        console.log(`Processing map drop: ${mapName} at position ${dropPosition}`)

                        // Sử dụng hàm tiện ích để kiểm tra vị trí
                        if (isPositionAvailable(dropPosition)) {
                            // console.log(`Adding map at position ${dropPosition}`)
                            gridModel.addMap(dropPosition, mapData)
                        } else {
                            // Tìm vị trí trống đầu tiên
                            var availablePosition = findFirstAvailablePosition()
                            // console.log(`Using alternative position ${availablePosition} for map`)
                            gridModel.addMap(availablePosition, mapData)
                        }
                    } catch (error) {
                        console.error(`Error processing map drop: ${error}`)
                    }
                }
                else if (drop.formats.includes("application/flooritem")) {
                    gridModel.isSave = false
                    try {
                        // Parse map data with error handling
                        var mapData = JSON.parse(drop.getDataAsString("application/x-qabstractitemmodeldatalist"))
                        if (!mapData || !mapData.data || !mapData.data.name) {
                            console.error("Failed to parse map data or missing name")
                            return
                        }

                        var mapName = mapData.data.name
                        console.log(`Processing map drop: ${mapName} at position ${dropPosition}`)

                        // Sử dụng hàm tiện ích để kiểm tra vị trí
                        if (isPositionAvailable(dropPosition)) {
                            console.log(`Adding map at position ${mapData}`)
                            gridModel.addMap(dropPosition, mapData)
                        } else {
                            // Tìm vị trí trống đầu tiên
                            var availablePosition = findFirstAvailablePosition()
                            // console.log(`Using alternative position ${availablePosition} for map`)
                            gridModel.addMap(availablePosition, mapData)
                        }
                    } catch (error) {
                        console.error(`Error processing map drop: ${error}`)
                    }
                }
                else if (drop.formats.includes("application/event")) {
                    gridModel.isSave = false
                    try {
                        // Parse event data with error handling
                        var eventData = JSON.parse(drop.getDataAsString("application/x-qabstractitemmodeldatalist"))
                        if (!eventData || !eventData.data || !eventData.data.name) {
                            console.error("Failed to parse event data or missing name")
                            return
                        }

                        var eventName = eventData.data.name
                        // console.log(`Processing event drop: ${eventName} at position ${dropPosition}`)

                        // Sử dụng hàm tiện ích để kiểm tra vị trí
                        if (isPositionAvailable(dropPosition)) {
                            // console.log(`Adding event at position ${dropPosition}`)
                            gridModel.addEvent(dropPosition, eventName)
                        } else {
                            // Tìm vị trí trống đầu tiên
                            var availablePosition = findFirstAvailablePosition()
                            // console.log(`Using alternative position ${availablePosition} for event`)
                            gridModel.addEvent(availablePosition, eventName)
                        }
                    } catch (error) {
                        console.error(`Error processing event drop: ${error}`)
                    }
                }
                else if (drop.formats.includes("application/multi-selection")) {
                    gridModel.isSave = false
                    try {
                        // Parse multi-selection data with error handling
                        var multiData = JSON.parse(drop.getDataAsString("application/json"))
                        if (!multiData) {
                            console.error("Failed to parse multi-selection data")
                            return
                        }

                        // console.log("Processing multi-selection drop")

                        // Extract camera dimensions from drag data for preserving resize
                        var draggedCameraWidth = 1
                        var draggedCameraHeight = 1

                        // Try to get dimensions from drag data directly
                        if (drop.hasText && drop.getDataAsString("camera/width") && drop.getDataAsString("camera/height")) {
                            draggedCameraWidth = parseInt(drop.getDataAsString("camera/width")) || 1
                            draggedCameraHeight = parseInt(drop.getDataAsString("camera/height")) || 1
                        } else {
                            // Fallback to global variables
                            draggedCameraWidth = dragCameraWidth || 1
                            draggedCameraHeight = dragCameraHeight || 1
                        }

                        console.log(`DEBUG: Multiselect drop - preserving camera dimensions: ${draggedCameraWidth}x${draggedCameraHeight}`)

                        // Đếm số lượng item trong multi-selection
                        var itemCount = 0
                        var cameraCount = 0

                        console.log("DEBUG: Multiselect data structure:", JSON.stringify(multiData))

                        for (var id in multiData) {
                            itemCount++
                            var item = multiData[id]

                            console.log(`DEBUG: Processing item ${id}, type: ${item.type}`)
                            console.log(`DEBUG: Item data:`, JSON.stringify(item))

                            if (item.type === "Camera") {
                                cameraCount++
                                console.log(`DEBUG: Camera ${id} counted, total cameras now: ${cameraCount}`)
                            } else if (item.type === "Group") {
                                console.log(`DEBUG: Group ${id} - getting group info from Python...`)

                                // Multiselect data không có cameraIds, cần gọi Python để lấy thông tin group
                                var numCamerasInGroup = 1 // Default fallback

                                try {
                                    // Gọi Python method để lấy group info thay vì parse name
                                    if (gridModel && typeof gridModel.getGroupCameraCount === 'function') {
                                        numCamerasInGroup = gridModel.getGroupCameraCount(id)
                                        console.log(`DEBUG: Got ${numCamerasInGroup} cameras from Python for group ${id}`)
                                    }
                                } catch (parseError) {
                                    console.error(`DEBUG: Error getting group camera count: ${parseError}`)
                                    numCamerasInGroup = 1
                                }

                                cameraCount += numCamerasInGroup
                                console.log(`DEBUG: Added ${numCamerasInGroup} cameras from group ${id}, total cameras now: ${cameraCount}`)
                            }
                            // Map và Event không đếm vào camera count
                        }

                        console.log(`DEBUG: Final count - Items: ${itemCount}, Cameras: ${cameraCount}`)

                        // Debug: Check multiData structure
                        console.log(`DEBUG: multiData object keys: ${Object.keys(multiData)}`)
                        console.log(`DEBUG: multiData object length: ${Object.keys(multiData).length}`)
                        console.log(`DEBUG: typeof multiData: ${typeof multiData}`)

                        // Kiểm tra giới hạn camera trước khi thêm multi-selection
                        if (cameraCount > 0 && !checkCameraLimit(cameraCount)) {
                            console.log(`DEBUG: Camera limit exceeded, aborting drop`)
                            isProcessingDrop = false
                            return
                        }

                        // console.log(`Multi-selection contains ${itemCount} items, ${cameraCount} cameras`)

                        // Kiểm tra auto_resize_enabled trước khi xử lý (giống như drop group)
                        var autoResizeEnabled = gridModel.autoResizeEnabled
                        console.log(`Multiselect drop - Auto resize enabled: ${autoResizeEnabled}`)

                        if (autoResizeEnabled === false) {
                            // Khi auto_resize_enabled = false, kiểm tra xem có đủ vị trí trống không
                            console.log("Auto resize disabled, checking available empty positions")

                            // Định nghĩa effectiveColumns và effectiveRows
                            var effectiveColumns = gridModel.isMaximized ? gridModel.previousColumns : videoGrid.columns
                            var effectiveRows = gridModel.isMaximized ? gridModel.previousRows : videoGrid.rows

                            // Tìm tất cả vị trí trống hiện có trong grid
                            var currentMaxPosition = effectiveRows * effectiveColumns
                            var availableEmptyPositions = findAvailablePositions(currentMaxPosition)

                            console.log(`Current grid: ${effectiveColumns}x${effectiveRows} = ${currentMaxPosition} positions`)
                            console.log(`Available empty positions: ${availableEmptyPositions.length}`)
                            console.log(`Items to add: ${itemCount}`)

                            if (availableEmptyPositions.length < cameraCount) {
                                // Không đủ vị trí trống, cần mở rộng grid
                                console.log("Not enough empty positions, need to expand grid")

                                // Tính toán kích thước grid cần thiết để chứa multiselect
                                // Tính đúng số cell bị chiếm thay vì chỉ đếm camera objects
                                var currentMaxPosition = effectiveRows * effectiveColumns
                                var totalOccupiedCells = currentMaxPosition - findAvailablePositions(currentMaxPosition).length
                                var totalCamerasNeeded = totalOccupiedCells + cameraCount

                                console.log(`Current occupied: ${totalOccupiedCells}, adding: ${cameraCount} cameras (${itemCount} items), total needed: ${totalCamerasNeeded}`)

                                // Gọi method từ grid_manager.py thay vì duplicate logic
                                var optimalSize = gridModel.calculateOptimalGridSize(totalCamerasNeeded)
                                var targetColumns = optimalSize.columns
                                var targetRows = optimalSize.rows

                                console.log(`Calculated target grid size: ${targetColumns}x${targetRows} for ${totalCamerasNeeded} total cameras`)

                                // Thực hiện mở rộng grid giống như drop group
                                console.log(`Expanding grid from ${effectiveColumns}x${effectiveRows} to ${targetColumns}x${targetRows}`)

                                // Safety check: Validate target size
                                if (targetColumns <= 0 || targetRows <= 0 || targetColumns > maxColumns || targetRows > maxRows) {
                                    console.error(`Invalid target grid size: ${targetColumns}x${targetRows}, max: ${maxColumns}x${maxRows}`)
                                    isProcessingDrop = false
                                    return
                                }

                                var expansionSuccess = expandGridLikeOnWheelCtrl(targetColumns, targetRows)

                                if (expansionSuccess) {
                                    // Update grid dimensions sau khi expand thành công (giống như drop group)
                                    effectiveColumns = targetColumns
                                    effectiveRows = targetRows
                                    console.log(`Grid expanded successfully to ${effectiveColumns}x${effectiveRows} for multiselect`)
                                } else {
                                    console.error("Failed to expand grid for multiselect")
                                    isProcessingDrop = false
                                    return
                                }
                            } else {
                                console.log(`Enough empty positions available (${availableEmptyPositions.length} >= ${cameraCount}), no need to expand`)
                            }
                        } else {
                            console.log(`Auto resize enabled, checking if grid expansion is needed`)

                            // Khi auto_resize_enabled = true, kiểm tra xem có đủ vị trí trống không
                            var effectiveColumns = gridModel.isMaximized ? gridModel.previousColumns : videoGrid.columns
                            var effectiveRows = gridModel.isMaximized ? gridModel.previousRows : videoGrid.rows
                            var currentMaxPosition = effectiveRows * effectiveColumns
                            var availableEmptyPositions = findAvailablePositions(currentMaxPosition)

                            console.log(`Current grid: ${effectiveColumns}x${effectiveRows} = ${currentMaxPosition} positions`)
                            console.log(`Available empty positions: ${availableEmptyPositions.length}`)
                            console.log(`Cameras to add: ${cameraCount}`)

                            if (availableEmptyPositions.length < cameraCount) {
                                // Không đủ vị trí trống, cần mở rộng grid ngay cả khi auto_resize_enabled = true
                                console.log("Auto resize enabled but not enough empty positions, expanding grid")

                                // Tính toán kích thước grid cần thiết để chứa multiselect
                                var totalOccupiedCells = currentMaxPosition - findAvailablePositions(currentMaxPosition).length
                                var totalCamerasNeeded = totalOccupiedCells + cameraCount

                                console.log(`Current occupied: ${totalOccupiedCells}, adding: ${cameraCount}, total needed: ${totalCamerasNeeded}`)

                                // Gọi method từ grid_manager.py để tính toán kích thước tối ưu
                                var optimalSize = gridModel.calculateOptimalGridSize(totalCamerasNeeded)
                                var targetColumns = optimalSize.columns
                                var targetRows = optimalSize.rows

                                console.log(`Calculated target grid size: ${targetColumns}x${targetRows} for ${totalCamerasNeeded} total cameras`)

                                // Thực hiện mở rộng grid
                                console.log(`Expanding grid from ${effectiveColumns}x${effectiveRows} to ${targetColumns}x${targetRows}`)
                                var expansionSuccess = expandGridLikeOnWheelCtrl(targetColumns, targetRows)

                                if (!expansionSuccess) {
                                    console.error("Grid expansion failed for multiselect with auto resize enabled")
                                    isProcessingDrop = false
                                    return
                                }

                                console.log("Grid expansion successful for multiselect with auto resize enabled")
                            } else {
                                console.log(`Enough empty positions available (${availableEmptyPositions.length} >= ${cameraCount}), no need to expand`)
                            }
                        }

                        // Tìm các vị trí trống rời rạc trong lưới (có thể đã được mở rộng)
                        var availablePositions = findAvailablePositions(cameraCount)
                        console.log(`Found ${availablePositions.length} available positions for multiselect (need ${cameraCount} for cameras)`)

                        // Safety check: Ensure we have valid positions
                        if (!availablePositions || availablePositions.length === 0) {
                            console.error("No available positions found, aborting multiselect drop")
                            isProcessingDrop = false
                            return
                        }

                        // Sử dụng vị trí rời rạc để add từng item (giống như group drop)
                        if (availablePositions.length >= cameraCount) {
                            // Có đủ vị trí trống rời rạc, sử dụng chúng
                            console.log(`Adding multiselect using scattered positions: ${JSON.stringify(availablePositions.slice(0, cameraCount))}`)
                            console.log(`DEBUG: Starting to add ${itemCount} items (${cameraCount} cameras) from multiselect`)

                            // Process each item type differently
                            var positionIndex = 0
                            var processedCount = 0
                            var totalCamerasAdded = 0

                            for (var id in multiData) {
                                var item = multiData[id]
                                console.log(`DEBUG: Processing item ${processedCount + 1}/${itemCount}, ID: ${id}, type: ${item.type}`)

                                if (item.type === "Camera") {
                                    // Recalculate available positions before each camera add
                                    var remainingCameras = cameraCount - totalCamerasAdded
                                    var currentAvailablePositions = findAvailablePositions(remainingCameras)
                                    console.log(`DEBUG: Recalculated available positions for camera ${id}: ${JSON.stringify(currentAvailablePositions)}`)

                                    if (currentAvailablePositions.length === 0) {
                                        console.warn(`No more positions available for camera ${id}`)
                                        break
                                    }

                                    var currentPosition = currentAvailablePositions[0]  // Use first available position
                                    console.log(`DEBUG: Adding camera at position ${currentPosition}, ID: ${id}`)

                                    try {
                                        if (gridModel) {
                                            console.log(`DEBUG: Calling addCameraRequested(${currentPosition}, ${id})`)
                                            gridModel.addCameraRequested(currentPosition, id)
                                            console.log(`DEBUG: addCameraRequested completed successfully`)

                                            // Preserve camera dimensions from drag data
                                            Qt.callLater(function() {
                                                console.log(`DEBUG: Updating camera dimensions to ${draggedCameraWidth}x${draggedCameraHeight} for position ${currentPosition}`)
                                                gridModel.updateCellDimensions(currentPosition, draggedCameraWidth, draggedCameraHeight)
                                            })

                                            totalCamerasAdded++
                                        } else {
                                            console.error(`DEBUG: gridModel is null/undefined`)
                                        }
                                        console.log(`DEBUG: Camera ${id} processed successfully, totalCamerasAdded: ${totalCamerasAdded}`)
                                    } catch (cameraError) {
                                        console.error(`DEBUG: Exception in camera processing: ${cameraError}`)
                                        console.error(`DEBUG: Exception details: ${cameraError.toString()}`)
                                        // Don't break, continue with next item
                                    }

                                } else if (item.type === "Group") {
                                    // Group - use multiple positions based on camera count
                                    var numCamerasInGroup = 1 // Default
                                    try {
                                        // Gọi Python method để lấy group info thay vì parse name
                                        if (gridModel && typeof gridModel.getGroupCameraCount === 'function') {
                                            numCamerasInGroup = gridModel.getGroupCameraCount(id)
                                            console.log(`DEBUG: Got ${numCamerasInGroup} cameras from Python for group ${id}`)
                                        } else {
                                            // Fallback: thử parse từ tên group nếu có format "X Camera"
                                            var groupName = item.name || ""
                                            console.log(`DEBUG: Group name: "${groupName}" - trying to parse camera count`)

                                            var match = groupName.match(/(\d+)\s*Camera/i)
                                            if (match && match[1]) {
                                                numCamerasInGroup = parseInt(match[1])
                                                console.log(`DEBUG: Parsed ${numCamerasInGroup} cameras from group name "${groupName}"`)
                                            } else {
                                                console.warn(`DEBUG: Could not parse camera count from group name "${groupName}", using fallback of 1`)
                                                numCamerasInGroup = 1
                                            }
                                        }
                                    } catch (e) {
                                        console.error(`Error getting group camera count: ${e}`)
                                        numCamerasInGroup = 1
                                    }

                                    console.log(`Adding group ${id} with ${numCamerasInGroup} cameras`)

                                    // Recalculate available positions before group add
                                    var remainingCameras = cameraCount - totalCamerasAdded
                                    var currentAvailablePositions = findAvailablePositions(remainingCameras)
                                    console.log(`DEBUG: Recalculated available positions for group ${id}: ${JSON.stringify(currentAvailablePositions)}`)

                                    // Check if we have enough positions for this group
                                    if (currentAvailablePositions.length < numCamerasInGroup) {
                                        console.warn(`Not enough positions for group ${id} (need ${numCamerasInGroup}, have ${currentAvailablePositions.length})`)
                                        break
                                    }

                                    // Get positions for this group
                                    var groupPositions = currentAvailablePositions.slice(0, numCamerasInGroup)
                                    console.log(`Group ${id} will use positions: ${JSON.stringify(groupPositions)}`)

                                    // Create group info for addGroupToPositions
                                    var groupInfo = {
                                        id: id,
                                        positions: groupPositions
                                    }

                                    if (gridModel) {
                                        gridModel.addGroupToPositionsRequested(JSON.stringify(groupInfo))
                                        totalCamerasAdded += numCamerasInGroup
                                    }

                                } else if (item.type === "Map") {
                                    // Single map - recalculate available positions
                                    var currentAvailablePositions = findAvailablePositions(1)
                                    console.log(`DEBUG: Recalculated available positions for map ${id}: ${JSON.stringify(currentAvailablePositions)}`)

                                    if (currentAvailablePositions.length === 0) {
                                        console.warn(`No more positions available for map ${id}`)
                                        break
                                    }

                                    var currentPosition = currentAvailablePositions[0]
                                    console.log(`Adding map at position ${currentPosition}, ID: ${id}`)

                                    var mapData = {"id": id, "name": item.name, "type": item.type}
                                    gridModel.addMap(currentPosition, mapData)

                                } else if (item.type === "Event") {
                                    // Single event - recalculate available positions
                                    var currentAvailablePositions = findAvailablePositions(1)
                                    console.log(`DEBUG: Recalculated available positions for event ${id}: ${JSON.stringify(currentAvailablePositions)}`)

                                    if (currentAvailablePositions.length === 0) {
                                        console.warn(`No more positions available for event ${id}`)
                                        break
                                    }

                                    var currentPosition = currentAvailablePositions[0]
                                    console.log(`Adding event at position ${currentPosition}, name: ${item.name}`)

                                    gridModel.addEvent(currentPosition, item.name)
                                }

                                processedCount++
                                console.log(`DEBUG: Processed item ${processedCount}/${itemCount}, used ${positionIndex} positions so far`)
                            }

                            console.log(`DEBUG: Finished adding multiselect. Processed ${processedCount}/${itemCount} items, used ${positionIndex}/${cameraCount} positions`)
                        } else {
                            // Không đủ vị trí trống rời rạc, sử dụng vị trí mặc định
                            console.log(`Not enough scattered positions (${availablePositions.length} < ${cameraCount} cameras), using default positions`)

                            // Xử lý từng item với recalculation cho mỗi item
                            for (var id in multiData) {
                                var item = multiData[id]

                                // Recalculate available position cho mỗi item
                                var currentPosition = findFirstAvailablePosition()

                                if (!isPositionAvailable(currentPosition)) {
                                    console.warn(`No available position found for item ${id} (${item.type})`)
                                    break
                                }

                                if (item.type === "Camera") {
                                    try {
                                        console.log(`Adding camera at fallback position ${currentPosition}, ID: ${id}`)
                                        if (gridModel) {
                                            gridModel.addCameraRequested(currentPosition, id)
                                            // Preserve camera dimensions from drag data
                                            Qt.callLater(function() {
                                                console.log(`DEBUG: Updating camera dimensions to ${draggedCameraWidth}x${draggedCameraHeight} for position ${currentPosition}`)
                                                gridModel.updateCellDimensions(currentPosition, draggedCameraWidth, draggedCameraHeight)
                                            })
                                        }
                                    } catch (cameraError) {
                                        console.error(`Error processing camera in multiselect: ${cameraError}`)
                                    }
                                } else if (item.type === "Group") {
                                    console.log(`Adding group at fallback position ${currentPosition}, ID: ${id}`)
                                    if (gridModel) {
                                        gridModel.addGroupRequested(currentPosition, id)
                                    }
                                } else if (item.type === "Map") {
                                    console.log(`Adding map at fallback position ${currentPosition}, ID: ${id}, name: ${item.name}`)
                                    var mapData = {"id": id, "name": item.name, "type": item.type}
                                    gridModel.addMap(currentPosition, mapData)
                                } else if (item.type === "Event") {
                                    console.log(`Adding event at fallback position ${currentPosition}, name: ${item.name}`)
                                    gridModel.addEvent(currentPosition, item.name)
                                }
                            }
                        }
                    } catch (error) {
                        console.error(`Error processing multi-selection drop: ${error}`)
                    }
                }
                else if (drop.formats.includes("application/position")) {
                    var swapData = JSON.parse(drop.getDataAsString("application/position"))
                }

                // Xóa highlight ngay sau khi thả
                clearOccupiedCells()
            }
        }

        // Selection MouseArea (top layer) - tối ưu hóa để cải thiện hiệu suất
        MouseArea {
            id: selectionArea
            anchors.fill: parent
            acceptedButtons: Qt.LeftButton
            z: isDragging ? 1000 : 3  // Z cao khi đang kéo chọn, thấp khi không
            focus: true
            property point startPoint
            property bool isDragging: false
            property bool processingSelection: false // Thêm flag để tránh xử lý quá nhiều


            Rectangle {
                id: selectionRect
                visible: parent.isDragging
                color: isDarkTheme ? "#3B82F640" : "#60A5FA40"
                border.color: isDarkTheme ? "#3B82F6" : "#60A5FA"
                border.width: 1
                x: 0
                y: 0
                width: 0
                height: 0

                // Loại bỏ animation để tối ưu hiệu suất
            }

            onPressed: function(mouse) {
                forceActiveFocus()
                startPoint = Qt.point(mouse.x, mouse.y)

                // ✅ FIX: Luôn clear tất cả selection khi click vào grid (bất kể có nhấn Ctrl hay không)
                console.log("🔄 [SELECTION] Grid clicked - clearing all camera selection")
                var hasSelected = false
                for (var pos in videoGrid.activeItems) {
                    if (videoGrid.activeItems[pos] && videoGrid.activeItems[pos].isSelected) {
                        hasSelected = true
                        videoGrid.activeItems[pos].isSelected = false
                    }
                }

                if (hasSelected) {
                    console.log("🔄 [SELECTION] All cameras deselected from grid click")
                }

                // ✅ FIX: Reset selection rectangle
                isDragging = false
                selectionRect.visible = false
                selectionRect.width = 0
                selectionRect.height = 0
                selectionRect.x = 0
                selectionRect.y = 0
                console.log("🔄 [SELECTION] Selection rectangle reset")
            }

            // Hàm cập nhật selection - tách riêng để tái sử dụng
            function updateSelection(mouse) {
                var x = Math.min(startPoint.x, mouse.x)
                var y = Math.min(startPoint.y, mouse.y)
                var width = Math.abs(mouse.x - startPoint.x)
                var height = Math.abs(mouse.y - startPoint.y)

                selectionRect.x = x
                selectionRect.y = y
                selectionRect.width = width
                selectionRect.height = height

                // Tối ưu: Chỉ kiểm tra các camera có khả năng giao nhau với selection
                var selectionLeft = x
                var selectionRight = x + width
                var selectionTop = y
                var selectionBottom = y + height

                for (var pos in videoGrid.activeItems) {
                    var item = videoGrid.activeItems[pos]
                    if (!item) continue

                    // Đảm bảo item có thuộc tính isSelected
                    if (typeof item.isSelected === 'undefined') {
                        // console.log("Warning: item at position " + pos + " does not have isSelected property")
                        continue
                    }

                    // Kiểm tra nhanh xem camera có gần vùng selection không
                    var itemRect = item.mapToItem(gridContainer, 0, 0, item.width, item.height)
                    var itemLeft = itemRect.x
                    var itemRight = itemRect.x + item.width
                    var itemTop = itemRect.y
                    var itemBottom = itemRect.y + item.height

                    // Kiểm tra xem có giao nhau không
                    var intersects = !(
                        itemLeft > selectionRight ||
                        itemRight < selectionLeft ||
                        itemTop > selectionBottom ||
                        itemBottom < selectionTop
                    )

                    if (intersects) {
                        item.isSelected = true
                    } else if (!(mouse.modifiers & Qt.ControlModifier)) {
                        item.isSelected = false
                    }
                }
            }

            onPositionChanged: function(mouse) {
                if (mouse.buttons & Qt.LeftButton) {
                    isDragging = true

                    updateSelection(mouse)

                    // Sử dụng timer để giảm số lần cập nhật selection
                    if (!processingSelection) {
                        processingSelection = true
                    }
                }
            }

            onReleased: function(mouse) {
                isDragging = false
                selectionRect.width = 0
                selectionRect.height = 0
                processingSelection = false
            }

            Keys.onDeletePressed: function(event) {
                // console.log("Delete key pressed in selectionArea")
                // Tối ưu: Thu thập các camera được chọn một cách hiệu quả hơn
                var selectedItems = []
                for (var pos in videoGrid.activeItems) {
                    if (videoGrid.activeItems[pos] && videoGrid.activeItems[pos].isSelected) {
                        selectedItems.push(parseInt(pos))
                    }
                }

                // console.log("Selected items: " + JSON.stringify(selectedItems))

                if (selectedItems.length > 0) {
                    // Emit signal to grid manager instead of direct handling
                    if (gridModel) {
                        // Đặt isSave = false trước khi xóa
                        gridModel.isSave = false
                        // Gửi yêu cầu xóa items đến grid manager
                        gridModel.removeItemsRequested(selectedItems)
                    }
                    event.accepted = true
                } else {
                    // console.log("No cameras selected (top level)")
                }
            }
        }

        // MouseArea for grid resize
        MouseArea {
            id: gridResizeArea
            anchors.fill: gridContainer
            acceptedButtons: Qt.NoButton
            propagateComposedEvents: true
            z: 1

            onWheel: function(wheel) {
                // Kiểm tra xem có camera nào đang ở chế độ maximize không
                if (gridModel && gridModel.isMaximized) {
                    // Nếu đang ở chế độ maximize, chặn tất cả sự kiện wheel
                    // console.log("Blocking wheel event in maximize mode");
                    wheel.accepted = true;
                    return;
                }

                if (wheel.modifiers & Qt.ControlModifier) {
                    // ✅ THÊM THROTTLER: Chỉ xử lý nếu không trong thời gian throttle
                    if (wheelThrottleTimer.running) {
                        wheel.accepted = true
                        return // Bỏ qua wheel event nếu đang trong thời gian throttle
                    }

                    // Bắt đầu throttle timer
                    wheelThrottleTimer.start()

                    // Chỉ thay đổi kích thước lưới khi nhấn Ctrl
                    if (wheel.angleDelta.y < 0) {
                        // Đặt isSave = false trước khi thay đổi grid
                        if (gridModel) {
                            gridModel.isSave = false
                        }
                        // Cuộn lên: tăng số cột và hàng
                        if (gridModel.columns < maxColumns) {
                            var oldColumns = gridModel.columns
                            var newColumns = gridModel.columns + 1
                            var positions = {}

                            // Thu thập vị trí hiện tại của tất cả camera
                            for (var pos in videoGrid.activeItems) {
                                positions[pos] = parseInt(pos)
                            }

                            // Hàm chuyển đổi vị trí
                            function convertIndex(oldIndex, oldSize, newSize) {
                                var row = Math.floor(oldIndex / oldSize)
                                var col = oldIndex % oldSize
                                return row * newSize + col
                            }

                            // Tính toán vị trí mới cho tất cả camera
                            var newPositions = {}

                            for (var key in positions) {
                                newPositions[key] = convertIndex(positions[key], oldColumns, newColumns)
                            }

                            // Kiểm tra xem số hàng và số cột có bằng nhau không
                            var newRows = gridModel.rows + 1

                            // Nếu số hàng và số cột lệch nhau, điều chỉnh để grid là hình vuông
                            if (newColumns !== newRows) {
                                // Lấy giá trị lớn nhất giữa số cột và số hàng
                                var maxDimension = Math.max(newColumns, newRows)
                                // console.log("Điều chỉnh grid thành hình vuông: " + maxDimension + "x" + maxDimension)
                                // ✅ REFACTORED: Use signal instead of direct call
                                gridModel.setGridSizeRequested(maxDimension, maxDimension)
                            } else {
                                // Nếu đã bằng nhau, thay đổi kích thước bình thường
                                // ✅ REFACTORED: Use signal instead of direct call
                                gridModel.setGridSizeRequested(newColumns, newRows)
                            }

                            // Show neon grid highlight
                            showGridResizeHighlight()

                            // Di chuyển các camera sang vị trí mới
                            var sortedPositions = Object.keys(newPositions).sort((a, b) => parseInt(b) - parseInt(a))
                            for (var oldPos of sortedPositions) {
                                var newPos = newPositions[oldPos]
                                var item = videoGrid.activeItems[oldPos]
                                if (item) {
                                    var currentDimensions = gridModel.getCellDimensions(oldPos)

                                    // Cập nhật position và vị trí hiển thị
                                    item.position = newPos

                                    // Tính toán kích thước mới cho tất cả camera
                                    item.width = (videoGrid.width / gridModel.columns) * currentDimensions.width
                                    item.height = (videoGrid.height / gridModel.rows) * currentDimensions.height
                                    item.x = (newPos % gridModel.columns) * (videoGrid.width / gridModel.columns)
                                    item.y = Math.floor(newPos / gridModel.columns) * (videoGrid.height / gridModel.rows)

                                    // Cập nhật trong activeItems
                                    delete videoGrid.activeItems[oldPos]
                                    videoGrid.activeItems[newPos] = item

                                    // Thông báo cho GridModel chỉ khi position thực sự thay đổi
                                    if (parseInt(oldPos) !== newPos) {
                                        gridModel.updateVideoPosition(parseInt(oldPos), newPos)
                                    }
                                    // Cập nhật kích thước camera sau khi di chuyển
                                    gridModel.updateCellDimensions(newPos, currentDimensions.width, currentDimensions.height)
                                }
                            }
                        }
                    } else {
                        // Đặt isSave = false trước khi thay đổi grid
                        if (gridModel) {
                            gridModel.isSave = false
                        }
                        // Cuộn xuống: giảm số cột và hàng
                        var canReduce = true
                        var currentColumns = gridModel.columns
                        var currentRows = gridModel.rows

                        // Kiểm tra camera ở hàng cuối hoặc cột cuối
                        for (var pos in videoGrid.activeItems) {
                            var row = Math.floor(parseInt(pos) / currentColumns)
                            var col = parseInt(pos) % currentColumns
                            if (row === currentRows - 1 || col === currentColumns - 1) {
                                canReduce = false
                                break
                            }
                        }

                        if (canReduce && currentColumns > baseColumns) {
                            var oldColumns = currentColumns
                            var newColumns = currentColumns - 1
                            var positions = {}

                            // Thu thập vị trí hiện tại của tất cả camera
                            for (var pos in videoGrid.activeItems) {
                                positions[pos] = parseInt(pos)
                            }

                            // Hàm chuyển đổi vị trí
                            function convertIndex(oldIndex, oldSize, newSize) {
                                var row = Math.floor(oldIndex / oldSize)
                                var col = oldIndex % oldSize
                                return row * newSize + col
                            }

                            // Tính toán vị trí mới cho tất cả camera
                            var newPositions = {}
                            for (var key in positions) {
                                newPositions[key] = convertIndex(positions[key], oldColumns, newColumns)
                            }

                            // Kiểm tra xem số hàng và số cột có bằng nhau không
                            var newRows = gridModel.rows - 1

                            // Nếu số hàng và số cột lệch nhau, điều chỉnh để grid là hình vuông
                            if (newColumns !== newRows) {
                                // Lấy giá trị nhỏ nhất giữa số cột và số hàng, nhưng không nhỏ hơn 1
                                var minDimension = Math.max(1, Math.min(newColumns, newRows))
                                // console.log("Điều chỉnh grid thành hình vuông: " + minDimension + "x" + minDimension)
                                // ✅ REFACTORED: Use signal instead of direct call
                                gridModel.setGridSizeRequested(minDimension, minDimension)
                            } else {
                                // Nếu đã bằng nhau, thay đổi kích thước bình thường
                                // ✅ REFACTORED: Use signal instead of direct call
                                gridModel.setGridSizeRequested(newColumns, newRows)
                            }

                            // Show neon grid highlight
                            showGridResizeHighlight()

                            // Di chuyển các camera sang vị trí mới
                            var sortedPositions = Object.keys(newPositions).sort((b, a) => parseInt(b) - parseInt(a))
                            for (var oldPos of sortedPositions) {
                                var newPos = newPositions[oldPos]
                                var item = videoGrid.activeItems[oldPos]
                                if (item) {
                                    var currentDimensions = gridModel.getCellDimensions(oldPos)
                                    // Cập nhật position và vị trí hiển thị
                                    item.position = newPos

                                    // Tính toán kích thước mới cho tất cả camera
                                    item.width = (videoGrid.width / gridModel.columns) * currentDimensions.width
                                    item.height = (videoGrid.height / gridModel.rows) * currentDimensions.height
                                    item.x = (newPos % gridModel.columns) * (videoGrid.width / gridModel.columns)
                                    item.y = Math.floor(newPos / gridModel.columns) * (videoGrid.height / gridModel.rows)

                                    // Cập nhật trong activeItems
                                    delete videoGrid.activeItems[oldPos]
                                    videoGrid.activeItems[newPos] = item

                                    // Thông báo cho GridModel chỉ khi position thực sự thay đổi
                                    if (parseInt(oldPos) !== newPos) {
                                        gridModel.updateVideoPosition(parseInt(oldPos), newPos)
                                    }
                                    // Cập nhật kích thước camera sau khi di chuyển
                                    gridModel.updateCellDimensions(newPos, currentDimensions.width, currentDimensions.height)
                                }
                            }
                        }
                    }
                    wheel.accepted = true
                } else {
                    wheel.accepted = false
                }
            }
        }
    }

    // Timer để throttle wheel events - tránh xử lý quá nhiều events liên tiếp
    Timer {
        id: wheelThrottleTimer
        interval: 100 // 100ms throttle để tránh xử lý quá nhiều wheel events
        repeat: false
    }



    // Grid Full Dialog
    GridFullDialog {
        id: gridFullDialog
        anchors.centerIn: parent
        z: 1000 // Ensure dialog is on top

        onCloseRequested: {
            hide()
        }
    }
}
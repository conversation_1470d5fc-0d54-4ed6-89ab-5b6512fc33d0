import QtQuick
import QtQuick.Controls
import models 1.0

Rectangle {
    id: root
    color: {
        // Nếu đang maximize và là item active thì màu đen
        if (gridModel && gridModel.isMaximized && position === gridModel.activeItemPosition) {
            return "black"
        }
        // Nếu chưa có frame thì màu đen
        else if (!hasFrames && camera_id !== "") {
            return "black"
        }
        // Mặc định là transparent
        else {
            return "transparent"
        }
    }

    property string camera_id: ""
    property int position: -1
    property bool isPlaying: false
    property bool hasFrames: false
    property string videoState: "connecting"  // Default state là connecting thay vì "No Data"
    property int streamType: 0  // 0: main stream, 1: sub stream
    property int previousStreamType: 0  // Store previous stream type for restore
    property bool isSelected: false // biến quản lý trạng thái người dùng có đang focus vào gridItem này không
    property var cameraModel: null // Tham chiếu đến camera model
    property bool isActuallyVisible: false // <PERSON> dõi trạng thái thực sự visible
    // Debug property
    property int frameCount: 0

        // Signals
    signal clicked()
    signal doubleClicked()
    signal rightClicked()

    // Phương pháp đơn giản hơn: kiểm tra effective visibility
    function getEffectiveVisibility() {
        // Kiểm tra application state
        if (Qt.application.state !== Qt.ApplicationActive) {
            return false
        }

        // Kiểm tra window visibility (0=Hidden, 3=Minimized)
        if (root.Window.window && (root.Window.window.visibility === 0 || root.Window.window.visibility === 3)) {
            return false
        }

        // Kiểm tra basic properties
        if (!visible || width <= 0 || height <= 0) {
            return false
        }

        return true
    }

    // Function để kiểm tra và cập nhật trạng thái visibility
    function updateVisibilityState() {
        if (!videoItem) return

        var isEffectivelyVisible = getEffectiveVisibility()

        // Gửi effective visibility state cho Python
        videoItem.updateEffectiveVisibility(isEffectivelyVisible)
    }

    // Hàm cập nhật kích thước video
    function updateVideoSize() {
        if (videoItem && camera_id !== "") {
            // // console.log("[DEBUG_VIDEO_SIZE]: Updating video size to: " + width + "x" + height + " at position: " + position)
            videoItem.updateVideoSize(width, height)
        }
    }

    VideoModel {
        id: videoItem
        anchors.fill: parent
        position: root.position
        isPlaying: root.isPlaying
        isSelected: root.isSelected
        onClicked: root.clicked()
        onDoubleClicked: root.doubleClicked()
        onRightClicked: root.rightClicked()
        onFrameCountChanged: {
            root.frameCount = frameCount
            root.hasFrames = frameCount > 0
            // console.log("[DEBUG_FRAMES] Frame count changed:", frameCount, "hasFrames:", root.hasFrames, "videoState:", root.videoState)
        }
        onCameraStateChanged: function(state) {
            // console.log("[DEBUG_STATE] Camera state changed:", typeof(state), state, "frameCount:", frameCount, "hasFrames:", root.hasFrames)
            root.videoState = state
        }
        onActuallyVisibleChanged: function(visible) {
            root.isActuallyVisible = visible
        }
    }

    // Debug info
    // Text {
    //     anchors.top: parent.top
    //     anchors.left: parent.left
    //     anchors.margins: 5
    //     color: "white"
    //     font.pixelSize: 10
    //     text: "Frames: " + (videoItem ? videoItem.frameCount : 0) + "\nPos: " + position
    //     visible: isPlaying
    //     z: 2
    // }

    // Timer for throttling size change updates
    Timer {
        id: sizeChangeThrottleTimer
        interval: 100  // 100ms throttle
        repeat: false
        onTriggered: {
            // Invalidate caches when size actually changes
            invalidateAreaCache();
            invalidateStreamTypeCache();

            // Only update video size if width or height > 300px for performance
            if (width > 300 || height > 300) {
                // console.log("[CustomVideo] Throttled updateVideoSize:", width + "x" + height, "position:", position)
                updateVideoSize()

                // Check if stream type should change based on new area
                checkAndUpdateStreamType()
            }
        }
    }

    // Lắng nghe sự kiện thay đổi kích thước
    onWidthChanged: {
        // console.log("[CustomVideo] onWidthChanged:", width + "x" + height, "camera_id:", camera_id, "position:", position)
        // Use throttled update instead of immediate update
        sizeChangeThrottleTimer.restart()
    }

    onHeightChanged: {
        // console.log("[CustomVideo] onHeightChanged:", width + "x" + height, "camera_id:", camera_id, "position:", position)
        // Use throttled update instead of immediate update
        sizeChangeThrottleTimer.restart()
    }



    // Lắng nghe sự kiện phóng to/thu nhỏ từ GridModel
    Connections {
        target: gridModel
        function onIsMaximizedChanged() {
            // // console.log("[DEBUG_VIDEO_SIZE]: onIsMaximizedChanged: " + gridModel.isMaximized);

            // Invalidate caches when maximize state changes
            invalidateAreaCache();
            invalidateStreamTypeCache();

            // Only update video size if width or height > 300px for performance
            if (width > 300 || height > 300) {
                updateVideoSize();
            } else {
                console.log("[CustomVideo] Skipping updateVideoSize on maximize - size too small:", width + "x" + height)
            }

            // Only handle stream switching for the active item
            if (position !== gridModel.activeItemPosition) {
                return;
            }

            // Store current stream type before maximizing
            if (gridModel.isMaximized) {
                previousStreamType = streamType;
                // // console.log("[DEBUG_STREAM] Stored previous stream type:", previousStreamType, "for position:", position);
            } else {
                // When restoring, use the previous stream type
                switchStreamType(previousStreamType);
                // // console.log("[DEBUG_STREAM] Restored to previous stream type:", previousStreamType, "for position:", position);
                return;
            }

            // Update stream type for maximize state
            var newStreamType = determineStreamType();
            switchStreamType(newStreamType);
        }

        function onColumnsChanged() {
            // Invalidate caches when grid layout changes
            invalidateAreaCache();
            invalidateStreamTypeCache();
        }

        function onRowsChanged() {
            // Invalidate caches when grid layout changes
            invalidateAreaCache();
            invalidateStreamTypeCache();
        }

        // Handle when cell dimensions change
        function onCellDimensionsChanged(pos, width, height) {
            // // console.log("[DEBUG_STREAM] Cell dimensions changed at position:", pos);
            // Invalidate caches when cell dimensions change
            invalidateAreaCache();
            invalidateStreamTypeCache();

            updateStreamTypeForLayout();
        }


    }

    // Cache for area percentage calculation
    property real cachedAreaPercentage: 0
    property real lastVideoWidth: 0
    property real lastVideoHeight: 0
    property real lastGridWidth: 0
    property real lastGridHeight: 0
    property bool areaCacheValid: false

    // Function to calculate area percentage of this video relative to CameraGrid
    function calculateAreaPercentage() {
        if (!gridModel || !parent) return 0;

        // Find CameraGrid component by traversing up the parent hierarchy
        var cameraGrid = parent;
        while (cameraGrid && !cameraGrid.hasOwnProperty('cameraCount')) {
            cameraGrid = cameraGrid.parent;
        }

        if (!cameraGrid) {
            console.log("[CustomVideo] Could not find CameraGrid parent");
            return 0;
        }

        // Check if cache is still valid
        if (areaCacheValid &&
            lastVideoWidth === width &&
            lastVideoHeight === height &&
            lastGridWidth === cameraGrid.width &&
            lastGridHeight === cameraGrid.height) {
            // console.log("[CustomVideo] Using cached area percentage:", cachedAreaPercentage.toFixed(2) + "%");
            return cachedAreaPercentage;
        }

        // Calculate area percentage
        var videoArea = width * height;
        var gridArea = cameraGrid.width * cameraGrid.height;
        var percentage = (videoArea / gridArea) * 100;

        // Update cache
        cachedAreaPercentage = percentage;
        lastVideoWidth = width;
        lastVideoHeight = height;
        lastGridWidth = cameraGrid.width;
        lastGridHeight = cameraGrid.height;
        areaCacheValid = true;

        return percentage;
    }

    // Function to invalidate area cache
    function invalidateAreaCache() {
        areaCacheValid = false;
    }

    // Cache for stream type determination
    property int lastDeterminedStreamType: -1
    property bool lastIsMaximized: false
    property int lastColumns: 0
    property int lastRows: 0
    property int lastTotalItems: 0
    property bool streamTypeCacheValid: false

    // Function to determine stream type based on grid state and area
    function determineStreamType() {
        if (!gridModel) return 0;  // Default to main stream

        var isMaximized = gridModel.isMaximized;
        var columns = gridModel.columns;
        var rows = gridModel.rows;
        var activePositions = gridModel.getActivePositions();
        var totalItems = activePositions.length;

        // Check if we can use cached result
        if (streamTypeCacheValid &&
            lastIsMaximized === isMaximized &&
            lastColumns === columns &&
            lastRows === rows &&
            lastTotalItems === totalItems &&
            areaCacheValid) {
            // console.log("[DEBUG_STREAM] Using cached stream type:", lastDeterminedStreamType);
            return lastDeterminedStreamType;
        }

        // Calculate area percentage only when needed
        var areaPercentage = calculateAreaPercentage();

        // console.log("[DEBUG_STREAM] Grid state - Maximized:", isMaximized,
        //            "Size:", columns + "x" + rows,
        //            "Total items:", totalItems,
        //            "Area percentage:", areaPercentage.toFixed(2) + "%");

        var streamType = 0; // Default to main stream

        // When maximizing, always use main stream
        if (isMaximized) {
            console.log("[DEBUG_STREAM] Using main stream - maximized view");
            streamType = 0;  // main stream
        }
        // If video occupies 25% or more of CameraGrid area, use main stream
        else if (areaPercentage >= 20) {
            console.log(`[DEBUG_STREAM] Using main stream - large area (>= 25%), ${areaPercentage}`);

            streamType = 0;  // main stream
        }
        // // For normal grid state, determine based on size
        // else if (columns <= 2 && rows <= 2 && totalItems <= 4) {
        //     console.log("[DEBUG_STREAM] Using main stream - small grid");
        //     streamType = 0;  // main stream
        // }
        // // Use sub stream for larger grids or more items
        // else if ((columns > 2 || rows > 2) || totalItems > 4) {
        //     console.log("[DEBUG_STREAM] Using sub stream - large grid or many items");
        //     streamType = 1;  // sub stream
        // }
        else {
            // Default to main stream
            // console.log("[DEBUG_STREAM] Using main stream (default)");
            streamType = 1;  // main stream
        }

        // Update cache
        lastDeterminedStreamType = streamType;
        lastIsMaximized = isMaximized;
        lastColumns = columns;
        lastRows = rows;
        lastTotalItems = totalItems;
        streamTypeCacheValid = true;

        return streamType;
    }

    // Function to invalidate stream type cache
    function invalidateStreamTypeCache() {
        streamTypeCacheValid = false;
    }

    // Timer để đảm bảo việc chuyển đổi stream type được thực hiện trong thread UI chính
    Timer {
        id: switchStreamTimer
        interval: 50
        repeat: false
        property int newStreamType: 0

        onTriggered: {
            // console.log("[THREAD_SAFE] Switching stream type in UI thread:", streamType, "->", newStreamType, "at position:", position)
            try {
                // If playing, unregister old stream first
                if (isPlaying && camera_id !== "") {
                    // console.log("[THREAD_SAFE] Unregistering old stream before switching")
                    videoItem.unregister_video_capture();

                    // Update stream type
                    streamType = newStreamType;

                    // Register new stream
                    // console.log("[THREAD_SAFE] Registering new stream type:", streamType)
                    videoItem.register_video_capture({
                        "id": camera_id,
                        "streamType": streamType
                    });
                } else {
                    // If not playing, just update the stream type
                    streamType = newStreamType;
                }
            } catch (e) {
                console.error("[THREAD_SAFE] Error switching stream type:", e)
            }
        }
    }

    // Function to switch stream type with proper unregister/register
    function switchStreamType(newStreamType) {
        if (newStreamType !== streamType) {
            // console.log("[THREAD_SAFE] Stream type update requested:", streamType, "->", newStreamType, "at position:", position);

            // Sử dụng Timer để đảm bảo việc chuyển đổi stream type được thực hiện trong thread UI chính
            switchStreamTimer.newStreamType = newStreamType;
            switchStreamTimer.start();
        }
    }

    // Function to update stream type based on current layout
    function updateStreamTypeForLayout() {
        // Skip if maximized
        if (gridModel.isMaximized) return;

        // Update stream type based on new layout
        var newStreamType = determineStreamType();
        switchStreamType(newStreamType);
    }

    // Function to check and update stream type based on area percentage
    function checkAndUpdateStreamType() {
        // Skip if maximized (handled separately)
        if (gridModel && gridModel.isMaximized) return;

        // Skip if not playing
        if (!isPlaying || camera_id === "") return;

        // Determine new stream type based on current area
        var newStreamType = determineStreamType();

        // Only switch if different from current
        if (newStreamType !== streamType) {
            console.log("[CustomVideo] Area-based stream type change:", streamType, "->", newStreamType, "position:", position);
            switchStreamType(newStreamType);
        }
    }

    // Theo dõi thay đổi trạng thái từ cameraModel
    Connections {
        target: cameraModel
        function onStateChanged() {
            // console.log("[CONNECTION_STATE] Camera state changed:", cameraModel.state, "for camera:", camera_id)
            // Cập nhật lại biểu tượng trạng thái kết nối
            connectionStateIcon.source = connectionStateIcon.source  // Trigger binding re-evaluation
        }

        function onIsRecordingChanged() {
            // console.log("[RECORDING_STATE] Recording state changed:", cameraModel.isRecording, "for camera:", camera_id)
            // Cập nhật lại biểu tượng ghi hình
            recordingIcon.source = recordingIcon.source  // Trigger binding re-evaluation
        }
    }

    Component.onCompleted: {
        // // console.log("[DEBUG_VIDEO_SIZE]: CustomVideo created at position:", position);
        // Only update video size if width or height > 300px for performance
        if (width > 300 || height > 300) {
            updateVideoSize();
        } else {
            console.log("[CustomVideo] Skipping updateVideoSize on creation - size too small:", width + "x" + height)
        }

        // Initialize stream type
        if (gridModel) {
            streamType = determineStreamType();
            // // console.log("[DEBUG_STREAM] Initial stream type:", streamType);
        }

        // Log camera model state if available
        if (cameraModel) {
            // console.log("[CONNECTION_STATE] Initial camera state:", cameraModel.state, "for camera:", camera_id)
        }

        // Kiểm tra visibility ban đầu
        updateVisibilityState()

        // console.log("QML load xong! ", root.videoState)
    }

    // Timer để đảm bảo việc đăng ký video capture được thực hiện trong thread UI chính
    Timer {
        id: registerTimer
        interval: 50
        repeat: false
        onTriggered: {
            // console.log("[THREAD_SAFE] Registering video capture in UI thread for camera:", camera_id, "at position:", position)
            try {
                if (isPlaying && camera_id !== "") {
                    // Pass stream type to register_video_capture
                    videoItem.register_video_capture({
                        "id": camera_id,
                        "streamType": streamType
                    });
                    hasFrames = false;

                    // Thông báo cho gridModel biết item đã đăng ký video capture
                    if (position !== -1) {
                        gridModel.itemRegisteredVideocapture(position, camera_id);
                    }
                }
            } catch (e) {
                console.error("[THREAD_SAFE] Error registering video capture:", e)
            }
        }
    }


    // Thêm signal để thông báo khi trạng thái playing thay đổi
    onIsPlayingChanged: {
        // // // console.log("[THREAD_SAFE] Playing state changed to:", isPlaying, "at position:", position);

        if (isPlaying && camera_id !== "") {
            // Sử dụng Timer để đảm bảo việc đăng ký được thực hiện trong thread UI chính
            registerTimer.start();

            // Khi bắt đầu play, kiểm tra lại visibility state
            updateVisibilityState()
        }
        else if (!isPlaying) {
            // Khi stop playing, cũng cập nhật visibility
            videoItem.unregister_video_capture()
            updateVisibilityState()
        }
    }

    // Logic kiểm tra và xử lý khi visibility thực sự thay đổi
    onIsActuallyVisibleChanged: {
        // console.log("[VISIBILITY] Actually visible changed to:", isActuallyVisible,
        //             "isPlaying:", isPlaying, "camera_id:", camera_id, "at position:", position)

        // Nếu không còn visible thực sự và đang play
        if (!isActuallyVisible && isPlaying && camera_id !== "") {
            // console.log("[VISIBILITY] Pausing video capture - not actually visible")
            // Có thể tạm dừng video capture để tiết kiệm tài nguyên
            if (videoItem && videoItem.video_capture) {
                // Implement pause logic if needed
            }
        }
        // Nếu trở lại visible và đang play
        else if (isActuallyVisible && isPlaying && camera_id !== "") {
            // console.log("[VISIBILITY] Resuming video capture - now actually visible")
            // Resume video capture if it was paused
            if (videoItem && videoItem.video_capture) {
                // Implement resume logic if needed
            }
        }
    }



    property int dotCount: 0
    property string connectingText: qsTr("Connecting")

    Timer {
        id: dotAnimationTimer
        interval: 500
        running: {
            // Nếu có frames thì không chạy animation
            if (root.hasFrames) return false
            // Chỉ chạy khi connecting hoặc không có data
            return (root.videoState === "connecting" || !root.hasFrames) && camera_id !== ""
        }
        repeat: true
        onTriggered: {
            dotCount = (dotCount + 1) % 4
            connectingText = qsTr("Connecting") + ".".repeat(dotCount)
        }
    }

    Text {
        anchors.centerIn: parent
        anchors.verticalCenterOffset: Math.max(25, Math.min(35, root.height / 12))  // Responsive offset
        color: "white"
        font.pixelSize: Math.max(12, Math.min(16, root.width / 20))  // Responsive font size
        text: (function() {
            // Ưu tiên: Nếu có frames thì không hiển thị text
            if (root.hasFrames) {
                return ""; // Không hiển thị text khi có frame
            }

            // Nếu không có frames, kiểm tra trạng thái
            if (root.videoState === "stopped" || root.videoState === "disconnected") {
                return qsTr("Disconnected");
            } else {
                // Default: hiển thị connecting khi không có frames
                return connectingText;
            }
        })()
        visible: text !== "" // Chỉ hiển thị khi có text
        z: 4  // Z-index cao hơn icon để text hiển thị trên icon
    }

    // Loading animation ở giữa màn hình khi connecting
    Item {
        id: loadingContainer
        anchors.centerIn: parent
        anchors.verticalCenterOffset: -15  // Đẩy icon lên trên một chút để text ở dưới
        width: Math.max(24, Math.min(48, root.width / 8))  // Responsive size, min 32, max 48
        height: width
        visible: {
            // Nếu có frames thì không hiển thị loading icon
            if (root.hasFrames) return false

            var isConnecting = false
            // Ưu tiên sử dụng trạng thái từ cameraModel nếu có
            if (cameraModel && cameraModel.state) {
                isConnecting = cameraModel.state === "connecting"
            } else {
                // Fallback sử dụng trạng thái nội bộ hoặc khi không có data
                isConnecting = root.videoState === "connecting" || !root.hasFrames
            }
            return isConnecting && camera_id !== ""
        }
        z: 3

        Image {
            id: loadingIcon
            anchors.centerIn: parent
            width: parent.width
            height: parent.height
            source: "qrc:src/assets/state/Loading.png"
            fillMode: Image.PreserveAspectFit

            // Animation quay
            RotationAnimation {
                target: loadingIcon
                property: "rotation"
                from: 0
                to: 360
                duration: 2000
                loops: Animation.Infinite
                running: loadingContainer.visible
            }
        }
    }

    // Icon disconnected ở giữa màn hình
    Item {
        id: disconnectedContainer
        anchors.centerIn: parent
        anchors.verticalCenterOffset: -15  // Đẩy icon lên trên một chút để text ở dưới
        width: Math.max(24, Math.min(48, root.width / 8))  // Responsive size, min 32, max 48
        height: width
        visible: {
            // Nếu có frames thì không hiển thị disconnected icon
            if (root.hasFrames) return false

            var isDisconnected = false
            // Ưu tiên sử dụng trạng thái từ cameraModel nếu có
            if (cameraModel && cameraModel.state) {
                isDisconnected = cameraModel.state === "disconnected"
            } else {
                // Fallback sử dụng trạng thái nội bộ
                isDisconnected = root.videoState === "stopped" || root.videoState === "disconnected"
            }
            return isDisconnected && camera_id !== ""
        }
        z: 3

        Image {
            id: disconnectedIcon
            anchors.centerIn: parent
            width: parent.width
            height: parent.height
            source: "qrc:src/assets/state/Disconnected.png"
            fillMode: Image.PreserveAspectFit
        }
    }



    // Biểu tượng ghi hình (recording)
    Image {
        id: recordingIcon
        anchors.left: parent.left
        anchors.bottom: parent.bottom
        anchors.margins: 5
        width: 24
        height: 24
        source: {
            if (cameraModel && cameraModel.isRecording) {
                return "qrc:src/assets/record_state_on.svg"
            } else {
                return ""
            }
        }
        visible: source !== "" && camera_id !== ""
        z: 2
    }

    // Kiểm tra visibility khi các property quan trọng thay đổi
    onVisibleChanged: {
        updateVisibilityState()
    }

    // Kiểm tra khi window state thay đổi
    Connections {
        target: root.Window.window
        function onVisibilityChanged() {
            updateVisibilityState()
        }

        function onActiveChanged() {
            updateVisibilityState()
        }
    }

    // Kiểm tra application state
    Connections {
        target: Qt.application
        function onStateChanged() {
            // console.log("[VISIBILITY] Application state changed to:", Qt.application.state, "at position:", position)
            updateVisibilityState()
        }
    }

    // Component.onDestruction: {
    //     if (camera_id !== "") {
    //         // console.log("[THREAD_SAFE] Unregistering video capture during destruction")
    //         videoItem.unregister_video_capture()
    //         hasFrames = false
    //     }
    // }
}
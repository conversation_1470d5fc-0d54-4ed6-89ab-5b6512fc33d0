import QtQuick
import QtQuick.Controls

Item {
    id: dragZoomControl
    anchors.fill: parent
    visible: false
    z: 100 // Đ<PERSON>m bảo hiển thị trên cùng

    property bool isDarkTheme: true
    property point startPoint: Qt.point(0, 0)
    property point endPoint: Qt.point(0, 0)
    property bool isDrawing: false
    property bool isActive: false

    // Tín hiệu khi người dùng vẽ hình chữ nhật để zoom
    signal dragZoom(point startPoint, point endPoint)

    // Tín hiệu khi người dùng click để tắt
    signal dragZoomClosed()

    // Canvas để vẽ hình chữ nhật khi kéo thả
    Can<PERSON> {
        id: dragCanvas
        anchors.fill: parent

        onPaint: {
            var ctx = getContext("2d");
            ctx.clearRect(0, 0, width, height);

            // Chỉ vẽ khi đang trong trạng thái vẽ
            if (isDrawing) {
                // Lưu trạng thái
                ctx.save();

                // Vẽ hình chữ nhật
                ctx.beginPath();

                // T<PERSON>h toán tọa độ và kích thước hình chữ nhật
                var rectX = Math.min(startPoint.x, endPoint.x);
                var rectY = Math.min(startPoint.y, endPoint.y);
                var rectWidth = Math.abs(endPoint.x - startPoint.x);
                var rectHeight = Math.abs(endPoint.y - startPoint.y);

                // Vẽ hình chữ nhật với viền đứt
                ctx.setLineDash([5, 3]);
                ctx.strokeStyle = isDarkTheme ? "white" : "black";
                ctx.lineWidth = 2;
                ctx.strokeRect(rectX, rectY, rectWidth, rectHeight);

                // Vẽ hình chữ nhật bán trong suốt
                ctx.fillStyle = "rgba(255, 255, 255, 0.2)";
                ctx.fillRect(rectX, rectY, rectWidth, rectHeight);

                // Khôi phục trạng thái
                ctx.restore();
            }
        }
    }

    // Vùng nhận sự kiện chuột
    MouseArea {
        id: dragZoomMouseArea
        anchors.fill: parent
        hoverEnabled: true
        propagateComposedEvents: false // Không cho phép sự kiện truyền qua mặc định

        // Phương thức kiểm tra xem chuột có nằm trong vùng của Flow button không
        function isInFlowButtonArea(mouseX, mouseY) {
            var parentItem = parent.parent; // GridItem
            var controlButtonsRow = null;

            // Tìm controlButtonsRow trong parent
            for (var i = 0; i < parentItem.children.length; i++) {
                if (parentItem.children[i].objectName === "controlButtonsRow") {
                    controlButtonsRow = parentItem.children[i];
                    break;
                }
            }

            // Kiểm tra xem chuột có nằm trong vùng của Flow button không
            if (controlButtonsRow && controlButtonsRow.visible) {
                // Chuyển đổi tọa độ chuột từ hệ tọa độ của DragZoomControl sang hệ tọa độ của parent
                var mousePos = mapToItem(parentItem, mouseX, mouseY);

                // Lấy vị trí và kích thước của Flow button
                var buttonRect = Qt.rect(
                    controlButtonsRow.x,
                    controlButtonsRow.y,
                    controlButtonsRow.width,
                    controlButtonsRow.height
                );

                // Kiểm tra xem chuột có nằm trong vùng của Flow button không
                return (
                    mousePos.x >= buttonRect.x &&
                    mousePos.x <= buttonRect.x + buttonRect.width &&
                    mousePos.y >= buttonRect.y &&
                    mousePos.y <= buttonRect.y + buttonRect.height
                );
            } else {
                // Nếu không tìm thấy controlButtonsRow, sử dụng cách tiếp cận đơn giản hơn
                return (mouseX > width * 0.8 && mouseY < height * 0.2);
            }
        }

        onPressed: function(mouse) {
            // Kiểm tra xem chuột có nằm trong vùng của Flow button không
            if (isInFlowButtonArea(mouse.x, mouse.y)) {
                // Nếu nằm trong vùng Flow button, cho phép sự kiện truyền qua
                mouse.accepted = false;
            } else {
                // Nếu không nằm trong vùng Flow button, bắt đầu vẽ
                isDrawing = true;
                startPoint = Qt.point(mouse.x, mouse.y);
                endPoint = startPoint;
                dragCanvas.requestPaint();
                mouse.accepted = true;
            }
        }

        onPositionChanged: function(mouse) {
            // Kiểm tra xem chuột có nằm trong vùng của Flow button không
            var inFlowArea = isInFlowButtonArea(mouse.x, mouse.y);

            // Thay đổi hình dạng con trỏ chuột dựa trên vị trí
            dragZoomMouseArea.cursorShape = inFlowArea ? Qt.ArrowCursor : Qt.CrossCursor;

            if (isDrawing) {
                endPoint = Qt.point(mouse.x, mouse.y);
                dragCanvas.requestPaint();
                mouse.accepted = true;
            } else {
                // Nếu nằm trong vùng Flow button, cho phép sự kiện truyền qua
                mouse.accepted = !inFlowArea;
            }
        }

        onReleased: function(mouse) {
            // Kiểm tra xem chuột có nằm trong vùng của Flow button không
            if (isInFlowButtonArea(mouse.x, mouse.y)) {
                // Nếu nằm trong vùng Flow button, cho phép sự kiện truyền qua
                mouse.accepted = false;
                return;
            }

            if (isDrawing) {
                isDrawing = false;

                // Kiểm tra xem hình chữ nhật có kích thước tối thiểu không
                var rectWidth = Math.abs(endPoint.x - startPoint.x);
                var rectHeight = Math.abs(endPoint.y - startPoint.y);

                if (rectWidth > 10 && rectHeight > 10) {
                    // Lưu lại điểm bắt đầu và kết thúc
                    var savedStartPoint = Qt.point(startPoint.x, startPoint.y);
                    var savedEndPoint = Qt.point(endPoint.x, endPoint.y);

                    // Xóa hình chữ nhật ngay lập tức
                    startPoint = Qt.point(0, 0);
                    endPoint = Qt.point(0, 0);
                    dragCanvas.requestPaint();

                    // Phát tín hiệu dragZoom với điểm bắt đầu và kết thúc đã lưu
                    dragZoom(savedStartPoint, savedEndPoint);
                } else {
                    // Xóa hình chữ nhật nếu kích thước quá nhỏ
                    startPoint = Qt.point(0, 0);
                    endPoint = Qt.point(0, 0);
                    dragCanvas.requestPaint();
                }

                mouse.accepted = true;
            } else {
                mouse.accepted = false;
            }
        }

        onClicked: function(mouse) {
            // Kiểm tra xem chuột có nằm trong vùng của Flow button không
            if (isInFlowButtonArea(mouse.x, mouse.y)) {
                // Nếu nằm trong vùng Flow button, cho phép sự kiện truyền qua
                mouse.accepted = false;
                return;
            }

            // Nếu chỉ là click (không phải kéo), chỉ xóa hình vẽ nếu có
            if (Math.abs(endPoint.x - startPoint.x) < 5 && Math.abs(endPoint.y - startPoint.y) < 5) {
                // Xóa hình chữ nhật nếu có
                startPoint = Qt.point(0, 0);
                endPoint = Qt.point(0, 0);
                dragCanvas.requestPaint();
                mouse.accepted = true;
            }
        }
    }

    // Hiển thị hướng dẫn sử dụng
    Rectangle {
        id: helpTextBackground
        anchors.bottom: parent.bottom
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottomMargin: 20
        width: Math.min(helpText.implicitWidth + 20, parent.width * 0.9) // Giới hạn chiều rộng tối đa là 90% chiều rộng của parent
        height: helpText.height + 10
        color: isDarkTheme ? "rgba(0, 0, 0, 0.7)" : "rgba(255, 255, 255, 0.7)"
        radius: 5

        Text {
            id: helpText
            anchors.centerIn: parent
            width: parent.width - 20 // Đảm bảo text không vượt quá chiều rộng của rectangle
            text: "Vẽ hình chữ nhật để zoom vào khu vực. Nhấn nút Drag Zoom để thoát."
            color: isDarkTheme ? "white" : "black"
            font.pixelSize: 14
            elide: Text.ElideRight // Hiển thị dấu "..." nếu text quá dài
            horizontalAlignment: Text.AlignHCenter // Căn giữa text
        }
    }

    // Phương thức để hiển thị UI điều khiển Drag Zoom
    function show() {
        // Reset các thuộc tính
        isDrawing = false;
        startPoint = Qt.point(0, 0);
        endPoint = Qt.point(0, 0);

        // Xóa canvas
        dragCanvas.requestPaint();

        // Hiển thị UI
        dragZoomControl.visible = true;
    }

    // Phương thức để ẩn UI điều khiển Drag Zoom
    function hide() {
        // Reset các thuộc tính
        isDrawing = false;
        startPoint = Qt.point(0, 0);
        endPoint = Qt.point(0, 0);

        // Xóa canvas
        dragCanvas.requestPaint();

        // Ẩn UI và phát tín hiệu đóng
        dragZoomControl.visible = false;
        dragZoomClosed();
    }
}

import QtQuick
import QtQuick.Controls

Item {
    id: ptz3dControl
    anchors.fill: parent
    visible: false
    z: 100 // Đảm bảo hiển thị trên cùng

    property bool isDarkTheme: true
    property point mousePosition: Qt.point(width / 2, height / 2) // Khởi tạo ở tâm
    property bool isActive: false
    property point centerPoint: Qt.point(width / 2, height / 2)
    property bool isDrawingLine: false // Trạng thái vẽ đường thẳng
    property real arrowSize: 10 // Kích thước của mũi tên
    property real rectSize: 30 // Kích thước của hình chữ nhật ở tâm

    // Cập nhật centerPoint khi kích thước thay đổi
    onWidthChanged: updateCenterPoint()
    onHeightChanged: updateCenterPoint()

    // Hàm cập nhật centerPoint
    function updateCenterPoint() {
        centerPoint = Qt.point(width / 2, height / 2)
        lineCanvas.requestPaint() // Vẽ lại canvas với tâm mới
    }

    // Thuộc tính để theo dõi điểm cuối cùng đã gửi
    property point lastSentEndPoint: Qt.point(0, 0)
    property int minMovementThreshold: 3 // Ngưỡng tối thiểu để gửi sự kiện PTZ (3px)

    // Không cần các thuộc tính và timer cho zoom
    // Sử dụng zoomMouseArea trong GridItem để xử lý zoom

    // Tín hiệu khi người dùng di chuyển PTZ
    signal ptzMove(point startPoint, point endPoint)

    // Tín hiệu khi người dùng dừng di chuyển PTZ
    signal ptzStop()

    // Tín hiệu khi người dùng click để tắt PTZ
    signal ptzClosed()

    // Tín hiệu khi người dùng sử dụng wheel để zoom
    signal ptzZoom(real factor)

    // Canvas để vẽ hình chữ nhật có 4 góc không có cạnh và đường thẳng
    Canvas {
        id: lineCanvas
        anchors.fill: parent

        onPaint: {
            var ctx = getContext("2d");
            ctx.reset();

            // Đảm bảo sử dụng giá trị centerPoint mới nhất
            var centerX = ptz3dControl.width / 2;
            var centerY = ptz3dControl.height / 2;
            // Cập nhật centerPoint nếu cần
            if (centerPoint.x !== centerX || centerPoint.y !== centerY) {
                centerPoint = Qt.point(centerX, centerY);
            }

            var rectWidth = rectSize;
            var rectHeight = rectSize;
            var cornerLength = rectSize * 0.2; // Độ dài của các đoạn góc

            // Vẽ hình chữ nhật có 4 góc không có cạnh
            ctx.strokeStyle = isDarkTheme ? "#FFFFFF" : "#000000";
            ctx.lineWidth = 2;

            // Góc trên bên trái
            ctx.beginPath();
            ctx.moveTo(centerX - rectWidth/2, centerY - rectHeight/2);
            ctx.lineTo(centerX - rectWidth/2 + cornerLength, centerY - rectHeight/2);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(centerX - rectWidth/2, centerY - rectHeight/2);
            ctx.lineTo(centerX - rectWidth/2, centerY - rectHeight/2 + cornerLength);
            ctx.stroke();

            // Góc trên bên phải
            ctx.beginPath();
            ctx.moveTo(centerX + rectWidth/2, centerY - rectHeight/2);
            ctx.lineTo(centerX + rectWidth/2 - cornerLength, centerY - rectHeight/2);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(centerX + rectWidth/2, centerY - rectHeight/2);
            ctx.lineTo(centerX + rectWidth/2, centerY - rectHeight/2 + cornerLength);
            ctx.stroke();

            // Góc dưới bên trái
            ctx.beginPath();
            ctx.moveTo(centerX - rectWidth/2, centerY + rectHeight/2);
            ctx.lineTo(centerX - rectWidth/2 + cornerLength, centerY + rectHeight/2);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(centerX - rectWidth/2, centerY + rectHeight/2);
            ctx.lineTo(centerX - rectWidth/2, centerY + rectHeight/2 - cornerLength);
            ctx.stroke();

            // Góc dưới bên phải
            ctx.beginPath();
            ctx.moveTo(centerX + rectWidth/2, centerY + rectHeight/2);
            ctx.lineTo(centerX + rectWidth/2 - cornerLength, centerY + rectHeight/2);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(centerX + rectWidth/2, centerY + rectHeight/2);
            ctx.lineTo(centerX + rectWidth/2, centerY + rectHeight/2 - cornerLength);
            ctx.stroke();

            // Vẽ đường thẳng từ tâm đến vị trí chuột nếu đang vẽ
            if (ptz3dControl.isActive && isDrawingLine) {
                var angle = Math.atan2(mousePosition.y - centerY, mousePosition.x - centerX);

                // Vẽ đường thẳng từ tâm đến chính xác vị trí chuột
                ctx.beginPath();
                ctx.moveTo(centerX, centerY);
                ctx.lineTo(mousePosition.x, mousePosition.y);
                ctx.strokeStyle = isDarkTheme ? "#FFFFFF" : "#000000";
                ctx.lineWidth = 2;
                ctx.stroke();
            }
        }
    }

    // Mũi tên di chuyển theo chuột
    Canvas {
        id: arrowCanvas
        width: arrowSize * 3  // Tăng kích thước để đảm bảo vẽ đủ mũi tên
        height: arrowSize * 3 // Tăng kích thước để đảm bảo vẽ đủ mũi tên
        // Đặt vị trí để đỉnh của mũi tên nằm đúng vị trí chuột
        x: mousePosition.x - width / 2
        y: mousePosition.y - height / 2
        visible: ptz3dControl.visible && ptz3dControl.isActive

        // Góc giữa hai cạnh của mũi tên (đơn vị: độ)
        property real arrowAngle: 35

        // Điểm đầu mũi tên (điểm gốc)
        property point arrowTip: Qt.point(width / 2, height / 2)

        // Xoay mũi tên theo hướng di chuyển
        property real rotation: {
            if (!ptz3dControl.isActive) return 0;

            var centerX = ptz3dControl.width / 2;
            var centerY = ptz3dControl.height / 2;
            var dx = mousePosition.x - centerX;
            var dy = mousePosition.y - centerY;

            // Tính góc xoay (đơn vị: độ)
            return Math.atan2(dy, dx) * 180 / Math.PI;
        }

        onPaint: {
            var ctx = getContext("2d");
            ctx.reset();

            // Lưu trạng thái hiện tại
            ctx.save();

            // Di chuyển gốc tọa độ đến tâm canvas
            ctx.translate(width / 2, height / 2);

            // Xoay theo hướng di chuyển
            ctx.rotate(rotation * Math.PI / 180);

            // Vẽ mũi tên
            ctx.beginPath();

            // Điểm đầu mũi tên (đỉnh nhọn)
            var tipX = 0;
            var tipY = 0;

            // Tính toán hai điểm còn lại của mũi tên
            var angle1 = (180 - arrowAngle / 2) * Math.PI / 180;
            var angle2 = (180 + arrowAngle / 2) * Math.PI / 180;

            var point1X = tipX + arrowSize * Math.cos(angle1);
            var point1Y = tipY + arrowSize * Math.sin(angle1);

            var point2X = tipX + arrowSize * Math.cos(angle2);
            var point2Y = tipY + arrowSize * Math.sin(angle2);

            // Vẽ mũi tên
            ctx.moveTo(tipX, tipY);
            ctx.lineTo(point1X, point1Y);
            ctx.moveTo(tipX, tipY);
            ctx.lineTo(point2X, point2Y);

            // Thiết lập màu và độ dày
            ctx.lineWidth = 2;
            ctx.strokeStyle = isDarkTheme ? "white" : "black";
            ctx.stroke();

            // Khôi phục trạng thái
            ctx.restore();
        }

        // Cập nhật khi vị trí chuột thay đổi
        onXChanged: requestPaint()
        onYChanged: requestPaint()
        onRotationChanged: requestPaint()
    }

    // Vùng nhận sự kiện chuột
    MouseArea {
        id: ptz3dMouseArea
        anchors.fill: parent
        hoverEnabled: true
        cursorShape: Qt.BlankCursor // Ẩn con trỏ chuột mặc định
        propagateComposedEvents: true // Cho phép sự kiện truyền qua

        // Biến để theo dõi hướng zoom trước đó
        property string previousZoomDirection: ""

        // Không xử lý sự kiện wheel trong PTZ3DControl
        // Để zoomMouseArea trong GridItem xử lý tất cả các sự kiện wheel
        onWheel: function(wheel) {
            // Chỉ kiểm tra xem chuột có nằm trong vùng của Flow button không
            var isInButtonArea = isInFlowButtonArea(wheel.x, wheel.y);

            // Nếu nằm trong vùng của Flow button, chặn sự kiện
            if (isInButtonArea) {
                wheel.accepted = true;
            } else {
                // Cho phép sự kiện truyền qua đến zoomMouseArea trong GridItem
                wheel.accepted = false;
            }
        }

        onPositionChanged: function(mouse) {
            if (ptz3dControl.visible) {
                mousePosition = Qt.point(mouse.x, mouse.y);

                // Kiểm tra xem chuột có nằm trong vùng của Flow button không
                var isInButtonArea = isInFlowButtonArea(mouse.x, mouse.y);

                // Thay đổi hình dạng con trỏ chuột dựa trên vị trí
                ptz3dMouseArea.cursorShape = isInButtonArea ? Qt.ArrowCursor : Qt.BlankCursor;

                // Chỉ cập nhật canvas để vẽ lại khi đang vẽ đường thẳng và không ở trong vùng của Flow button
                if (isDrawingLine && !isInButtonArea) {
                    lineCanvas.requestPaint();
                }

                // Chỉ phát tín hiệu di chuyển PTZ nếu đang vẽ đường thẳng, nút chuột trái được nhấn, và không ở trong vùng của Flow button
                if (isDrawingLine && (mouse.buttons & Qt.LeftButton) && !isInButtonArea) {
                    // Tính toán điểm kết thúc
                    var endPoint = Qt.point(mouse.x, mouse.y);

                    // Chỉ xử lý nếu chuột đã di chuyển đủ xa so với lần cuối cùng
                    if (hasMovedEnough(endPoint)) {
                        // Gửi sự kiện di chuyển PTZ với giới hạn tần suất
                        sendPTZMove(endPoint);
                    }

                    // Ngăn sự kiện truyền qua khi đang vẽ đường thẳng
                    mouse.accepted = true;
                } else {
                    // Cho phép sự kiện truyền qua đến các phần tử bên dưới
                    mouse.accepted = false;
                }
            }
        }

        onPressed: function(mouse) {
            // Kiểm tra xem chuột có nằm trong vùng của Flow button không
            var isInButtonArea = isInFlowButtonArea(mouse.x, mouse.y);

            // Chỉ xử lý sự kiện nếu không nằm trong vùng của Flow button
            if (mouse.button === Qt.LeftButton && !isInButtonArea) {
                isDrawingLine = true;
                lineCanvas.requestPaint();

                // Tính toán điểm bắt đầu và điểm kết thúc
                var centerX = width / 2;
                var centerY = height / 2;
                var startPoint = Qt.point(centerX, centerY);
                var endPoint = Qt.point(mouse.x, mouse.y);

                // Khởi tạo điểm cuối cùng đã gửi
                lastSentEndPoint = endPoint;

                // Khởi tạo thời gian gửi cuối cùng
                lastSendTime = new Date().getTime();

                // Đặt lại trạng thái
                hasPendingMove = false;
                throttleTimer.stop();

                // Phát tín hiệu di chuyển PTZ với điểm bắt đầu và điểm kết thúc
                ptzMove(startPoint, endPoint);

                // Ngăn sự kiện truyền qua khi đang vẽ đường thẳng
                mouse.accepted = true;
            } else {
                // Cho phép sự kiện truyền qua đến các phần tử bên dưới
                mouse.accepted = false;
            }
        }

        onReleased: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                // Đặt isDrawingLine = false để không vẽ đường thẳng khi nhả chuột
                isDrawingLine = false;
                lineCanvas.requestPaint();

                // Dừng timer và đặt lại trạng thái
                throttleTimer.stop();
                hasPendingMove = false;

                // Phát tín hiệu dừng PTZ khi nhả chuột
                console.log("Mouse released - stopping PTZ");
                ptzStop();
            }

            // Cho phép sự kiện truyền qua đến các phần tử bên dưới
            mouse.accepted = false;
        }

        onEntered: {
            ptz3dControl.isActive = true;
        }

        onExited: {
            ptz3dControl.isActive = false;
            isDrawingLine = false;
            lineCanvas.requestPaint();

            // Dừng timer và đặt lại trạng thái
            throttleTimer.stop();
            hasPendingMove = false;

            // Không cần dừng timer zoom vì đã sử dụng zoomMouseArea trong GridItem

            // Phát tín hiệu dừng PTZ khi chuột rời khỏi vùng điều khiển
            ptzStop();
        }
    }

    // Hiển thị hướng dẫn
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottomMargin: 20
        width: Math.min(helpText.implicitWidth + 20, parent.width * 0.9) // Giới hạn chiều rộng tối đa là 90% chiều rộng của parent
        height: helpText.height + 10
        radius: 5
        color: isDarkTheme ? "rgba(0, 0, 129, 0.3)" : "rgba(255, 255, 255, 0.3)" // Làm nền xuyên thấu hơn
        visible: ptz3dControl.visible

        Text {
            id: helpText
            anchors.centerIn: parent
            width: parent.width - 20 // Đảm bảo text không vượt quá chiều rộng của rectangle
            text: "Di chuyển chuột để điều khiển PTZ 3D. Lăn chuột để zoom. Click nút PTZ 3D để thoát."
            color: isDarkTheme ? "white" : "black"
            font.pixelSize: 14
            elide: Text.ElideRight // Hiển thị dấu "..." nếu text quá dài
            horizontalAlignment: Text.AlignHCenter // Căn giữa text
        }
    }

    // Phương thức để hiển thị UI điều khiển PTZ
    function show() {
        // Cập nhật tâm và vẽ lại canvas trước khi hiển thị
        updateCenterPoint();
        // Đặt vị trí chuột ban đầu ở tâm
        mousePosition = centerPoint;
        // Hiển thị UI
        ptz3dControl.visible = true;
    }

    // Phương thức để ẩn UI điều khiển PTZ
    function hide() {
        ptz3dControl.visible = false;
        ptzClosed();
    }

    // Thuộc tính để theo dõi thời gian gửi sự kiện cuối cùng
    property var lastSendTime: new Date().getTime()
    property int minSendInterval: 150 // Khoảng thời gian tối thiểu giữa các lần gửi sự kiện (ms)
    property bool hasPendingMove: false // Có đang chờ gửi sự kiện di chuyển không
    property point pendingEndPoint: Qt.point(0, 0) // Điểm kết thúc đang chờ gửi

    // Timer để giới hạn tần suất gửi sự kiện
    Timer {
        id: throttleTimer
        interval: minSendInterval
        repeat: false
        running: false

        onTriggered: {
            if (hasPendingMove) {
                // Tính toán điểm bắt đầu
                var centerX = width / 2;
                var centerY = height / 2;
                var startPoint = Qt.point(centerX, centerY);

                // Phát tín hiệu di chuyển PTZ với điểm bắt đầu và điểm kết thúc mới nhất
                ptzMove(startPoint, pendingEndPoint);

                // Cập nhật điểm cuối cùng đã gửi
                lastSentEndPoint = pendingEndPoint;

                // Cập nhật thời gian gửi cuối cùng
                lastSendTime = new Date().getTime();

                // Đánh dấu không còn sự kiện đang chờ
                hasPendingMove = false;
            }
        }
    }

    // Phương thức kiểm tra xem điểm kết thúc có thay đổi đủ lớn không
    function hasMovedEnough(endPoint) {
        var dx = Math.abs(endPoint.x - lastSentEndPoint.x);
        var dy = Math.abs(endPoint.y - lastSentEndPoint.y);

        return dx >= minMovementThreshold || dy >= minMovementThreshold;
    }

    // Phương thức để gửi sự kiện di chuyển PTZ với giới hạn tần suất
    function sendPTZMove(endPoint) {
        // Luôn cập nhật điểm kết thúc đang chờ với vị trí mới nhất
        pendingEndPoint = endPoint;

        // Đánh dấu có sự kiện đang chờ
        hasPendingMove = true;

        // Kiểm tra xem đã đủ thời gian kể từ lần gửi cuối cùng chưa
        var currentTime = new Date().getTime();
        var timeSinceLastSend = currentTime - lastSendTime;

        if (timeSinceLastSend >= minSendInterval) {
            // Nếu đã đủ thời gian, gửi ngay lập tức
            throttleTimer.stop();
            throttleTimer.start();
        } else if (!throttleTimer.running) {
            // Nếu chưa đủ thời gian và timer chưa chạy, bắt đầu timer
            throttleTimer.start();
        }
        // Nếu timer đang chạy, không làm gì cả, chờ timer kích hoạt
    }

    // Phương thức kiểm tra xem chuột có nằm trong vùng của Flow button không
    function isInFlowButtonArea(mouseX, mouseY) {
        var parentItem = parent;
        var controlButtonsRow = null;

        // Tìm controlButtonsRow trong parent
        for (var i = 0; i < parentItem.children.length; i++) {
            if (parentItem.children[i].objectName === "controlButtonsRow") {
                controlButtonsRow = parentItem.children[i];
                break;
            }
        }

        // Kiểm tra xem chuột có nằm trong vùng của Flow button không
        if (controlButtonsRow && controlButtonsRow.visible) {
            // Chuyển đổi tọa độ chuột từ hệ tọa độ của PTZ3DControl sang hệ tọa độ của parent
            var mousePos = mapToItem(parentItem, mouseX, mouseY);

            // Lấy vị trí và kích thước của Flow button
            var buttonRect = Qt.rect(
                controlButtonsRow.x,
                controlButtonsRow.y,
                controlButtonsRow.width,
                controlButtonsRow.height
            );

            // Kiểm tra xem chuột có nằm trong vùng của Flow button không
            return (
                mousePos.x >= buttonRect.x &&
                mousePos.x <= buttonRect.x + buttonRect.width &&
                mousePos.y >= buttonRect.y &&
                mousePos.y <= buttonRect.y + buttonRect.height
            );
        } else {
            // Nếu không tìm thấy controlButtonsRow, sử dụng cách tiếp cận đơn giản hơn
            return (mouseX > width * 0.8 && mouseY < height * 0.2);
        }
    }
}

import QtQuick
import QtQuick.Controls

Item {
    id: ptzControl
    anchors.fill: parent
    visible: false
    z: 100 // <PERSON><PERSON>m bảo hiển thị trên cùng

    property bool isDarkTheme: true
    property point mousePosition: Qt.point(0, 0)
    property bool isActive: false

    // Tín hiệu khi người dùng di chuyển PTZ
    signal ptzMove(real x, real y)

    // Tín hiệu khi người dùng click để tắt PTZ
    signal ptzClosed()

    // Hình vuông ở giữa (chỉ có các góc, không có cạnh)
    Canvas {
        id: squareCanvas
        anchors.centerIn: parent
        width: Math.min(parent.width, parent.height) * 0.5
        height: width

        onPaint: {
            var ctx = getContext("2d");
            ctx.reset();

            var lineLength = width * 0.2; // Độ dài của các đoạn góc
            var strokeColor = isDarkTheme ? "#FFFFFF" : "#000000";
            var lineWidth = 2;

            ctx.strokeStyle = strokeColor;
            ctx.lineWidth = lineWidth;

            // Vẽ góc trên bên trái
            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(lineLength, 0);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(0, 0);
            ctx.lineTo(0, lineLength);
            ctx.stroke();

            // Vẽ góc trên bên phải
            ctx.beginPath();
            ctx.moveTo(width, 0);
            ctx.lineTo(width - lineLength, 0);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(width, 0);
            ctx.lineTo(width, lineLength);
            ctx.stroke();

            // Vẽ góc dưới bên trái
            ctx.beginPath();
            ctx.moveTo(0, height);
            ctx.lineTo(lineLength, height);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(0, height);
            ctx.lineTo(0, height - lineLength);
            ctx.stroke();

            // Vẽ góc dưới bên phải
            ctx.beginPath();
            ctx.moveTo(width, height);
            ctx.lineTo(width - lineLength, height);
            ctx.stroke();

            ctx.beginPath();
            ctx.moveTo(width, height);
            ctx.lineTo(width, height - lineLength);
            ctx.stroke();
        }
    }

    // Mũi tên di chuyển theo chuột
    Text {
        id: arrowText
        text: ">"
        font.pixelSize: 24
        font.bold: true
        color: isDarkTheme ? "white" : "black"
        x: mousePosition.x - width / 2
        y: mousePosition.y - height / 2
        visible: ptzControl.visible && ptzControl.isActive

        // Xoay mũi tên theo hướng di chuyển
        rotation: {
            if (!ptzControl.isActive) return 0;

            var centerX = ptzControl.width / 2;
            var centerY = ptzControl.height / 2;
            var dx = mousePosition.x - centerX;
            var dy = mousePosition.y - centerY;

            // Tính góc xoay (đơn vị: độ)
            var angle = Math.atan2(dy, dx) * 180 / Math.PI;
            return angle;
        }
    }

    // Vùng nhận sự kiện chuột
    MouseArea {
        id: ptzMouseArea
        anchors.fill: parent
        hoverEnabled: true
        cursorShape: Qt.BlankCursor // Ẩn con trỏ chuột mặc định

        onPositionChanged: {
            if (ptzControl.visible) {
                mousePosition = Qt.point(mouse.x, mouse.y);

                // Tính toán vị trí tương đối so với tâm
                var centerX = width / 2;
                var centerY = height / 2;
                var dx = (mouse.x - centerX) / centerX; // Chuẩn hóa về khoảng [-1, 1]
                var dy = (mouse.y - centerY) / centerY; // Chuẩn hóa về khoảng [-1, 1]

                // Phát tín hiệu di chuyển PTZ
                ptzMove(dx, dy);
            }
        }

        onClicked: {
            // Tắt PTZ control khi click
            ptzControl.visible = false;
            ptzControl.isActive = false;
            ptzClosed();
        }

        onEntered: {
            ptzControl.isActive = true;
        }

        onExited: {
            ptzControl.isActive = false;
        }
    }

    // Hiển thị hướng dẫn
    Rectangle {
        anchors.bottom: parent.bottom
        anchors.horizontalCenter: parent.horizontalCenter
        anchors.bottomMargin: 20
        width: helpText.width + 20
        height: helpText.height + 10
        radius: 5
        color: isDarkTheme ? "rgba(0, 0, 0, 0.7)" : "rgba(255, 255, 255, 0.7)"
        visible: ptzControl.visible

        Text {
            id: helpText
            anchors.centerIn: parent
            text: "Di chuyển chuột để điều khiển PTZ. Click để thoát."
            color: isDarkTheme ? "white" : "black"
            font.pixelSize: 14
        }
    }

    // Phương thức để hiển thị UI điều khiển PTZ
    function show() {
        ptzControl.visible = true;
    }

    // Phương thức để ẩn UI điều khiển PTZ
    function hide() {
        ptzControl.visible = false;
        ptzControl.isActive = false;
    }
}

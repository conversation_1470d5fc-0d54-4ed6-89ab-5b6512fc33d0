import QtQuick 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: ptzControlPanel

    // Properties
    property real speedValue: 1.0  // Gi<PERSON> trị mặc định là 1.0 (ở giữa slider)
    property bool isDarkTheme: true
    property real buttonSize: 50  // Tăng kích thước nút lên 40
    property real iconSize: 30
    property real lineWidth: 1

    width: buttonSize * 7  // Giữ nguyên chiều rộng
    height: buttonSize * 6  // Giảm chiều cao để phù hợp với các nút hình chữ nhật
    color: "#30375099" // Màu tím xuyên thấu
    radius: 2
    border.color: "#555555"
    border.width: 0.5

    // Signals
    signal ptzMove(string direction)
    signal ptzZoom(real factor)
    signal ptzSpeed(real speed)
    signal ptzClose()
    signal ptzStop()

    // Thêm một MouseArea ở phía dưới để chặn sự kiện truyền xuống GridItem
    MouseArea {
        id: backgroundBlocker
        anchors.fill: parent
        hoverEnabled: true
        z: 1 // Đảm bảo nằm dưới các nút

        // Chặn tất cả các sự kiện, không cho truyền xuống GridItem
        onClicked: function(mouse) { mouse.accepted = true }
        onPressed: function(mouse) { mouse.accepted = true }
        onReleased: function(mouse) { mouse.accepted = true }
        onPositionChanged: function(mouse) { mouse.accepted = true }
        onWheel: function(wheel) { wheel.accepted = true }
    }

    // Main content
    ColumnLayout {
        id: mainContent
        anchors.fill: parent
        anchors.margins: 4
        spacing: 3
        z: 2 // Đảm bảo nằm trên MouseArea để nhận sự kiện

        // Direction control grid
        Grid {
            Layout.alignment: Qt.AlignHCenter
            columns: 3
            rows: 3
            spacing: 8  // Tăng khoảng cách giữa các nút

            // Top-left
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/left_top.svg" : "qrc:src/assets/ptz_icon/left_top_light.svg"
                onClicked: ptzControlPanel.ptzMove("top-left")
            }

            // Up
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/top.svg" : "qrc:src/assets/ptz_icon/top_light.svg"
                onClicked: ptzControlPanel.ptzMove("up")
            }

            // Top-right
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/right_top.svg" : "qrc:src/assets/ptz_icon/right_top_light.svg"
                onClicked: ptzControlPanel.ptzMove("top-right")
            }

            // Left
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/left.svg" : "qrc:src/assets/ptz_icon/left_light.svg"
                onClicked: ptzControlPanel.ptzMove("left")
            }

            // Center (Home)
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/around.svg" : "qrc:src/assets/ptz_icon/around_light.svg"
                onClicked: ptzControlPanel.ptzMove("home")
            }

            // Right
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/right.svg" : "qrc:src/assets/ptz_icon/right_light.svg"
                onClicked: ptzControlPanel.ptzMove("right")
            }

            // Bottom-left
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/left_bottom.svg" : "qrc:src/assets/ptz_icon/left_bottom_light.svg"
                onClicked: ptzControlPanel.ptzMove("bottom-left")
            }

            // Down
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/bottom.svg" : "qrc:src/assets/ptz_icon/bottom_light.svg"
                onClicked: ptzControlPanel.ptzMove("down")
            }

            // Bottom-right
            PTZDirectionButton {
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/right_bottom.svg" : "qrc:src/assets/ptz_icon/right_bottom_light.svg"
                onClicked: ptzControlPanel.ptzMove("bottom-right")
            }
        }

        // Zoom controls
        RowLayout {
            Layout.fillWidth: true
            Layout.alignment: Qt.AlignHCenter
            spacing: 10  // Tăng khoảng cách từ 2 lên 10

            // Zoom out
            PTZZoomButton {
                Layout.preferredWidth: ptzControlPanel.buttonSize * 1.7  // Tăng từ 1.2 lên 1.5
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/zoom_out.svg" : "qrc:src/assets/ptz_icon/zoom_out_light.svg"
                onClicked: ptzControlPanel.ptzZoom(-1)
            }

            // Zoom in
            PTZZoomButton {
                Layout.preferredWidth: ptzControlPanel.buttonSize * 1.7  // Tăng từ 1.2 lên 1.5
                iconSource: isDarkTheme ? "qrc:src/assets/ptz_icon/zoom_in.svg" : "qrc:src/assets/ptz_icon/zoom_in_light.svg"
                onClicked: ptzControlPanel.ptzZoom(1)
            }
        }

        // Speed slider
        RowLayout {
            Layout.fillWidth: true
            spacing: 2

            // Minus icon
            Rectangle {
                id: minusButton
                Layout.preferredWidth: ptzControlPanel.iconSize + 4
                Layout.preferredHeight: ptzControlPanel.iconSize + 4
                radius: (ptzControlPanel.iconSize + 4) / 2
                color: ptzControlPanel.isDarkTheme ? "#444444" : "#dddddd"

                Text {
                    anchors.centerIn: parent
                    text: "−"
                    color: ptzControlPanel.isDarkTheme ? "white" : "black"
                    font.pixelSize: ptzControlPanel.iconSize - 2
                    font.bold: true
                }

                MouseArea {
                    id: minusMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: function(mouse) {
                        // Giảm tốc độ khi click vào nút minus
                        var newValue = Math.max(0, ptzControlPanel.speedValue - 0.1);
                        customSlider.value = newValue;
                        // Không cần gọi ptzSpeed ở đây vì updateValue của customSlider sẽ xử lý
                        mouse.accepted = true;
                    }
                }
            }

            // Custom Slider Implementation to avoid native style issues
            Rectangle {
                id: customSlider
                Layout.fillWidth: true
                Layout.preferredHeight: ptzControlPanel.iconSize + 8
                color: "transparent"

                property real from: 0
                property real to: 1
                property real value: ptzControlPanel.speedValue
                property real stepSize: 0.1
                property real visualPosition: (value - from) / (to - from)

                // Background track
                Rectangle {
                    id: sliderTrack
                    anchors.centerIn: parent
                    width: parent.width - 16
                    height: Math.max(2, ptzControlPanel.lineWidth)
                    radius: Math.max(1, ptzControlPanel.lineWidth / 2)
                    color: ptzControlPanel.isDarkTheme ? "#555555" : "#cccccc"

                    // Progress indicator
                    Rectangle {
                        width: customSlider.visualPosition * parent.width
                        height: parent.height
                        color: "#6666ff"
                        radius: Math.max(1, ptzControlPanel.lineWidth / 2)
                    }
                }

                // Handle
                Rectangle {
                    id: sliderHandle
                    x: 8 + customSlider.visualPosition * (sliderTrack.width - width)
                    y: (parent.height - height) / 2
                    width: ptzControlPanel.iconSize
                    height: ptzControlPanel.iconSize
                    radius: ptzControlPanel.iconSize / 2
                    color: sliderMouseArea.pressed ? "#8888ff" : "#6666ff"
                    border.color: "#6666ff"
                    border.width: 1
                }

                MouseArea {
                    id: sliderMouseArea
                    anchors.fill: parent

                    function updateValue(mouseX) {
                        var newPosition = Math.max(0, Math.min(1, (mouseX - 8) / sliderTrack.width))
                        var newValue = customSlider.from + newPosition * (customSlider.to - customSlider.from)

                        // Apply step size
                        if (customSlider.stepSize > 0) {
                            newValue = Math.round(newValue / customSlider.stepSize) * customSlider.stepSize
                        }

                        customSlider.value = Math.max(customSlider.from, Math.min(customSlider.to, newValue))
                        ptzControlPanel.speedValue = customSlider.value

                        // Calculate PTZ speed
                        var ptzSpeed = customSlider.value
                        ptzControlPanel.ptzSpeed(ptzSpeed)
                    }

                    onPressed: function(mouse) {
                        updateValue(mouse.x)
                    }

                    onPositionChanged: function(mouse) {
                        if (pressed) {
                            updateValue(mouse.x)
                        }
                    }
                }
            }

            // Plus icon
            Rectangle {
                id: plusButton
                Layout.preferredWidth: ptzControlPanel.iconSize + 4
                Layout.preferredHeight: ptzControlPanel.iconSize + 4
                radius: (ptzControlPanel.iconSize + 4) / 2
                color: ptzControlPanel.isDarkTheme ? "#444444" : "#dddddd"

                Text {
                    anchors.centerIn: parent
                    text: "+"
                    color: ptzControlPanel.isDarkTheme ? "white" : "black"
                    font.pixelSize: ptzControlPanel.iconSize - 2
                    font.bold: true
                }

                MouseArea {
                    id: plusMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: function(mouse) {
                        // Tăng tốc độ khi click vào nút plus
                        var newValue = Math.min(1, ptzControlPanel.speedValue + 0.1);
                        customSlider.value = newValue;
                        // Không cần gọi ptzSpeed ở đây vì updateValue của customSlider sẽ xử lý
                        mouse.accepted = true;
                    }
                }
            }
        }
    }
}

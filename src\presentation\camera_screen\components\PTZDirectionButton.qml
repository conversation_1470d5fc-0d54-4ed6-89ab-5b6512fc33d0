import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: directionButton
    width: parent && parent.parent && parent.parent.parent ? parent.parent.parent.buttonSize * 1.2 : 36  // Chiều rộng lớn hơn chiều cao
    height: parent && parent.parent && parent.parent.parent ? parent.parent.parent.buttonSize * 0.8 : 24  // Chiều cao nhỏ hơn chiều rộng
    radius: 2
    color: mouseArea.pressed ? "#222222" : (mouseArea.containsMouse ? "#444444" : "#333333")
    border.color: "#555555"
    border.width: 0.5

    // Properties
    property string iconSource: ""
    property bool isDarkTheme: parent ? parent.parent.parent.isDarkTheme : true

    // Signals
    signal clicked()

    Image {
        id: iconImage
        anchors.centerIn: parent
        width: parent.width * 0.8  // 80% chiều rộng nút
        height: parent.height * 0.9  // 90% chiều cao nút
        source: iconSource ? iconSource : ""
        visible: iconSource !== ""
        fillMode: Image.PreserveAspectFit
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true

        // <PERSON>hi nhấn nút, ph<PERSON>t tín hiệu clicked
        onPressed: directionButton.clicked()

        // Khi thả nút, phát tín hiệu ptzStop từ parent
        onReleased: {
            // Lấy parent là PTZControlPanel
            var ptzPanel = parent.parent.parent.parent;
            if (ptzPanel && typeof ptzPanel.ptzStop === "function") {
                ptzPanel.ptzStop();
            }
        }
    }
}

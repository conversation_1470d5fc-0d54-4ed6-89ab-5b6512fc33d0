import QtQuick 2.15
import QtQuick.Controls 2.15

Rectangle {
    id: zoomButton
    width: parent && parent.parent && parent.parent.parent ? parent.parent.parent.buttonSize * 1.5 : 45
    height: parent && parent.parent && parent.parent.parent ? parent.parent.parent.buttonSize : 30
    radius: 2
    color: mouseArea.pressed ? "#222222" : (mouseArea.containsMouse ? "#444444" : "#333333")
    border.color: "#555555"
    border.width: 0.5

    // Properties
    property string icon: "🔍+"
    property string iconSource: ""
    property bool isDarkTheme: parent ? parent.parent.parent.isDarkTheme : true

    // Signals
    signal clicked()

    Image {
        id: iconImage
        anchors.centerIn: parent
        width: parent.width * 0.9  // 90% kích thước nút
        height: parent.height * 0.9  // 90% kích thước nút
        source: iconSource ? iconSource : ""
        visible: iconSource !== ""
        fillMode: Image.PreserveAspectFit
    }

    Text {
        anchors.centerIn: parent
        text: zoomButton.icon
        color: "white"
        font.pixelSize: parent.parent.parent.parent.iconSize
        font.bold: true
        visible: iconSource === ""
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true

        // Khi nhấn nút, phát tín hiệu clicked
        onPressed: zoomButton.clicked()

        // Khi thả nút, phát tín hiệu ptzStop từ parent
        onReleased: {
            // Lấy parent là PTZControlPanel
            var ptzPanel = parent.parent.parent.parent;
            if (ptzPanel && typeof ptzPanel.ptzStop === "function") {
                ptzPanel.ptzStop();
            }
        }
    }
}

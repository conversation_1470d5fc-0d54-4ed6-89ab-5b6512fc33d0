import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

// Overlay cho chức năng PTZ
Rectangle {
    id: root
    color: "transparent"
    
    // Thuộc tính
    property bool isEnabled: false
    property bool isDrawing: false
    property bool isLine: false
    property point startPos: Qt.point(width/2, height/2)
    property point endPos: Qt.point(width/2, height/2)
    
    // Callbacks
    property var onPtzStart: null
    property var onPtzStop: null
    property var onPtzMove: null
    
    // Canvas để vẽ mũi tên
    Canvas {
        id: drawingCanvas
        anchors.fill: parent
        
        onPaint: {
            if (!root.isEnabled) return;
            
            var ctx = getContext("2d");
            ctx.reset();
            
            if (root.isDrawing) {
                ctx.strokeStyle = "#FFFFFF";
                ctx.lineWidth = 4;
                
                if (root.isLine) {
                    ctx.beginPath();
                    ctx.moveTo(root.startPos.x, root.startPos.y);
                    ctx.lineTo(root.endPos.x, root.endPos.y);
                    ctx.stroke();
                }
                
                // Vẽ đầu mũi tên
                var angle = Math.atan2(root.endPos.y - root.startPos.y, root.endPos.x - root.startPos.x);
                var x = 10 * Math.cos(angle);
                var y = 10 * Math.sin(angle);
                var x1 = root.endPos.x - x;
                var y1 = root.endPos.y - y;
                var x2 = 5 * Math.sin(angle);
                var y2 = 5 * Math.cos(angle);
                
                ctx.beginPath();
                ctx.moveTo(x1 + x2, y1 - y2);
                ctx.lineTo(root.endPos.x, root.endPos.y);
                ctx.stroke();
                
                ctx.beginPath();
                ctx.moveTo(x1 - x2, y1 + y2);
                ctx.lineTo(root.endPos.x, root.endPos.y);
                ctx.stroke();
            }
        }
    }
    
    // Xử lý sự kiện chuột
    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        enabled: root.isEnabled
        visible: root.isEnabled
        
        onPressed: function(mouse) {
            if (!root.isEnabled) return;
            
            if (mouse.button === Qt.LeftButton) {
                root.isDrawing = true;
                root.isLine = true;
                drawingCanvas.requestPaint();
                
                if (root.onPtzStart) {
                    root.onPtzStart(root.startPos.x, root.startPos.y, root.endPos.x, root.endPos.y);
                }
            }
        }
        
        onReleased: function(mouse) {
            if (!root.isEnabled) return;
            
            if (mouse.button === Qt.LeftButton) {
                root.isDrawing = false;
                drawingCanvas.requestPaint();
                
                if (root.onPtzStop) {
                    root.onPtzStop();
                }
            }
        }
        
        onPositionChanged: function(mouse) {
            if (!root.isEnabled) return;
            
            root.endPos = Qt.point(mouse.x, mouse.y);
            drawingCanvas.requestPaint();
            
            if (root.onPtzMove) {
                root.onPtzMove(root.startPos.x, root.startPos.y, root.endPos.x, root.endPos.y);
            }
        }
        
        onEntered: {
            if (root.isEnabled) {
                root.isDrawing = true;
                root.endPos = Qt.point(mouseX, mouseY);
                root.startPos = Qt.point(width/2, height/2);
                drawingCanvas.requestPaint();
            }
        }
        
        onExited: {
            if (root.isEnabled) {
                root.isDrawing = false;
                drawingCanvas.requestPaint();
                
                if (root.onPtzStop) {
                    root.onPtzStop();
                }
            }
        }
    }
    
    // Phương thức để bật/tắt chức năng vẽ
    function enable(enabled) {
        root.isEnabled = enabled;
        drawingCanvas.requestPaint();
    }
}

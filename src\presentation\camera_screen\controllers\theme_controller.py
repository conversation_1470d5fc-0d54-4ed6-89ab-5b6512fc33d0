from PySide6.QtCore import QObject, Signal, Slot, Property
from PySide6.QtWidgets import QApplication
from src.common.controller.main_controller import main_controller
from src.styles.style import Theme, Style

class ThemeController(QObject):
    """Controller for providing theme colors to QML"""

    themeChanged = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        # Connect to theme change signal
        main_controller.theme_change_signal.connect(self.on_theme_changed)
        # Apply menu style
        self.apply_menu_style()

    def on_theme_changed(self):
        """Handle theme change"""
        # Apply menu style
        self.apply_menu_style()
        # Emit signal for QML
        self.themeChanged.emit()

    @Property(bool, notify=themeChanged)
    def isDarkTheme(self):
        """Check if current theme is dark"""
        return main_controller.current_theme == Theme.DARK

    @Slot(str, str, result=str)
    def getColor(self, attribute, key):
        """Get color from current theme

        Args:
            attribute: The attribute in theme (e.g., 'Color')
            key: The color key (e.g., 'primary')

        Returns:
            str: The color value
        """
        try:
            return main_controller.get_theme_attribute(attribute, key)
        except Exception as e:
            print(f"Error getting theme color {attribute}.{key}: {e}")
            return "#FFFFFF"  # Default to white on error

    @Property(str, notify=themeChanged)
    def backgroundColor(self):
        """Get menu background color"""
        return self.getColor("Color", "main_background")

    @Property(str, notify=themeChanged)
    def textColor(self):
        """Get menu text color"""
        return self.getColor("Color", "text_color_all_app")

    @Property(str, notify=themeChanged)
    def primaryColor(self):
        """Get primary color"""
        return self.getColor("Color", "primary")

    @Property(str, notify=themeChanged)
    def hoverColor(self):
        """Get hover color"""
        return self.getColor("Color", "on_hover_primary")

    @Property(str, notify=themeChanged)
    def errorColor(self):
        """Get error color"""
        return self.getColor("Color", "error")

    @Property(str, notify=themeChanged)
    def disabledTextColor(self):
        """Get disabled text color"""
        return self.getColor("Color", "text_disable")

    @Property(str, notify=themeChanged)
    def separatorColor(self):
        """Get separator color"""
        return self.getColor("Color", "divider")

    @Property(int, notify=themeChanged)
    def borderRadius(self):
        """Get border radius"""
        return 4  # Default border radius

    def apply_menu_style(self):
        """Apply menu style to the application"""
        try:
            # Get the application instance
            app = QApplication.instance()
            if not app:
                print("No QApplication instance found")
                return

            # Get the menu style from Style
            menu_style = Style.PrimaryStyleSheet.get_context_menu_style(main_controller)

            # Apply the style
            app.setStyleSheet(app.styleSheet() + menu_style)
            print("Applied menu style successfully")
        except Exception as e:
            print(f"Error applying menu style: {e}")
            import traceback
            print(traceback.format_exc())

    @Slot(str, result=str)
    def getMenuStyle(self, selector):
        """Get menu style for QML

        Args:
            selector: The CSS selector to get style for (e.g., 'QMenu')

        Returns:
            str: The style string
        """
        try:
            # Get colors from theme
            bg_color = self.backgroundColor
            text_color = self.textColor
            primary_color = self.primaryColor
            border_color = self.separatorColor
            text_on_primary = "#FFFFFF"

            # Build style based on selector
            if selector == "menu":
                return f"""
                    background-color: {bg_color};
                    border-radius: 4px;
                    border: 1px solid {border_color};
                """
            elif selector == "menuItem":
                return f"""
                    padding: 2px 20px 2px 2px;
                    color: {text_color};
                    margin: 2px 2px 2px 2px;
                    border-radius: 0px;
                    background-color: transparent;
                """
            elif selector == "menuItemSelected":
                return f"""
                    background-color: {primary_color};
                    color: {text_on_primary};
                    border-radius: 2px;
                """
            else:
                return ""
        except Exception as e:
            print(f"Error getting menu style: {e}")
            return ""

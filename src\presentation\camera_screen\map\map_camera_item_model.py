from typing import List
from PySide6.QtCore import Qt, QModelIndex, QAbstractListModel

from src.common.model.map_config_camera_model import MapConfigCameraModel

class Map2DCameraItemModel(QAbstractListModel):
    IdRole = Qt.UserRole + 1
    PositionRole = Qt.UserRole + 2
    ColorRole = Qt.UserRole + 3
    NameRole = Qt.UserRole + 4
    RtspUrlRole = Qt.UserRole + 5
    ArcStartAngleRole = Qt.UserRole + 6
    ArcRangeAngleRole = Qt.UserRole + 7
    FovEnableRole = Qt.UserRole + 8
    def __init__(self, parent=None):
        super().__init__(parent)
        self.listCameraMapItems: List[MapConfigCameraModel] = []

    def data(self, index, role):
        if role == Qt.DisplayRole:
            return self.listCameraMapItems[index.row()]
        elif role == self.IdRole:
            return self.listCameraMapItems[index.row()].camera_id
        elif role == self.PositionRole:
            return self.listCameraMapItems[index.row()].position
        elif role == self.ColorRole:
            return self.listCameraMapItems[index.row()].color
        elif role == self.NameRole:
            return self.listCameraMapItems[index.row()].camera_name
        elif role == self.RtspUrlRole:
            return self.listCameraMapItems[index.row()].rtsp
        elif role == self.ArcStartAngleRole:
            return self.listCameraMapItems[index.row()].arc_start_angle
        elif role == self.ArcRangeAngleRole:
            return self.listCameraMapItems[index.row()].arc_range_angle
        elif role == self.FovEnableRole:
            return self.listCameraMapItems[index.row()].fov_enable
        return None
    
    def rowCount(self, parent=QModelIndex()):
        return len(self.listCameraMapItems)
    
    def roleNames(self):
        return {
            self.IdRole: b"id",
            self.PositionRole: b"position",
            self.ColorRole: b"color",
            self.NameRole: b"name",
            self.RtspUrlRole: b"rtsp",
            self.ArcStartAngleRole: b"arc_start_angle",
            self.ArcRangeAngleRole: b"arc_range_angle",
            self.FovEnableRole: b"fov_enable",
        }
    
    def add_camera(self, camera):
        self.beginInsertRows(QModelIndex(), self.rowCount(), self.rowCount())
        for i in range(self.rowCount()):
            if self.listCameraMapItems[i].camera_id == camera.camera_id:
                return
        self.listCameraMapItems.append(camera)
        self.endInsertRows()

    def insert_camera_list(self, camera_list):
        self.beginInsertRows(QModelIndex(), self.rowCount(), self.rowCount() + len(camera_list) - 1)
        for camera in camera_list:
            self.listCameraMapItems.append(camera)
        self.endInsertRows()

    def remove_camera(self, id):
        for i in range(self.rowCount()):
            if self.listCameraMapItems[i].camera_id == id:
                self.beginRemoveRows(QModelIndex(), i, i)
                self.listCameraMapItems.pop(i)
                self.endRemoveRows()
                break
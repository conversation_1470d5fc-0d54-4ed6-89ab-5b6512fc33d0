from PySide6.QtCore import Qt
from PySide6.QtWidgets import QWidget, QHBoxLayout

from src.presentation.device_management_screen.widget.list_custom_widgets import CustomIcon
from src.utils.config import Config
from src.styles.style import Style
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.controller.main_controller import main_controller
from src.common.model.tab_model import TabModel,tab_model_manager,GridItem,ItemType,SignalType
from src.common.model.camera_model import CameraModel
from src.common.model.device_models import TabType
from src.common.controller.controller_manager import Controller, controller_manager
import uuid
import logging

logger = logging.getLogger(__name__)


class StreamObjectBaseWidget(QWidget):
    HEIGHT_HEADER = 48

    def __init__(self, parent=None, stack_item=None, tab_model: TabModel = None, 
                 is_virtual_window=False, is_demo=False, is_show_header=True, is_show_footer=True):
        super().__init__(parent)
        self.is_demo = is_demo
        self.is_show_header = is_show_header
        self.is_show_footer = is_show_footer
        
        # Only set focus policy and mouse tracking if not view_only
        if self.is_show_header:
            self.setFocusPolicy(Qt.StrongFocus)
            self.setMouseTracking(True)
        
        self.is_virtual_window = is_virtual_window
        self.stack_item = stack_item
        self.tab_model = tab_model
        self.root_width = None
        self.root_height = None
        self.header_top_widget = None
        self.is_fullscreen = False
        self.screen_name = 'Main'
        self.base_grid_item = None

    def create_header_top_widget_base(self):
        widget = QWidget(self)
        layout = QHBoxLayout()
        layout.setObjectName("header_top_layout")
        layout.setContentsMargins(8, 0, 8, 0)
        layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.btn_full_screen = CustomIcon(icon= main_controller.get_theme_attribute("Image", "expand_camera"), icon_clicked= self.btn_full_screen_clicked,style=Style.StyleSheet.button_style19,icon_size = 28)
        self.btn_close = CustomIcon(icon= main_controller.get_theme_attribute("Image", "icon_close"), icon_clicked= self.btn_close_clicked,style=Style.StyleSheet.button_style19,icon_size = 28)
        layout.addWidget(self.btn_full_screen)
        layout.addWidget(self.btn_close)
        widget.setLayout(layout)
        widget.setStyleSheet("background-color: transparent;")
        # get header_top_layout from widget
        return widget
    
    def create_header_top_widget_base_for_map(self):
        widget = QWidget(self)
        layout = QHBoxLayout()
        layout.setObjectName("header_top_layout")
        layout.setContentsMargins(8, 0, 8, 0)
        layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.btn_lock_map = CustomIcon(icon=Style.PrimaryImage.icon_ptz_arrow_off, style=Style.StyleSheet.button_style19,icon_size = 28)
        self.btn_full_screen = CustomIcon(icon= main_controller.get_theme_attribute("Image", "expand_camera"), icon_clicked= self.btn_full_screen_clicked,style=Style.StyleSheet.button_style19,icon_size = 28)
        self.btn_close = CustomIcon(icon= main_controller.get_theme_attribute("Image", "icon_close"), icon_clicked= self.btn_close_clicked,style=Style.StyleSheet.button_style19,icon_size = 28)
        layout.addWidget(self.btn_lock_map)
        layout.addWidget(self.btn_full_screen)
        layout.addWidget(self.btn_close)
        widget.setLayout(layout)
        widget.setStyleSheet("background-color: transparent;")
        # get header_top_layout from widget
        return widget

    def btn_close_clicked(self):
        # logger.debug(f'btn_close_clicked')
        if self.tab_model is not None and self.stack_item is not None:
            if grid_item_selected.data['widget'] != self:
                self.stack_item.grid_item_clicked(main_controller.current_tab)
            grid_item = self.tab_model.data.listGridData.get(self.stack_item.index, None)
            if grid_item is not None:
                self.tab_model.remove_grid_item_signal.emit(grid_item.index)
                if grid_item.is_fullscreen:
                    grid_item.is_fullscreen = False
                    grid_item.is_virtual_fullscreen = False
                    self.tab_model.change_grid_view_signal.emit(self.tab_model.data.currentGrid)
                if isinstance(grid_item.model,CameraModel):
                    main_controller.gc_collect(grid_item.widget)
                if self.tab_model.data.type != TabType.Invalid:
                    controller = controller_manager.get_controller(server_ip = self.tab_model.data.server_ip)
                    self.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.RemoveItem,'data':{'key': grid_item.index}}
                    controller.update_tabmodel_by_put(parent=main_controller.list_parent['CameraScreen'], tab= self.tab_model.data)

    def btn_full_screen_clicked(self):
        if self.tab_model is not None and self.stack_item is not None:
            if grid_item_selected.data['widget'] != self:
                self.stack_item.grid_item_clicked(main_controller.current_tab)
            grid_item = self.tab_model.data.listGridData.get(self.stack_item.index, None)
            if grid_item is not None:
                # logger.debug(f'btn_full_screen_clicked')
                self.tab_model.fullscreen_grid_item_signal.emit(grid_item.index)
                if self.tab_model.data.type != TabType.Invalid:
                    controller:Controller = controller_manager.get_controller(server_ip=self.camera_model.get_property('server_ip'))
                    self.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.FullScreenItem,'data':{'key': grid_item.index}}
                    controller.update_tabmodel_by_put(parent=main_controller.list_parent['CameraScreen'], tab= self.tab_model.data)

    def enterEvent(self, event):
        if not self.is_show_header or self.is_virtual_window:
            return
        if self.header_top_widget is not None:
            self.header_top_widget.setVisible(True)
        
    def leaveEvent(self, event):
        if not self.is_show_header or self.is_virtual_window:
            return
        if self.header_top_widget is not None:
            self.header_top_widget.setVisible(False)

    def mouseMoveEvent(self, event):
        if not self.is_show_header or self.is_virtual_window:
            return
        if self.header_top_widget is not None and not self.header_top_widget.isVisible():
            self.header_top_widget.setVisible(True)

    def mouseDoubleClickEvent(self, event):
        if not self.is_show_header:
            return
        if Config.ENABLE_WARNING_ALERT_CAMERA:
            pass
            # if event.button() == Qt.LeftButton and not self.warning_alert_widget.isVisible():
            #     self.callback_fullscreen(self, self.video_capture.fullscreen_state,self.is_fullscreen)
            #     return True
        if event.button() == Qt.LeftButton:
            if self.tab_model is not None and self.stack_item is not None:
                grid_item = self.tab_model.data.listGridData.get(self.stack_item.index, None)
                if grid_item is not None:
                    self.tab_model.fullscreen_grid_item_signal.emit(grid_item.index)
                    if self.tab_model.data.type != TabType.Invalid:
                        controller:Controller = controller_manager.get_controller(server_ip=self.camera_model.get_property('server_ip'))
                        self.tab_model.data.direction = {'id':str(uuid.uuid4()),'type': SignalType.FullScreenItem,'data':{'key': grid_item.index}}
                        controller.update_tabmodel_by_put(parent=main_controller.list_parent['CameraScreen'], tab= self.tab_model.data)
            return True
        return super().mouseDoubleClickEvent(event)


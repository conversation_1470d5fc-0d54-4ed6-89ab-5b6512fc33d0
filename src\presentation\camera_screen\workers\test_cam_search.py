from PySide6.QtCore import QThread, Signal, QThreadPool, QRunnable
from PySide6.QtPositioning import QGeoCoordinate
from src.common.model.camera_model import camera_model_manager
import requests
import time

class RouteThreadWorker(QRunnable):
    def __init__(self, camera, currentPosition, updateCameraListSignal):
        super().__init__()
        self.camera = camera
        self.currentPosition = currentPosition
        self.updateCameraListSignal = updateCameraListSignal
        self.max_retries = 3
        self.retry_delay = 1  # seconds

    def run(self):
        for attempt in range(self.max_retries):
            routeData = self.calculateRoute(self.camera, self.currentPosition)
            if routeData:
                self.updateCameraListSignal.emit(routeData)
                break
            elif attempt < self.max_retries - 1:
                time.sleep(self.retry_delay)  # Wait before retry
            else:
                # Use straight-line distance as fallback
                fallback_distance = self.currentPosition.distanceTo(
                    QGeoCoordinate(float(self.camera.coordinateLat), float(self.camera.coordinateLong))
                )
                fallback_data = {
                    'id': self.camera.id,
                    'distance': fallback_distance
                }

                self.updateCameraListSignal.emit(fallback_data)

    def calculateRoute(self, camera, currentPosition):
        try:
            start_time = time.time()
            start = f"{currentPosition.longitude()},{currentPosition.latitude()}"
            end = f"{camera.coordinateLong},{camera.coordinateLat}"
            url = f"http://172.24.107.154:5000/route/v1/driving/{start};{end}?overview=full&geometries=geojson"
            response = requests.get(url)
            
            if response.status_code == 200:
                data = response.json()
                if 'routes' in data and len(data['routes']) > 0:
                    route = data['routes'][0]
                    route_data = {
                        'id': self.camera.id,
                        'distance': route['distance'],
                    }

                    end_time = time.time()
                    print(f"Route distance for camera {self.camera.id}: {route['distance']} in time: {(end_time - start_time):.2f}")
                    return route_data
                else:
                    print(f"No routes found for camera {self.camera.id}")
            elif response.status_code == 429:
                print(f"Rate limit hit for camera {self.camera.id}, will retry...")
            else:
                print(f"OSRM request failed with status {response.status_code} for camera {self.camera.id}")
            return None
        except Exception as e:
            print(f"Error calculating route for camera {self.camera.id}: {e}")
            return None

def sortByEuclideanDistance(current_cam_id, current_camera_coordinate, max_camera = 0):
    start_time = time.time()
    cameraList = camera_model_manager.camera_list["https://api.gpstech.vn"]
    camera_num_to_show = max_camera if max_camera > 0 else len(cameraList)
    cam_list = []

    for id, camera in cameraList.items():
        if id == current_cam_id:
            continue
        if camera_num_to_show == 0:
            break
        camera_num_to_show -= 1
        try:
            lon = float(camera.data.coordinateLong)
            lat = float(camera.data.coordinateLat)
            coordinate = QGeoCoordinate(lat, lon)
        except:
            continue

        distance = current_camera_coordinate.distanceTo(coordinate)
        cam_list.append({"camera": camera, "distance": distance})

    cam_list.sort(key=lambda x: x["distance"])
    end_time = time.time()
    print(f"sorted list by euclidean distance: {len(cam_list)} in time: {(end_time - start_time):.2f}")
    return cam_list

        
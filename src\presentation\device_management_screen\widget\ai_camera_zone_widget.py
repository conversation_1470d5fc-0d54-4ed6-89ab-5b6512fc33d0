from re import M
from src.common.controller.controller_manager import Controller, controller_manager
from src.common.model.camera_model import Camera
from src.presentation.device_management_screen.widget.ai_state import AIFlowType, AIType
from src.common.onvif_api.worker_thread import WorkerThread
from src.common.camera.video_capture import CameraState, StreamCameraType, VideoCapture,video_capture_controller
import json
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, QApplication, QMainWindow, QComboBox, QDialog, QFormLayout, QDialogButtonBox, QSizePolicy
from PySide6.QtCore import Qt
from PySide6.QtGui import QStandardItem
from src.common.controller.main_controller import main_controller
from src.common.model.draw_shape_model import DrawShapeModel, ShapeType
from src.presentation.device_management_screen.widget.ai_zone_dropdown_draw import AIZoneDropDownDraw
from src.presentation.device_management_screen.widget.list_custom_widgets import Input<PERSON>ithNewStyle
from src.utils.config import Config
from src.utils.utils import Utils
from src.utils.theme_setting import theme_setting
from src.common.widget.camera_draw_frame import CameraDrawFrame
from src.styles.style import Style
from src.common.widget.tree_view_widget import TreeViewWidget, ViewTypePicker, TreeViewType, GroupTreeModel
from src.common.model.zone_model import ZoneModel
from src.common.model.camera_model import camera_model_manager,CameraModel
from typing import List
import threading
import logging
logger = logging.getLogger(__name__)

# Zone colors with opacity 0.2
zone_in_color = (0, 255, 56, 50)      # Green for entry zones
zone_out_color = (255, 51, 51, 50)    # Red for exit zones
zone_detect_color = (0, 0, 255, 50)   # Blue for detection zones
zone_intrusion_color = (255, 0, 0, 50) # Red for intrusion zones

class InputType:
    """Input types for the AI Camera Zone Widget"""
    CAMERA = 0  # Single camera input
    GROUP = 1   # Group of cameras input

class AICameraZoneWidget(QWidget):
    """
    Widget for managing AI camera zones including detection, counting, and access control zones.
    Allows drawing and configuring different types of zones on camera feed.
    """

    def __init__(self, parent, camera_data, ai_flow_type, input_type = InputType.CAMERA) -> None:
        """
        Initialize the AI Camera Zone Widget.
        
        Args:
            parent: Parent widget/dialog
            camera_data: Camera model data
            ai_flow_type: Type of AI flow (detection, counting, access control)
            input_type: Type of input (camera or group)
        """
        super().__init__()
        # Parent dialog reference
        self.dialog = parent
        self.recognition_types = None

        # AI flow configuration
        self.ai_flow_id = None
        self.ai_flow_type = ai_flow_type
        logger.debug(f'ai_flow_type: {self.ai_flow_type}')
        self.input_type = input_type
        
        # Zone type labels
        self.entry_counting_zone_label = self.tr('Entry Counting Zone')
        self.exit_counting_zone_label = self.tr('Exit Counting Zone')
        self.entry_protection_zone_label = self.tr('Entry Protection Zone')
        self.exit_protection_zone_label = self.tr('Exit Protection Zone')
        self.intrusion_zone_label = self.tr('Intrusion Zone')
        self.entry_access_zone_label = self.tr('Entry Access Zone')
        self.exit_access_zone_label = self.tr('Exit Access Zone')
        self.recognition_zone_label = self.tr('Recognition Zone')
        self.protection_zone_label = self.tr('Protection Zone')

        # Camera configuration
        self.camera = camera_data
        self.drawing_enabled = False
        
        # Setup UI components
        self._setup_camera_view()
        self._setup_zone_controls()
        self._setup_result_view()
        
        # Initialize saved zones
        self.list_zone_data_saved: List[ZoneModel] = []
        self.zones = None
        
        # Update UI with any existing zones
        for zone in self.list_zone_data_saved:
            self.update_zone_to_preview(zone)

    def _setup_camera_view(self):
        """Setup the camera view and its container"""
        self.view_center_camera_layout = QVBoxLayout()
        self.root_camera_widget = CameraDrawFrame(camera=self.camera)
        
        # Center the fixed size camera widget
        self.view_center_camera_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.root_camera_widget.camera_state_changed.connect(self.on_camera_state_changed)
        self.root_camera_widget.enable_save_button_signal.connect(self.enable_save_zones_button)
        
        # Setup region of interest widget
        self.zone_controller_widget = QWidget()
        self.camera_layout = QHBoxLayout()
        self.zone_controller_widget.setLayout(self.camera_layout)
        self.camera_layout.setSpacing(16)
        self.camera_layout.setContentsMargins(0, 0, 0, 0)
        self.camera_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Setup instruction note
        self.note_label = QLabel(self.tr("Note: Choose 4 points to form a Quadrilateral."))
        self.note_label.setStyleSheet('''
            QLabel {
                color: #B5122E;
                font-style: italic;
            }
        ''')
        self.note_label.setVisible(False)
        
        # Add components to layout
        self.view_center_camera_layout.addWidget(self.note_label)
        self.view_center_camera_layout.addWidget(self.root_camera_widget)

    def _setup_zone_controls(self):
        """Setup zone type selection and control buttons"""
        self.pick_zone_layout = QVBoxLayout()
        
        # Zone type selection dropdown
        self.zone_type_dropdown = AIZoneDropDownDraw()
        self.zone_type_dropdown.item_selected.connect(self.on_zone_type_selected)
        
        # Recognition type dropdown
        self.recognition_type_dropdown = AIZoneDropDownDraw(multiple_selection=True)
        self.recognition_type_dropdown.item_selected.connect(self.on_recognition_type_selected)
        
        # Zone name input
        self.input_zone_name = InputWithNewStyle()
        self.input_zone_name.setPlaceholderText(self.tr('Enter zone name'))
        
        # Access control device selection
        self.device_access_control_combo_box = QComboBox()
        self.device_access_control_combo_box.setPlaceholderText(self.tr('Select device'))
        disable = True
        self._setup_access_control_style(disable)
        
        # Control buttons
        self.draw_zones_button = QPushButton(self.tr('Draw'))
        self.draw_zones_button.setStyleSheet(Style.StyleSheet.button_positive)
        self.draw_zones_button.clicked.connect(self.enable_drawing_or_edit)
        
        self.reset_zones_button = QPushButton(self.tr('Clear'))
        self.reset_zones_button.setStyleSheet(Style.StyleSheet.button_negative)
        self.reset_zones_button.clicked.connect(self.clear_zone_drawing)
        
        self.save_zones_button = QPushButton(self.tr('Save'))
        self.save_zones_button.setStyleSheet(Style.StyleSheet.button_negative)
        self.save_zones_button.clicked.connect(self.save_zone_drawing)

    def _setup_access_control_style(self, disable):
        """Setup the style for access control combo box"""
        if disable:
            self.device_access_control_combo_box.setStyleSheet(f'''
                QComboBox {{ 
                    background-color: transparent; 
                    border: 1px solid {main_controller.get_theme_attribute("Color", "border_line_edit_not_focus")}; 
                    border-radius: 4px;
                    padding: 4px;
                    color: {main_controller.get_theme_attribute("Color", "text_disable")};
                }}
                QComboBox::drop-down {{
                     background-color: transparent;
                     border: none;
                     subcontrol-origin: padding;
                     subcontrol-position: right;
                     width: 20px;
                 }}
                QComboBox::down-arrow {{ 
                    image: url({main_controller.get_theme_attribute("Image", "down_arrow_linedit")}); 
                }}
            ''')
        else:
            self.device_access_control_combo_box.setStyleSheet(f'''
                QComboBox {{ 
                    background-color: transparent; 
                    border: 1px solid {main_controller.get_theme_attribute("Color", "border_line_edit_not_focus")}; 
                    border-radius: 4px;
                    padding: 4px;
                    color: {main_controller.get_theme_attribute("Color", "text")};
                }}
                QComboBox::drop-down {{
                     background-color: transparent;
                     border: none;
                     subcontrol-origin: padding;
                     subcontrol-position: right;
                     width: 20px;
                 }}
                QComboBox::down-arrow {{ 
                    image: url({main_controller.get_theme_attribute("Image", "down_arrow_linedit")}); 
                }}
                QComboBox:hover {{
                    border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
                }}
            ''')

    def get_list_device_access_control(self):
        """Get list of available access control devices"""
        pass

    def get_zone_and_update_ui(self, ai_flow_id):
        """
        Get zones for the given AI flow and update the UI.
        
        Args:
            ai_flow_id: ID of the AI flow to get zones for
        """
        logger.debug(f'get_zone_and_update_ui: ai_flow_id: {ai_flow_id}')
        self.ai_flow_id = ai_flow_id

        # clear zone preview
        self.list_zone_data_saved = []
        self.root_camera_widget.list_draw_shape_model = []
        self.list_zone_result_tree_view_widget.list_tree_view_items = []
        self.update_list_zone_result_treeview([])
        
        def get_zones(ai_flowId):
            """Get zones from the controller"""
            if ai_flowId is None:
                return None
            self.controller = controller_manager.get_controller(server_ip=self.camera.server_ip)
            self.zones = self.controller.get_zones(aiFlowIds=[ai_flowId])
            logger.debug(f'get_zones: zones: {self.zones}')
            return self.zones

        def callback(result):
            """Handle the result of getting zones"""
            logger.debug(f'callback ai_flow_id: {ai_flow_id} -\n {result}')
            if self.zones:
                # Parse data to ZoneModel
                self.list_zone_data_saved = [ZoneModel.from_dict(item) for item in self.zones]
            list_item = self.create_list_zone_result_for_treeview(
                self.list_zone_data_saved)
            logger.debug(f'callback ai_flow_id: {ai_flow_id} - list_item: {list_item}')
            self.update_list_zone_result_treeview(list_item)
            logger.debug(f'callback ai_flow_id: {ai_flow_id} - list_zone_data_saved: {self.list_zone_data_saved}')
            for zone in self.list_zone_data_saved:
                self.update_zone_to_preview(zone)
        
        WorkerThread(parent=self, target=get_zones, callback=callback, args=(ai_flow_id,)).start()

    def center_relative_to_parent(self):
        """Center the dialog relative to its parent window"""
        main_window = None
        for widget in QApplication.topLevelWidgets():
            if isinstance(widget, QMainWindow):
                main_window = widget
                break

        if main_window:
            parent_geometry = main_window.geometry()
            dialog_geometry = self.dialog.geometry()

            x = (parent_geometry.width() - dialog_geometry.width()) // 2 + parent_geometry.x()
            y = (parent_geometry.height() - dialog_geometry.height()) // 2 + parent_geometry.y()
            self.dialog.move(x, y/2)

    def enable_save_zones_button(self, is_enable: bool):
        """
        Enable or disable the save zones button.
        
        Args:
            is_enable: Whether to enable the button
        """
        if self.save_zones_button.isEnabled() == is_enable:
            return
        self.save_zones_button.setEnabled(is_enable)
        if is_enable:
            self.save_zones_button.setStyleSheet(Style.PrimaryStyleSheet.get_dialog_tabbutton_active_style(theme_instance=main_controller))
        else:
            self.save_zones_button.setStyleSheet(Style.StyleSheet.button_disable)

    def enable_draw_zones_button(self, is_enable: bool):
        """
        Enable or disable the draw zones button.
        
        Args:
            is_enable: Whether to enable the button
        """
        if self.draw_zones_button.isEnabled() == is_enable:
            return
        self.draw_zones_button.setEnabled(is_enable)
        if is_enable:
            self.draw_zones_button.setStyleSheet(Style.PrimaryStyleSheet.get_dialog_tabbutton_active_style(theme_instance=main_controller))
        else:
            self.draw_zones_button.setStyleSheet(Style.StyleSheet.button_disable)

    def create_list_zone_result_for_treeview(self, list_zone_model: List[ZoneModel]) -> List[GroupTreeModel]:
        """
        Create tree view items from zone models.
        
        Args:
            list_zone_model: List of zone models to convert
            
        Returns:
            List of GroupTreeModel items for the tree view
        """
        logger.debug(f'create_list_zone_result_for_treeview: {list_zone_model}')
        # Parse ZoneModel to GroupTreeModel
        zone_result_model_list = []
        for zone_data in list_zone_model:
            item_zone_data = GroupTreeModel(id=zone_data.id, name=zone_data.name, description=zone_data.name,
                                            childGroups=None, checked=zone_data.active)
            zone_result_model_list.append(item_zone_data)

        # Add item zone data child of all
        item_all = GroupTreeModel(id=0, name=self.tr('All'), description=self.tr('All'),
                                  childGroups=zone_result_model_list)
        return [item_all]

    def update_list_zone_result_treeview(self, data: List[GroupTreeModel]):
        """
        Update the tree view with new zone data.
        
        Args:
            data: List of GroupTreeModel items to display
        """
        logger.debug(f'update_list_zone_result_treeview: {data}')
        self.list_zone_result_tree_view_widget.update_tree_view_hybrid(list_trees=data)

    def update_zone_result_after_checked(self, item: QStandardItem):
        """
        Update zone data when a zone is checked/unchecked in the tree view.
        
        Args:
            item: The QStandardItem that was checked/unchecked
        """
        # Update data in list_zone_data_saved
        zone_name = item.text()
        for zone in self.list_zone_data_saved:
            if zone.name == zone_name:
                zone.active = item.checkState() == Qt.CheckState.Checked
                self.update_zone_to_preview(zone)

    def update_zone_to_preview(self, zone: ZoneModel):
        # draw zone activated
        x_scale_to_display = self.root_camera_widget.frame_width / \
            Config.FRAME_WIDTH_DEFAULT_SERVER
        y_scale_to_display = self.root_camera_widget.frame_height / \
            Config.FRAME_HEIGHT_DEFAULT_SERVER
        zone_to_preview = zone.copy()
        zone_to_preview.convert_point(x_scale_to_display, y_scale_to_display)
        if zone_to_preview.active:
            item_draw_shape = self.convert_zone_model_to_shape_preview(zone_to_preview)
            if item_draw_shape is None:
                return
            # check rect is exist in list_draw_shape_model
            is_exist = False
            for item in self.root_camera_widget.list_draw_shape_model:
                if item.shape_name == zone_to_preview.name:
                    is_exist = True
                    break
            if not is_exist:
                self.root_camera_widget.list_draw_shape_model.append(
                    item_draw_shape)
        else:
            # remove zone from list_draw_shape_model
            for draw_shape_model in self.root_camera_widget.list_draw_shape_model:
                if draw_shape_model.rect == Utils.convert_string_to_coordinate_points(zone_to_preview.polygon):
                    self.root_camera_widget.list_draw_shape_model.remove(
                        draw_shape_model)
                    break

    def convert_zone_model_to_shape_preview(self, zone: ZoneModel) -> DrawShapeModel:
        try:
            line = None
            rect = None

            if zone.polygon:
                rect = Utils.convert_string_to_coordinate_points(zone.polygon)

            if zone.line:
                line = Utils.convert_string_to_coordinate_points(zone.line)
                # [(225, 317), (149, 218), (506, 165), (225, 317), (375, 50), (506, 165)]
                # convert to
                # [((225, 317), (149, 218)), ((506, 165), (225, 317)), ((375, 50), (506, 165))]
                convert = []
                for i in range(len(line) // 2):
                    convert.append((line[2 * i], line[2 * i + 1]))
                line = convert

            logger.debug(f'zone INFO: {zone.to_dict()}')
            # convert zone.type str to ShapeType
            shape_type: ShapeType = None
            
            # Validate hex colors before conversion
            color = (255, 0, 0)
            colorLine = (0, 0, 255)
            if zone.color and Utils.is_valid_hex_color(zone.color):
                color = Utils.convert_hex_to_rgb(zone.color)
            if zone.colorLine and Utils.is_valid_hex_color(zone.colorLine):
                colorLine = Utils.convert_hex_to_rgb(zone.colorLine)
            # convert zone activate to drawshapemodel
            return DrawShapeModel(
                rect=rect, line=line, shape_type=shape_type, color_point=color, color=colorLine, shape_name=zone.name)
        except Exception as e:
            logger.error(f'convert_zone_model_to_shape_preview: {e}')
            return None

    def update_zone_result_after_deleted(self, data):
        zone_name, callback_remove_ui = data
        # update data at self.list_zone_data_saved
        for zone in self.list_zone_data_saved:
            if zone.name == zone_name:
                # update server
                response = main_controller.current_controller.delete_zones(id=zone.id)
                if response is None:
                    QMessageBox.warning(self, self.tr(
                        'Warning'), self.tr('Failed to delete the zone'))
                    return
                callback_remove_ui()
                self.list_zone_data_saved.remove(zone)
                list_tree_view_items = self.list_zone_result_tree_view_widget.list_tree_view_items
                item_all: GroupTreeModel = list_tree_view_items[0]
                for item in item_all.childGroups:
                    if item.name == zone_name:
                        item_all.childGroups.remove(item)
                        break
                # update list_zone_result_tree_view_widget
                self.update_list_zone_result_treeview(list_tree_view_items)
                zone.active = False
                self.update_zone_to_preview(zone)
                break

    def trigger_enable_edit_zones(self, zone_name):
        self.edit_zone_model_temp = None
        # update data at self.list_zone_data_saved
        for zone in self.list_zone_data_saved:
            if zone.name == zone_name:
                self.enable_edit_zone()

                # convert zone to preview
                x_scale_to_display = self.root_camera_widget.frame_width / \
                    Config.FRAME_WIDTH_DEFAULT_SERVER
                y_scale_to_display = self.root_camera_widget.frame_height / \
                    Config.FRAME_HEIGHT_DEFAULT_SERVER
                zone_to_preview = zone.copy()
                zone_to_preview.convert_point(x_scale_to_display, y_scale_to_display)

                # Update name and type of zone
                self.input_zone_name.setText(zone_to_preview.name)
                if zone_to_preview.type == 'ZONE_ACCESS_CONTROL_IN' or zone_to_preview.type == 'ZONE_ACCESS_CONTROL_OUT':
                    pass

                # update zone type to treeview
                if zone_to_preview.type == 'ZONE_FLOW_IN':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_FLOW_IN
                    self.zone_type_dropdown.combo_box.setCurrentText(self.entry_counting_zone_label)
                elif zone_to_preview.type == 'ZONE_FLOW_OUT':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_FLOW_OUT
                    self.zone_type_dropdown.combo_box.setCurrentText(self.exit_counting_zone_label)
                elif zone_to_preview.type == 'ZONE_DETECT':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_DETECT
                    self.zone_type_dropdown.combo_box.setCurrentText(self.recognition_zone_label)
                    # Restore recognition types if they exist
                    if zone_to_preview.objects:
                        translated_types = []
                        for ai_type in zone_to_preview.objects:
                            if ai_type == AIType.HUMAN.name:
                                translated_types.append(self.tr('Human'))
                            elif ai_type == AIType.VEHICLE.name:
                                translated_types.append(self.tr('Vehicle'))
                        self.recognition_type_dropdown.set_selected_items(translated_types)
                elif zone_to_preview.type == 'ZONE_ACCESS_CONTROL_IN':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_ACCESS_CONTROL_IN
                    self.zone_type_dropdown.combo_box.setCurrentText(self.entry_access_zone_label)
                elif zone_to_preview.type == 'ZONE_ACCESS_CONTROL_OUT':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_ACCESS_CONTROL_OUT
                    self.zone_type_dropdown.combo_box.setCurrentText(self.exit_access_zone_label)
                elif zone_to_preview.type == 'ZONE_INTRUSION':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_INTRUSION
                    self.zone_type_dropdown.combo_box.setCurrentText(self.intrusion_zone_label)
                elif zone_to_preview.type == 'ZONE_PROTECT_IN':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_PROTECT_IN
                    self.zone_type_dropdown.combo_box.setCurrentText(self.entry_protection_zone_label)
                elif zone_to_preview.type == 'ZONE_PROTECT_OUT':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_PROTECT_OUT
                    self.zone_type_dropdown.combo_box.setCurrentText(self.exit_protection_zone_label)
                elif zone_to_preview.type == 'ZONE_PROTECT':
                    self.root_camera_widget.current_zone_type = ShapeType.ZONE_PROTECT
                    self.zone_type_dropdown.combo_box.setCurrentText(self.protection_zone_label)

                # update params of zone
                self.input_zone_name.setText(zone_to_preview.name)
                self.root_camera_widget.temp_name_shape = zone_to_preview.name
                self.root_camera_widget.points = Utils.convert_string_to_coordinate_points(
                    zone_to_preview.polygon)
                print(f'trigger_enable_edit_zones: self.root_camera_widget.points: {self.root_camera_widget.points}')
                self.root_camera_widget.temp_line = Utils.convert_string_to_coordinate_points(
                    zone_to_preview.line)
                item_shape = self.convert_zone_model_to_shape_preview(
                    zone=zone_to_preview)
                if item_shape is None:
                    continue
                self.root_camera_widget.temp_draw_shape_model = item_shape
                self.edit_zone_model_temp = zone_to_preview
                break

    def are_all_items_same(self, list_point):
        first_item = list_point[0]
        second_item = list_point[1]
        if first_item[0] == second_item[0] and first_item[1] == second_item[1]:
            return True
        return False

    def option_line_button_clicked(self, index):
        # selected_item = self.option_line_button.get_selected_items()
        # if index == 0:
        #     self.root_camera_widget.draw_mode = "polygon"
        #     self.note_label.setText(
        #         self.tr("Note: Choose 4 points to form a Quadrilateral."))
        # else:
        #     if len(self.root_camera_widget.points) > 2:
        #         self.root_camera_widget.points = []
        #     self.root_camera_widget.draw_mode = "line"
        #     self.note_label.setText(
        #         self.tr("Note: Choose 2 points to form a Line."))
        pass

    def enable_drawing_or_edit(self):
        logger.debug(f"enable_drawing_or_edit = {self.zone_type_dropdown.combo_box.currentText()}")
        if self.zone_type_dropdown.combo_box.currentText() == "":
            QMessageBox.warning(self, self.tr('Warning'),
                                self.tr('Please choose Zone type'))
            return
        name_zone = self.input_zone_name.text()
        if name_zone == '':
            QMessageBox.warning(self, self.tr('Warning'),
                                self.tr('Zone name is empty'))
            return

        if name_zone in [zone.name for zone in self.list_zone_data_saved]:
            QMessageBox.warning(self, self.tr('Warning'),
                                self.tr('Zone name is exist'))
            return

        # clear temp drawing
        self.root_camera_widget.clear_current_temp_shape()

        self.root_camera_widget.enable_draw = True

        zone_type = None

        # if self.check_current_zone_type(self.tr('Exit Counting Zone')):
        #     zone_type = ShapeType.ZONE_FLOW_OUT
        # elif self.check_current_zone_type(self.tr('Entry Counting Zone')):
        #     zone_type = ShapeType.ZONE_FLOW_IN
        if self.check_current_zone_type(self.recognition_zone_label):
            zone_type = ShapeType.ZONE_DETECT
        elif self.check_current_zone_type(self.entry_access_zone_label):
            zone_type = ShapeType.ZONE_ACCESS_CONTROL_IN
        elif self.check_current_zone_type(self.exit_access_zone_label):
            zone_type = ShapeType.ZONE_ACCESS_CONTROL_OUT
        elif self.check_current_zone_type(self.intrusion_zone_label):
            zone_type = ShapeType.ZONE_INTRUSION
        elif self.check_current_zone_type(self.entry_protection_zone_label):
            zone_type = ShapeType.ZONE_PROTECT_IN
        elif self.check_current_zone_type(self.exit_protection_zone_label):
            zone_type = ShapeType.ZONE_PROTECT_OUT
        elif self.check_current_zone_type(self.protection_zone_label):
            zone_type = ShapeType.ZONE_PROTECT

        self.root_camera_widget.current_zone_type = zone_type
        self.root_camera_widget.temp_name_shape = name_zone
        self.root_camera_widget.points = []
        self.root_camera_widget.temp_line = []
        self.note_label.setVisible(True)

        # disable treeview result zone
        self.list_zone_result_tree_view_widget.setEnabledCustom(False)
        self.zone_type_dropdown.set_enabled(False)

        # change color button start draw gray
        self.draw_zones_button.setEnabled(False)
        self.draw_zones_button.setStyleSheet(Style.StyleSheet.button_disable)

    def enable_edit_zone(self):
        # clear temp drawing
        self.root_camera_widget.clear_current_temp_shape()
        self.root_camera_widget.enable_draw = True
        self.root_camera_widget.enable_edit = True

        self.note_label.setVisible(True)

        # disable treeview result zone
        self.list_zone_result_tree_view_widget.setEnabledCustom(False)
        self.zone_type_dropdown.set_enabled(False)

        # change color button start draw gray
        self.enable_draw_zones_button(False)
        self.enable_save_zones_button(False)

    def clear_zone_drawing(self):
        self.root_camera_widget.points = []
        # self.drawing_enabled = False
        self.note_label.setVisible(False)
        self.root_camera_widget.enable_draw = False
        self.root_camera_widget.enable_edit = False
        # self.draw_button.setEnabled(True)
        self.root_camera_widget.clear_current_temp_shape()
        self.root_camera_widget.list_draw_shape_model.clear()
        for item in self.list_zone_data_saved:
            self.update_zone_to_preview(item)

        self.list_zone_result_tree_view_widget.setEnabledCustom(True)
        self.zone_type_dropdown.set_enabled(True)

        self.enable_draw_zones_button(True)
        self.enable_save_zones_button(False)

    def save_zone_drawing(self):
        zone_model: ZoneModel = None
        name_zone = self.input_zone_name.text()
        ai_flowId = self.ai_flow_id
        polygonType = None
        polygon = None
        lines = None
        color = None
        colorLine = None
        objects = None

        if self.zone_type_dropdown.combo_box.currentText() == "" and not self.root_camera_widget.enable_edit:
            QMessageBox.warning(self, self.tr('Warning'),
                                self.tr('Please choose Zone type'))
            return

        if name_zone == '':
            QMessageBox.warning(self, self.tr('Warning'),
                                self.tr('Zone name is empty'))
            return

        if name_zone in [zone.name for zone in self.list_zone_data_saved] and not self.root_camera_widget.enable_edit:
            QMessageBox.warning(self, self.tr('Warning'),
                                self.tr('Zone name is exist'))
            return

        if len(self.root_camera_widget.points) < 4:
            self.root_camera_widget.points.clear()
            QMessageBox.warning(self, self.tr('Warning'),
                                self.tr('Please choose 4 points to form a Quadrilateral.'))
            return
        if len(self.root_camera_widget.temp_line) == 0 and not self.root_camera_widget.not_allow_use_pick_edge():
            self.root_camera_widget.temp_line.clear()
            QMessageBox.warning(self, self.tr('Warning'), self.tr(
                'Please click on one of the four edges to determine the direction.'))
            return
        coordinate_points = self.root_camera_widget.points
        lines = self.root_camera_widget.temp_line

        # Define zone type mappings
        zone_type_mappings = {
            self.entry_counting_zone_label: ('ZONE_FLOW_IN', 'Entry Counting Zone'),
            self.exit_counting_zone_label: ('ZONE_FLOW_OUT', 'Exit Counting Zone'),
            self.entry_access_zone_label: ('ZONE_ACCESS_CONTROL_IN', 'Entry Access Zone'),
            self.exit_access_zone_label: ('ZONE_ACCESS_CONTROL_OUT', 'Exit Access Zone'),
            self.intrusion_zone_label: ('ZONE_INTRUSION', 'Intrusion Zone'),
            self.recognition_zone_label: ('ZONE_DETECT', 'Recognition Zone'),
            self.entry_protection_zone_label: ('ZONE_PROTECT_IN', 'Entry Protection Zone'),
            self.exit_protection_zone_label: ('ZONE_PROTECT_OUT', 'Exit Protection Zone'),
            self.protection_zone_label: ('ZONE_PROTECT', 'Protection Zone')
        }

        # Find matching zone type and set values
        for zone_label, (zone_type, zone_name) in zone_type_mappings.items():
            if self.check_current_zone_type(zone_label):
                logger.debug(f'zone_type: {zone_type}')
                polygonType = zone_type
                polygon = Utils.convert_coordinate_points_to_string(coordinate_points)
                lines = Utils.convert_coordinate_points_to_string(lines)
                break
        logger.debug(f'save_zone_drawing: polygon: {polygon}')
        logger.debug(f'save_zone_drawing: lines: {lines}')

        # when create new active always False, must be checked in list_zone_result_tree_view_widget then update to server
        active = self.edit_zone_model_temp.active if self.root_camera_widget.enable_edit else False
        print(f'polygonType: {polygonType}')

        # color and colorLine
        color = Utils.convert_rgb_to_hex(self.root_camera_widget.color) if Utils.is_valid_rgb_color(self.root_camera_widget.color) else None
        colorLine = Utils.convert_rgb_to_hex(self.root_camera_widget.color_line) if Utils.is_valid_rgb_color(self.root_camera_widget.color_line) else None
        logger.debug(f'save_zone_drawing: color: {color} - colorLine: {colorLine}')
        # Get recognition types if this is a detection zone
        objects = self.create_ai_recognition_types()

        logger.info(f'save_zone_drawing: objects: {objects}')
            
        zone_model = ZoneModel(name=name_zone, type=polygonType, polygon=polygon, line=lines, active=active,
                               aiFlowId=ai_flowId, objects=objects, color=color, colorLine=colorLine)
        if self.root_camera_widget.enable_edit:
            zone_model.id = self.edit_zone_model_temp.id

        # convert point to FullHD size 1920x1080
        zone_model.convert_point(x_scale_to_server=self.root_camera_widget.x_scale,
                                             y_scale_to_server=self.root_camera_widget.y_scale)
        logger.debug(f'save_zone_drawing: zone_model: BEFORE {zone_model.to_dict()}')

        
        if self.root_camera_widget.enable_edit:
            self.edit_zone_model_temp = None
            # update zone_model to list_zone_data_saved
            for index, zone in enumerate(self.list_zone_data_saved):
                if zone.id == zone_model.id:
                    self.list_zone_data_saved[index] = zone_model
                    break
        else:
            self.list_zone_data_saved.append(zone_model)

        # parser ZoneModel to GroupTreeModel
        item_zone_data = GroupTreeModel(
            id=zone_model.id, name=zone_model.name,
            description=zone_model.name, childGroups=None, checked=active)

        list_tree_view_items = self.list_zone_result_tree_view_widget.list_tree_view_items
        print(f'list_tree_view_items: {list_tree_view_items}')
        # get All item and add item_zone_data to All item
        all_item = list_tree_view_items[0]
        
        if self.root_camera_widget.enable_edit:
            # update item_zone_data to list_tree_view_items
            for index, item in enumerate(all_item.childGroups):
                if item.id == zone_model.id:
                    all_item.childGroups[index] = item_zone_data
                    break
            pass
        else:
            # get length all_item.childGroups
            all_item.childGroups.append(item_zone_data)

        # update self.list_zone_result_tree_view_widget trigger model data change
        self.update_list_zone_result_treeview(list_tree_view_items)

        self.root_camera_widget.clear_current_temp_shape()

        self.input_zone_name.clear()

        self.root_camera_widget.enable_draw = False
        self.root_camera_widget.enable_edit = False
        self.note_label.setVisible(False)

        self.list_zone_result_tree_view_widget.setEnabledCustom(True)
        self.zone_type_dropdown.set_enabled(True)
        # change color button start draw gray
        self.enable_draw_zones_button(True)
        self.enable_save_zones_button(False)
        self.clear_zone_drawing()

    def check_current_zone_type(self, current_zone_str: str) -> bool:
        return self.zone_type_dropdown.combo_box.currentText() == current_zone_str

    def convert_coordinate_points_to_string(self, coordinate_points):
        list_coordinate_points = []
        for point in coordinate_points:
            list_coordinate_points.append([point[0], point[1]])
        return json.dumps(list_coordinate_points)

    def close_camera_stream_zone(self):
        logger.debug(f'close_camera_stream_zone: self.root_camera_widget: {self.root_camera_widget}')
        if self.root_camera_widget is not None and self.root_camera_widget.camera_widget is not None:
            logger.debug(f'close_camera_stream_zone: stop video')
            self.root_camera_widget.camera_widget.stop_video()

    def update_zone_type_with_ai_flows(self, ai_flow):
        """
        Update available zone types based on AI flow type.
        
        Args:
            ai_flow: The AI flow type to configure zone types for
        """
        list_zones_type = []
        list_recognition_types = []
        
        if ai_flow is None:
            return
        
        list_recognition_types = [self.tr('Human'), self.tr('Vehicle')]
        if ai_flow == AIFlowType.RECOGNITION or ai_flow == AIFlowType.FREQUENCY or ai_flow == AIFlowType.MOTION or ai_flow == AIFlowType.TRAFFIC or ai_flow == AIFlowType.WEAPON or ai_flow == AIFlowType.UFO:
            list_zones_type.append(self.recognition_zone_label)
        elif ai_flow == AIFlowType.PROTECTION:
            list_zones_type.append(self.protection_zone_label)
            list_zones_type.append(self.entry_protection_zone_label)
            list_zones_type.append(self.exit_protection_zone_label)
            list_zones_type.append(self.intrusion_zone_label)
        elif ai_flow == AIFlowType.ACCESS:
            list_zones_type.append(self.entry_access_zone_label)
            list_zones_type.append(self.exit_access_zone_label)
            
        # Update zone type dropdown
        self.zone_type_dropdown.add_items(list_zones_type)
        
        # Update recognition type dropdown
        self.recognition_type_dropdown.add_items(list_recognition_types)
        
        # Show/hide access control combo box
        if ai_flow == AIFlowType.ACCESS:
            self.device_access_control_combo_box.show()
        else:
            self.device_access_control_combo_box.hide()

    def on_camera_state_changed(self, state):
        # Get all interactive widgets that should be disabled/enabled together
        widgets_to_control = [
            self.zone_type_dropdown,
            # self.option_line_button,
            self.input_zone_name,
            self.draw_zones_button,
            self.reset_zones_button,
            self.save_zones_button,
            self.list_zone_result_tree_view_widget
        ]
        
        if self.ai_flow_type is not None and (self.ai_flow_type == AIFlowType.ACCESS):
            widgets_to_control.append(self.device_access_control_combo_box)

        if state == CameraState.connecting:
            # Disable all interactive widgets
            for widget in widgets_to_control:
                if hasattr(widget, 'setEnabledCustom'):
                    widget.setEnabledCustom(False)
                else:
                    widget.setEnabled(False)
        elif state == CameraState.stopped:
            # Disable all interactive widgets
            for widget in widgets_to_control:
                if hasattr(widget, 'setEnabledCustom'):
                    widget.setEnabledCustom(False)
                else:
                    widget.setEnabled(False)
        else:
            # Enable all interactive widgets when camera is available
            for widget in widgets_to_control:
                if hasattr(widget, 'setEnabledCustom'):
                    widget.setEnabledCustom(True)
                else:
                    widget.setEnabled(True)

    def on_zone_type_selected(self, zone_type: str):
        """
        Handle zone type selection.
        
        Args:
            zone_type: The type of zone selected
        """
        logger.debug(f'on_zone_type_selected: {zone_type}')
        self.zone_type = zone_type
        
        # Set the zone type directly on the camera widget
        if zone_type == self.tr('Entry Counting Zone'):
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_FLOW_IN
        elif zone_type == self.tr('Exit Counting Zone'):
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_FLOW_OUT
        elif zone_type == self.tr('Detection Zone'):
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_DETECT
        elif zone_type == self.tr('Intrusion Zone'):
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_INTRUSION
        elif zone_type == self.entry_protection_zone_label:
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_PROTECT_IN
        elif zone_type == self.exit_protection_zone_label:
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_PROTECT_OUT
        elif zone_type == self.protection_zone_label:
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_PROTECT
        elif zone_type == self.entry_access_zone_label:
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_ACCESS_CONTROL_IN
        elif zone_type == self.exit_access_zone_label:
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_ACCESS_CONTROL_OUT
        elif zone_type == self.recognition_zone_label:
            self.root_camera_widget.current_zone_type = ShapeType.ZONE_DETECT
            
        self.enable_draw_zones_button(True)

    def on_draw_zones_clicked(self):
        """Handle draw zones button click by enabling zone drawing mode"""
        # Enable drawing mode directly
        self.root_camera_widget.enable_draw = True
        self.enable_draw_zones_button(False)

    def on_save_zones_clicked(self):
        """
        Save the drawn zones to the server.
        Validates zone data and sends it to the controller.
        """
        logger.debug('on_save_zones_clicked')
        if not self.root_camera_widget.zone_data:
            return

        def save_zones():
            """Send zone data to the controller for saving"""
            self.controller = controller_manager.get_controller(server_ip=self.camera.server_ip)
            zone_data = self.root_camera_widget.zone_data
            zone_data.name = self.input_zone_name.text()
            zone_data.active = True
            zone_data.ai_flow_id = self.ai_flow_id
            
            if self.ai_flow_type is not None and (self.ai_flow_type == AIFlowType.ACCESS):
                zone_data.device_access_control_id = self.device_access_control_combo_box.currentData()
                
            return self.controller.save_zone(zone_data)

        def callback(result):
            """Handle the result of saving zones"""
            logger.debug(f'callback: {result}')
            if result:
                self.get_zone_and_update_ui(self.ai_flow_id)
                self.root_camera_widget.clear_zone_data()
                self.input_zone_name.clear()
                self.enable_save_zones_button(False)

        WorkerThread(parent=self, target=save_zones, callback=callback).start()

    def on_delete_zone_clicked(self):
        """
        Delete selected zones from the server.
        Gets selected items from the tree view and sends delete requests.
        """
        logger.debug('on_delete_zone_clicked')
        selected_items = self.list_zone_result_tree_view_widget.get_selected_items()
        if not selected_items:
            return

        def delete_zones():
            """Send delete requests to the controller"""
            self.controller = controller_manager.get_controller(server_ip=self.camera.server_ip)
            for item in selected_items:
                for zone in self.list_zone_data_saved:
                    if zone.name == item.text():
                        self.controller.delete_zone(zone.id)
            return True

        def callback(result):
            """Handle the result of deleting zones"""
            logger.debug(f'callback: {result}')
            if result:
                self.get_zone_and_update_ui(self.ai_flow_id)

        WorkerThread(parent=self, target=delete_zones, callback=callback).start()

    def on_edit_zone_clicked(self):
        """
        Edit selected zone properties.
        Gets the selected zone and opens the edit dialog.
        """
        logger.debug('on_edit_zone_clicked')
        selected_items = self.list_zone_result_tree_view_widget.get_selected_items()
        if not selected_items or len(selected_items) > 1:
            return

        selected_item = selected_items[0]
        for zone in self.list_zone_data_saved:
            if zone.name == selected_item.text():
                self.show_edit_zone_dialog(zone)
                break

    def show_edit_zone_dialog(self, zone: ZoneModel):
        """
        Show dialog for editing zone properties.
        
        Args:
            zone: The zone model to edit
        """
        self.dialog = QDialog(self)
        self.dialog.setWindowTitle(self.tr('Edit Zone'))
        self.dialog.setFixedSize(400, 200)
        self.center_relative_to_parent()

        layout = QVBoxLayout()
        form_layout = QFormLayout()

        # Zone name input
        self.edit_zone_name_input = QLineEdit()
        self.edit_zone_name_input.setText(zone.name)
        form_layout.addRow(self.tr('Zone Name:'), self.edit_zone_name_input)

        # Device access control combo box for access control zones
        if self.ai_flow_type is not None and (self.ai_flow_type == AIFlowType.ACCESS):
            self.edit_device_access_control_combo_box = QComboBox()
            self.edit_device_access_control_combo_box.setCurrentText(zone.device_access_control_id)
            form_layout.addRow(self.tr('Device Access Control:'), self.edit_device_access_control_combo_box)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(lambda: self.on_edit_zone_accepted(zone))
        button_box.rejected.connect(self.dialog.reject)
        layout.addWidget(button_box)

        self.dialog.setLayout(layout)
        self.dialog.exec()

    def on_edit_zone_accepted(self, zone: ZoneModel):
        """
        Handle zone edit acceptance.
        Updates zone properties and saves changes.
        
        Args:
            zone: The zone model being edited
        """
        logger.debug('on_edit_zone_accepted')
        zone.name = self.edit_zone_name_input.text()
        if self.ai_flow_type is not None and (self.ai_flow_type == AIFlowType.ACCESS):
            zone.device_access_control_id = self.edit_device_access_control_combo_box.currentData()

        def update_zone():
            """Send updated zone data to the controller"""
            self.controller = controller_manager.get_controller(server_ip=self.camera.server_ip)
            return self.controller.update_zone(zone)

        def callback(result):
            """Handle the result of updating the zone"""
            logger.debug(f'callback: {result}')
            if result:
                self.get_zone_and_update_ui(self.ai_flow_id)
                self.dialog.accept()

        WorkerThread(parent=self, target=update_zone, callback=callback).start()

    def on_recognition_type_selected(self, selected_items):
        """
        Handle recognition type selection.
        
        Args:
            selected_items: List of selected recognition types
        """
        logger.debug(f'on_recognition_type_selected: {selected_items}')
        self.recognition_types = selected_items

    def create_ai_recognition_types(self):
        """
        Create or update AI configuration based on selected recognition types.
        Returns a list of AI types (HUMAN, VEHICLE) based on selected recognition types.
        """
        ai_types = []
        if self.recognition_types is None:
            # get from self.recognition_type_dropdown
            self.recognition_types = self.recognition_type_dropdown.get_selected_items()
            logger.info(f'create_ai_recognition_types: self.recognition_types: {self.recognition_types}')
        if self.recognition_types is not None:
            for item in self.recognition_types:
                if item == self.tr('Human'):
                    ai_types.append(AIType.HUMAN.name)
                    logger.info(f'create_ai_recognition_types: add human')
                elif item == self.tr('Vehicle'):
                    ai_types.append(AIType.VEHICLE.name)
                    logger.info(f'create_ai_recognition_types: add vehicle')
        return ai_types

    def _setup_result_view(self):
        """Setup the result view for displaying saved zones"""
        style_treeview_custom_result = f"""
            QTreeView {{
                background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")};
                alternate-background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                border: None;
                border-radius: 4px;
            }}
            QTreeView::item:disabled {{
                background-color: {Style.PrimaryColor.button_disable_background};
                alternate-background-color: {Style.PrimaryColor.button_disable_background};
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                border: None;
                border-radius: 4px;
            }}
            QTreeView::item {{
                padding: 4px;
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                background-color: transparent;
            }}
            QTreeView::item:selected {{
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                background-color: transparent;
            }}
            QTreeView::branch:has-children:closed {{
                image:  url({main_controller.get_theme_attribute("Image", "treeview_expand_item")});
            }}
            QTreeView::branch:has-children:open {{
                image: url({main_controller.get_theme_attribute("Image", "treeview_collapse_item")});
            }}
            QTreeView::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            QTreeView::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
        """

        # Create tree view for saved zones
        self.list_zone_result_tree_view_widget = TreeViewWidget(
            list_item=[],
            label_name=self.tr('Saved Zone List'),
            tree_view_type=TreeViewType.HYBRID,
            enable_only_item_checkbox=False,
            enable_checkbox=True,
            view_type_picker=ViewTypePicker.PICK_AI_ZONE_RESULT,
            enable_click=True,
            hide_change_mode=True,
            hide_filter=True,
            hide_search_bar=True,
            hide_dropdown_item=True,
            style_treeview_custom=style_treeview_custom_result,
            hide_icon_item=True,
            enable_dragndrop=False,
            hide_header=True
        )

        # Connect signals
        self.list_zone_result_tree_view_widget.update_zone_signal.connect(
            self.update_zone_result_after_checked)
        self.list_zone_result_tree_view_widget.delete_zone_signal.connect(
            self.update_zone_result_after_deleted)
        self.list_zone_result_tree_view_widget.edit_zone_signal.connect(
            self.trigger_enable_edit_zones)

        # Initialize with empty root item
        list_item_zone_result_tree_view: list[GroupTreeModel] = []
        item_all = GroupTreeModel(id=0, name=self.tr('All'), description=self.tr('All'),
                                  childGroups=[])
        list_item_zone_result_tree_view.append(item_all)
        self.update_list_zone_result_treeview(list_item_zone_result_tree_view)

        # Setup layouts
        self._setup_layouts()

    def _setup_layouts(self):
        """Setup the main layouts of the widget"""
        # Setup button layout
        layout_button = QHBoxLayout()
        layout_button.setContentsMargins(0, 0, 0, 0)
        layout_button.setSpacing(8)
        layout_button.addWidget(self.draw_zones_button)
        layout_button.addWidget(self.reset_zones_button)
        layout_button.addWidget(self.save_zones_button)

        # Setup pick zone layout
        self.pick_zone_layout = QVBoxLayout()
        self.pick_zone_layout.setSpacing(12)  # Increased spacing between sections
        self.pick_zone_layout.setContentsMargins(16, 16, 16, 16)  # Add padding around the layout
        
        # Common label style
        label_style = f'''
            QLabel {{
                color: {main_controller.get_theme_attribute("Color", "dialog_text")};
                font-weight: bold;
                margin-bottom: 4px;
            }}
        '''
        
        # Zone type section
        label_zone_type = QLabel(self.tr('Zone Type'))
        label_zone_type.setStyleSheet(label_style)
        self.pick_zone_layout.addWidget(label_zone_type)
        self.pick_zone_layout.addWidget(self.zone_type_dropdown)
        
        # Recognition type section
        label_recognition_type = QLabel(self.tr('Recognition Type'))
        label_recognition_type.setStyleSheet(label_style)
        self.pick_zone_layout.addWidget(label_recognition_type)
        self.pick_zone_layout.addWidget(self.recognition_type_dropdown)
        
        # Drawing type section
        # label_drawing_type = QLabel(self.tr('Drawing Type'))
        # label_drawing_type.setStyleSheet(label_style)
        # self.pick_zone_layout.addWidget(label_drawing_type)
        # self.pick_zone_layout.addWidget(self.option_line_button)
        
        # Zone name input
        label_zone_name = QLabel(self.tr('Zone Name'))
        label_zone_name.setStyleSheet(label_style)
        self.pick_zone_layout.addWidget(label_zone_name)
        self.pick_zone_layout.addWidget(self.input_zone_name)
        
        # Access control device selection (if applicable)
        if self.ai_flow_type is not None and (self.ai_flow_type == AIFlowType.ACCESS):
            label_device = QLabel(self.tr('Access Control Device'))
            label_device.setStyleSheet(label_style)
            self.pick_zone_layout.addWidget(label_device)
            self.pick_zone_layout.addWidget(self.device_access_control_combo_box)
            
        # Add some space before buttons
        self.pick_zone_layout.addSpacing(8)
        
        # Control buttons
        self.pick_zone_layout.addLayout(layout_button)

        # Setup pick zone widget
        widget_pick_zone = QWidget()
        widget_pick_zone.setObjectName('widget_pick_zone')
        widget_pick_zone.setStyleSheet(f'''
            QWidget#widget_pick_zone {{
                background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")}; 
                border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                border-radius: 4px;
            }}
            QComboBox {{
                padding: 4px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                border-radius: 4px;
                background-color: white;
            }}
            QLineEdit {{
                padding: 4px;
                border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                border-radius: 4px;
                background-color: white;
            }}
        ''')
        widget_pick_zone.setLayout(self.pick_zone_layout)

        # Setup result tree view widget
        widget_result_treeview = QWidget()
        widget_result_treeview.setObjectName('widget_result_treeview')
        widget_result_treeview.setStyleSheet(f'''
            QWidget#widget_result_treeview {{
                background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")}; 
                border: 1px solid {main_controller.get_theme_attribute("Color", "dialog_header_background")};
                border-radius: 4px;
            }}
        ''')
        layout_result_treeview = QVBoxLayout(widget_result_treeview)
        label_saved_zone = QLabel(self.tr('Saved Zone List'))
        label_saved_zone.setStyleSheet(
            f'color:{main_controller.get_theme_attribute("Color", "dialog_text")};  font-weight: bold')
        layout_result_treeview.addWidget(label_saved_zone)
        layout_result_treeview.addWidget(self.list_zone_result_tree_view_widget)

        # Setup main layout
        self.camera_layout.addWidget(widget_pick_zone, 20)
        self.camera_layout.addLayout(self.view_center_camera_layout, 60)
        self.camera_layout.addWidget(widget_result_treeview, 10)

        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(8, 0, 8, 8)
        main_layout.addWidget(self.zone_controller_widget)
        self.setLayout(main_layout)
        self.setStyleSheet(f'background-color: {main_controller.get_theme_attribute("Color", "ai_zone_background")}')

        # Initialize widget state
        self.update_zone_type_with_ai_flows(self.ai_flow_type)
        if self.ai_flow_type is not None and (self.ai_flow_type == AIFlowType.ACCESS):
            self.get_list_device_access_control()

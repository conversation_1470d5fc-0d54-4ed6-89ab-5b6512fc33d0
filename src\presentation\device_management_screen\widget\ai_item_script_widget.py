import logging
logger = logging.getLogger(__name__)
from src.common.widget.toggle.custom_toggle import CustomSwitch
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout

class ItemAIScriptWidget(QWidget):
    def __init__(self, parent=None, number_script = 0):
        super(ItemAIScriptWidget, self).__init__(parent)

        v_main_layout = QVBoxLayout()
    
        label = QLabel(f"Kịch bản {number_script}")
        custom_switch = CustomSwitch()
        # custom_switch.clicked = self.on_switch
        custom_switch.stateChanged.connect(
            lambda direct_right: self.on_switch())
        edit = QPushButton("Edit")
        delete = QPushButton("Delete")
        

        layout = QHBoxLayout()
        layout.addWidget(label)
        layout.addWidget(custom_switch)
        layout.addWidget(edit)
        layout.addWidget(delete)
        v_main_layout.addLayout(layout)

        v_ai_content = QVBoxLayout()
        # create ai content
        ai_type = QLabel("Loại AI")
        # bài toán AI
        ai_problem = QLabel("Bài toán AI")
        # Vung nhan dien
        ai_area = QLabel("Vùng nhận diện")
        v_ai_content.addWidget(ai_type)
        v_ai_content.addWidget(ai_problem)
        v_ai_content.addWidget(ai_area)

        v_main_layout.addLayout(v_ai_content)
        
        self.setLayout(v_main_layout)


    def on_switch(self):
        print("Switched")
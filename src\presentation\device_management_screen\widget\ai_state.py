import enum


class AIType(enum.Enum):
    HUMAN = 0
    VEHICLE = 1

class AIScriptState(enum.Enum):
    CREATE = 0
    EDIT = 1

class AIFlowType:
    RECOGNITION = "RECOGNITION"
    PROTECTION = "PROTECTION"
    FREQUENCY = "FREQUENCY"
    ACCESS = "ACCESS"
    MOTION = "MOTION"
    TRAFFIC = "TRAFFIC"
    WEAPON = "WEAPON"
    UFO = "UFO"

    @staticmethod
    def get_display_text(flow_type):
        """Get display text for a flow type"""
        display_texts = {
            AIFlowType.RECOGNITION: "Recognition",
            AIFlowType.PROTECTION: "Protection",
            AIFlowType.FREQUENCY: "Frequency",
            AIFlowType.ACCESS: "Access",
            AIFlowType.MOTION: "Motion",
            AIFlowType.TRAFFIC: "Traffic",
            AIFlowType.WEAPON: "Weapon",
            AIFlowType.UFO: "UFO"
        }
        return display_texts.get(flow_type, flow_type)
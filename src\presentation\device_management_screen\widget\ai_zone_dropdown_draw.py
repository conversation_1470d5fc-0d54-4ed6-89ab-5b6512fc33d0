from PySide6.QtWidgets import (QComboBox, QItemDelegate, QWidget, QVBoxLayout, 
                             QAbstractItemView, QListView, QStyle, QApplication,
                             QLineEdit, QMenu)
from PySide6.QtGui import QIcon, QStandardItemModel, QStandardItem, QPainter, QColor
from PySide6.QtCore import Qt, Signal, QRect, QSize, QModelIndex, QEvent
from src.styles.style import Style
from src.common.controller.main_controller import main_controller
import logging
logger = logging.getLogger(__name__)


class MultiCheckComboModel(QStandardItemModel):
    """Model for the multi-select combobox with checkboxes"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self._multiple_selection = False

    def set_multiple_selection(self, enabled):
        self._multiple_selection = enabled

    def flags(self, index):
        flags = Qt.ItemIsSelectable | Qt.ItemIsEnabled
        if self._multiple_selection:
            flags |= Qt.ItemIsUserCheckable
        return flags

    def data(self, index, role):
        value = super().data(index, role)
        if index.isValid() and role == Qt.CheckStateRole and self._multiple_selection:
            if not value:
                return Qt.Unchecked
            return value
        return value

    def setData(self, index, value, role):
        if role == Qt.CheckStateRole and not self._multiple_selection:
            return False
        result = super().setData(index, value, role)
        if result and role == Qt.CheckStateRole:
            self.dataChanged.emit(index, index)
        return True


class AIZoneDropDownDraw(QWidget):
    # Signal emitted when items are selected from the combo box
    item_selected = Signal(list)
    
    def __init__(self, use_icons=True, icon_path=None, combo_box_height=30, multiple_selection=False):
        super().__init__()
        self.use_icons = use_icons
        self.icon_path = icon_path
        self.combo_box_height = combo_box_height
        self.multiple_selection = multiple_selection
        self._separator = ', '
        self._defaultText = self.tr('Select Items')
        self._selectAllText = self.tr('All Items Selected')
        self._placeholderText = self.tr('Select Items')
        self._maxTextLength = 42
        self.initUI()

    def initUI(self):
        # Create model and combo box
        self.model = MultiCheckComboModel(self)
        self.model.set_multiple_selection(self.multiple_selection)
        self.combo_box = QComboBox(self)
        self.combo_box.setFixedHeight(self.combo_box_height)
        self.combo_box.setModel(self.model)
        
        # Create line edit for display
        self.line_edit = QLineEdit()
        self.line_edit.setReadOnly(True)
        self.line_edit.setFocusPolicy(Qt.ClickFocus)  # Allow clicking to focus
        self.line_edit.installEventFilter(self)  # Install event filter
        self.line_edit.setPlaceholderText(self._placeholderText)
        self.combo_box.setLineEdit(self.line_edit)
        self.combo_box.setInsertPolicy(QComboBox.NoInsert)

        # Set style
        style = f'''
            QComboBox {{ 
                background-color: {main_controller.get_theme_attribute("Color", "input_background")}; 
                border: 1px solid {main_controller.get_theme_attribute("Color", "border_line_edit_not_focus")}; 
                border-radius: 4px;
                padding: 4px;
                color: {main_controller.get_theme_attribute("Color", "text")};
            }}
            QComboBox:hover {{ 
                border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
            }}
            QComboBox:disabled {{ 
                background-color: {main_controller.get_theme_attribute("Color", "button_disable_background")};
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
                border: 1px solid {main_controller.get_theme_attribute("Color", "border_line_edit_not_focus")};
            }}
            QComboBox::drop-down {{
                 background-color: transparent;
                 border: none;
                 subcontrol-origin: padding;
                 subcontrol-position: right;
                 width: 20px;
             }}
            QComboBox::down-arrow {{ 
                image: url({main_controller.get_theme_attribute("Image", "down_arrow_linedit")}); 
            }}
            QLineEdit {{
                color: {main_controller.get_theme_attribute("Color", "text")};
            }}
            QLineEdit:disabled {{
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
            }}
            QLineEdit::placeholder {{
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
            }}
            QLineEdit:disabled::placeholder {{
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
            }}
            QListView {{
                background-color: {main_controller.get_theme_attribute("Color", "input_background")};
                border: 1px solid {main_controller.get_theme_attribute("Color", "border_line_edit_not_focus")};
                border-radius: 4px;
                outline: none;
                color: {main_controller.get_theme_attribute("Color", "text")};
            }}
            QListView::item {{
                padding: 4px;
                border: none;
                margin: 2px;
                background-color: transparent;
            }}
            QListView::item:selected {{
                background-color: {main_controller.get_theme_attribute("Color", "input_background")};
                color: {main_controller.get_theme_attribute("Color", "text")};
            }}
            QListView:disabled {{
                background-color: {main_controller.get_theme_attribute("Color", "button_disable_background")};
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
            }}
            QListView::item:disabled {{
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
            }}
        '''
        if self.multiple_selection:
            style += f'''
                QListView::indicator {{
                    width: 16px;
                    height: 16px;
                    border: none;
                    background-color: transparent;
                }}
                QListView::indicator:unchecked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                    background-color: transparent;
                }}
                QListView::indicator:checked {{
                    border: none;
                    image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                    background-color: transparent;
                }}
                QListView::indicator:hover {{
                    border: none;
                    background-color: transparent;
                }}
            '''
        self.combo_box.setStyleSheet(style)

        # Configure for multiple selection
        if self.multiple_selection:
            self.list_view = QListView()
            self.list_view.setModel(self.model)
            self.list_view.setSelectionMode(QAbstractItemView.MultiSelection)
            self.list_view.setEditTriggers(QAbstractItemView.NoEditTriggers)
            self.combo_box.setView(self.list_view)
            self.combo_box.setModelColumn(0)
            
            # Connect signals
            self.model.dataChanged.connect(self.updateCheckedItems)
            self.combo_box.activated.connect(self.toggleCheckState)
            
            # Prevent closing on item click
            self.list_view.clicked.connect(lambda: None)
            self.list_view.pressed.connect(lambda: None)
        else:
            # Single selection mode
            self.combo_box.activated.connect(self._on_single_selection)
            # Prevent closing on item click
            self.combo_box.view().clicked.connect(lambda: None)
            self.combo_box.view().pressed.connect(lambda: None)

        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.combo_box)
        self.setLayout(self.layout)

    def add_items(self, items: list, icons=None):
        """Add items to the combobox"""
        self.model.clear()
        
        for i, item in enumerate(items):
            combo_item = QStandardItem()
            if self.use_icons and icons and i < len(icons):
                combo_item.setIcon(QIcon(icons[i]))
            combo_item.setText(self.tr(item))
            combo_item.setCheckable(self.multiple_selection)
            if self.multiple_selection:
                combo_item.setCheckState(Qt.Unchecked)
            self.model.appendRow(combo_item)
        
        # Set placeholder text when no items are selected
        self.line_edit.setPlaceholderText(self._placeholderText)
        self.line_edit.setText("")

    def _on_single_selection(self, index):
        """Handle single selection mode"""
        selected_item = self.combo_box.currentText()
        if selected_item:
            self.line_edit.setText(selected_item)
            self.item_selected.emit([selected_item])
        else:
            self.line_edit.setText("")
            self.line_edit.setPlaceholderText(self._placeholderText)

    def toggleCheckState(self, index):
        """Toggle the check state of an item"""
        if self.multiple_selection:
            item = self.model.item(index)
            logger.debug(f"Toggling checkbox state for item: {item.text()}, current state: {item.checkState()}")
            if item.checkState() == Qt.Checked:
                item.setCheckState(Qt.Unchecked)
                logger.debug(f"Changed state to Unchecked for item: {item.text()}")
            else:
                item.setCheckState(Qt.Checked)
                logger.debug(f"Changed state to Checked for item: {item.text()}")
            # Don't hide popup
            return True

    def updateCheckedItems(self, topLeft=None, bottomRight=None):
        """Update the display text based on checked items"""
        if not self.multiple_selection:
            return

        checked_items = []
        for i in range(self.model.rowCount()):
            item = self.model.item(i)
            if item.checkState() == Qt.Checked:
                checked_items.append(item.text())
        
        logger.debug(f"Current checked items: {checked_items}")

        if not checked_items:
            self.line_edit.setText("")
            self.line_edit.setPlaceholderText(self._placeholderText)
            logger.debug("No items checked, showing placeholder text")
        # elif len(checked_items) == self.model.rowCount():
        #     self.line_edit.setText(self._selectAllText)
        #     logger.debug("All items checked, showing 'All Items Selected'")
        else:
            self.line_edit.setText(self._separator.join(checked_items))
            logger.debug(f"Showing selected items: {self.line_edit.text()}")

        # Only emit signal if the checked items have actually changed
        if hasattr(self, '_last_checked_items') and self._last_checked_items != checked_items:
            self.item_selected.emit(checked_items)
            logger.debug(f"Emitted item_selected signal with items: {checked_items}")
        self._last_checked_items = checked_items.copy()

    def get_selected_items(self):
        """Get all currently selected items"""
        if self.multiple_selection:
            selected_items = []
            for i in range(self.model.rowCount()):
                item = self.model.item(i)
                if item.checkState() == Qt.Checked:
                    selected_items.append(item.text())
            return selected_items
        else:
            return [self.combo_box.currentText()]

    def set_selected_items(self, items):
        """Set the checked state for given items"""
        logger.debug(f"Setting selected items: {items}")
        if not self.multiple_selection:
            index = self.combo_box.findText(items[0] if items else "")
            if index >= 0:
                self.combo_box.setCurrentIndex(index)
                logger.debug(f"Single selection: Set current index to {index}")
            return

        for i in range(self.model.rowCount()):
            item = self.model.item(i)
            new_state = Qt.Checked if item.text() in items else Qt.Unchecked
            item.setCheckState(new_state)
            logger.debug(f"Set item '{item.text()}' state to {'Checked' if new_state == Qt.Checked else 'Unchecked'}")

    def check_all(self, check=True):
        """Check or uncheck all items"""
        if not self.multiple_selection:
            return

        logger.debug(f"Setting all items to {'Checked' if check else 'Unchecked'}")
        for i in range(self.model.rowCount()):
            item = self.model.item(i)
            item.setCheckState(Qt.Checked if check else Qt.Unchecked)
            logger.debug(f"Set item '{item.text()}' state to {'Checked' if check else 'Unchecked'}")

    def contextMenuEvent(self, event):
        """Show context menu with check all/clear all options"""
        if not self.multiple_selection:
            return

        menu = QMenu(self)
        clear_all = menu.addAction("Clear All")
        check_all = menu.addAction("Check All")
        
        action = menu.exec_(event.globalPos())
        if action == clear_all:
            self.check_all(False)
        elif action == check_all:
            self.check_all(True)

    def remove_item_by_name(self, name: str):
        """Remove an item by name"""
        index = self.combo_box.findText(name)
        if index >= 0:
            self.combo_box.removeItem(index)
                
    def set_combo_box_style(self, style_sheet):
        """Set custom style sheet"""
        self.combo_box.setStyleSheet(style_sheet)
        
    def set_combo_box_height(self, height):
        """Set the height of the combo box"""
        self.combo_box.setFixedHeight(height)

    def set_enabled(self, enabled: bool):
        """Enable or disable the widget"""
        self.setEnabled(enabled)
        self.combo_box.setEnabled(enabled)
        
        style = f'''
            QComboBox {{ 
                background-color: {main_controller.get_theme_attribute("Color", "input_background") if enabled else main_controller.get_theme_attribute("Color", "button_disable_background")}; 
                border: 1px solid {main_controller.get_theme_attribute("Color", "border_line_edit_not_focus")}; 
                border-radius: 4px;
                color: {main_controller.get_theme_attribute("Color", "text") if enabled else main_controller.get_theme_attribute("Color", "text_disable")};
            }}
            QComboBox:hover {{ 
                border: 1px solid {main_controller.get_theme_attribute("Color", "primary")};
            }}
            QComboBox:disabled {{ 
                background-color: {main_controller.get_theme_attribute("Color", "button_disable_background")};
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
                border: 1px solid {main_controller.get_theme_attribute("Color", "border_line_edit_not_focus")};
            }}
            QComboBox::drop-down {{
                 background-color: transparent;
             }}
            QComboBox::down-arrow {{ 
                image: url({main_controller.get_theme_attribute("Image", "down_arrow_linedit")}); 
            }}
            QLineEdit:disabled {{
                color: {main_controller.get_theme_attribute("Color", "text_disable")};
            }}
        '''
        self.combo_box.setStyleSheet(style)

    def showPopup(self, event):
        """Show the dropdown list when line edit is clicked"""
        # Don't store states when just showing popup
        self.combo_box.showPopup()

    def _store_checkbox_states(self):
        """Store current checkbox states"""
        if self.multiple_selection:
            self._checkbox_states = {}
            for i in range(self.model.rowCount()):
                item = self.model.item(i)
                self._checkbox_states[item.text()] = item.checkState()
            logger.debug(f"Stored checkbox states: {self._checkbox_states}")

    def _restore_checkbox_states(self):
        """Restore checkbox states after popup is shown"""
        if self.multiple_selection and hasattr(self, '_checkbox_states'):
            for i in range(self.model.rowCount()):
                item = self.model.item(i)
                if item.text() in self._checkbox_states:
                    item.setCheckState(self._checkbox_states[item.text()])
            logger.debug(f"Restored checkbox states: {self._checkbox_states}")

    def eventFilter(self, obj, event):
        if obj == self.line_edit:
            if event.type() == QEvent.MouseButtonPress:
                if event.button() == Qt.LeftButton:
                    # Store states before showing popup
                    self._store_checkbox_states()
                    return True
            elif event.type() == QEvent.MouseButtonRelease:
                if event.button() == Qt.LeftButton:
                    # Show popup on release
                    self.combo_box.showPopup()
                    # Restore checkbox states after popup is shown
                    self._restore_checkbox_states()
                    return True
        return False
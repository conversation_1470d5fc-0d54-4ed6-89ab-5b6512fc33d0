from PySide6.QtWidgets import QTableView
from PySide6.QtGui import QPainter, QColor, QPen, Qt
from PySide6.QtCore import Signal

from src.styles.style import Style, Theme
from src.utils.config import Config
from src.common.controller.main_controller import main_controller


class TableViewBase(QTableView):
    current_hoverred_row = Signal(int)
    def __init__(self,callback_onclicked = None, is_use_paint_event=False):
        super().__init__()
        self.setMouseTracking(True)
        self.hovered_row = -1
        self.is_use_paint_event = is_use_paint_event
        if callback_onclicked is not None:
            self.clicked.connect(callback_onclicked)

    def onClicked(self, index):
        if index.isValid():
            row = index.row()
            print(f"Clicked on row {row}")

    def enterEvent(self, event):
        # self.viewport().update()
        index = self.indexAt(event.pos())
        if index.isValid():
            self.hovered_row = index.row()
            self.current_hoverred_row.emit(self.hovered_row)
            self.viewport().update()
        super().enterEvent(event)

    def leaveEvent(self, event):
        self.hovered_row = -1
        self.current_hoverred_row.emit(self.hovered_row)
        self.viewport().update()
        super().leaveEvent(event)

    def mouseMoveEvent(self, event):
        index = self.indexAt(event.pos())
        
        if index.row() != self.hovered_row:
            self.current_hoverred_row.emit(index.row())
            self.hovered_row = index.row()
        self.viewport().update()
        super().mouseMoveEvent(event)

    def paintEvent(self, event):
        super().paintEvent(event)
        if not self.is_use_paint_event:
            painter = QPainter(self.viewport())
            hoverred_color = QColor(main_controller.get_theme_attribute("Color", "table_row_hoverred"))  # hoverred color 
            theme = main_controller.current_theme

            for row in range(self.model().rowCount()):
                for col in range(self.model().columnCount()):
                    index = self.model().index(row, col)
                    rect = self.visualRect(index)

                    # Check if the current cell has a widget set with setIndexWidget
                    if self.indexWidget(index) is not None:
                        # Apply background color for widget-based cells
                        if row == self.hovered_row:
                            painter.fillRect(rect, hoverred_color)
                        else:
                            if theme == Theme.DARK:
                                painter.fillRect(rect, QColor(main_controller.get_theme_attribute("Color", "widget_background_1")))
                            else:
                                painter.fillRect(rect, QColor(
                                    main_controller.get_theme_attribute("Color", "table_even_row_background") if row % 2 != 0 else
                                    main_controller.get_theme_attribute("Color", "table_odd_row_background")
                                ))
                    else:
                        # Apply background color for QStandardItem-based cells
                        if row == self.hovered_row:
                            painter.fillRect(rect, hoverred_color)
                            # Optionally: redraw the text manually
                        else:
                            if theme == Theme.DARK:
                                painter.fillRect(rect, QColor(main_controller.get_theme_attribute("Color", "widget_background_1")))
                            else:
                                painter.fillRect(rect, QColor(
                                    main_controller.get_theme_attribute("Color", "table_even_row_background") if row % 2 != 0 else
                                    main_controller.get_theme_attribute("Color", "table_odd_row_background")
                                ))
                        item = self.model().itemFromIndex(index)
                        if item is not None:
                            if theme == Theme.LIGHT and row == self.hovered_row:
                                painter.setPen(QPen(Style.PrimaryColor.white_2))
                                painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, item.text())
                            else:
                                painter.setPen(QPen(item.foreground().color()))
                                painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, item.text())

            painter.end()
        
import sys

from PySide6.QtGui import QImage
from PySide6.QtCore import Qt, QCoreApplication
from PySide6.QtWidgets import QApplication, QWidget,  QVBoxLayout, QLabel, QHBoxLayout, QPushButton, QDialog

from src.common.model.warning_and_alert_model import WarningMethodAndAlertModel
from src.common.controller.main_controller import main_controller


class MapScreen(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.load_ui()

    def load_ui(self):
        # create label
        self.mylabel = QLabel(self.tr("Map Screen"))
        # center

        self.button = QPushButton(self.tr("Get"))
        self.button.clicked.connect(self.showDialog)
        self.button.setFixedSize(200, 50)

        self.button_two = QPushButton(self.tr("Update"))
        self.button_two.clicked.connect(self.update_data)
        self.button_two.setFixedSize(200, 50)

        self.button_three = QPushButton(self.tr("Button 3"))
        self.button_three.setFixedSize(200, 50)

        # create layout
        self.layout = QVBoxLayout()
        self.layout.setAlignment(Qt.AlignTop)

        self.layout.addWidget(self.mylabel)
        self.layout.addWidget(self.button)
        self.layout.addWidget(self.button_two)
        self.layout.addWidget(self.button_three)
        self.mylabel.setStyleSheet("color: white; font-size: 16px;")
        self.setLayout(self.layout)

    def retranslateUi_map(self):
        self.mylabel.setText(QCoreApplication.translate("MapScreen", u"Map Screen", None))
        # center
        self.button.setText(QCoreApplication.translate("MapScreen", u"Button 1", None))

        self.button_two.setText(QCoreApplication.translate("MapScreen", u"Button 2", None))

        self.button_three.setText(QCoreApplication.translate("MapScreen", u"Button 3", None))
        self.update()

    def showDialog(self):
        main_controller.get_setting_warning_and_alert()

    def update_data(self):
        data = WarningMethodAndAlertModel(warningMethods=[], alertChannels=[])
        main_controller.update_setting_warning_and_alert_by_patch(data)

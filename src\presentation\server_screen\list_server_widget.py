


from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QGuiApplication,QShortcut,QKeySequence
from PySide6.QtWidgets import QStackedWidget, QWidget, QVBoxLayout, QGraphicsView,QGraphicsScene,QGraphicsProxyWidget
from src.utils.camera_qsettings import camera_qsettings
from src.common.server.server_info import ServerInfo,ServerInfoModel,server_info_model_manager
from src.presentation.server_screen.server_item import ServerItem
from src.common.controller.main_controller import connect_slot,main_controller
import os
import logging
logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)

class ListServerWidget(QWidget):
    change_logo_position = Signal(int)
    def __init__(self):
        super().__init__()
        self.connect_slot()
        self.calculate_layout()
        self.spacing = 0
        self.num_grid = 4
        self.main_layout = QVBoxLayout(self)
        self.graphicsview = QGraphicsView()
        # self.graphicsview.setAttribute(Qt.WA_TranslucentBackground)
        self.graphicsview.setStyleSheet("background: transparent")
        self.graphicsview.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.graphicsview.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        
        self.scene = QGraphicsScene()
        self.graphicsview.setScene(self.scene)
        self.main_layout.addWidget(self.graphicsview)
        self.stackedwidget = QStackedWidget()
        self.list_widgets = []
        self.list_widgets_filtered = []
        # self.load_qsetting_history()

    def connect_slot(self):
        connect_slot(
            (main_controller.search_server_signal,self.search_server_signal),
            (server_info_model_manager.add_server_signal,self.add_server_signal),
            (server_info_model_manager.add_server_list_signal,self.add_server_list_signal),
            (server_info_model_manager.delete_server_signal,self.delete_server_signal))

    def set_style(self):
        self.setStyleSheet(f'''
            QGraphicsView {{
                background-color: transparent;
                }}
        '''
        )
    def calculate_layout(self, desktop_screen_size=None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        screen_width = desktop_screen_size.width()
        screen_height = desktop_screen_size.height()
        percent_menu_bar = 0.03
        mergin_width = 0.97
        mergin_height = 0.89
        padding = 10
        self.screen_available_width = mergin_width *screen_width - 2*padding
        self.screen_available_height = mergin_height *screen_height - 2*padding

    def update_servers_filtered(self):
        if len(self.list_widgets_filtered) == 1:
            x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2 - self.spacing/2
            y = self.screen_available_height - self.screen_available_height/self.num_grid
            self.list_widgets_filtered[0].setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(self.list_widgets_filtered) == 2:
            for idx,item in enumerate(self.list_widgets):
                if idx == 0:
                    x = 2*self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(self.list_widgets_filtered) == 3:
            for idx,item in enumerate(self.list_widgets):
                if idx == 2:
                    x = (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
                elif idx == 0:
                    x = self.screen_available_width/2 + (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
        else:
            for idx,item in enumerate(self.list_widgets_filtered):
                row = (15 - idx)//self.num_grid
                col = (15 - idx)%self.num_grid
                item.setGeometry(col * self.screen_available_width/self.num_grid,row * self.screen_available_height/self.num_grid,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        if len(self.list_widgets_filtered) < 5 and len(self.list_widgets_filtered) > 0:
            self.change_logo_position.emit(1)
        elif len(self.list_widgets_filtered) < 9 and len(self.list_widgets_filtered) > 0:
            self.change_logo_position.emit(2)
    #slot    
    def add_server_signal(self,data):
        proxy_widget = QGraphicsProxyWidget()
        proxy_widget.setOpacity(0.7)
        widget = ServerItem(server_info = data)
        proxy_widget.setWidget(widget)
        self.scene.addItem(proxy_widget)
        self.list_widgets.append(proxy_widget)
        if len(self.list_widgets) == 1:
            x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2 - self.spacing/2
            y = self.screen_available_height - self.screen_available_height/self.num_grid
            self.list_widgets[0].setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(self.list_widgets) == 2:
            for idx,item in enumerate(self.list_widgets):
                if idx == 0:
                    x = 2*self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(self.list_widgets) == 3:
            for idx,item in enumerate(self.list_widgets):
                if idx == 2:
                    x = (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
                elif idx == 0:
                    x = self.screen_available_width/2 + (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
        else:
            for idx,item in enumerate(self.list_widgets):
                row = (15 - idx)//self.num_grid
                col = (15 - idx)%self.num_grid
                item.setGeometry(col * self.screen_available_width/self.num_grid,row * self.screen_available_height/self.num_grid,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        if len(self.list_widgets) < 5 and len(self.list_widgets) > 0:
            self.change_logo_position.emit(1)
        elif len(self.list_widgets) < 9 and len(self.list_widgets) > 0:
            self.change_logo_position.emit(2)
    #slot
    def add_server_list_signal(self,data):
        # logger.debug("add_server_list_signal")
        servers = server_info_model_manager.server_list
        proxy_widget = QGraphicsProxyWidget()
        proxy_widget.setWidget(QWidget())
        self.scene.addItem(proxy_widget)   
        proxy_widget.setGeometry(0,0,self.screen_available_width,self.screen_available_height)
        proxy_widget.setVisible(False)
        if len(servers) == 1:
            for idx,server in servers.items():
                proxy_widget = QGraphicsProxyWidget()
                widget = ServerItem(server_info = server)
                proxy_widget.setWidget(widget)
                self.scene.addItem(proxy_widget)
                
                self.list_widgets.append(proxy_widget)
                x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2 - self.spacing/2
                y = self.screen_available_height - self.screen_available_height/self.num_grid
                proxy_widget.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(servers) == 2:
            for idx,server in servers.items():
                proxy_widget = QGraphicsProxyWidget()
                widget = ServerItem(server_info = server)
                proxy_widget.setWidget(widget)
                self.scene.addItem(proxy_widget)
                self.list_widgets.append(proxy_widget)  
                if idx == 0:
                    x = 2*self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    proxy_widget.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    proxy_widget.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(servers) == 3:
            for idx,server in servers.items():
                proxy_widget = QGraphicsProxyWidget()
                widget = ServerItem(server_info = server)
                proxy_widget.setWidget(widget)
                self.scene.addItem(proxy_widget)
                self.list_widgets.append(proxy_widget)  
                if idx == 0:
                    x = (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    proxy_widget.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    proxy_widget.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
                elif idx == 2:
                    x = self.screen_available_width/2 + (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    proxy_widget.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
        else:
            for idx,server in servers.items():
                row = (15 - idx)//self.num_grid
                col = (15 - idx)%self.num_grid
                proxy_widget = QGraphicsProxyWidget()
                proxy_widget.setOpacity(0.7)
                widget = ServerItem(server_info = server)
                proxy_widget.setWidget(widget)
                self.scene.addItem(proxy_widget)
                proxy_widget.setGeometry(col * self.screen_available_width/self.num_grid,row * self.screen_available_height/self.num_grid,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                self.list_widgets.append(proxy_widget)
        if len(servers) < 5 and len(servers) > 0:
            self.change_logo_position.emit(1)
        elif len(servers) < 9 and len(servers) > 0:
            self.change_logo_position.emit(2)

    #slot
    def delete_server_signal(self,data):
        server:ServerInfoModel = data
        for item in self.list_widgets:
            if item.widget().server_info == server:
                # logger.debug(f"Deleting server = {server.data}")
                camera_qsettings.delete_server(server.data.to_dict())
                self.list_widgets.remove(item)
                self.scene.removeItem(item)
                break
        self.update_widget_position()
        
    #slot
    def search_server_signal(self, text):
        self.list_widgets_filtered = []
        for widget in self.list_widgets:
            server_item = widget.widget()
            if text in server_item.server_info.data.username or text in server_item.server_info.data.server_ip or text in server_item.server_info.data.server_port:
                self.list_widgets_filtered.append(widget)
                widget.setVisible(True)
            else:
                widget.setVisible(False)
        self.update_servers_filtered()

    def load_qsetting_history(self):
        servers = camera_qsettings.json_servers
        list_data = []
        for idx,server in servers.items():
            server_info_model = ServerInfoModel(server = ServerInfo.from_dict(server))
            # data = ServerInfoModel(server=ServerInfo(username='ahihi',server_ip='*************',server_port='58080',websocket_port='18080'))
            list_data.append(server_info_model)

        server_info_model_manager.add_server_list(server_list= list_data)

    def change_status_signal(self,data):
        pass
    def update_widget_position(self):
        if len(self.list_widgets) == 1:
            x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2 - self.spacing/2
            y = self.screen_available_height - self.screen_available_height/self.num_grid
            self.list_widgets[0].setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(self.list_widgets) == 2:
            for idx,item in enumerate(self.list_widgets):
                if idx == 0:
                    x = 2*self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/self.num_grid
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        elif len(self.list_widgets) == 3:
            for idx,item in enumerate(self.list_widgets):
                if idx == 2:
                    x = (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
                elif idx == 1:
                    x = self.screen_available_width/2 - (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
                elif idx == 0:
                    x = self.screen_available_width/2 + (self.screen_available_width/self.num_grid)/2
                    y = self.screen_available_height - self.screen_available_height/self.num_grid
                    item.setGeometry(x,y,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing) 
        else:
            for idx,item in enumerate(self.list_widgets):
                row = (15 - idx)//self.num_grid
                col = (15 - idx)%self.num_grid
                item.setGeometry(col * self.screen_available_width/self.num_grid,row * self.screen_available_height/self.num_grid,self.screen_available_width/self.num_grid - self.spacing,self.screen_available_height/self.num_grid - self.spacing)
        
        for idx,item in enumerate(self.list_widgets):
            server_item: ServerItem = item.widget()
            if isinstance(server_item,ServerItem):
                server_item.setFixedWidth((self.screen_available_width/self.num_grid - self.spacing))

        if len(self.list_widgets) < 5 and len(self.list_widgets) > 0:
            self.change_logo_position.emit(1)
        elif len(self.list_widgets) < 9 and len(self.list_widgets) > 0:
            self.change_logo_position.emit(2)

    def update_resize(self):
        self.update_widget_position()

    def resizeEvent(self,event):
        # logger.debug(f'resizeEvent')
        size = event.size()
        self.screen_available_width = size.width() - 20
        self.screen_available_height = size.height() - 20
        self.update_resize()

    def retranslate_ui_server(self):
        for item in self.scene.items():
            # print(f'item = {item}    \n'
            #       f'item.widget = {item.widget()}\n')
            if isinstance(item.widget(), ServerItem):
                item_server: ServerItem = item.widget()
                item_server.restranslate_item_server()

    def set_dynamic_stylesheet(self):
        for item in self.list_widgets:
            item.widget().set_dynamic_stylesheet()
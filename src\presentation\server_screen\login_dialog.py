from src.styles.style import Style
from PySide6.QtCore import Qt, QPropertyAnimation, QRect
from PySide6.QtGui import QIcon, QAction,QImage,QPixmap
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton,QDialog,QHBoxLayout,QLineEdit
from src.presentation.device_management_screen.widget.list_custom_widgets import InputWithTitle
from src.utils.camera_qsettings import camera_qsettings
from src.common.server.server_info import ServerInfo,ServerInfoModel,server_info_model_manager
from src.common.controller.controller_manager import Controller,controller_manager
from src.common.controller.main_controller import connect_slot,main_controller
from src.common.widget.notifications.notify import Notifications
import os
import requests
import base64
import logging
from ipaddress import ip_address

from src.utils.config import Config

logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)
class LoginDialog(QDialog):
    def __init__(self,parent = None,server: ServerInfoModel = None,controller:Controller = None,change_password = False):
        super().__init__(parent)
        self.setModal(False)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setFixedWidth(500)
        self.setFixedHeight(600)  # Set fixed height for dialog
        self.controller = controller
        self.server = server
        self.change_password = change_password
        self.shake_animation = None
        main_layout = QVBoxLayout(self)
        self.login_widget = self.create_login_widget()
        main_layout.addWidget(self.login_widget)
        if not self.change_password:
            # Only get captcha if server IP contains cygate
            if self.server_ip_widget.line_edit.text() and Config.CYGATE_API_URL in self.server_ip_widget.line_edit.text():
                self.get_captcha()
        self.set_dynamic_style()
        self.connect_slot()

    def connect_slot(self):
        connect_slot(
            (controller_manager.result_login,self.result_login),
            # (controller_manager.start_login,self.start_login)
            )
        
    def get_captcha(self):
        try:
            captcha = requests.get(url = "https://" +Config.CYGATE_API_URL + Config.CAPTCHA_URL_PATH)
            if captcha.status_code == 200:
                data = captcha.json()
                image = data['data']['image']
                self.captcha_id = data['data']['id']
                base64_string = image

                # Remove base64 prefix if present
                base64_data = base64_string.split(",")[1] if "," in base64_string else base64_string

                # Decode base64 string
                image_data = base64.b64decode(base64_data)
                image = QImage()
                image.loadFromData(image_data)

                # Convert QImage to QPixmap
                pixmap = QPixmap.fromImage(image)
                self.captcha_image.setPixmap(pixmap)
            else:
                logger.warning(f"Failed to get captcha - status code: {captcha.status_code}")
                self.handle_captcha_error()
                Notifications(
                    parent=main_controller.list_parent['MainScreen'],
                    title=self.tr("Failed to load captcha. Please try again later."),
                    icon=Style.PrimaryImage.fail_result
                )
        except (requests.exceptions.RequestException, ValueError) as e:
            logger.error(f"Error getting captcha: {str(e)}")
            self.handle_captcha_error()
            Notifications(
                parent=main_controller.list_parent['MainScreen'],
                title=self.tr("Unable to connect to captcha service. Please check your network connection."),
                icon=Style.PrimaryImage.fail_result
            )

    def handle_captcha_error(self):
        # Make captcha optional by setting empty values
        self.captcha_id = None
        if hasattr(self, 'captcha_image'):
            self.captcha_image.hide()
        if hasattr(self, 'text_captcha'):
            self.text_captcha.hide()

    def create_login_widget(self):
        widget = QWidget()
        self.title = QLabel(self.tr('Add Server'))
        self.title.setAlignment(Qt.AlignCenter)
        
        self.server_ip_widget = InputWithTitle(title=self.tr("Server IP"), text_placeholder=self.tr("Server IP: *************"), is_require_field=True)

        self.server_port_widget = InputWithTitle(title=self.tr("Server Port"), text_placeholder=self.tr("Enter server port"), is_require_field=True)

        self.server_websocket_widget = InputWithTitle(title=self.tr("Event Port"), text_placeholder=self.tr("Enter event port"), is_require_field=True)

        self.server_username_widget = InputWithTitle(title=self.tr("Username"), text_placeholder=self.tr("Enter username"), is_require_field=True)

        self.server_password_widget = InputWithTitle(title=self.tr("Password"), text_placeholder=self.tr("Enter password"), is_password_line=True, is_require_field=True)
        if self.controller is not None:
            self.server_ip_widget.line_edit.setText(self.controller.server.data.server_ip)
            self.server_port_widget.line_edit.setText(self.controller.server.data.server_port)
            self.server_websocket_widget.line_edit.setText(self.controller.server.data.websocket_port)
            self.server_username_widget.line_edit.setText(self.controller.server.data.username)
            self.server_password_widget.line_edit.setText(self.controller.server.data.password)

        self.error_label = QLabel("")
        self.error_label.setStyleSheet(
            f"color: {Style.PrimaryColor.primary}; font-weight: 400")
        self.error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.showPassAction = QAction(
            QIcon(Style.PrimaryImage.eye_close), self.tr("Show password"), self)

        self.showPassAction.setCheckable(True)
        self.showPassAction.toggled.connect(self.togglePasswordVisibility)
        
        control_layout = QHBoxLayout()

        if self.change_password:
            self.connect_server_button = QPushButton(self.tr("Change"))
        else:
            self.connect_server_button = QPushButton(self.tr("Login"))
        self.connect_server_button.setFixedHeight(50)
        self.connect_server_button.clicked.connect(
            self.click_to_login)
        
        self.btn_cancel = QPushButton(self.tr("Cancel"))
        self.btn_cancel.setFixedHeight(50)
        self.btn_cancel.clicked.connect(
            self.cancel_clicked)
        control_layout.addWidget(self.connect_server_button)
        control_layout.addWidget(self.btn_cancel)

        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.title,Qt.AlignmentFlag.AlignCenter)

        layout.addWidget(self.server_ip_widget)
        layout.addWidget(self.server_port_widget)
        layout.addWidget(self.server_websocket_widget)
        layout.addWidget(self.server_username_widget)
        layout.addWidget(self.server_password_widget)
        layout.addWidget(self.error_label)

        # Create captcha widgets but keep them hidden initially
        if not self.change_password:
            self.captcha_container = QWidget()
            captcha_layout = QHBoxLayout(self.captcha_container)
            self.captcha_image = QLabel()
            self.captcha_image.setStyleSheet(f'background-color: lightgray')
            self.text_captcha = QLineEdit()
            captcha_layout.addWidget(self.captcha_image)
            captcha_layout.addWidget(self.text_captcha)
            self.captcha_container.hide()
            layout.addWidget(self.captcha_container)

        self.server_ip_widget.line_edit.returnPressed.connect(self.server_port_widget.line_edit.setFocus)
        self.server_port_widget.line_edit.returnPressed.connect(self.server_websocket_widget.line_edit.setFocus)
        layout.addLayout(control_layout)

        # Add text changed signal to server IP input
        self.server_ip_widget.line_edit.textChanged.connect(self.on_server_ip_changed)

        # Initially hide port fields
        self.server_port_widget.hide()
        self.server_websocket_widget.hide()

        return widget
    
    def start_shake_animation(self, widget: QWidget):
        start_rect = widget.geometry()
        shake_rect = QRect(start_rect.x() - 5, start_rect.y(),
                           start_rect.width(), start_rect.height())
        self.shake_animation.setStartValue(start_rect)
        self.shake_animation.setEndValue(shake_rect)
        self.shake_animation.start()
        # set old geometry after animation end
        self.shake_animation.finished.connect(
            lambda: widget.setGeometry(start_rect))
        
    def togglePasswordVisibility(self):
        pass
        # if self.password_edit.echoMode() == QLineEdit.EchoMode.Password:
        #     self.password_edit.setEchoMode(QLineEdit.EchoMode.Normal)
        #     self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye))
        #     self.showPassAction.setToolTip(self.tr("Hide password"))
        # else:
        #     self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        #     self.showPassAction.setIcon(QIcon(Style.PrimaryImage.eye_close))
        #     self.showPassAction.setToolTip(self.tr("Show password"))

    def cancel_clicked(self):
        self.close()

    def click_to_login(self):
        logger.debug(f'click_to_login')
        # check if self.shake_animation running -> return
        if self.shake_animation and self.shake_animation.state() == QPropertyAnimation.State.Running:
            return

        # Get the entered username and password
        self.server_ip_data = self.server_ip_widget.line_edit.text()
        self.server_port_data = self.server_port_widget.line_edit.text()
        self.event_port_data = self.server_websocket_widget.line_edit.text()
        self.username_data = self.server_username_widget.line_edit.text()
        self.password_data = self.server_password_widget.line_edit.text()
        if not self.change_password:
            self.captcha = self.text_captcha.text()

        if self.error_label:
            self.error_label.setText("")
            # add effect shake text
            self.shake_animation = QPropertyAnimation(
                self.error_label, b"geometry")
            self.shake_animation.setDuration(100)
            self.shake_animation.setLoopCount(5)

        if self.server_ip_data == '':
            self.error_label.setText(self.tr("Please enter a address Server."))
            self.start_shake_animation(self.error_label)
        elif self.server_port_widget.isVisible() and self.server_port_data == '':
            self.error_label.setText(self.tr("Please enter a server port."))
            self.start_shake_animation(self.error_label)
        elif self.server_websocket_widget.isVisible() and self.event_port_data == '':
            self.error_label.setText(self.tr("Please enter a websocket port."))
            self.start_shake_animation(self.error_label)
        elif self.username_data == '':
            self.error_label.setText(self.tr("Please enter a username."))
            self.start_shake_animation(self.error_label)
        elif self.password_data == '':
            self.error_label.setText(self.tr("Please enter a password."))
            self.start_shake_animation(self.error_label)
        else:
            if self.change_password:
                server_info = self.controller.server
                server_info_model = server_info_model_manager.get_serverinfo_model(server_ip=self.server_ip_data)
                if server_info_model is not None and server_info_model != server_info:
                    self.error_label.setText(self.tr("This Server already exists."))
                    return
                
                server_info.edit_server(username = self.username_data, password = self.password_data,server_ip=self.server_ip_data,server_port=self.server_port_data,websocket_port=self.event_port_data)
                camera_qsettings.set_server(json_server=server_info.data.to_dict())
                server_info.change_server_info_signal.emit('change_server_info_signal')
                self.close()
                return 
            
            # Only include captcha if available
            captcha_data = {
                'captcha': self.text_captcha.text() if hasattr(self, 'text_captcha') and self.text_captcha.isVisible() else None,
                'captcha_id': self.captcha_id
            } if self.captcha_id else {}

            if self.controller:
                self.controller.server.edit_server(
                    username=self.username_data, 
                    password=self.password_data,
                    server_ip=self.server_ip_data,
                    server_port=self.server_port_data,
                    websocket_port=self.event_port_data,
                    **captcha_data
                )
                
                controller = Controller(server=self.controller.server)
                main_controller.signal_load_animation.emit(True)
                controller.login(parent=self)
            else:
                server = ServerInfoModel(
                    server=ServerInfo(
                        username=self.username_data, 
                        password=self.password_data,
                        server_ip=self.server_ip_data,
                        server_port=self.server_port_data,
                        websocket_port=self.event_port_data,
                        **captcha_data
                    )
                )
                controller:Controller = controller_manager.get_controller(server_ip=server.data.server_ip)
                # kiểm tra server ip này có đang connect chưa, nếu có thì gửi cảnh báo disconnect rồi đang nhập lại
                if controller is not None:
                    logger.debug(f'controller.server.data = {controller.server.data}')
                    Notifications(parent=main_controller.list_parent['MainScreen'], title=self.tr("This Server is connected. Please disconnect and login again."),icon=Style.PrimaryImage.fail_result)
                    self.close()
                # kiểm tra server có đang tồn tại trong lịch sử đăng nhập chưa, nếu có thì gửi cảnh báo đã tồn tại
                server_info_model = server_info_model_manager.get_serverinfo_model(server_ip=self.server_ip_data)
                if server_info_model is not None:
                    self.error_label.setText(self.tr("This Server already exists."))
                    return
                controller = Controller(server = server)
                main_controller.signal_load_animation.emit(True)
                controller.login(parent=self)

    #@slot
    def result_login(self,data):
        result, controller, deny_permission = data

        if result:
            if controller.server.data.id is not None:
                pass 
                # case này là đã tồn tại ServerItem rồi-> lên việc add_controller được xử lý bên ServerItem
                # controller_manager.add_controller(controller=controller)
                # camera_qsettings.set_server(json_server=controller.server.data.to_dict())
            else:
                is_id_selected = False
                id_selected = 0
                while not is_id_selected:
                    if id_selected not in server_info_model_manager.server_list:
                        is_id_selected = True
                    else:
                        id_selected += 1

                controller.server.data.id = id_selected
                controller_manager.add_controller(controller=controller)
                camera_qsettings.set_server(json_server=controller.server.data.to_dict())
                controller.server.set_status(True)
            self.close()
        else:
            main_controller.signal_load_animation.emit(False)
            Notifications(parent=main_controller.list_parent['MainScreen'], title=self.tr("Failed to connect to Server"),icon=Style.PrimaryImage.fail_result)
            if deny_permission:
                self.error_label.setText(
                    self.tr("This account is not allowed to log in to the system."))
            else:
                self.error_label.setText(
                    self.tr("Username or password is incorrect!"))
            self.start_shake_animation(self.error_label)

    def set_dynamic_style(self):
        self.setStyleSheet(f'''
            /*-----QWidget-----*/
            QWidget{{
                background-color: {main_controller.get_theme_attribute("Color", "add_server_widget_background")};
            }}
            /*-----QLabel-----*/
            QLabel{{
                background-color: transparent;
                /*color: {main_controller.get_theme_attribute("Color", "test")};*/
                font-size: 14px;
                font-weight: 200;}}
            /*-----QPushButton-----*/
            QPushButton{{
                background-color: {Style.PrimaryColor.primary};
                color: {Style.PrimaryColor.white};
                font-size: 14px;
                font-weight: bold;
                border: none;
                border-radius: 4px;
                padding: 5px;}}
            QPushButton::disabled{{
                background-color: #5c5c5c;}}
            QPushButton::pressed{{
                background-color: {Style.PrimaryColor.button_color};}}
            /*-----QLineEdit-----*/
            QLineEdit{{
                background-color: transparent;
                border-radius: 4px;
                color: {Style.PrimaryColor.white};
                font-size: 14px;
                font-weight: 400;
                letter-spacing: 0em;
                text-align: left;
                border: 1px solid {Style.PrimaryColor.border_line_edit_not_focus};
                padding: 5px 15px;}}
            QLineEdit::focus{{
                border: 1px solid {main_controller.get_theme_attribute("Color", "add_server_lineedit_focused")};
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")} }}
            QLineEdit:!focus{{
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")};}}
            QLineEdit::placeholder{{
                color: {main_controller.get_theme_attribute("Color", "add_server_sub_title")};}}
        
        ''')
        self.connect_server_button.setStyleSheet(Style.StyleSheet.button_positive)
        self.btn_cancel.setStyleSheet(Style.StyleSheet.button_negative)
        self.title.setStyleSheet(f'''
                QLabel{{
                background-color: transparent;
                color: {main_controller.get_theme_attribute("Color", "add_server_main_title")};
                font-size: 24px;
                font-weight: 200;}}
            ''')

    def is_valid_ip(self, ip):
        """Check if the given string is a valid IP address using ipaddress module"""
        try:
            ip_address(ip)
            return True
        except ValueError:
            return False

    def on_server_ip_changed(self, text):
        """Handle server IP text changes to show/hide captcha and port fields"""
        # Handle captcha visibility
        if not self.change_password:
            if text and Config.CYGATE_API_URL in text:
                if hasattr(self, 'captcha_container'):
                    self.captcha_container.show()
                    self.get_captcha()
            else:
                if hasattr(self, 'captcha_container'):
                    self.captcha_container.hide()
                    self.captcha_id = None

        # Handle port fields visibility
        if self.is_valid_ip(text):
            self.server_port_widget.show()
            self.server_websocket_widget.show()
        else:
            self.server_port_widget.hide()
            self.server_websocket_widget.hide()

from src.common.widget.custom_titlebar.custom_component.login_title_bar import LoginTitleBar
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QGuiApplication,QShortcut,QKeySequence
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QSizePolicy, QDialog, QProgressBar, QPushButton
from src.presentation.server_screen.logo_widget import LogoWidget
from src.presentation.server_screen.list_server_widget import ListServerWidget
from src.presentation.server_screen.add_server_widget import AddServerWidget
from src.common.controller.main_controller import connect_slot,main_controller
import os
import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)

basedir = os.path.dirname(__file__)

class ServerScreen(QWidget):
    def __init__(self, parent=None, window_parent=None):
        super().__init__()
        self.type_grid = 1
        self.window_parent = window_parent
        # self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.calculate_layout()
        layout = QVBoxLayout()
        layout.setContentsMargins(0,0,0,0)
        self.setLayout(layout)

        self.logo = LogoWidget(self)
        self.logo.setGeometry(0,0, self.screen_available_width,self.screen_available_height)

        self.servers = ListServerWidget()
        self.servers.change_logo_position.connect(self.change_logo_position)
        self.servers.load_qsetting_history()
        layout.addWidget(QWidget())
        layout.addWidget(self.servers)
        layout.setStretch(0,10)
        layout.setStretch(1,90)
        # Add server
        self.btn_add_server = AddServerWidget(self)
        self.btn_add_server.setGeometry(0,25,self.screen_available_width,80)

        self.title_bar = LoginTitleBar(parent=self, window_parent=self.window_parent)
        self.title_widget = QWidget(self)
        self.title_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        btn_test = QPushButton('Test')
        btn_test.clicked.connect(self.retranslate_server_screen)

        self.title_layout = QHBoxLayout(self.title_widget)
        self.title_layout.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.title_layout.setContentsMargins(0, 0, 2, 0)
        # self.title_layout.addWidget(btn_test)
        self.title_layout.addWidget(self.title_bar)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.shortcut = QShortcut(QKeySequence(Qt.CTRL | Qt.Key_F), self)
        self.connect_slot()
        self.set_dynamic_stylesheet()

    def connect_slot(self):
        connect_slot(
            (self.shortcut.activated,self.shortcut_activated))
        
    def shortcut_activated(self):
        # logger.debug(f'shortcut_activated')
        self.btn_add_server.btn_search_server_clicked()

    def change_logo_position(self,data):
        self.type_grid=data
        if data == 1:
            self.logo.setGeometry(0,0, self.screen_available_width ,self.screen_available_height - self.screen_available_height/4)
        elif data == 2:
            self.logo.setGeometry(0,0, self.screen_available_width ,self.screen_available_height - self.screen_available_height/2)

    def calculate_layout(self, desktop_screen_size=None):
        if desktop_screen_size is None:
            screen = QGuiApplication.primaryScreen()
            desktop_screen_size = screen.availableGeometry()
        screen_width = desktop_screen_size.width()
        screen_height = desktop_screen_size.height()
        percent_menu_bar = 0.03
        self.screen_available_width = screen_width - percent_menu_bar*screen_width
        self.screen_available_height = screen_height - percent_menu_bar*screen_height

    def retranslate_server_screen(self):
        self.servers.retranslate_ui_server()

    def resizeEvent(self, event):
        frame_size = self.geometry()
        self.title_widget.setGeometry(0, 0, self.width(), 32)
        self.screen_available_width = self.width()
        self.screen_available_height = self.height()
        
        self.btn_add_server.setGeometry(0,25,self.screen_available_width,80)
        self.change_logo_position(self.type_grid)
        
    def window_state_changed(self, state):
        if state == Qt.WindowState.WindowFullScreen:
            self.title_bar.stacked_button.setCurrentIndex(1)
        else:
            self.title_bar.stacked_button.setCurrentIndex(0)

    def restyle_ui_server_screen(self):
        self.set_dynamic_stylesheet()
        self.title_bar.set_dynamic_stylesheet()

    def set_dynamic_stylesheet(self):
        self.logo.set_dynamic_stylesheet()
        self.servers.set_dynamic_stylesheet()
        self.btn_add_server.set_dynamic_stylesheet()
        # self.title_bar.set_dynamic_stylesheet()
# from PySide6.QtWidgets import (Q<PERSON><PERSON><PERSON>, QVBoxLayout, QHBoxLayout, QPushButton, 
#                              QLabel, QProgressBar, QMessageBox)
# from PySide6.QtCore import Qt, QThread, Signal, QCoreApplication
# import requests
# import os
# import json
# import subprocess
# import logging
# import xml.etree.ElementTree as ET
# from src.common.controller.main_controller import main_controller
# import sys

# class UpdateCheckerThread(QThread):
#     update_available = Signal(str, str)  # version, download_url
#     no_update = Signal()
#     error = Signal(str)

#     def __init__(self):
#         super().__init__()
#         self.minio_url = "https://minio.gpstech.vn/videos/"
#         self.logger = logging.getLogger(__name__)

#     def get_version_file_path(self):
#         """Get the correct path to version.json whether running from source or packaged"""
#         if getattr(sys, 'frozen', False):
#             # Running in a bundle
#             base_path = os.path.join(sys._MEIPASS, 'version')
#         else:
#             # Running in normal Python environment
#             base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))
        
#         version_path = os.path.join(base_path, 'version.json')
#         self.logger.debug(f"Version file path: {version_path}")  # Add debug logging
#         return version_path

#     def run(self):
#         try:
#             self.logger.debug("Starting update check...")
            
#             # Get current version from version.json
#             try:
#                 version_path = self.get_version_file_path()
#                 with open(version_path, 'r') as f:
#                     current_version = json.load(f)['version_string']
#                 self.logger.debug(f"Current version: {current_version}")
#             except Exception as e:
#                 self.logger.error(f"Error reading version.json: {str(e)}")
#                 raise

#             # List files in MinIO videos directory
#             try:
#                 self.logger.debug(f"Checking for updates at {self.minio_url}")
#                 # Add parameters to filter for .dmg files
#                 params = {
#                     'list-type': '2',
#                     'delimiter': '/',
#                     'prefix': 'iVMS-',  # Only list files starting with iVMS-
#                 }
#                 response = requests.get(self.minio_url, params=params)
#                 response.raise_for_status()

#                 # Parse XML response
#                 root = ET.fromstring(response.content)
                
#                 # Extract versions from file names
#                 versions = []
#                 # Define the XML namespace
#                 ns = {'s3': 'http://s3.amazonaws.com/doc/2006-03-01/'}
                
#                 # Find all Contents elements that are .dmg files
#                 for content in root.findall('.//s3:Contents', ns):
#                     key = content.find('s3:Key', ns)
#                     if key is not None:
#                         filename = key.text
#                         if filename.endswith('.dmg'):
#                             self.logger.debug(f"Found DMG file: {filename}")
#                             try:
#                                 # Extract version from filename (e.g., 'iVMS-1.2.3.dmg' -> '1.2.3')
#                                 version = filename[5:-4]  # Remove 'iVMS-' and '.dmg'
#                                 # Validate version format (should be numbers separated by dots)
#                                 if all(part.isdigit() for part in version.split('.')):
#                                     self.logger.debug(f"Found valid version: {version}")
#                                     versions.append(version)
#                                 else:
#                                     self.logger.warning(f"Invalid version format in filename: {filename}")
#                             except Exception as e:
#                                 self.logger.warning(f"Could not parse version from filename: {filename}, error: {str(e)}")

#                 if versions:
#                     # Sort versions and get the latest one
#                     latest_version = max(versions, key=lambda v: [int(x) for x in v.split('.')])
#                     self.logger.debug(f"Latest version found: {latest_version}")
                    
#                     if self._version_is_newer(latest_version, current_version):
#                         download_url = f"{self.minio_url}iVMS-{latest_version}.dmg"
#                         self.logger.debug(f"Update available. New version: {latest_version}")
#                         self.update_available.emit(latest_version, download_url)
#                     else:
#                         self.logger.debug("No updates available")
#                         self.no_update.emit()
#                 else:
#                     self.logger.debug("No valid DMG files found")
#                     self.no_update.emit()
                    
#             except requests.exceptions.RequestException as e:
#                 self.logger.error(f"Network error while checking for updates: {str(e)}")
#                 raise
                
#         except Exception as e:
#             self.logger.error(f"Error in update checker: {str(e)}", exc_info=True)
#             self.error.emit(str(e))

#     def _version_is_newer(self, latest, current):
#         try:
#             latest_parts = [int(x) for x in latest.split('.')]
#             current_parts = [int(x) for x in current.split('.')]
#             is_newer = latest_parts > current_parts
#             self.logger.debug(f"Version comparison: {latest} vs {current} -> is newer: {is_newer}")
#             return is_newer
#         except Exception as e:
#             self.logger.error(f"Error comparing versions: {str(e)}")
#             return False

# class UpdateDownloaderThread(QThread):
#     progress = Signal(int)
#     finished = Signal(str)
#     error = Signal(str)

#     def __init__(self, url, version):
#         super().__init__()
#         self.url = url
#         self.version = version
#         self.download_path = f"/tmp/iVMS-{version}.dmg"

#     def run(self):
#         try:
#             response = requests.get(self.url, stream=True)
#             total_size = int(response.headers.get('content-length', 0))
#             block_size = 1024
#             downloaded = 0

#             with open(self.download_path, 'wb') as f:
#                 for data in response.iter_content(block_size):
#                     downloaded += len(data)
#                     f.write(data)
#                     progress = int((downloaded / total_size) * 100)
#                     self.progress.emit(progress)

#             self.finished.emit(self.download_path)
#         except Exception as e:
#             self.error.emit(str(e))

# class AutoUpdateTab(QWidget):
#     def __init__(self):
#         super().__init__()
#         self.logger = logging.getLogger(__name__)
#         self.setup_ui()
#         self.update_checker = None
#         self.update_downloader = None

#     def get_version_file_path(self):
#         """Get the correct path to version.json whether running from source or packaged"""
#         if getattr(sys, 'frozen', False):
#             # Running in a bundle
#             base_path = os.path.join(sys._MEIPASS, 'version')
#         else:
#             # Running in normal Python environment
#             base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../../'))
        
#         version_path = os.path.join(base_path, 'version.json')
#         self.logger.debug(f"Version file path: {version_path}")  # Add debug logging
#         return version_path

#     def get_current_version(self):
#         """Safely get the current version"""
#         try:
#             version_path = self.get_version_file_path()
#             with open(version_path, 'r') as f:
#                 current_version = json.load(f)['version_string']
#             return current_version
#         except (FileNotFoundError, json.JSONDecodeError, KeyError) as e:
#             self.logger.error(f"Error reading version.json: {str(e)}")
#             return "Unknown"

#     def setup_ui(self):
#         self.layout = QVBoxLayout(self)
#         self.layout.setAlignment(Qt.AlignTop)
#         self.layout.setContentsMargins(20, 20, 20, 20)
#         self.layout.setSpacing(20)

#         # Current Version
#         version_layout = QHBoxLayout()
#         self.version_label = QLabel(self.tr("Current Version:"))
        
#         current_version = self.get_current_version()
#         self.current_version = QLabel(current_version)
#         version_layout.addWidget(self.version_label)
#         version_layout.addWidget(self.current_version)
#         version_layout.addStretch()
#         self.layout.addLayout(version_layout)

#         # Check for Updates Button
#         self.check_button = QPushButton(self.tr("Check for Updates"))
#         self.check_button.clicked.connect(self.check_for_updates)
#         if current_version == "Unknown":
#             self.check_button.setEnabled(False)
#             self.check_button.setToolTip(self.tr("Version information not available"))
#         self.layout.addWidget(self.check_button)

#         # Progress Bar (hidden by default)
#         self.progress_bar = QProgressBar()
#         self.progress_bar.hide()
#         self.layout.addWidget(self.progress_bar)

#         # Status Label
#         self.status_label = QLabel()
#         self.status_label.setWordWrap(True)
#         if current_version == "Unknown":
#             self.status_label.setText(self.tr("Version information not available. Auto-update is disabled."))
#         self.layout.addWidget(self.status_label)

#         self.layout.addStretch()
#         self.set_dynamic_stylesheet()

#     def check_for_updates(self):
#         current_version = self.get_current_version()
#         if current_version == "Unknown":
#             self.status_label.setText(self.tr("Cannot check for updates: Version information not available"))
#             self.check_button.setEnabled(False)
#             return

#         self.check_button.setEnabled(False)
#         self.status_label.setText(self.tr("Checking for updates..."))
#         self.update_checker = UpdateCheckerThread()
#         self.update_checker.update_available.connect(self.on_update_available)
#         self.update_checker.no_update.connect(self.on_no_update)
#         self.update_checker.error.connect(self.on_check_error)
#         self.update_checker.start()

#     def on_update_available(self, version, url):
#         self.check_button.setEnabled(True)
#         response = QMessageBox.question(
#             self,
#             self.tr("Update Available"),
#             self.tr(f"Version {version} is available. Would you like to download and install it?"),
#             QMessageBox.Yes | QMessageBox.No
#         )
#         if response == QMessageBox.Yes:
#             self.download_update(url, version)

#     def download_update(self, url, version):
#         self.progress_bar.show()
#         self.progress_bar.setValue(0)
#         self.status_label.setText(self.tr("Downloading update..."))
#         self.check_button.setEnabled(False)

#         self.update_downloader = UpdateDownloaderThread(url, version)
#         self.update_downloader.progress.connect(self.progress_bar.setValue)
#         self.update_downloader.finished.connect(self.install_update)
#         self.update_downloader.error.connect(self.on_download_error)
#         self.update_downloader.start()

#     def install_update(self, dmg_path):
#         try:
#             response = QMessageBox.information(
#                 self,
#                 self.tr("Update Ready"),
#                 self.tr("The update has been downloaded. The application will close to install the update. Continue?"),
#                 QMessageBox.Yes | QMessageBox.No
#             )
            
#             if response == QMessageBox.Yes:
#                 # Create a shell script to perform the installation after app closes
#                 install_script = f"""#!/bin/bash
#                 # Wait for the app to close
#                 sleep 2
                
#                 # Mount the DMG
#                 hdiutil attach "{dmg_path}"
                
#                 # Find the mounted volume
#                 for volume in /Volumes/*; do
#                     if [[ $volume == *"iVMS"* ]]; then
#                         # Copy to Applications
#                         cp -R "$volume/iVMS.app" /Applications/
                        
#                         # Unmount
#                         hdiutil detach "$volume"
#                         break
#                     fi
#                 done
                
#                 # Clean up
#                 rm "{dmg_path}"
                
#                 # Launch the updated app
#                 open /Applications/iVMS.app
                
#                 # Clean up this script
#                 rm "$0"
#                 """
                
#                 # Write the install script
#                 script_path = "/tmp/ivms_update_install.sh"
#                 with open(script_path, "w") as f:
#                     f.write(install_script)
                
#                 # Make it executable
#                 os.chmod(script_path, 0o755)
                
#                 # Launch the script in background
#                 subprocess.Popen([script_path])
                
#                 # Quit the application
#                 QCoreApplication.quit()
                
#         except Exception as e:
#             QMessageBox.critical(
#                 self,
#                 self.tr("Installation Error"),
#                 str(e),
#                 QMessageBox.Ok
#             )

#     def on_no_update(self):
#         self.check_button.setEnabled(True)
#         self.status_label.setText(self.tr("You have the latest version."))

#     def on_check_error(self, error):
#         self.check_button.setEnabled(True)
#         self.status_label.setText(self.tr(f"Error checking for updates: {error}"))

#     def on_download_error(self, error):
#         self.check_button.setEnabled(True)
#         self.progress_bar.hide()
#         self.status_label.setText(self.tr(f"Error downloading update: {error}"))

#     def translate_ui(self):
#         self.version_label.setText(self.tr("Current Version:"))
#         self.check_button.setText(self.tr("Check for Updates"))

#     def set_dynamic_stylesheet(self):
#         self.setStyleSheet(f'''
#             QLabel {{
#                 color: {main_controller.get_theme_attribute("Color", "text")};
#             }}
#             QPushButton {{
#                 background-color: {main_controller.get_theme_attribute("Color", "button_background_normal")};
#                 color: {main_controller.get_theme_attribute("Color", "text_on_primary")};
#                 border: 1px solid {main_controller.get_theme_attribute("Color", "border_color")};
#                 padding: 5px 15px;
#                 border-radius: 4px;
#             }}
#             QPushButton:hover {{
#                 background-color: {main_controller.get_theme_attribute("Color", "video_control_button_hover")};
#             }}
#             QPushButton:disabled {{
#                 background-color: {main_controller.get_theme_attribute("Color", "button_background_normal")};
#                 color: {main_controller.get_theme_attribute("Color", "text_disable")};
#             }}
#             QProgressBar {{
#                 border: 1px solid {main_controller.get_theme_attribute("Color", "border_color")};
#                 border-radius: 4px;
#                 text-align: center;
#             }}
#             QProgressBar::chunk {{
#                 background-color: {main_controller.get_theme_attribute("Color", "primary")};
#             }}
#         ''') 
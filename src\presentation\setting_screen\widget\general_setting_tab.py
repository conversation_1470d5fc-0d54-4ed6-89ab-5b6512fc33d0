from PySide6.QtCore import Qt, QCoreApplication, Signal, Slot
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, QPushButton, QRadioButton, QGroupBox
import logging
from src.common.controller.main_controller import main_controller
from src.styles.style import Style, Theme
from src.utils.setting_screen_qsetting import SettingScreenQSettings
from src.utils import theme_setting
from src.utils.theme_setting import theme_setting
from src.utils.dialog_settings import dialog_settings
from src.common.widget.notifications.notify import Notifications

logger = logging.getLogger(__name__)

class GeneralSettingTab(QWidget):
    language_change_signal = Signal(object)
    # button_logout_signal = Signal(str)
    # button_change_server_signal = Signal(str)
    #######
    theme_change_signal = Signal(Theme)
    #######

    def __init__(self, parent=None):
        super().__init__(parent)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        # create layout
        self.widget_main = QWidget()
        self.setObjectName('widget_main')
        self.layout_child = QVBoxLayout()
        self.layout_child.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.set_up_ui_language()
        self.setup_dialog_settings()

        self.language_label = QLabel(self.tr("1. Language:"))
        self.language_label.setStyleSheet(f"""
            QLabel {{
                font-style: bold;
            }}
        """)

        self.personal_label = QLabel(self.tr("2. Personalize:"))
        self.personal_label.setStyleSheet(f"""
                    QLabel {{
                        font-style: bold;
                    }}
                """)

        self.dialog_label = QLabel(self.tr("3. Dialog Settings:"))
        self.dialog_label.setStyleSheet(f"""
                    QLabel {{
                        font-style: bold;
                    }}
                """)

        self.widget_button_personal_setting = QWidget()
        self.layout_button_personal_setting = QHBoxLayout(self.widget_button_personal_setting)
        self.layout_button_personal_setting.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.layout_button_personal_setting.setContentsMargins(0, 0, 0, 0)
        self.layout_button_personal_setting.setSpacing(10)

        ############################
        self.radio_button_auto_theme = QRadioButton(self.tr('Auto'))
        self.radio_button_auto_theme.setChecked(theme_setting.get_theme_color_from_qsetting() == "AUTO")
        self.radio_button_auto_theme.toggled.connect(self.auto_theme_clicked)

        self.radio_button_light_theme = QRadioButton(self.tr('Light Theme'))
        self.radio_button_light_theme.setChecked(theme_setting.get_theme_color_from_qsetting() == "LIGHT")
        self.radio_button_light_theme.toggled.connect(self.light_theme_clicked)

        self.radio_button_dark_theme = QRadioButton(self.tr('Dark Theme'))
        self.radio_button_dark_theme.setChecked(theme_setting.get_theme_color_from_qsetting() == "DARK")
        self.radio_button_dark_theme.toggled.connect(self.dark_theme_clicked)
        ############################

        # self.button_logout = QPushButton(self.tr('Logout'))
        # self.button_logout.clicked.connect(self.logout_clicked)
        # self.button_change_server = QPushButton(self.tr('Change Server'))
        # self.button_change_server.clicked.connect(self.change_server_clicked)
        # self.layout_button_personal_setting.addWidget(self.button_logout)
        # self.layout_button_personal_setting.addWidget(self.button_change_server)

        self.layout_child.addWidget(self.language_label)
        self.layout_child.addWidget(self.widget_language)
        self.layout_child.addWidget(self.personal_label)

        self.layout_child.addWidget(self.radio_button_light_theme)
        self.layout_child.addWidget(self.radio_button_dark_theme)
        self.layout_child.addWidget(self.radio_button_auto_theme)
        self.layout_child.addWidget(self.widget_button_personal_setting)

        self.layout_child.addWidget(self.dialog_label)
        self.layout_child.addWidget(self.widget_dialog_settings)

        self.widget_main.setLayout(self.layout_child)

        self.layout_main = QVBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.addWidget(self.widget_main)
        self.setLayout(self.layout_main)

    def set_up_ui_language(self):
        self.widget_language = QWidget()
        self.widget_language.setObjectName('widget_language')
        self.widget_language.setStyleSheet(f"""

        """)
        self.layout_language = QVBoxLayout()
        self.layout_language.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.language_list_checkbox = []
        self.layout_english = QHBoxLayout()
        self.layout_english.setContentsMargins(0, 0, 0, 0)
        self.english_checkbox = CustomQCheckBox(
            title=self.tr("English"), type_checkbox="ENGLISH")
        self.english_checkbox.clicked.connect(
            self.checkbox_language_change)
        self.language_list_checkbox.append(self.english_checkbox)
        self.layout_english.addWidget(self.english_checkbox)

        self.layout_vietnamese = QHBoxLayout()
        self.layout_vietnamese.setContentsMargins(0, 0, 0, 0)
        self.vietnamese_checkbox = CustomQCheckBox(
            title=self.tr("Vietnamese"), type_checkbox="VIETNAMESE")
        self.vietnamese_checkbox.clicked.connect(
            self.checkbox_language_change)
        self.language_list_checkbox.append(self.vietnamese_checkbox)
        self.layout_vietnamese.addWidget(self.vietnamese_checkbox)

        self.layout_russian = QHBoxLayout()
        self.layout_russian.setContentsMargins(0, 0, 0, 0)
        self.russian_checkbox = CustomQCheckBox(
            title=self.tr("Russian"), type_checkbox="RUSSIAN")
        self.russian_checkbox.clicked.connect(
            self.checkbox_language_change)
        self.language_list_checkbox.append(self.russian_checkbox)
        self.layout_russian.addWidget(self.russian_checkbox)

        if SettingScreenQSettings.get_instance().get_current_language() == Style.Language.en:
            self.english_checkbox.setChecked(True)
        elif SettingScreenQSettings.get_instance().get_current_language() == Style.Language.ru:
            self.russian_checkbox.setChecked(True)
        else:
            self.vietnamese_checkbox.setChecked(True)

        self.layout_language.addLayout(self.layout_english)
        self.layout_language.addLayout(self.layout_vietnamese)
        self.layout_language.addLayout(self.layout_russian)
        self.widget_language.setLayout(self.layout_language)

    def checkbox_language_change(self, state):
        sender = self.sender()  # Get the checkbox that emitted the signal
        checked_position = self.language_list_checkbox.index(sender)
        current_language = SettingScreenQSettings.get_instance().get_current_language()
        new_language = SettingScreenQSettings.get_instance().get_current_language()

        if checked_position == 0:
            # User tried to select English, but it's already selected, so prevent unchecking
            sender.setChecked(True)
            self.vietnamese_checkbox.setChecked(False)
            self.russian_checkbox.setChecked(False)
            new_language = Style.Language.en
        elif checked_position == 1:
            # User tried to select Vietnamese, but it's already selected, so prevent unchecking
            sender.setChecked(True)
            self.english_checkbox.setChecked(False)
            self.russian_checkbox.setChecked(False)
            new_language = Style.Language.vie
        elif checked_position == 2:
            # User tried to select Vietnamese, but it's already selected, so prevent unchecking
            sender.setChecked(True)
            self.english_checkbox.setChecked(False)
            self.vietnamese_checkbox.setChecked(False)
            new_language = Style.Language.ru

        self.language_change_signal.emit(new_language)
        if new_language != current_language:
            SettingScreenQSettings.get_instance().set_current_language(new_language)

    # def logout_clicked(self):
    #     self.button_logout_signal.emit("Logout")

    # def change_server_clicked(self):
    #     self.button_change_server_signal.emit("Change Server")


    ###########################
    @Slot(bool)
    def auto_theme_clicked(self, state):
        if state:
            theme_setting.set_theme_color_to_qsetting("AUTO")
            device_theme = theme_setting.device_theme()
            main_controller.change_theme(device_theme)

    @Slot(bool)
    def light_theme_clicked(self, state):
        if state:
            theme_setting.set_theme_color_to_qsetting("LIGHT")
            main_controller.change_theme(Theme.LIGHT)

    @Slot(bool)
    def dark_theme_clicked(self, state):
        if state:
            theme_setting.set_theme_color_to_qsetting("DARK")
            main_controller.change_theme(Theme.DARK)
    ###########################

    def setup_dialog_settings(self):
        """Tạo phần UI cho cài đặt dialog"""
        self.widget_dialog_settings = QWidget()
        self.layout_dialog_settings = QVBoxLayout(self.widget_dialog_settings)
        self.layout_dialog_settings.setContentsMargins(0, 0, 0, 0)
        self.layout_dialog_settings.setSpacing(10)

        # Tạo nút đặt lại các cài đặt dialog
        self.reset_dialog_settings_button = QPushButton(self.tr("Reset all dialog settings"))
        self.reset_dialog_settings_button.setStyleSheet(Style.StyleSheet.button_positive)
        self.reset_dialog_settings_button.setFixedWidth(200)  # Đặt chiều rộng cố định nhỏ hơn
        self.reset_dialog_settings_button.setFixedHeight(30)  # Đặt chiều cao cố định nhỏ hơn
        self.reset_dialog_settings_button.clicked.connect(self.reset_dialog_settings_clicked)

        # Thêm mô tả
        self.dialog_settings_description = QLabel(self.tr("Reset all 'Don't show again' settings for confirmation dialogs."))
        self.dialog_settings_description.setWordWrap(True)
        self.dialog_settings_description.setStyleSheet(f"""
            color: {main_controller.get_theme_attribute("Color", "label_title_1")};
            background-color: transparent;
        """)

        # Tạo layout ngang để căn chỉnh nút
        self.button_layout = QHBoxLayout()
        self.button_layout.setContentsMargins(0, 0, 0, 0)
        self.button_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.button_layout.addWidget(self.reset_dialog_settings_button)

        self.layout_dialog_settings.addWidget(self.dialog_settings_description)
        self.layout_dialog_settings.addLayout(self.button_layout)

    @Slot()
    def reset_dialog_settings_clicked(self):
        """Xử lý sự kiện khi nhấn nút đặt lại cài đặt dialog"""
        dialog_settings.reset_all_dialog_settings()
        Notifications.show_local_message(message=self.tr("All dialog settings have been reset."))

    def translate_ui_general(self):
        self.english_checkbox.setText(
            QCoreApplication.translate("GeneralSettingTab", u"English", None))
        self.vietnamese_checkbox.setText(
            QCoreApplication.translate("GeneralSettingTab", u"Vietnamese", None))
        self.russian_checkbox.setText(
            QCoreApplication.translate("GeneralSettingTab", u"Russian", None))
        self.language_label.setText(QCoreApplication.translate("GeneralSettingTab", u"1. Language:", None))
        self.personal_label.setText(QCoreApplication.translate("GeneralSettingTab", u"2. Personalize:", None))
        self.dialog_label.setText(QCoreApplication.translate("GeneralSettingTab", u"3. Dialog Settings:", None))

        self.radio_button_auto_theme.setText(QCoreApplication.translate("GeneralSettingTab", u"Auto", None))
        self.radio_button_light_theme.setText(QCoreApplication.translate("GeneralSettingTab", u"Light Theme", None))
        self.radio_button_dark_theme.setText(QCoreApplication.translate("GeneralSettingTab", u"Dark Theme", None))

        self.reset_dialog_settings_button.setText(QCoreApplication.translate("GeneralSettingTab", u"Reset all dialog settings", None))
        self.dialog_settings_description.setText(QCoreApplication.translate("GeneralSettingTab", u"Reset all 'Don't show again' settings for confirmation dialogs.", None))

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f"""
                    QWidget {{
                        color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                        background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                        border-bottom-left-radius: 5px;
                        border-top-right-radius: 5px;
                        border-bottom-right-radius: 5px;

                    }}
                    QWidget {{
                        color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                    }}
                    """)

        self.english_checkbox.set_dynamic_stylesheet()
        self.vietnamese_checkbox.set_dynamic_stylesheet()
        self.russian_checkbox.set_dynamic_stylesheet()


class CustomQCheckBox(QCheckBox):
    def __init__(self, title=None, type_checkbox='NO_CHECK'):
        super().__init__()
        self.title = title
        self.type_checkbox = type_checkbox
        self.init_ui()
        self.set_dynamic_stylesheet()

    def init_ui(self):
        self.setText(self.title)
        self.setChecked(False)  # Set the initial state of the QCheckBox
        # Connect the stateChanged signal to a custom slot (function)
        # self.stateChanged.connect(self.checkBox_state_changed)


    def checkBox_state_changed(self, state):
        if state == 2:  # Qt.Checked
            logger.debug("Feature enabled")
        else:
            logger.debug("Feature disabled")

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f"""
            QCheckBox {{
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                spacing: 10px;
            }}
            QCheckBox:disabled {{
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                color: gray;
                spacing: 10px;
            }}
            QCheckBox::indicator:checked {{
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 20px;
                height: 20px;
            }}
            QCheckBox::indicator:unchecked {{
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 20px;
                height: 20px;
            }}
            """)

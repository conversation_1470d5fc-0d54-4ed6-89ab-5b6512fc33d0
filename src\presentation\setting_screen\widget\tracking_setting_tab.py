from PySide6.QtCore import Qt, QCoreApplication, Signal, Slot
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QCheckBox, QLabel, QPushButton, QRadioButton
import logging
from src.common.controller.main_controller import main_controller
from src.styles.style import Style
logger = logging.getLogger(__name__)
import logging

from PySide6.QtWidgets import QScrollArea
from PySide6.QtWidgets import QWidget, QGridLayout, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox

from src.styles.style import Style
from src.common.controller.main_controller import main_controller
from src.common.widget.elided_label import ElidedLabel
from src.common.model.screen_model import ScreenModel, screenModelManager
from src.utils.camera_qsettings import camera_qsettings
logger = logging.getLogger(__name__)

class RowItem(QWidget):
    checkboxStateChanged = Signal(tuple)
    def __init__(self, parent=None,idx = None,model = None):
        super().__init__(parent)
        self.idx = idx
        self.model = model
        self.list_groups = {}
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0,0,0,0)
        self.main_widget = QWidget()
        self.main_widget.setObjectName('RowItem')
        camera_layout = QGridLayout(self.main_widget)
        self.checkbox = self.create_checkbox()
        self.label_idx = QLabel('-')
        self.label_screen_name= ElidedLabel('-')
        # self.label_screen_name.setMaximumWidth(150)
        camera_layout.addWidget(self.label_idx,0,0)
        camera_layout.addWidget(self.label_screen_name,0,1)
        camera_layout.addWidget(self.checkbox,0,2,Qt.AlignmentFlag.AlignLeft)
        camera_layout.setColumnStretch(0, 1)
        camera_layout.setColumnStretch(1, 3)
        camera_layout.setColumnStretch(2, 1)
        self.main_layout.addWidget(self.main_widget)
        self.update_data()
        self.setStyleSheet(f"""           
            QWidget#RowItem {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                border-radius: 4px;
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                font-size: {Style.Size.caption}px;
            }}


            """)
    def update_data(self):
        self.label_idx.setText(str(self.idx))
        self.label_screen_name.setText(self.model.get_property("name",""))

    def create_checkbox(self):
        checkbox = QCheckBox(self, objectName='checkbox')
        checkbox.setStyleSheet(
            f"""
            QCheckBox {{
                background-color: transparent;
                border: none;
            }}
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 16px;
                height: 16px;
            }}
            """)
        checkbox.stateChanged.connect(self.checkbox_clicked)
        return checkbox
    
    def btn_edit_clicked(self):
        pass

    def checkbox_clicked(self,state):
        self.checkboxStateChanged.emit((self,state))

    def mousePressEvent(self,event):
        if self.checkbox.checkState() == Qt.Checked:
            self.checkbox.setCheckState(Qt.CheckState.Unchecked)
        else:
            self.checkbox.setCheckState(Qt.CheckState.Checked)

    def enterEvent(self, event):
        self.setStyleSheet(f"""           
            QWidget#RowItem {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background_hoverred")};
                color: {main_controller.get_theme_attribute("Color", "table_item_header_text")};
                border-radius: 4px;
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background_hoverred")};
                color: {main_controller.get_theme_attribute("Color", "table_item_header_text")};
                font-size: {Style.Size.caption}px;
            }}
            """)
        
    def leaveEvent(self, event):
        self.setStyleSheet(f"""           
            QWidget#RowItem {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "table_item_header_text")};
                border-radius: 4px;
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "table_item_header_text")};
                font-size: {Style.Size.caption}px;
            }}
            """)
    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f"""           
            QWidget#RowItem {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                border-radius: 4px;
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                font-size: {Style.Size.caption}px;
            }}


            """)
        self.checkbox.setStyleSheet(
            f"""
            QCheckBox {{
                background-color: transparent;
                border: none;
            }}
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 16px;
                height: 16px;
            }}
            """)
class ScreenTable(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMaximumSize(400,500)
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0,0,0,0)
        main_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.header_widget = self.create_header_widget()
        self.scroll_area = self.create_scroll_area()
        self.table = None
        main_layout.addWidget(self.header_widget)
        main_layout.addWidget(self.scroll_area)
        self.update_table()
        self.setStyleSheet(f"""
            QWidget {{
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                border-bottom-left-radius: 5px;
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
                
            }}
            """)
    def create_scroll_area(self):
        widget = QScrollArea()
        widget.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        widget.setWidgetResizable(True)
        widget.setStyleSheet(
            f'''   
                QScrollArea {{
                    border: none;  /* Ẩn border của QScrollArea */
                }}
                QScrollBar:vertical {{
                    background-color: {Style.PrimaryColor.background};
                    width: 2px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {Style.PrimaryColor.on_background};
                    border-radius: 5px;
                    min-height: 20px;
                }}
                QScrollBar::add-line:vertical {{
                    background: none;
                }}
                QScrollBar::sub-line:vertical {{
                    background: none;
                }}
                QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                    background: none;
                }}
                QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
            ''')
        return widget
    
    def create_header_widget(self):
        widget = QWidget()
        widget.setObjectName('HeaderItem')
        layout = QGridLayout(widget)
        self.lb_stt = QLabel(self.tr('NO'))
        self.lb_screen_name = QLabel(self.tr('SCREEN NAME'))
        self.lb_select = QLabel(self.tr('SELECT'))
        layout.addWidget(self.lb_stt,0,0,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(self.lb_screen_name,0,1,Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(self.lb_select,0,2,Qt.AlignmentFlag.AlignLeft)

        layout.setColumnStretch(0, 1)
        layout.setColumnStretch(1, 3)
        layout.setColumnStretch(2, 1)
        widget.setStyleSheet(f"""           
            QWidget#HeaderItem {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                border-radius: 4px;
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                font-size: {Style.Size.caption}px;
            }}
            """)
        return widget
    
    def clear_scroll_area(self):
        # Lấy widget chứa tất cả các widget con bên trong scroll area
            # Kiểm tra xem widget có tồn tại không
        if self.table is not None:
            # Lấy layout của widget đó
            layout = self.table.layout()
            
            # Xóa tất cả các widget con trong layout
            if layout is not None:
                while layout.count():
                    item = layout.takeAt(0)
                    widget = item.widget()
                    if widget is not None:
                        widget.deleteLater()

    def update_table(self):
        widget = self.scroll_area.widget()
        if widget is not None:
            widget.deleteLater()
        self.table = QWidget()
        layout = QVBoxLayout(self.table)
        layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        layout.setContentsMargins(0,0,0,0)
        layout.setSpacing(0)
        for idx,screenModel in screenModelManager.data.items():
            widget = RowItem(idx =idx + 1,model = screenModel)
            widget.checkboxStateChanged.connect(self.checkboxStateChanged)
            if screenModel.get_property("state",False):
                widget.checkbox.setCheckState(Qt.CheckState.Checked)
            layout.addWidget(widget)
        self.scroll_area.setWidget(self.table)

    def checkboxStateChanged(self,data):
        rowItem,state = data
        if isinstance(rowItem,RowItem):
            if state == 2:
                for i in range(self.table.layout().count()):
                    widget = self.table.layout().itemAt(i).widget()
                    if widget is not None and widget != rowItem:
                        widget.checkbox.setCheckState(Qt.CheckState.Unchecked)

    def getConfig(self):
        data = {}
        for i in range(self.table.layout().count()):
            widget:RowItem = self.table.layout().itemAt(i).widget()
            if widget is not None:
                if widget.checkbox.checkState() == Qt.CheckState.Unchecked:
                    data[i] = ScreenModel(data = {"index": i, "name":widget.label_screen_name.text(), "state": False})
                else:
                    data[i] = ScreenModel(data = {"index": i, "name":widget.label_screen_name.text(), "state": True})
        return data
    
    def create_checkbox(self):
        checkbox = QCheckBox(self, objectName='checkbox')
        checkbox.setStyleSheet(
            f"""
            QCheckBox {{
                background-color: transparent;
                border: none;
            }}
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                width: 16px;
                height: 16px;
            }}
            """)
        checkbox.stateChanged.connect(self.checkbox_clicked)
        return checkbox
    
    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f"""
            QWidget {{
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                border-bottom-left-radius: 5px;
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
                
            }}
            """)
        self.header_widget.setStyleSheet(f"""           
            QWidget#HeaderItem {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                border-radius: 4px;
            }}
            QLabel {{
                background-color: {main_controller.get_theme_attribute("Color", "table_edit_item_background")};
                color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                font-size: {Style.Size.caption}px;
            }}
            """)
        for i in range(self.table.layout().count()):
            widget = self.table.layout().itemAt(i).widget()
            if widget is not None:
                widget.set_dynamic_stylesheet()

    def translate_ui_general(self):
        self.lb_stt.setText(QCoreApplication.translate("ScreenTable", u"NO", None))
        self.lb_screen_name.setText(QCoreApplication.translate("ScreenTable", u"SCREEN NAME", None))
        self.lb_select.setText(QCoreApplication.translate("ScreenTable", u"SELECT", None))

class TrackingSettingTab(QWidget):


    def __init__(self, parent=None):
        super().__init__(parent)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        # create layout
        self.widget_main = QWidget()
        self.setObjectName('widget_main')
        self.layout_child = QVBoxLayout()
        self.layout_child.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.setup_ui_tracking()

        self.tracking_cfg_label = QLabel(self.tr("Tracking configuration"))
        self.tracking_cfg_label.setStyleSheet(f"""
            QLabel {{
                font-style: bold;
            }}
        """)
        
        self.layout_child.addWidget(self.tracking_cfg_label)
        self.layout_child.addWidget(self.widget_tracking)

        self.widget_main.setLayout(self.layout_child)

        self.layout_main = QVBoxLayout()
        self.layout_main.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.addWidget(self.widget_main)
        self.setLayout(self.layout_main)

    def setup_ui_tracking(self):
        self.widget_tracking = QWidget()
        self.widget_tracking.setObjectName('widget_tracking')
        self.widget_tracking.setStyleSheet(f"""
            
        """)
        self.layout_tracking = QVBoxLayout()
        self.layout_tracking.setAlignment(Qt.AlignmentFlag.AlignTop)
        h_layout = QHBoxLayout()
        # h_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.show_tracking_screen_label = QLabel(self.tr("Show tracking screen"))


        self.radio_button_yes = QRadioButton(self.tr('Yes'))
        # self.radio_button_yes.setChecked(theme_setting.get_theme_color_from_qsetting() == "AUTO")
        self.radio_button_yes.toggled.connect(self.radio_button_yes_clicked)

        self.radio_button_no = QRadioButton(self.tr('No'))
        self.radio_button_no.toggled.connect(self.radio_button_no_clicked)
        if screenModelManager.isTracking:
            self.radio_button_yes.setChecked(True)
        else:
            self.radio_button_no.setChecked(True)

        self.screen_tracking_list_label = QLabel(self.tr("Screen tracking list"))
        # tracking_screen_list_label.setStyleSheet(f"""
        #     QLabel {{
        #         font-style: bold;
        #     }}
        # """)

        h_layout.addWidget(self.radio_button_yes)
        h_layout.addWidget(self.radio_button_no)

        self.group_table = ScreenTable()

        # self.button_close = QPushButton(self.btn_close_title)
        # self.button_close.setStyleSheet(Style.StyleSheet.button_negative)
        # self.button_close.clicked.connect(lambda: (self.close_cancel_signal.emit()))
        h_save_layout = QHBoxLayout()
        self.btn_save = QPushButton(self.tr("Save"))
        self.btn_save.setFixedWidth(100)
        self.btn_save.setStyleSheet(Style.StyleSheet.button_positive)
        self.btn_save.clicked.connect(self.save_clicked)
        h_save_layout.addWidget(QWidget())
        h_save_layout.addWidget(self.btn_save)
        h_save_layout.addWidget(QWidget())
        self.layout_tracking.addWidget(self.show_tracking_screen_label)
        self.layout_tracking.addLayout(h_layout)
        self.layout_tracking.addWidget(self.screen_tracking_list_label)
        self.layout_tracking.addWidget(self.group_table)
        self.layout_tracking.addLayout(h_save_layout)
        self.layout_tracking.addWidget(QWidget(),30)
        self.widget_tracking.setLayout(self.layout_tracking)
    ###########################
    def save_clicked(self):
        if self.radio_button_yes.isChecked():
            screenModelManager.isTracking = True
            screenModelManager.data = self.group_table.getConfig()
            output = {1:screenModelManager.to_dict()}
            camera_qsettings.set_screen(json_screens=output)
        else:
            screenModelManager.isTracking = False
            screenModelManager.data = self.group_table.getConfig()
            output = {0:screenModelManager.to_dict()}
            camera_qsettings.set_screen(json_screens=output)
    @Slot(bool)
    def radio_button_yes_clicked(self, state):
        pass

    @Slot(bool)
    def radio_button_no_clicked(self, state):
        pass

    ###########################

    def translate_ui_general(self):
        self.tracking_cfg_label.setText(QCoreApplication.translate("TrackingSettingTab", u"Tracking configuration", None))
        self.show_tracking_screen_label.setText(QCoreApplication.translate("TrackingSettingTab", u"Show tracking screen", None))
        self.screen_tracking_list_label.setText(QCoreApplication.translate("TrackingSettingTab", u"Screen tracking list", None))
        self.radio_button_yes.setText(QCoreApplication.translate("TrackingSettingTab", u"Yes", None))
        self.radio_button_no.setText(QCoreApplication.translate("TrackingSettingTab", u"No", None))
        self.btn_save.setText(QCoreApplication.translate("TrackingSettingTab", u"Save", None))
        self.group_table.translate_ui_general()

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f"""
                    QWidget {{
                        color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                        background-color: {main_controller.get_theme_attribute("Color", "widget_background_1")};
                        border-bottom-left-radius: 5px;
                        border-top-right-radius: 5px;
                        border-bottom-right-radius: 5px;
                        
                    }}
                    QWidget {{
                        color: {main_controller.get_theme_attribute("Color", "label_title_1")};
                    }}
                    """)
        self.group_table.set_dynamic_stylesheet()



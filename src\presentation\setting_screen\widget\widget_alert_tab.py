from src.utils.setting_screen_qsetting import SettingScreenQSettings
from src.utils.config import Config
from src.styles.style import Style
from src.common.controller.main_controller import main_controller
from src.common.model.warning_and_alert_model import WarningMethodAndAlertModel
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QHB<PERSON><PERSON>ayout, QCheckBox, QMenu, QWidgetAction, \
    QLineEdit, QComboBox, QItemDelegate
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtGui import QPixmap, QStandardItemModel, QStandardItem
from PySide6.QtCore import Qt, QCoreApplication
import PySide6
from typing import List
import src.utils.log_utils as LogUtils
import logging

from src.utils.theme_setting import theme_setting

logger = logging.getLogger(__name__)


class TypeWarningToServer:
    VOICE = "VOICE"
    ALARM_SOUND = "ALARM_SOUND"
    NOTIFICATION_MESSAGE = "NOTIFICATION_MESSAGE"
    HIGHLIGHT_CAMERA = "HIGHLIGHT_CAMERA"


class TypeAlertChannelToServer:
    VMS = "VMS"
    EMS = "EMS"
    TACTICAL_OPERATOR = "TACTICAL_OPERATOR"
    INTERNAL_SMS = "INTERNAL_SMS"
    INTERNAL_EMAIL = "INTERNAL_EMAIL"
    EXTERNAL_SMS = "EXTERNAL_SMS"
    EXTERNAL_EMAIL = "EXTERNAL_EMAIL"
    TELEGRAM = "TELEGRAM"


class WidgetAlertTab(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.list_checked_box_channel_to_save = []
        self.list_checked_box_warning_to_save = []
        self.list_warning_method_msg = []
        self.list_alert_channel_msg = []
        self.list_warning_setting = []
        self.list_alert_setting = []
        self.set_up_ui_main()

    def set_up_ui_main(self):
        self.widget_alert = QWidget()
        self.setObjectName('widget_main')
        # self.label_enable_alert = QLabel()
        # self.label_enable_alert.setText(self.tr("<b>Disable Alert</b>"))
        # self.label_enable_alert.setStyleSheet("font-size: 16px")
        # self.switch = CustomSwitch(self)
        # self.switch.clicked = self.switch_clicked
        # switch_value = SettingScreenQSettings.get_instance().get_state_switch_disable_alert()
        # self.switch.setChecked(switch_value)
        # self.layout_disable_alert = QHBoxLayout()
        # self.layout_disable_alert.setAlignment(Qt.AlignmentFlag.AlignLeft)
        # self.layout_disable_alert.setSpacing(20)
        # self.layout_disable_alert.addWidget(self.label_enable_alert)
        # self.layout_disable_alert.addWidget(self.switch)

        self.setup_widget_warning_method()
        self.setup_widget_alert_channel()
        if Config.SERVER_AVAILABLE:
            self.list_setting_checked_from_server(list_warning_from_server=self.mainController.list_warning_method,
                                                  list_channel_from_server=self.mainController.list_alert_channel)

        self.label_warning_method = QLabel(
            self.tr("<b>1. Warning Methods:</b>"))
        self.label_warning_method.setStyleSheet(f"")
        self.label_alert_channel = QLabel(
            self.tr("<b>2. Alert Channel Via:</b>"))
        self.label_alert_channel.setStyleSheet(f"")

        self.layout_alert = QVBoxLayout()
        self.layout_alert.setAlignment(Qt.AlignmentFlag.AlignTop)
        # self.layout_alert.addLayout(self.layout_disable_alert)

        self.layout_alert.addWidget(self.label_warning_method)
        self.layout_alert.addWidget(self.widget_warning_method)

        self.layout_alert.addWidget(self.label_alert_channel)
        self.layout_alert.addWidget(self.widget_alert_channel)
        self.widget_alert.setLayout(self.layout_alert)

        self.layout_main = QVBoxLayout()
        self.layout_main.setContentsMargins(0, 0, 0, 0)
        self.layout_main.addWidget(self.widget_alert)
        self.setLayout(self.layout_main)
        self.setStyleSheet(f"""
            QWidget {{
                color: white;
                background-color: {Style.PrimaryColor.on_background};
                border-bottom-left-radius: 5px;
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
                
            }}
            QWidget {{
                color: white;
            }}
            """)

    def list_setting_checked_from_server(self, list_warning_from_server: List[str], list_channel_from_server: List[str]):
        self.list_warning_setting.clear()
        self.list_alert_setting.clear()
        for item_warning in list_warning_from_server:
            if item_warning != '':
                self.list_warning_setting.append(item_warning)

        for item_alert in list_channel_from_server:
            if item_alert != '':
                self.list_alert_setting.append(item_alert)
        if len(self.list_alert_setting) == 0:
            self.load_alert_channel_QSetting()
        else:
            # Nếu load từ server về có dữ liệu, lưu dữ liệu đó vào QSetting
            # Lưu để phục vụ cho việc đổi server và vẫn muốn giữ nguyên cài đặt
            SettingScreenQSettings.get_instance().set_current_alert_channel(
                self.list_alert_setting)

        if len(self.list_warning_setting) == 0:
            self.load_warning_method_QSetting()
        else:
            # Nếu load từ server về có dữ liệu, lưu dữ liệu đó vào QSetting
            # Lưu để phục vụ cho việc đổi server và vẫn muốn giữ nguyên cài đặt
            SettingScreenQSettings.get_instance().set_current_warning_method(
                self.list_warning_setting)

        self.setup_ui_from_qsetting_and_server()

    def load_warning_method_QSetting(self):
        list_warning_checked = SettingScreenQSettings.get_instance().get_current_warning_method()
        if list_warning_checked and all(item != '' for item in list_warning_checked):
            for item in list_warning_checked:
                self.list_warning_setting.append(item)

    def load_alert_channel_QSetting(self):
        list_alert_checked = SettingScreenQSettings.get_instance().get_current_alert_channel()
        if list_alert_checked and all(item != '' for item in list_alert_checked):
            for item in list_alert_checked:
                # print(f'load_alert_channel_QSetting: {item}')
                self.list_alert_setting.append(item)

    def setup_ui_from_qsetting_and_server(self):
        # if SettingScreenQSettings.get_instance().get_state_switch_disable_alert():
        #     self.widget_warning_method.setDisabled(True)
        #     self.widget_alert_channel.setDisabled(True)
        # else:
        for checkbox_warning in self.warning_methods_list_checkbox:
            if checkbox_warning.type_checkbox in self.list_warning_setting:
                checkbox_warning.setChecked(True)
            else:
                checkbox_warning.setChecked(False)

        if Config.ENABLE_VOICE:
            check_alarm_or_voice = self.alarm_checkbox.isChecked(
            ) or self.voice_checkbox.isChecked()
        else:
            check_alarm_or_voice = self.alarm_checkbox.isChecked()

        if self.highlight_checkbox.isChecked():
            for index, item in enumerate(self.alert_channel_list_checkbox):
                if item.type_checkbox == "VMS":
                    item.setEnabled(True)
                else:
                    item.setDisabled(True)
        elif self.notification_msg_checkbox.isChecked():
            if check_alarm_or_voice:
                for index, item in enumerate(self.alert_channel_list_checkbox):
                    if item.type_checkbox == "VMS" or item.type_checkbox == "EMS" \
                            or item.type_checkbox == "TACTICAL_OPERATOR":
                        item.setEnabled(True)
                    else:
                        item.setDisabled(True)
            else:
                for item_child_alert_list in self.alert_channel_list_checkbox:
                    item_child_alert_list.setEnabled(True)
        elif check_alarm_or_voice:
            for index, item in enumerate(self.alert_channel_list_checkbox):
                if item.type_checkbox == "VMS" or item.type_checkbox == "EMS" \
                        or item.type_checkbox == "TACTICAL_OPERATOR":
                    item.setEnabled(True)
        else:
            for index, item in enumerate(self.alert_channel_list_checkbox):
                item.setDisabled(True)

        for checkbox in self.alert_channel_list_checkbox:
            if checkbox.type_checkbox in self.list_alert_setting:
                checkbox.setChecked(True)
            else:
                checkbox.setChecked(False)

    def setup_widget_warning_method(self):
        self.widget_warning_method = QWidget()
        self.layout_warning_method = QVBoxLayout()
        self.layout_warning_method.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.warning_methods_list_checkbox = []

        if Config.ENABLE_VOICE:
            self.layout_voice = QHBoxLayout()
            self.layout_voice.setContentsMargins(0, 0, 0, 0)
            self.voice_checkbox = CustomQCheckBox(
                title=self.tr("Voice"), type_checkbox="VOICE")
            self.voice_checkbox.stateChanged.connect(
                self.checkbox_warning_method_change)
            self.warning_methods_list_checkbox.append(self.voice_checkbox)
            self.layout_voice.addWidget(self.voice_checkbox)
        else:
            self.checkbox_temp = CustomQCheckBox()
            self.warning_methods_list_checkbox.append(self.checkbox_temp)

        self.layout_alarm_sound = QHBoxLayout()
        self.layout_alarm_sound.setContentsMargins(0, 0, 0, 0)
        self.layout_alarm_sound.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.layout_alarm_sound.setSpacing(28)
        self.alarm_checkbox = CustomQCheckBox(
            title=self.tr("Alarm Sound"), type_checkbox="ALARM_SOUND")
        self.alarm_checkbox.stateChanged.connect(
            self.checkbox_warning_method_change)
        self.button_pick_alarm_sound = PlaySoundButton()
        self.button_pick_alarm_sound.clicked = self.listen_demo_sound
        if self.alarm_checkbox.isChecked():
            self.button_pick_alarm_sound.load(Style.PrimaryImage.play_alarm_sound)
            self.button_pick_alarm_sound.setDisabled(False)
        else:
            self.button_pick_alarm_sound.load(
                Style.PrimaryImage.disable_play_alarm_sound)
            self.button_pick_alarm_sound.setDisabled(True)
        self.warning_methods_list_checkbox.append(self.alarm_checkbox)
        self.layout_alarm_sound.addWidget(self.alarm_checkbox)
        self.layout_alarm_sound.addWidget(self.button_pick_alarm_sound)

        self.layout_notification_msg = QHBoxLayout()
        self.layout_notification_msg.setContentsMargins(0, 0, 0, 0)
        self.notification_msg_checkbox = CustomQCheckBox(title=self.tr("Notification Message"),
                                                         type_checkbox="NOTIFICATION_MESSAGE")
        self.notification_msg_checkbox.stateChanged.connect(
            self.checkbox_warning_method_change)
        self.warning_methods_list_checkbox.append(
            self.notification_msg_checkbox)
        self.layout_notification_msg.addWidget(self.notification_msg_checkbox)

        self.layout_highlight_camera = QHBoxLayout()
        self.layout_highlight_camera.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.layout_highlight_camera.setContentsMargins(0, 0, 0, 0)
        self.layout_highlight_camera.setSpacing(28)
        self.highlight_checkbox = CustomQCheckBox(title=self.tr("Highlight Camera"),
                                                  type_checkbox="HIGHLIGHT_CAMERA")
        self.highlight_checkbox.stateChanged.connect(
            self.checkbox_warning_method_change)
        self.button_pick_highlight_camera = CustomOptionBox(
            title=self.tr("Select your highlight type"))
        self.button_pick_highlight_camera.clicked = self.select_highlight_type
        if SettingScreenQSettings.get_instance().get_current_highlight_type() == 0:
            self.button_pick_highlight_camera.text_option.setText(
                self.tr("Highlight until the user interacts with that camera."))
        elif SettingScreenQSettings.get_instance().get_current_highlight_type() == 1:
            if SettingScreenQSettings.get_instance().get_time_unit_highlight() == 0:
                text = self.tr("Second")
            elif SettingScreenQSettings.get_instance().get_time_unit_highlight() == 1:
                text = self.tr("Minute")
            else:
                text = self.tr("Hour")
            self.button_pick_highlight_camera.text_option.setText(
                self.tr("Highlight duration: ")+f"{str(SettingScreenQSettings.get_instance().get_duration_highlight())} {text}")
        else:
            pass
        if self.highlight_checkbox.isChecked():
            self.button_pick_highlight_camera.setDisabled(False)
        else:
            self.button_pick_highlight_camera.setDisabled(True)
        self.warning_methods_list_checkbox.append(self.highlight_checkbox)
        self.layout_highlight_camera.addWidget(self.highlight_checkbox)
        self.layout_highlight_camera.addWidget(
            self.button_pick_highlight_camera)

        if Config.ENABLE_VOICE:
            self.layout_warning_method.addLayout(self.layout_voice)
        self.layout_warning_method.addLayout(self.layout_alarm_sound)
        self.layout_warning_method.addLayout(self.layout_notification_msg)
        # self.layout_warning_method.addLayout(self.layout_highlight_camera)

        self.widget_warning_method.setLayout(self.layout_warning_method)

    def setup_widget_alert_channel(self):
        self.widget_alert_channel = QWidget()
        self.layout_alert_channel = QVBoxLayout()
        self.layout_alert_channel.setSpacing(10)
        self.alert_channel_list_checkbox = []
        self.layout_iVMS = QHBoxLayout()
        self.layout_iVMS.setContentsMargins(0, 0, 0, 0)
        self.ivms_checkbox = CustomQCheckBox(
            title=self.tr("iVMS"), type_checkbox="VMS")
        self.ivms_checkbox.stateChanged.connect(
            self.checkbox_alert_channel_change)
        self.alert_channel_list_checkbox.append(self.ivms_checkbox)
        self.layout_iVMS.addWidget(self.ivms_checkbox)

        self.layout_iems = QHBoxLayout()
        self.layout_iems.setContentsMargins(0, 0, 0, 0)
        self.iems_checkbox = CustomQCheckBox(
            title=self.tr("iEMS"), type_checkbox="EMS")
        self.iems_checkbox.stateChanged.connect(
            self.checkbox_alert_channel_change)
        self.alert_channel_list_checkbox.append(self.iems_checkbox)
        self.layout_iems.addWidget(self.iems_checkbox)

        if Config.ENABLE_MULTI_ALERT_CHANNEL:
            self.layout_tactical_operation = QHBoxLayout()
            self.layout_tactical_operation.setContentsMargins(0, 0, 0, 0)
            self.tactical_operation_checkbox = CustomQCheckBox(title=self.tr("Tactical Operation"),
                                                               type_checkbox="TACTICAL_OPERATOR")
            self.tactical_operation_checkbox.stateChanged.connect(
                self.checkbox_alert_channel_change)
            self.alert_channel_list_checkbox.append(
                self.tactical_operation_checkbox)
            self.layout_tactical_operation.addWidget(
                self.tactical_operation_checkbox)

            self.layout_internal_sms = QHBoxLayout()
            self.layout_internal_sms.setContentsMargins(0, 0, 0, 0)
            self.internal_sms_checkbox = CustomQCheckBox(
                title=self.tr("Internal SMS"), type_checkbox="INTERNAL_SMS")
            self.internal_sms_checkbox.stateChanged.connect(
                self.checkbox_alert_channel_change)
            self.alert_channel_list_checkbox.append(self.internal_sms_checkbox)
            self.layout_internal_sms.addWidget(self.internal_sms_checkbox)

            self.layout_internal_email = QHBoxLayout()
            self.layout_internal_email.setContentsMargins(0, 0, 0, 0)
            self.internal_email_checkbox = CustomQCheckBox(title=self.tr("Internal Email"),
                                                           type_checkbox="INTERNAL_EMAIL")
            self.internal_email_checkbox.stateChanged.connect(
                self.checkbox_alert_channel_change)
            self.alert_channel_list_checkbox.append(
                self.internal_email_checkbox)
            self.layout_internal_email.addWidget(self.internal_email_checkbox)

            self.layout_external_sms = QHBoxLayout()
            self.layout_external_sms.setContentsMargins(0, 0, 0, 0)
            self.external_sms_checkbox = CustomQCheckBox(
                title=self.tr("External SMS"), type_checkbox="EXTERNAL_SMS")
            self.external_sms_checkbox.stateChanged.connect(
                self.checkbox_alert_channel_change)
            self.alert_channel_list_checkbox.append(self.external_sms_checkbox)
            self.layout_external_sms.addWidget(self.external_sms_checkbox)

            self.layout_external_email = QHBoxLayout()
            self.layout_external_email.setContentsMargins(0, 0, 0, 0)
            self.external_email_checkbox = CustomQCheckBox(title=self.tr("External Email"),
                                                           type_checkbox="EXTERNAL_EMAIL")
            self.external_email_checkbox.stateChanged.connect(
                self.checkbox_alert_channel_change)
            self.alert_channel_list_checkbox.append(
                self.external_email_checkbox)
            self.layout_external_email.addWidget(self.external_email_checkbox)

            self.layout_telegram = QHBoxLayout()
            self.layout_telegram.setContentsMargins(0, 0, 0, 0)
            self.telegram_checkbox = CustomQCheckBox(
                title=self.tr("Telegram"), type_checkbox="TELEGRAM")
            self.telegram_checkbox.stateChanged.connect(
                self.checkbox_alert_channel_change)
            self.alert_channel_list_checkbox.append(self.telegram_checkbox)
            self.layout_telegram.addWidget(self.telegram_checkbox)
        self.layout_alert_channel.addLayout(self.layout_iVMS)
        self.layout_alert_channel.addLayout(self.layout_iems)
        if Config.ENABLE_MULTI_ALERT_CHANNEL:
            self.layout_alert_channel.addLayout(self.layout_tactical_operation)
            self.layout_alert_channel.addLayout(self.layout_internal_sms)
            self.layout_alert_channel.addLayout(self.layout_internal_email)
            self.layout_alert_channel.addLayout(self.layout_external_sms)
            self.layout_alert_channel.addLayout(self.layout_external_email)
            self.layout_alert_channel.addLayout(self.layout_telegram)

        self.widget_alert_channel.setLayout(self.layout_alert_channel)

    def listen_demo_sound(self, event):
        pass

    def select_highlight_type(self, event):
        self.menu = QMenu()
        self.checkboxes_2light_option = []
        highlight_until_interact_action = QWidgetAction(self.menu)
        config_duration_action = QWidgetAction(self.menu)

        highlight_until_interact_widget = ItemCheckboxMenu(
            title=self.tr("Highlight until the user interacts with that camera."))
        self.checkboxes_2light_option.append(
            highlight_until_interact_widget.checkbox)
        highlight_until_interact_widget.checkbox.stateChanged.connect(
            self.on_checkbox_highlight_change)

        self.config_duration_widget = ItemCheckboxMenu(title=self.tr("Configure the highlight duration: "),
                                                       editable_widget=True)
        self.config_duration_widget.unit_combo_box.combo_box.activated.connect(
            self.comboBox_time_unit_change)
        self.config_duration_widget.time_line_edit.textChanged.connect(
            self.duration_timeline_text_changed)
        self.config_duration_widget.time_line_edit.setText(
            str(SettingScreenQSettings.get_instance().get_duration_highlight()))
        self.config_duration_widget.unit_combo_box.combo_box.setCurrentIndex(
            int(SettingScreenQSettings.get_instance().get_time_unit_highlight()))
        self.checkboxes_2light_option.append(
            self.config_duration_widget.checkbox)
        self.config_duration_widget.checkbox.stateChanged.connect(
            self.on_checkbox_highlight_change)
        if SettingScreenQSettings.get_instance().get_current_highlight_type() == 0:
            highlight_until_interact_widget.checkbox.setChecked(True)
            self.button_pick_highlight_camera.text_option.setText(
                self.tr("Highlight until the user interacts with that camera."))
        elif SettingScreenQSettings.get_instance().get_current_highlight_type() == 1:
            self.config_duration_widget.checkbox.setChecked(True)
            self.button_pick_highlight_camera.text_option.setText(
                self.tr(
                    "Highlight duration: ")+f"{self.config_duration_widget.time_line_edit.text()} {self.config_duration_widget.unit_combo_box.combo_box.currentText()}")
        else:
            pass

        highlight_until_interact_action.setDefaultWidget(
            highlight_until_interact_widget)
        config_duration_action.setDefaultWidget(self.config_duration_widget)
        self.menu.addAction(highlight_until_interact_action)
        self.menu.addAction(config_duration_action)
        self.menu.setWindowFlags(
            Qt.WindowType.FramelessWindowHint | Qt.WindowType.Popup)
        self.menu.setAttribute(
            Qt.WidgetAttribute.WA_TranslucentBackground, True)
        self.menu.setStyleSheet(Style.PrimaryStyleSheet.get_context_menu_style(theme_instance=main_controller))
        self.menu.exec(event.globalPos())

    def on_checkbox_highlight_change(self, state):
        sender = self.sender()  # Get the checkbox that emitted the signal
        checked_position = self.checkboxes_2light_option.index(sender)
        if state == 2:  # Checked state
            for checkbox in self.checkboxes_2light_option:
                if checkbox != sender:
                    checkbox.setChecked(False)
            if checked_position == 0:
                self.button_pick_highlight_camera.text_option.setText(
                    self.tr("Highlight until the user interacts with that camera."))
                self.config_duration_widget.time_line_edit.setDisabled(True)
                self.config_duration_widget.unit_combo_box.setDisabled(True)
                SettingScreenQSettings.get_instance().set_current_highlight_type(
                    0)
            elif checked_position == 1:
                self.button_pick_highlight_camera.text_option.setText(
                    self.tr("Highlight duration: ")+f"{self.config_duration_widget.time_line_edit.text()} {self.config_duration_widget.unit_combo_box.combo_box.currentText()}")
                self.config_duration_widget.time_line_edit.setDisabled(False)
                self.config_duration_widget.unit_combo_box.setDisabled(False)
                SettingScreenQSettings.get_instance().set_current_highlight_type(
                    1)

        else:
            num_checked_in_list_one = sum(
                1 for item in self.checkboxes_2light_option if item.isChecked())
            if num_checked_in_list_one == 0:  # If no checkboxes in list_one are checked
                self.config_duration_widget.time_line_edit.setDisabled(True)
                self.config_duration_widget.unit_combo_box.setDisabled(True)
                self.button_pick_highlight_camera.text_option.setText(
                    self.tr("Select your highlight type"))
                SettingScreenQSettings.get_instance().set_current_highlight_type(
                    -1)

    def duration_timeline_text_changed(self, new_text):
        self.button_pick_highlight_camera.text_option.setText(
            self.tr("Highlight duration: ") + f"{new_text} {self.config_duration_widget.unit_combo_box.combo_box.currentText()}")
        SettingScreenQSettings.get_instance().set_duration_highlight(
            int(new_text))

    def comboBox_time_unit_change(self, index):
        # Get the selected item text
        selected_item = self.sender().currentText()
        self.button_pick_highlight_camera.text_option.setText(
            self.tr("Highlight duration: ")+f"{self.config_duration_widget.time_line_edit.text()} {selected_item}")

        SettingScreenQSettings.get_instance().set_time_unit_highlight(index)

    def checkbox_warning_method_change(self, state):
        sender = self.sender()  # Get the checkbox that emitted the signal
        if state == 2:  # Checked state
            checked_position = self.warning_methods_list_checkbox.index(sender)
            if checked_position == 0:  # voice
                if not Config.ENABLE_VOICE:
                    return
                checked_items = []
                if self.voice_checkbox.isChecked():
                    self.alarm_checkbox.setChecked(False)
                if not self.highlight_checkbox.isChecked():
                    for index, item in enumerate(self.alert_channel_list_checkbox):
                        if item.type_checkbox == "VMS" or item.type_checkbox == "EMS" \
                                or item.type_checkbox == "TACTICAL_OPERATOR":
                            item.setEnabled(True)
                        else:
                            item.setEnabled(False)
                        if item.isChecked():
                            checked_items.append(item)
                    # If more than one checkbox is checked, clear all except the first one
                    if len(checked_items) > 1:
                        self.list_checked_box_channel_to_save.clear()
                        for i, item in enumerate(checked_items):
                            if (
                                    item.type_checkbox == "VMS" or item.type_checkbox == "EMS" or item.type_checkbox == "TACTICAL_OPERATOR") and item.isChecked():
                                if i != 0:  # Clear all except the first one
                                    item.setChecked(False)
                                else:
                                    self.list_checked_box_channel_to_save.append(
                                        item.type_checkbox)
                            else:
                                item.setChecked(False)
                self.list_warning_method_msg.append(TypeWarningToServer.VOICE)
                SettingScreenQSettings.get_instance().set_current_alert_channel(
                    self.list_checked_box_channel_to_save)
                # SettingScreenQSettings.get_instance().set_current_voice_method(self.voice_checkbox.isChecked())
            elif checked_position == 1:  # alarm sound
                self.button_pick_alarm_sound.load(Style.PrimaryImage.play_alarm_sound)
                self.button_pick_alarm_sound.setDisabled(False)
                checked_items = []
                if Config.ENABLE_VOICE:
                    if self.alarm_checkbox.isChecked():
                        self.voice_checkbox.setChecked(False)
                if not self.highlight_checkbox.isChecked():
                    for index, item in enumerate(self.alert_channel_list_checkbox):
                        if item.type_checkbox == "VMS" or item.type_checkbox == "EMS" \
                                or item.type_checkbox == "TACTICAL_OPERATOR":
                            item.setEnabled(True)
                        else:
                            item.setEnabled(False)
                        if item.isChecked():
                            checked_items.append(item)

                    # If more than one checkbox is checked, clear all except the first one
                    if len(checked_items) > 1:
                        self.list_checked_box_channel_to_save.clear()
                        for i, item in enumerate(checked_items):
                            if (
                                    item.type_checkbox == "VMS" or item.type_checkbox == "EMS" or item.type_checkbox == "TACTICAL_OPERATOR") and item.isChecked():
                                if i != 0:  # Clear all except the first one
                                    item.setChecked(False)
                                else:
                                    self.list_checked_box_channel_to_save.append(
                                        item.type_checkbox)
                            else:
                                item.setChecked(False)
                if self.list_checked_box_channel_to_save:
                    SettingScreenQSettings.get_instance().set_current_alert_channel(
                        self.list_checked_box_channel_to_save)
                self.list_warning_method_msg.append(
                    TypeWarningToServer.ALARM_SOUND)
                # SettingScreenQSettings.get_instance().set_current_alarm_method(self.alarm_checkbox.isChecked())
            elif checked_position == 2:  # noti message
                for item in self.alert_channel_list_checkbox:
                    if Config.ENABLE_VOICE:
                        check_alarm_or_voice = self.alarm_checkbox.isChecked(
                        ) or self.voice_checkbox.isChecked()
                    else:
                        check_alarm_or_voice = self.alarm_checkbox.isChecked()
                    if not self.highlight_checkbox.isChecked() and check_alarm_or_voice:
                        if item.type_checkbox == "VMS" or item.type_checkbox == "EMS" \
                                or item.type_checkbox == "TACTICAL_OPERATOR":
                            item.setEnabled(True)
                        else:
                            item.setEnabled(False)
                    elif self.highlight_checkbox.isChecked():
                        if item.type_checkbox != "VMS":
                            item.setDisabled(True)
                    else:
                        if not item.isEnabled():
                            item.setEnabled(True)
                self.list_warning_method_msg.append(
                    TypeWarningToServer.NOTIFICATION_MESSAGE)
                # SettingScreenQSettings.get_instance().set_current_notification_method(self.notification_msg_checkbox.isChecked())
            elif checked_position == 3:  # 2light
                self.button_pick_highlight_camera.setDisabled(False)
                list_temp = []
                self.list_checked_box_channel_to_save.clear()
                for item in self.alert_channel_list_checkbox:
                    if item.isChecked() and item.type_checkbox == "VMS":
                        list_temp.append(item.type_checkbox)
                    else:
                        item.setChecked(False)
                        item.setDisabled(item.type_checkbox != "VMS")
                self.list_warning_method_msg.append(
                    TypeWarningToServer.HIGHLIGHT_CAMERA)
                self.list_checked_box_channel_to_save = list_temp
                SettingScreenQSettings.get_instance().set_current_alert_channel(
                    self.list_checked_box_channel_to_save)
                # SettingScreenQSettings.get_instance().set_current_highlight_method(self.highlight_checkbox.isChecked())
            for checkbox in self.warning_methods_list_checkbox:
                if checkbox.isChecked() and checkbox.type_checkbox not in self.list_checked_box_warning_to_save:
                    self.list_checked_box_warning_to_save.append(
                        checkbox.type_checkbox)
            SettingScreenQSettings.get_instance().set_current_warning_method(
                self.list_checked_box_warning_to_save)
        else:
            num_checked_in_list_one = sum(
                1 for item in self.warning_methods_list_checkbox if item.isChecked())
            if num_checked_in_list_one == 0:  # If no checkboxes in list_one are checked
                self.button_pick_alarm_sound.setDisabled(True)
                self.button_pick_alarm_sound.load(
                    Style.PrimaryImage.disable_play_alarm_sound)
                self.button_pick_highlight_camera.setDisabled(True)
                # Clear all checked checkboxes in list_two and disable them
                for item in self.alert_channel_list_checkbox:
                    if item.isChecked():
                        item.setChecked(False)
                    item.setDisabled(True)
                self.list_warning_method_msg.clear()
                self.list_checked_box_channel_to_save.clear()
                self.list_checked_box_warning_to_save.clear()
                SettingScreenQSettings.get_instance().set_current_alert_channel(
                    self.list_checked_box_channel_to_save)
                SettingScreenQSettings.get_instance().set_current_warning_method(
                    self.list_checked_box_warning_to_save)
            else:  # At least one checkbox
                checked_position = self.warning_methods_list_checkbox.index(
                    sender)
                if Config.ENABLE_VOICE:
                    check_alarm_or_voice = self.alarm_checkbox.isChecked(
                    ) or self.voice_checkbox.isChecked()
                else:
                    check_alarm_or_voice = self.alarm_checkbox.isChecked()
                if checked_position == 0:  # voice
                    if not Config.ENABLE_VOICE:
                        return
                    for item in self.alert_channel_list_checkbox:
                        if self.notification_msg_checkbox.isChecked() and not self.highlight_checkbox.isChecked():
                            item.setEnabled(True)
                    # SettingScreenQSettings.get_instance().set_current_voice_method(self.voice_checkbox.isChecked())
                    if TypeWarningToServer.VOICE in self.list_warning_method_msg:
                        self.list_warning_method_msg.remove(
                            TypeWarningToServer.VOICE)
                elif checked_position == 1:  # alarm
                    self.button_pick_alarm_sound.setDisabled(True)
                    self.button_pick_alarm_sound.load(
                        Style.PrimaryImage.disable_play_alarm_sound)
                    for item in self.alert_channel_list_checkbox:
                        if self.notification_msg_checkbox.isChecked() and not self.highlight_checkbox.isChecked():
                            item.setEnabled(True)
                    if TypeWarningToServer.ALARM_SOUND in self.list_warning_method_msg:
                        self.list_warning_method_msg.remove(
                            TypeWarningToServer.ALARM_SOUND)
                    # SettingScreenQSettings.get_instance().set_current_alarm_method(self.alarm_checkbox.isChecked())
                elif checked_position == 2:  # notification
                    for item in self.alert_channel_list_checkbox:
                        if self.highlight_checkbox.isChecked():
                            if item.type_checkbox == "VMS":
                                item.setEnabled(True)
                            else:
                                item.setEnabled(False)
                        elif check_alarm_or_voice:
                            if item.type_checkbox == "VMS" or item.type_checkbox == "EMS" \
                                    or item.type_checkbox == "TACTICAL_OPERATOR":
                                item.setEnabled(True)
                            else:
                                item.setEnabled(False)
                    if TypeWarningToServer.NOTIFICATION_MESSAGE in self.list_warning_method_msg:
                        self.list_warning_method_msg.remove(
                            TypeWarningToServer.NOTIFICATION_MESSAGE)
                    # SettingScreenQSettings.get_instance().set_current_notification_method(self.notification_msg_checkbox.isChecked())
                elif checked_position == 3:  # 2light
                    self.button_pick_highlight_camera.setDisabled(True)
                    for item in self.alert_channel_list_checkbox:
                        if check_alarm_or_voice:
                            if item.type_checkbox == "VMS" or item.type_checkbox == "EMS" \
                                    or item.type_checkbox == "TACTICAL_OPERATOR":
                                item.setEnabled(True)
                            else:
                                item.setEnabled(False)
                        else:
                            item.setDisabled(False)
                    if TypeWarningToServer.HIGHLIGHT_CAMERA in self.list_warning_method_msg:
                        self.list_warning_method_msg.remove(
                            TypeWarningToServer.HIGHLIGHT_CAMERA)
                    # SettingScreenQSettings.get_instance().set_current_highlight_method(self.highlight_checkbox.isChecked())

                for item in self.warning_methods_list_checkbox:
                    if not item.isChecked():
                        # Remove the type of the unchecked checkbox from list_checked_box
                        if item.type_checkbox in self.list_checked_box_warning_to_save:
                            self.list_checked_box_warning_to_save.remove(
                                item.type_checkbox)
                SettingScreenQSettings.get_instance().set_current_warning_method(
                    self.list_checked_box_warning_to_save)
        data = WarningMethodAndAlertModel(
            alertChannels=self.list_alert_channel_msg, warningMethods=self.list_warning_method_msg)
        self.mainController.update_setting_warning_and_alert_by_patch(data)

    def checkbox_alert_channel_change(self, state):
        sender = self.sender()  # Get the checkbox that emitted the signal
        if Config.ENABLE_VOICE:
            check_alarm_or_voice = self.alarm_checkbox.isChecked(
            ) or self.voice_checkbox.isChecked()
        else:
            check_alarm_or_voice = self.alarm_checkbox.isChecked()
        if state == 2:
            if check_alarm_or_voice:
                for checkbox in self.alert_channel_list_checkbox:
                    if checkbox != sender:
                        checkbox.setChecked(False)
            checked_position = self.alert_channel_list_checkbox.index(sender)
            if checked_position == 0:
                self.list_alert_channel_msg.append(
                    TypeAlertChannelToServer.VMS)
            elif checked_position == 1:
                self.list_alert_channel_msg.append(
                    TypeAlertChannelToServer.EMS)

            for checkbox in self.alert_channel_list_checkbox:
                if checkbox.isChecked() and checkbox.type_checkbox not in self.list_checked_box_channel_to_save:
                    self.list_checked_box_channel_to_save.append(
                        checkbox.type_checkbox)
            SettingScreenQSettings.get_instance().set_current_alert_channel(
                self.list_checked_box_channel_to_save)
        else:
            num_checked_in_list_one = sum(
                1 for item in self.alert_channel_list_checkbox if item.isChecked())
            if num_checked_in_list_one == 0:  # If no checkboxes in list_one are checked
                self.list_checked_box_channel_to_save.clear()
                self.list_alert_channel_msg.clear()
                SettingScreenQSettings.get_instance().set_current_alert_channel(
                    self.list_checked_box_channel_to_save)
            else:  # At least one checkbox
                for item in self.alert_channel_list_checkbox:
                    if not item.isChecked():
                        # Remove the type of the unchecked checkbox from list_checked_box
                        if item.type_checkbox in self.list_checked_box_channel_to_save:
                            self.list_checked_box_channel_to_save.remove(
                                item.type_checkbox)
                SettingScreenQSettings.get_instance().set_current_alert_channel(
                    self.list_checked_box_channel_to_save)

                checked_position = self.alert_channel_list_checkbox.index(
                    sender)
                if checked_position == 0:
                    self.list_alert_channel_msg.remove(
                        TypeAlertChannelToServer.VMS)
                elif checked_position == 1:
                    self.list_alert_channel_msg.remove(
                        TypeAlertChannelToServer.EMS)
        data = WarningMethodAndAlertModel(alertChannels=self.list_alert_channel_msg,
                                          warningMethods=self.list_warning_method_msg)
        self.mainController.update_setting_warning_and_alert_by_patch(data)

    def retranslateUi_Alert(self):
        # self.label_enable_alert.setText(QCoreApplication.translate("WidgetAlertTab", u"<b>Disable Alert</b>", None))
        self.label_warning_method.setText(
            QCoreApplication.translate("WidgetAlertTab", u"<b>1. Warning Methods:</b>", None))
        self.label_alert_channel.setText(
            QCoreApplication.translate("WidgetAlertTab", u"<b>2. Alert Channel Via:</b>", None))
        if Config.ENABLE_VOICE:
            self.voice_checkbox.setText(
                QCoreApplication.translate("WidgetAlertTab", u"Voice", None))
        self.alarm_checkbox.setText(QCoreApplication.translate(
            "WidgetAlertTab", u"Alarm Sound", None))
        self.notification_msg_checkbox.setText(
            QCoreApplication.translate("WidgetAlertTab", u"Notification Message", None))
        self.highlight_checkbox.setText(QCoreApplication.translate(
            "WidgetAlertTab", u"Highlight Camera", None))
        if SettingScreenQSettings.get_instance().get_current_highlight_type() == 0:
            self.button_pick_highlight_camera.text_option.setText(
                QCoreApplication.translate("WidgetAlertTab", u"Highlight until the user interacts with that camera.", None))
        elif SettingScreenQSettings.get_instance().get_current_highlight_type() == 1:
            if SettingScreenQSettings.get_instance().get_time_unit_highlight() == 0:
                text = QCoreApplication.translate(
                    "WidgetAlertTab", u"Second", None)
            elif SettingScreenQSettings.get_instance().get_time_unit_highlight() == 1:
                text = QCoreApplication.translate(
                    "WidgetAlertTab", u"Minute", None)
            else:
                text = QCoreApplication.translate(
                    "WidgetAlertTab", u"Hour", None)
            self.button_pick_highlight_camera.text_option.setText(
                QCoreApplication.translate("WidgetAlertTab", u"Highlight duration: ", None) + f"{str(SettingScreenQSettings.get_instance().get_duration_highlight())} {text}")
        else:
            self.button_pick_highlight_camera.text_option.setText(
                QCoreApplication.translate("WidgetAlertTab", u"Select your highlight type", None))

        if Config.ENABLE_MULTI_ALERT_CHANNEL:
            self.tactical_operation_checkbox.setText(
                QCoreApplication.translate("WidgetAlertTab", u"Tactical Operation", None))
            self.internal_sms_checkbox.setText(
                QCoreApplication.translate("WidgetAlertTab", u"Internal SMS", None))
            self.internal_email_checkbox.setText(
                QCoreApplication.translate("WidgetAlertTab", u"Internal Email", None))
            self.external_sms_checkbox.setText(
                QCoreApplication.translate("WidgetAlertTab", u"External SMS", None))
            self.external_email_checkbox.setText(
                QCoreApplication.translate("WidgetAlertTab", u"External Email", None))


class ItemCheckboxMenu(QWidget):
    def __init__(self, title, editable_widget=False):
        super().__init__()
        self.title = title
        self.editable_widget = editable_widget
        self.load_ui()

    def load_ui(self):
        self.layout = QHBoxLayout()
        # self.label = QLabel(self.title)
        self.checkbox = QCheckBox(self.title)
        self.layout.addWidget(self.checkbox)
        if self.editable_widget:
            self.time_line_edit = QLineEdit()
            self.time_line_edit.setStyleSheet('''
                QLineEdit {
                    color: #333333;
                    border: 1px solid #BBBBBB;
                    border-radius: 2px;
                }''')
            self.time_line_edit.setFixedWidth(50)
            self.time_line_edit.setDisabled(True)
            self.time_line_edit.setText("30")
            self.unit_combo_box = ComboBoxUnit()
            self.unit_combo_box.setDisabled(True)
            self.layout.addWidget(self.time_line_edit)
            self.layout.addWidget(self.unit_combo_box)
            self.layout.setSpacing(3)

        self.layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_widget = QWidget()
        self.main_widget.setLayout(self.layout)
        self.main_widget.setObjectName("parent")

        self.main_layout = QHBoxLayout()
        self.main_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.main_layout.addWidget(self.main_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        self.setLayout(self.main_layout)


class ComboBoxUnit(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        # Create a QComboBox widget
        self.combo_box = QComboBox(self)
        self.combo_box.setFixedWidth(90)

        # Create a QStandardItemModel
        model = QStandardItemModel()

        # Create items with icons and text
        item1 = QStandardItem(self.tr("Second"))
        item2 = QStandardItem(self.tr("Minute"))
        item3 = QStandardItem(self.tr("Hour"))

        # Add items to the model
        model.appendRow(item1)
        model.appendRow(item2)
        model.appendRow(item3)

        # Set the model for the ComboBox
        self.combo_box.setModel(model)

        # Create a delegate to display icons and text
        delegate = QItemDelegate(self.combo_box)
        self.combo_box.setItemDelegate(delegate)

        # Set a style sheet for the ComboBox
        self.combo_box.setStyleSheet(f'''
            QComboBox {{ 
                background-color: white; 
                border: 1px solid #BBBBBB; border-radius: 2px;
            }}
            QComboBox::drop-down {{
                 background-color: white;
             }}
            QComboBox::down-arrow {{ 
                image: url({Style.PrimaryImage.expand_bottom}); 
            }}
        ''')

        # Connect the ComboBox to a function that handles item selection
        # self.combo_box.activated.connect(self.comboBoxItemSelected)
        self.layout = QVBoxLayout()
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.addWidget(self.combo_box)
        self.setLayout(self.layout)

    def comboBoxItemSelected(self, index):
        # Get the selected item text
        selected_item = self.sender().currentText()
        logger.debug(f"Selected Item: {selected_item},   {index}")


class CustomOptionBox(QWidget):
    def __init__(self, title=''):
        super().__init__()
        self.title = title
        self.layout = None
        self.clicked = None
        self.init_ui()

    def init_ui(self):
        self.layout = QHBoxLayout()
        self.layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        self.hbox_layout = QHBoxLayout()
        self.hbox_layout.setContentsMargins(0, 0, 0, 0)
        self.hbox_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)

        self.icon_bottom = Style.PrimaryImage.expand_bottom
        self.pixmap_bottom = QPixmap(self.icon_bottom)
        self.icon_dropdown = QLabel()
        self.icon_dropdown.setPixmap(self.pixmap_bottom)
        self.icon_dropdown.setFixedSize(self.pixmap_bottom.width(),
                                        self.pixmap_bottom.height())
        self.text_option = QLabel(self.title)
        self.text_option.setFixedWidth(200)

        self.hbox_layout.addWidget(self.text_option)
        self.hbox_layout.addWidget(self.icon_dropdown)
        self.layout.addLayout(self.hbox_layout)

        self.central_widget = QWidget()
        self.central_widget.setStyleSheet('''
            QWidget {
                color: white;
                border: 1px solid #BBBBBB; /* Border width and color */
                border-radius: 5px; /* Rounded corners */
            }
            QWidget:disabled {
                color: gray;
                border: 1px solid gray; /* Border width and color */
                border-radius: 5px; /* Rounded corners */
            }
            QLabel {
                border: none
            }
            QLabel:disabled  {
               border: none
            }
        ''')
        self.central_widget.setLayout(self.layout)
        self.central_layout = QVBoxLayout()
        self.central_layout.addWidget(self.central_widget)
        self.central_layout.setContentsMargins(0, 0, 0, 0)
        self.setLayout(self.central_layout)

    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent) -> None:
        if self.clicked is not None:
            self.clicked(event)

    def enterEvent(self, event: PySide6.QtGui.QEnterEvent) -> None:
        self.central_widget.setStyleSheet('''
           QWidget {
                color: white;
                border: 2px solid #BBBBBB; /* Border width and color */
                border-radius: 5px; /* Rounded corners */
           }
           QWidget:disabled {
                color: gray;
                border: 1px solid gray; /* Border width and color */
                border-radius: 5px; /* Rounded corners */
            }
           QLabel {
               border: none
           }
           QLabel:disabled  {
               border: none
           }
       ''')

    def leaveEvent(self, event: PySide6.QtCore.QEvent) -> None:
        self.central_widget.setStyleSheet('''
           QWidget {
                color: white;
                border: 1px solid #BBBBBB; /* Border width and color */
                border-radius: 5px; /* Rounded corners */
           }
           QWidget:disabled {
                color: gray;
                border: 1px solid gray; /* Border width and color */
                border-radius: 5px; /* Rounded corners */
           }
           QLabel {
               border: none
           }
           QLabel:disabled  {
               border: none
           }
       ''')


class CustomQCheckBox(QCheckBox):
    def __init__(self, title=None, type_checkbox='NO_CHECK'):
        super().__init__()
        self.title = title
        self.type_checkbox = type_checkbox
        self.init_ui()

    def init_ui(self):
        self.setText(self.title)
        self.setChecked(False)  # Set the initial state of the QCheckBox
        # Connect the stateChanged signal to a custom slot (function)
        # self.stateChanged.connect(self.checkBox_state_changed)

        self.setStyleSheet(f"""
            QCheckBox {{
                background-color: {Style.PrimaryColor.on_background};
                color: white;
                spacing: 10px;
            }}
            QCheckBox:disabled {{
                background-color: {Style.PrimaryColor.on_background};
                color: gray;
                spacing: 10px;
            }}
            QCheckBox::indicator:checked {{
                color: white;
                border: none;
                image: url({Style.PrimaryImage.checkbox_checked});
                background-color: {Style.PrimaryColor.on_background};
                width: 20px;
                height: 20px;
            }}
            QCheckBox::indicator:unchecked {{
                color: white;
                border: none;
                image: url({Style.PrimaryImage.checkbox_unchecked});
                background-color: {Style.PrimaryColor.on_background};
                width: 20px;
                height: 20px;
            }}
            """)

    def checkBox_state_changed(self, state):
        if state == 2:  # Qt.Checked
            logger.debug("Feature enabled")
        else:
            logger.debug("Feature disabled")


class PlaySoundButton(QSvgWidget):
    def __init__(self):
        super().__init__()
        self.clicked = None
        self.load(Style.PrimaryImage.disable_play_alarm_sound)
        self.setFixedSize(24, 24)

    def mousePressEvent(self, event: PySide6.QtGui.QMouseEvent):
        if self.clicked is not None:
            self.clicked(event)

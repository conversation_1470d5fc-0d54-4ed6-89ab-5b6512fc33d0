from functools import partial
from queue import Queue

from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QStandardItemModel, QIcon, QPixmap, QColor, QBrush, QGuiApplication
from PySide6.QtWidgets import QWidget, QVBoxLayout, QCheckBox, QAbstractItemView, QSizePolicy, \
    QPushButton, QHBoxLayout, QLabel, QDialog

import logging

from src.utils.theme_setting import theme_setting
from src.common.controller.controller_manager import Controller
from src.common.controller.main_controller import main_controller
from src.common.widget.custom_checkbox_dialog import CustomCheckBoxDialogs
from src.common.model.user_role_model import role_model_manager, RoleModel
from src.common.widget.custom_qtable_view import TableWithCustomHeader
from src.common.widget.dialogs.warning_dialog import WarningDialog
from src.common.widget.dialogs.dialogs_permission_screen import RoleInfoDialog
from src.common.widget.pagination.page_indicator.page_indicator import Pagination
from src.styles.style import Style, Theme

logger = logging.getLogger(__name__)

class UserGroupsTableView(QWidget):
    def __init__(self, parent=None, widget_width=None, widget_height=None, controller: Controller = None):
        super().__init__(parent)
        main_controller.list_parent['UserGroupsTableView'] = self
        self.is_programmatically_changing_combobox = True
        self.controller = controller
        self.widget_width = widget_width
        self.widget_height = widget_height
        self.table_result_user_groups = None
        self.model_table_user_groups = None
        self.page_indicator = None
        self.column_init_table = 6
        self.is_loading = False
        self.filter_role_name = False
        self.filter_queue = Queue()
        self.create_model()
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        # create layout
        self.main_layout = QVBoxLayout()
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setAlignment(Qt.AlignTop)

        list_horizontal_header = ["", self.tr("NO"), self.tr("USER GROUP NAME"), self.tr("DESCRIPTION"), self.tr("STATUS"), self.tr("ACTIONS")]
        widget_checkbox = QWidget()
        layout_checkbox = QVBoxLayout()
        layout_checkbox.setContentsMargins(0, 0, 0, 0)
        layout_checkbox.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.checkbox_all_roles = QCheckBox()
        self.checkbox_all_roles.stateChanged.connect(self.check_all)
        self.checkbox_all_roles.setTristate(False)
        layout_checkbox.addWidget(self.checkbox_all_roles)
        widget_checkbox.setLayout(layout_checkbox)
        list_widget_for_header = {0: widget_checkbox}
        self.model_table_user_groups = QStandardItemModel()
        self.table_result_user_groups = TableWithCustomHeader(horizontal_label_list=list_horizontal_header,
                                                              list_widget_to_header=list_widget_for_header,
                                                              model_for_table=self.model_table_user_groups,
                                                              use_stylesheet_header=True)

        self.table_result_user_groups.table.clicked.connect(
            partial(self.on_clicked_table, model_table=self.model_table_user_groups,
                    table=self.table_result_user_groups.table, checkbox_all=self.checkbox_all_roles))
        self.set_up_dimension_row()
        self.table_result_user_groups.table.verticalHeader().setVisible(False)
        self.table_result_user_groups.table.verticalHeader().setDefaultSectionSize(0.08 * self.widget_height)
        self.table_result_user_groups.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table_result_user_groups.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.table_result_user_groups.table.setSelectionMode(QAbstractItemView.SelectionMode.NoSelection)
        self.table_result_user_groups.table.setFocusPolicy(Qt.NoFocus)
        self.table_result_user_groups.table.setEditTriggers(QAbstractItemView.EditTrigger.NoEditTriggers)
        self.table_result_user_groups.table.setShowGrid(False)
        horizontal_scroll_bar = self.table_result_user_groups.table.horizontalScrollBar()
        horizontal_scroll_bar.setStyleSheet(f'''
                                        QScrollBar::horizontal {{
                                            background-color: transparent;
                                            height: 8px;
                                            width: 48px;
                                            margin: 0px 0px 0px 0px;
                                        }}
                                        QScrollBar::handle:horizontal {{
                                            background-color: #656475;
                                            border-radius: 4px;
                                            min-width: 8px;
                                            width: 48px;
                                        }}
                                        QScrollBar::add-line:horizontal {{
                                            background: none;
                                        }}
                                        QScrollBar::sub-line:horizontal {{
                                            background: none;
                                        }}
                                        QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                                            background: none;
                                        }}
                                        QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                                            width: 0px;
                                            height: 0px;
                                            background: none;
                                        }}
                                ''')

        # Page Indicator:
        self.widget_indicator = QWidget()
        self.widget_indicator.setObjectName('widget_indicator_role')
        # self.widget_indicator.setStyleSheet(
        #     f"""
        #             QWidget {{
        #                 background-color: {Style.PrimaryColor.background};
        #                 color: {Style.PrimaryColor.text_unselected};
        #             }}
        #         """)
        layout_indicator = QHBoxLayout()
        layout_indicator.setContentsMargins(0, 0, 0, 0)
        layout_indicator.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.page_indicator = Pagination()
        self.page_indicator.signal_update_table.connect(self.temp_update_table)
        self.label_total_pages = QLabel(self.tr('Total: 50'))
        self.label_total_pages.setStyleSheet('padding: 16px 0px')
        self.label_item_per_page = QLabel(self.tr('Show records/page: ')+f'10')
        self.label_item_per_page.setStyleSheet('padding: 16px 0px')
        layout_indicator.addWidget(self.label_total_pages)
        layout_indicator.addWidget(self.page_indicator)
        layout_indicator.addWidget(self.label_item_per_page)
        self.widget_indicator.setLayout(layout_indicator)

        widget_table = QWidget()
        layout_table = QVBoxLayout(widget_table)
        layout_table.setContentsMargins(8, 8, 8, 0)
        layout_table.addWidget(self.table_result_user_groups)

        self.main_layout.addWidget(widget_table)
        self.main_layout.addWidget(self.widget_indicator)
        self.setLayout(self.main_layout)

    def set_up_dimension_row(self):
        self.table_result_user_groups.table.setColumnWidth(0, 0.025 * self.widget_width)  # checkbox
        self.table_result_user_groups.table.setColumnWidth(1, 0.1 * self.widget_width)  # STT
        self.table_result_user_groups.table.setColumnWidth(2, 0.2 * self.widget_width)  # name
        self.table_result_user_groups.table.setColumnWidth(3, 0.3 * self.widget_width)  # DESCRIPTION
        self.table_result_user_groups.table.setColumnWidth(4, 0.2 * self.widget_width)  # status
        self.table_result_user_groups.table.setColumnWidth(5, 0.175 * self.widget_width)  # action

    def put_filter_queue(self, msg):
        self.filter_queue.put(msg)
        # Tạm thời comment vì chưa rõ kịch bản đang tìm kiếm
        # self.create_camera_data()
        self.create_roles_data()

    def create_roles_data(self, is_thread=True):
        data = role_model_manager.get_role_list(server_ip=self.controller.server.data.server_ip)
        role_list = []
        for role_model in data.values():
            role_list.append(role_model)

        if is_thread:
            # Search by role name
            while not self.filter_queue.empty():
                msg = self.filter_queue.get()
                if not self.is_loading:
                    self.is_loading = True
                    self.controller.roles_data_filter = []
                    role_dist = []
                    if self.filter_role_name:
                        for role in role_list:
                            if msg in role.data.name:
                                role_dist.append(role)
                    else:
                        role_dist = role_list
                    # print(f"HanhLT: role_dist = {role_dist}")
                    self.page_indicator.set_total_rows_and_total_pages(len(role_dist))
                    self.controller.roles_data_filter = role_dist
                self.is_loading = False
                self.filter_queue.task_done()
        else:
            self.page_indicator.set_total_rows_and_total_pages(len(self.controller.roles_data_filter))
            self.controller.roles_data_filter = role_list
        self.callback_filter_role()

    def temp_update_table(self, data):
        current_page, item_per_page = data
        self.controller.current_roles_table_page = current_page
        if self.checkbox_all_roles.checkState() == Qt.CheckState.Checked:
            self.checkbox_all_roles.setCheckState(Qt.CheckState.Unchecked)
            self.checkbox_all_roles.clearFocus()
        self.update_model()

    def callback_filter_role(self):
        self.create_model()

    def create_model(self):
        if self.page_indicator is not None:
            self.page_indicator.set_total_rows_and_total_pages(len(self.controller.roles_data_filter))
        self.update_model()

    def update_model(self):
        if self.model_table_user_groups is not None:
            self.model_table_user_groups.clear()
        else:
            self.model_table_user_groups = QStandardItemModel()

        if self.table_result_user_groups is not None:
            self.model_table_user_groups.setHorizontalHeaderLabels(self.table_result_user_groups.horizontal_label_list)
            self.table_result_user_groups.table.setModel(self.model_table_user_groups)
        else:
            return
        self.set_up_dimension_row()
        self.model_table_user_groups.setRowCount(self.controller.total_role_items)
        self.label_total_pages.setText(self.tr(f'Total: ')+f'{len(self.controller.roles_data_filter)}')
        # Calculate the start and end rows based on pagination
        start_row = (self.page_indicator.current_page - 1) * self.page_indicator.rows_per_page
        end_row = min(start_row + self.page_indicator.rows_per_page, len(self.controller.roles_data_filter))

        for index_in_page, row in enumerate(range(start_row, end_row)):
            role_data_row: RoleModel = self.controller.roles_data_filter[row]

            for column in range(self.column_init_table):
                if column == 0:
                    model_index = self.model_table_user_groups.index(index_in_page, column)
                    widget = CustomCheckBoxDialogs(parent=self.table_result_user_groups, index=model_index)
                    self.table_result_user_groups.table.setIndexWidget(model_index, widget)
                if column == 1:
                    index = self.model_table_user_groups.index(index_in_page, column)
                    label_stt = CustomLabelRowOrderTable(f"{row + 1}", index_in_page=index_in_page)
                    # label_stt.setAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
                    # label_stt.setStyleSheet(f"color: {Style.PrimaryColor.white_2}")
                    self.table_result_user_groups.table.current_hoverred_row.connect(label_stt.on_hover_row_changed)
                    self.table_result_user_groups.table.setIndexWidget(index, label_stt)
                    self.table_result_user_groups.table.setColumnWidth(column, 0.1 * self.widget_width)
                if column == 2:
                    index = self.model_table_user_groups.index(index_in_page, column)
                    widget_role_name = CustomLabelRoleTable(content=role_data_row.data.name, role_model=role_data_row, key='name', index_in_page=index_in_page)
                    self.table_result_user_groups.table.current_hoverred_row.connect(widget_role_name.on_hover_row_changed)
                    self.table_result_user_groups.table.setIndexWidget(index, widget_role_name)
                    self.table_result_user_groups.table.setColumnWidth(column, 0.2 * self.widget_width)
                if column == 3:
                    index = self.model_table_user_groups.index(index_in_page, column)
                    widget_description = WidgetDescription(description=role_data_row.data.description, label_width=0.2 * self.widget_width,
                                                           role_model=role_data_row, key='description', index_in_page=index_in_page)
                    self.table_result_user_groups.table.current_hoverred_row.connect(widget_description.on_hover_row_changed)
                    self.table_result_user_groups.table.setIndexWidget(index, widget_description)
                    self.table_result_user_groups.table.setColumnWidth(column, 0.3 * self.widget_width)
                if column == 4:
                    index = self.model_table_user_groups.index(index_in_page, column)
                    widget_status = WidgetStatus(controller=self.controller, key='status',
                                                 status=role_data_row.data.status, role_model=role_data_row)
                    self.table_result_user_groups.table.setIndexWidget(index, widget_status)
                    self.table_result_user_groups.table.setColumnWidth(column, 0.2 * self.widget_width)
                if column == 5:
                    index = self.model_table_user_groups.index(index_in_page, column)
                    widget_button_action = WidgetButtonActionsUserRoleTable(controller=self.controller, index=index)
                    self.table_result_user_groups.table.setIndexWidget(index, widget_button_action)
                    self.table_result_user_groups.table.setColumnWidth(column, 0.175 * self.widget_width)

            # if role_data_row.data.name is not None and role_data_row.data.name != '':
            #     item_name = QStandardItem(f"{role_data_row.data.name}")
            #     item_name.setData(f"{role_data_row.data.name}", Config.ROLE_UNDERLINE)
            # else:
            #     item_name = QStandardItem(f"-")
            #     item_name.setData(f"-", Config.ROLE_UNDERLINE)
            #
            # item_name.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            # item_name.setForeground(QBrush(QColor(Style.PrimaryColor.white_2)))
            #
            # self.model_table_user_groups.setItem(index_in_page, 2, item_name)

    def check_all(self, checked):
        for row in range(self.model_table_user_groups.rowCount()):
            index = self.model_table_user_groups.index(row, 0)
            item = self.table_result_user_groups.table.indexWidget(index)
            if item is not None:
                item.checkbox.setCheckState(Qt.CheckState.Checked if checked else Qt.CheckState.Unchecked)

    def on_clicked_table(self, index=None, model_table=None, table=None, checkbox_all=None):
        index_find = model_table.index(index.row(), 0)
        item = table.indexWidget(index_find)
        all_checked = True
        all_unchecked = True
        if item is not None:
            if item.checkbox.checkState() == Qt.CheckState.Checked:
                item.checkbox.setCheckState(Qt.CheckState.Unchecked)
                for row in range(model_table.rowCount()):
                    index = model_table.index(row, 0)
                    item = table.indexWidget(index)
                    if item is not None:
                        if item.checkbox.checkState() != Qt.CheckState.Checked:
                            all_unchecked = False
                            break
                if checkbox_all.checkState() == Qt.CheckState.Checked:
                    if not all_unchecked:
                        self.is_programmatically_changing_combobox = False
                        checkbox_all.setCheckState(Qt.CheckState.Unchecked)
                        self.is_programmatically_changing_combobox = True
            else:
                item.checkbox.setCheckState(Qt.CheckState.Checked)
                for row in range(model_table.rowCount()):
                    index = model_table.index(row, 0)
                    item = table.indexWidget(index)
                    if item is not None:
                        if item.checkbox.checkState() != Qt.CheckState.Checked:
                            all_checked = False

                if all_checked:
                    checkbox_all.setCheckState(Qt.CheckState.Checked)

    def refresh_table(self):
        self.create_roles_data(is_thread=False)

    def retranslate_table_role(self):
        self.model_table_user_groups.clear()
        self.table_result_user_groups.horizontal_label_list = ["", self.tr("NO"), self.tr("USER GROUP NAME"), self.tr("DESCRIPTION"), self.tr("STATUS"), self.tr("ACTIONS")]
        self.create_model()

    def set_dynamic_stylesheet(self):
        self.checkbox_all_roles.setStyleSheet(f"""
            QCheckBox::indicator:checked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_checked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            QCheckBox::indicator:unchecked {{
                border: none;
                image: url({main_controller.get_theme_attribute("Image", "checkbox_unchecked")});
                background-color: transparent;
                width: 16px;
                height: 16px;
            }}
            """)
        
        horizontal_scroll_bar = self.table_result_user_groups.table.horizontalScrollBar()
        horizontal_scroll_bar.setStyleSheet(f'''
                QScrollBar::horizontal {{
                    background-color: transparent;
                    height: 8px;
                    width: 48px;
                    margin: 0px 0px 0px 0px;
                }}
                QScrollBar::handle:horizontal {{
                    background-color: #656475;
                    border-radius: 4px;
                    min-width: 8px;
                    width: 48px;
                }}
                QScrollBar::add-line:horizontal {{
                    background: none;
                }}
                QScrollBar::sub-line:horizontal {{
                    background: none;
                }}
                QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
                    background: none;
                }}
                QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
                    width: 0px;
                    height: 0px;
                    background: none;
                }}
        ''')

        self.table_result_user_groups.set_dynamic_stylesheet()
        self.widget_indicator.setStyleSheet(
        f"""
            QWidget {{
                background-color: {main_controller.get_theme_attribute("Color", "table_page_indicator_background")};
                color: {main_controller.get_theme_attribute("Color", "table_page_indicator_text")};
            }}
        """)

        self.page_indicator.set_dynamic_stylesheet()

    def resize_ui_table_groups(self, widget_width, widget_height):
        self.widget_width = widget_width
        self.widget_height = widget_height
        self.table_result_user_groups.table.verticalHeader().setDefaultSectionSize(0.08 * self.widget_height)
        self.set_up_dimension_row()


class WidgetButtonActionsUserRoleTable(QWidget):
    def __init__(self, parent=None, index=None, controller: Controller = None):
        super().__init__(parent)
        self.setMouseTracking(True)
        self.controller = controller
        self.index = index
        key_value = '{row} - {current_page}'.format(
            row=self.index.row(), current_page=self.controller.current_roles_table_page)

        self.btn_view_info = QPushButton()
        self.btn_view_info.setObjectName('btn_view_info')
        self.btn_view_info.setFixedSize(22, 22)
        self.btn_view_info.setIconSize(QSize(20, 20))
        self.btn_view_info.setToolTip('View')
        self.btn_view_info.clicked.connect(lambda: self.btn_view_info_clicked(index))

        self.btn_edit = QPushButton()
        self.btn_edit.setObjectName('btn_edit')
        self.btn_edit.setFixedSize(22, 22)
        self.btn_edit.setIconSize(QSize(20, 20))
        self.btn_edit.setToolTip('Edit')
        self.btn_edit.clicked.connect(lambda: self.btn_edit_clicked(index))

        self.btn_trash = QPushButton()
        self.btn_trash.setObjectName('btn_trash')
        self.btn_trash.setFixedSize(22, 22)
        self.btn_trash.setIconSize(QSize(20, 20))
        self.btn_trash.setToolTip('Remove')
        self.btn_trash.clicked.connect(lambda: self.btn_trash_clicked(index))

        self.event_clicked = None

        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.setSpacing(8)
        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        layout.addWidget(self.btn_view_info)
        layout.addWidget(self.btn_edit)
        layout.addWidget(self.btn_trash)
        self.set_dynamic_stylesheet()
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)

    def setHideDisabled(self, value):
        self.btn_edit.setDisabled(value)

    def setTrashDisabled(self, value):
        self.btn_trash.setDisabled(value)

    def btn_view_info_clicked(self, index):
        data = self.controller.roles_data_filter[(self.controller.current_roles_table_page - 1) * self.controller.total_role_items + index.row()]
        role_info = RoleInfoDialog(parent=main_controller.list_parent['UserGroupsTableView'], role_model=data, just_view_info=True, controller=self.controller)
        role_info.exec()

    def btn_edit_clicked(self, index):
        data = self.controller.roles_data_filter[
            (self.controller.current_roles_table_page - 1) * self.controller.total_role_items + index.row()]
        role_info = RoleInfoDialog(parent=main_controller.list_parent['UserGroupsTableView'], role_model=data,
                                   just_view_info=False, controller=self.controller)
        role_info.exec()

    def btn_trash_clicked(self, index):
        role_model = self.controller.roles_data_filter[(self.controller.current_roles_table_page - 1) * self.controller.total_role_items + index.row()]
        dialog = WarningDialog()
        result = dialog.exec()
        if result == QDialog.Accepted:
            self.controller.delete_single_role(role_id=role_model.data.id)
        elif result == QDialog.Rejected:
            pass

    def set_dynamic_stylesheet(self):
        self.btn_view_info.setStyleSheet(f"""
            QPushButton {{
                border: None;
                qproperty-icon: url({main_controller.get_theme_attribute("Image", "table_eye")});
            }}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0px;
            }}
            QPushButton:pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0.5px;
            }}
        """
        )
        
        self.btn_edit.setStyleSheet(f"""
            QPushButton {{
                border: None;
                qproperty-icon: url({main_controller.get_theme_attribute("Image", "table_edit")});
            }}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0px;
            }}
            QPushButton:pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0.5px;
            }}
        """
        )
        
        self.btn_trash.setIcon(QIcon(QPixmap(main_controller.get_theme_attribute("Image", "table_trash"))))
        self.btn_trash.setStyleSheet(f"""
            QPushButton {{border: None}}
            QPushButton:hover {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0px;
            }}
            QPushButton:pressed {{
                background-color: {main_controller.get_theme_attribute("Color", "table_action_background_hoverred")};
                border-radius: 4px;
                margin: 0.5px;
            }}
        """
        )
        

class WidgetStatus(QWidget):
    def __init__(self, parent=None, key=None, controller: Controller = None, status=None, role_model: RoleModel = None):
        super().__init__(parent)
        self.role_model = role_model
        self.role_model.change_role_model_signal.connect(self.update_content)
        self.key = key
        self.status = status
        self.setMouseTracking(True)
        self.label_status = QLabel()
        self.load_ui()

    def load_ui(self):
        if self.status == 0:
            self.label_status.setText(self.tr('In-Active'))
        else:
            self.label_status.setText(self.tr('Active'))

        layout_label = QVBoxLayout()
        layout_label.setContentsMargins(0, 0, 0, 0)
        layout_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_label.addWidget(self.label_status)

        self.widget_label = QWidget()
        self.widget_label.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.widget_label.setLayout(layout_label)
        if self.status == 0:
            self.widget_label.setStyleSheet(f'''
                        QWidget{{
                            border-radius: 4px;
                            color: white;
                            background-color: {Style.PrimaryColor.primary};
                            padding: 8px 4px
                        }}
                    ''')
        else:
            self.widget_label.setStyleSheet(f'''
                        QWidget{{
                            border-radius: 4px;
                            color: white;
                            background-color: #4C9008;
                            padding: 8px 4px
                        }}
                    ''')
        self.widget_label.setMinimumWidth(64)

        layout = QHBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.widget_label)
        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def update_content(self, data):
        key, value, model = data
        if key == 'status' and key == self.key:
            self.status = value
            if self.status == 0:
                self.label_status.setText(self.tr('In-Active'))
                self.widget_label.setStyleSheet(f'''
                                        QWidget{{
                                            border-radius: 4px;
                                            color: white;
                                            background-color: {Style.PrimaryColor.primary};
                                            padding: 8px 4px
                                        }}
                                    ''')
            else:
                self.label_status.setText(self.tr('Active'))
                self.widget_label.setStyleSheet(f'''
                                        QWidget{{
                                            border-radius: 4px;
                                            color: white;
                                            background-color: green;
                                            padding: 8px 4px
                                        }}
                                    ''')

class WidgetDescription(QWidget):
    def __init__(self, parent=None, key=None, controller: Controller = None, description=None, label_width=None, role_model: RoleModel = None, index_in_page=None):
        super().__init__(parent)
        self.role_model = role_model
        self.role_model.change_role_model_signal.connect(self.update_content)
        self.key = key
        self.index_in_page = index_in_page
        self.description = description
        self.label_width = label_width
        self.setMouseTracking(False)
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        self.label_description = QLabel()
        if self.description is not None and self.description != '':
            message = self.description
            self.label_description.setToolTip(message)
        else:
            message = "-"
        self.label_description.setText(message)
        self.label_description.setWordWrap(True)
        # self.label_description.setStyleSheet(f'color: {Style.PrimaryColor.white_2}')
        if self.label_width is not None:
            self.label_description.setFixedWidth(self.label_width)

        layout_label = QVBoxLayout()
        layout_label.setContentsMargins(8, 4, 8, 4)
        layout_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout_label.addWidget(self.label_description)
        widget_label = QWidget()
        widget_label.setLayout(layout_label)
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(widget_label)
        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        # self.setStyleSheet(f"""
        #                     QToolTip {{
        #                         background-color: {Style.PrimaryColor.background};
        #                         color: {Style.PrimaryColor.white_2};
        #                         border: 1px solid black;
        #                         padding: 5px;
        #                         font-size: 12px;
        #                         font-family: Arial, sans-serif;
        #                     }}
        #                 """)

    def update_content(self, data):
        key, value, model = data
        if key == 'description' and key == self.key:
            self.label_description.setText(value)

    def on_hover_row_changed(self, row):
        if self.index_in_page == row:
            self.label_description.setStyleSheet(f'color: {Style.PrimaryColor.white_2}')
        else:
            self.label_description.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "table_row_text")}')

    def resizeEvent(self, event):
        self.label_description.setWordWrap(True)

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f"""
            QToolTip {{
                background-color: {Style.PrimaryColor.background};
                color: {Style.PrimaryColor.white_2};
                border: 1px solid black;
                padding: 5px;
                font-size: 12px;
                font-family: Arial, sans-serif;
            }}
        """)
        
        self.label_description.setStyleSheet(f'color: {main_controller.get_theme_attribute("Color", "table_row_text")}')

class CustomLabelRowOrderTable(QLabel):
    def __init__(self, text=None, index_in_page=None):
        super().__init__(text)
        self.index_in_page = index_in_page
        self.setAlignment(Qt.AlignmentFlag.AlignCenter | Qt.AlignmentFlag.AlignVCenter)
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.set_dynamic_stylesheet()

    def set_dynamic_stylesheet(self):
        self.setStyleSheet(f"color: {main_controller.get_theme_attribute('Color', 'table_row_text')};")

    def on_hover_row_changed(self, row):
        if self.index_in_page == row and main_controller.current_theme == Theme.LIGHT:
            self.setStyleSheet(f"QLabel {{color: {Style.PrimaryColor.white_2}; }}")
        else:
            self.set_dynamic_stylesheet()

class CustomLabelRoleTable(QWidget):
    def __init__(self, parent=None, role_model: RoleModel = None, controller: Controller = None, content=None, key=None, index_in_page=None):
        super().__init__(parent)
        self.content = content
        self.key = key
        self.role_model = role_model
        self.index_in_page = index_in_page
        self.controller = controller
        if self.role_model is not None:
            self.role_model.change_role_model_signal.connect(self.update_content)
        main_controller.theme_change_signal.connect(self.set_dynamic_stylesheet)
        self.setMouseTracking(True)
        self.load_ui()
        self.set_dynamic_stylesheet()

    def load_ui(self):
        if self.content is not None and self.content != '':
            message = self.content
        else:
            message = "-"
        self.label_content = QLabel(message)
        self.label_content.setToolTip(self.content)
        self.label_content.toolTip()
        self.label_content.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.label_content.setWordWrap(True)
        # self.label_content.setStyleSheet(f'color: {Style.PrimaryColor.white_2}')
        # if self.label_width is not None:
        #     self.label_list_group_name.setFixedWidth(self.label_width)

        layout_label = QVBoxLayout()
        layout_label.setContentsMargins(8, 4, 8, 4)
        layout_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout_label.addWidget(self.label_content)
        widget_label = QWidget()
        widget_label.setLayout(layout_label)
        widget_label.mousePressEvent = self.on_click_label
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(widget_label)

        # self.setStyleSheet(f"""
        #             QToolTip {{
        #                 background-color: {Style.PrimaryColor.background};
        #                 color: {Style.PrimaryColor.white_2};
        #                 border: 1px solid black;
        #                 padding: 5px;
        #                 font-size: 12px;
        #                 font-family: Arial, sans-serif;
        #             }}
        #             QLabel {{color: {Style.PrimaryColor.white_2}; }}
        #         """)

        self.setLayout(layout)
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    def update_content(self, data):
        key, value, model = data
        if key == 'name' and key == self.key:
            self.content = value
            self.label_content.setText(value)

    def on_click_label(self, event):
        if self.key == "name":
            # user_info = UserInformationDialog(parent=main_controller.list_parent['UsersTableView'], user_model=self.user_model,
            #                                   just_view_infor=False, controller=self.controller)
            # user_info.exec()
            pass
        else:
            pass

    def on_hover_row_changed(self, row):
        if self.index_in_page == row and main_controller.current_theme == Theme.LIGHT:
            self.label_content.setStyleSheet(f"QLabel {{color: {Style.PrimaryColor.white_2}; }}")
        else:
            self.label_content.setStyleSheet(f'color: {self.label_content_color}')

    def enterEvent(self, event):
        if self.key == 'name':
            self.label_content.setStyleSheet(f"QLabel {{ text-decoration: underline; color: {self.label_content_color_hoverred}; }}")

    def leaveEvent(self, event):
        if self.key == 'name':
            self.label_content.setStyleSheet(f"QLabel {{color: {self.label_content_color}; }}")

    def set_dynamic_stylesheet(self):
        self.label_content_color_hoverred = Style.PrimaryColor.primary
        self.label_content_color = main_controller.get_theme_attribute("Color", "table_row_text")
        self.label_content.setStyleSheet(f'color: {self.label_content_color}')

        self.setStyleSheet(f"""
            QToolTip {{
                background-color: {Style.PrimaryColor.background};
                color: {Style.PrimaryColor.white_2};
                border: 1px solid black;
                padding: 5px;
                font-size: 12px;
                font-family: Arial, sans-serif;
            }}
            QLabel {{color: {Style.PrimaryColor.white_2}; }}
        """)
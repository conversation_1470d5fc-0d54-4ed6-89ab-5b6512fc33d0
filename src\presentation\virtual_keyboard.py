
from PySide6.QtCore import Qt,QObject,Signal
from PySide6.QtWidgets import <PERSON>App<PERSON>, QMainWindow, QLabel
from PySide6.QtGui import QKeyEvent
from src.common.onvif_api.worker_thread import WorkerThread
import time
class VirtualKeyBoard(QObject):
    add_key_signal = Signal(int)
    def __init__(self,parent = None):
        super().__init__(parent)
        print(f'__init__')
        self.home_screen = parent
        self.key_thread = WorkerThread(parent=self, target=self.process)
        
    def start(self):
        print(f'start1 = {self.key_thread}')
        self.key_thread.start()

    def process(self):
        print(f'start2')
        time.sleep(15)
        while True:
            print(f'start3')
            self.short_cut_opened(Qt.Key.Key_1)
            time.sleep(5)
            self.short_cut_opened(Qt.Key.Key_2)
            time.sleep(5)
            self.short_cut_opened(Qt.Key.Key_3)
            time.sleep(5)
    
    def short_cut_opened(self,key):
        self.add_key_signal.emit(Qt.Key.Key_Slash)
        # self.add_key(Qt.Key.Key_Slash)
        time.sleep(1)
        self.add_key_signal.emit(key)

        time.sleep(1)
        self.add_key_signal.emit(Qt.Key.Key_Return)



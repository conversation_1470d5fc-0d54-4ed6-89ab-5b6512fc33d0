import src.utils.log_utils as LogUtils
import logging

logger = logging.getLogger(__name__)
from PySide6.QtCore import QSettings
import json
from src.utils.config import Config


class AuthQSettings:
    __instance = None

    def __init__(self):
        self.settings = QSettings("GPS", "Auth Settings")

    @staticmethod
    def get_instance():
        if AuthQSettings.__instance is None:
            AuthQSettings.__instance = AuthQSettings()
        return AuthQSettings.__instance

    def get_refresh_token(self):
        if self.settings.contains("refreshToken"):
            json_string = self.settings.value("refreshToken")
        else:
            json_string = ''
        return json_string

    def get_access_token(self):
        if self.settings.contains("jwt"):
            json_string = self.settings.value("jwt")
        else:
            json_string = ''
        return json_string

    def save_refresh_token(self, refresh_token):
        self.settings.setValue("refreshToken", refresh_token)
        json_string = self.settings.value("refreshToken")

    def save_access_token(self, jwt):
        self.settings.setValue("jwt", jwt)

    def delete_refresh_token(self):
        self.settings.remove("refreshToken")

    def delete_access_token(self):
        self.settings.remove("jwt")

    def set_ip_server(self, ip_server):
        logger.debug(f"set_ip_server: {ip_server}")
        self.settings.setValue("ip_server", ip_server)

    def load_ip_server(self):
        if self.settings.contains("ip_server"):
            return self.settings.value("ip_server")
        else:
            return Config.SERVER_IP_ADDRESS_DEFAULT

    def load_port_server(self):
        if self.settings.contains("port_server"):
            return self.settings.value("port_server")
        else:
            return Config.SERVER_VMS_PORT_DEFAULT

    def set_port_server(self, port_server):
        self.settings.setValue("port_server", port_server)

    def load_port_websocket(self):
        if self.settings.contains("port_websocket"):
            return self.settings.value("port_websocket")
        else:
            return Config.SERVER_EVENT_PORT_DEFAULT

    def set_port_websocket(self, port_websocket):
        self.settings.setValue("port_websocket", port_websocket)

    def delete_ip_server(self):
        self.settings.remove("ip_server")

    def set_username(self, username):
        self.settings.setValue("username", username)

    def load_username(self):
        if self.settings.contains("username"):
            return self.settings.value("username")
        else:
            return None

    def clear_all(self):
        self.settings.clear()

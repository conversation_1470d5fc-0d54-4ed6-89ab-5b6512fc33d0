from PySide6.QtCore import QSettings

class DialogSettings:
    __instance = None

    def __init__(self):
        self.settings = QSettings("GPS", "Dialog Settings")

    @staticmethod
    def get_instance():
        if DialogSettings.__instance is None:
            DialogSettings.__instance = DialogSettings()
        return DialogSettings.__instance

    def get_dont_show_delete_camera_dialog(self):
        """
        Kiểm tra xem có hiển thị dialog xác nhận xóa camera hay không
        
        Returns:
            bool: True nếu không hiển thị dialog, False nếu hiển thị dialog
        """
        if self.settings.contains("DontShowDeleteCameraDialog"):
            return self.settings.value("DontShowDeleteCameraDialog", False, type=bool)
        else:
            return False

    def set_dont_show_delete_camera_dialog(self, value):
        """
        Đặt giá trị cho việc hiển thị dialog xác nhận xóa camera
        
        Args:
            value (bool): True nếu không hiển thị dialog, False nếu hiển thị dialog
        """
        self.settings.setValue("DontShowDeleteCameraDialog", value)

    def reset_all_dialog_settings(self):
        """
        Đặt lại tất cả các cài đặt dialog về mặc định (hiển thị tất cả các dialog)
        """
        self.settings.remove("DontShowDeleteCameraDialog")
        # Thêm các dialog khác ở đây khi cần

    def clear_all_dialog_settings(self):
        """
        Xóa tất cả các settings liên quan đến dialog
        """
        self.settings.clear()

# Singleton instance
dialog_settings = DialogSettings.get_instance()

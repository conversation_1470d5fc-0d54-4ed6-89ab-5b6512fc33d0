from queue import Queue
from typing import Callable, List
import threading
import logging
from PySide6.QtCore import QObject
from src.common.model.camera_model import Camera
import json
from src.common.controller.main_controller import main_controller
from pathlib import Path
# from onvif import ONVIFCamera
import shutil
import av
import re
from threading import RLock
LOCK = RLock()
logger = logging.getLogger(__name__)
def start_threads(number: int, target: Callable, *args) -> List[threading.Thread]:
    threads = []
    for _ in range(number):
        thread = threading.Thread(target=target, args=args)
        thread.daemon = True
        threads.append(thread)
        thread.start()
    return threads

def wait_for(queue: Queue, threads: List[threading.Thread]):
    queue.join()
    [queue.put(None) for _ in range(len(threads))]
    [t.join() for t in threads]

class DumpCameraData:
    def __init__(self, number = 1,selected_folder = None):
        super().__init__()
        self.number = number
        self.camera_queue = Queue()
        self.ip_queue = Queue()
        self.rtsp_queue = Queue()
        self.configuration_options_queue = Queue()
        self.screenshot_queue = Queue()
        self.selected_folder = selected_folder
        self.onvif_camera = {}
        self.media_service = {}
        self.ptz = {}
        self.image_service = {}
        self.profiles = {}

    def start(self,camera_list):
        self.folder_server = Path(self.selected_folder) / f"server_data_{main_controller.api_client.ip_server}"
        create_folder(self.folder_server)
        onvif_threads = start_threads(self.number,self.connect_onvif,self.camera_queue,self.ip_queue)
        profile_threads = start_threads(self.number,self.get_profiles,self.ip_queue,self.rtsp_queue,self.configuration_options_queue)
        rtsp_threads = start_threads(self.number,self.get_list_cameras,self.rtsp_queue,self.screenshot_queue)
        configuration_options_threads = start_threads(self.number,self.get_configuration_options,self.configuration_options_queue)
        screenshot_threads = start_threads(self.number,self.get_screenshot,self.screenshot_queue)
        for camera in camera_list:
            self.camera_queue.put(camera)

        wait_for(self.camera_queue,onvif_threads)
        wait_for(self.ip_queue,profile_threads)
        wait_for(self.rtsp_queue,rtsp_threads)
        wait_for(self.configuration_options_queue,configuration_options_threads)
        wait_for(self.screenshot_queue,screenshot_threads)
        logger.debug(f'Hoan thanh CheckOnvif')
    
    def connect_onvif(self,input_queue: Queue, output_queue: Queue) -> None:
        while True:
            camera:Camera = input_queue.get()
            if camera is None:
                break
            logger.debug(f'connect_onvif = {camera.id} {camera.name}')
            rtsp = camera.urlMainstream
            try:
                result = scan_address_ip(rtsp)
                
                if result['detect'] == 'success':
                    ip= result['ip']
                    username = result['username']
                    password = result['password']
                    port = result['port']
                    is_check_ping = ping(ip)
                    if is_check_ping:
                        # logger.debug(f'connect_onvif done {ip} {username} {password} {port}')
                        if ip != None and username != None and password != None and port != None:
                            ip_folder = self.folder_server / ip
                            create_folder(ip_folder)
                            try:
                                # self.onvif_camera[ip] = ONVIFCamera(ip, port, username, password)
                                self.media_service[ip] = self.onvif_camera[ip].create_media_service()
                                self.ptz[ip] = self.onvif_camera[ip].create_ptz_service()
                                self.image_service[ip] = self.onvif_camera[ip].create_imaging_service()
                                msg = {'ip':ip, 'username': username,'password':password, 'ip_folder': str(ip_folder)}
                                output_queue.put(msg)

                                logger.debug(f'connect_onvif done {ip} {username} {password} {port}')
                            except Exception as e:
                                logger.debug('Connect ONVIFCamera fail')
                    else:
                        logger.debug(f'ping fail')
                else:
                    logger.debug(f'loi dinh dang')
            except Exception as e:
                logger.debug(f'link rtsp fail = {e}')

            input_queue.task_done()
    def get_profiles(self,input_queue: Queue, rtsp_queue: Queue,configuration_options_queue: Queue) -> None:
        while True:
            msg = input_queue.get()
            if msg is None:
                break
            ip = msg['ip']
            username = msg['username']
            password = msg['password']
            ip_folder = Path(msg['ip_folder'])
            logger.debug(f'get_profiles = {ip} {ip_folder}')
            if self.media_service[ip] != None:
                try:
                    self.profiles[ip] = self.media_service[ip].GetProfiles()
                    profile_file = ip_folder / "profiles.txt"
                    create_file(profile_file)
                    with profile_file.open("a") as f:
                        f.write(f"{self.profiles[ip]}\n")
                    msg = {'ip':ip, 'username': username,'password':password, 'ip_folder': str(ip_folder)}
                    rtsp_queue.put(msg)
                    configuration_options_queue.put(msg)

                except Exception as e:
                    logger.debug(f'get_profiles error {e}')
            input_queue.task_done()

    def get_list_cameras(self,input_queue: Queue, output_queue: Queue)-> None:
        while True:
            msg = input_queue.get()
            if msg is None:
                break
            ip = msg['ip']
            username = msg['username']
            password = msg['password']
            ip_folder = Path(msg['ip_folder'])
            profiles = self.profiles[ip]

            if self.media_service[ip] != None:
                try:
                    list_cameras = []
                    if profiles != None:
                        for index, profile in enumerate(profiles):
                            stream_setup = {
                                'StreamSetup': {
                                    'Stream': 'RTP-Unicast',
                                    'Transport': {
                                        'Protocol': 'RTSP'
                                    }
                                },
                                'ProfileToken': profile.token
                            }
                            stream_uri = self.media_service[ip].GetStreamUri(stream_setup)
                            rtsp = stream_uri.Uri
                            rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
                            list_cameras.append(rtsp)
                    if len(list_cameras) > 0:
                        image_file = ip_folder / "images"
                        create_folder(image_file)
                        html_file = ip_folder / "index.html"
                        generate_html(html_file)
                        rtsp_file = ip_folder / "rtsps.txt"
                        create_file(rtsp_file)
                        with rtsp_file.open("a") as f:
                            for rtsp in list_cameras:
                                f.write(f"{rtsp}\n")
                                msg = {'rtsp':rtsp,'image_file': str(image_file),'html_file': str(html_file)}
                                output_queue.put(msg)
                        # msg = {'ip':ip, 'username': username,'password':password}
                        # output_queue.put(msg)
                except Exception as e:
                    logger.debug(f'get_profiles error {e}')
            input_queue.task_done()

    def get_configuration_options(self,input_queue: Queue)-> None:
        while True:
            msg = input_queue.get()
            if msg is None:
                break
            ip = msg['ip']
            username = msg['username']
            password = msg['password']
            ip_folder = Path(msg['ip_folder'])
            profiles = self.profiles[ip]
            if profiles != None:
                configuration_options_file = ip_folder / "configuration_options.txt"
                create_file(configuration_options_file)
                with configuration_options_file.open("a") as f:
                    for index, profile in enumerate(profiles):
                        try:
                            ptz_configuration_options = self.ptz[ip].GetConfigurationOptions({'ConfigurationToken':profile.token})
                            f.write(f"{ptz_configuration_options}\n")
                        except Exception as e:
                            logger.debug(f'get_configuration_options error {e}')
            input_queue.task_done()

    def get_screenshot(self,input_queue: Queue) -> None:
        while True:
            msg = input_queue.get()
            if msg is None:
                break

            rtsp = msg['rtsp']
            image_file = Path(msg['image_file'])
            html_file = Path(msg['html_file'])
            image = self.get_image(rtsp,image_file)
            if image:
                with LOCK:
                    self.append_result(image,rtsp,html_file)

            input_queue.task_done()

    def get_image(self,rtsp_url: str, image_file: Path):
        try:
            with av.open(
                    rtsp_url,
                    timeout=30.0,
            ) as container:
                stream = container.streams.video[0]
                if _is_video_stream(stream):
                    file_name = escape_chars(f"{rtsp_url.lstrip('rtsp://')}.jpg")
                    file_path = image_file / file_name
                    stream.thread_type = "AUTO"
                    frame_count = 0
                    for frame in container.decode(video=0):
                        if frame_count == 200:
                            frame.to_image().save(file_path)
                            break
                        frame_count += 1
                    return file_path

        except Exception as e:
            pass

    def append_result(self,image: Path,rtsp: str,html_file: Path):
        # Insert to .html gallery file
        if image.exists():
            with html_file.open("a") as f:
                f.write(
                    (
                        '<div class="responsive"><div class="gallery">\n'
                        f'<img src="{image.parent.name}/{image.name}" alt="{rtsp}" '
                        'width="600" height="400" onclick="f(this)"></div></div>\n\n'
                    )
                )

def create_folder(path: Path):
    if path.exists():
        shutil.rmtree(path)
    path.mkdir(parents=True)

def create_file(path: Path):
    if logger.isEnabledFor(logging.DEBUG):
        logger.debug(f"Creating {path}")
    path.touch()

def _is_video_stream(stream):
    return (
            stream.profile is not None
            and stream.start_time is not None
            and stream.codec_context.format is not None
    )

def escape_chars(s: str):
    # Escape every character that's not a letter,
    # '_', '-', '.' or space with an '_'.
    return re.sub(r"[^\w\-_. ]", "_", s)

def generate_html(path: Path):
    html = (
        f"<!DOCTYPE html>\n<html>\n<head>\n<title>{path.parent.name}</title>\n"
        """<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<style>   
html{background-color: #141414}
p{text-align: center;color: white;font-family: monospace;}
img{cursor: pointer;border: 2px solid #707070;}
img:hover {border: 2px solid white;}
div.gallery img {width: 100%;height: auto;}
*{box-sizing: border-box;}
.responsive {padding: 6px 6px;float: left;width: 25%;}
@media only screen and (max-width: 700px){.responsive {width: 50%;margin: 1px 0;}}
@media only screen and (max-width: 500px){.responsive {width: 100%;}}
</style>
<link rel="shortcut icon" href="data:image/png;base64,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"/>
</head><body>
<script>window.onload = function () {
var totalNumberOfImages = document.querySelectorAll("div.responsive").length;
document.getElementById("total").innerHTML = ":: Total images: " + totalNumberOfImages + " ::";};
function f(img){
navigator.clipboard.writeText(img.alt);}
</script>
<p id="total"></p>
<p>:: With Javascript enabled: Click on the image to get the corresponding RTSP link ::</p>\n\n"""
    )
    if logger.isEnabledFor(logging.DEBUG):
        logger.debug(f"Generating {path}")
    path.write_text(html)

# def get_list_cameras1(self,input_queue: Queue)-> None:
#     while True:
#         ip = input_queue.get()
#         if ip is None:
#             break
#         profiles = self.profiles[ip]
#         if self.media_service[ip] != None:
#             try:
#                 list_cameras = {}
#                 if profiles != None:
#                     video_encoder_configurations = None
#                     try:
#                         video_encoder_configurations = self.media_service[ip].GetVideoEncoderConfigurations()
#                     except Exception as e:
#                         video_encoder_configurations = None
#                         return None
#                     for index, profile in enumerate(profiles):
#                         video_source_configuration_token = profile.VideoSourceConfiguration.token
#                         if video_source_configuration_token not in list_cameras:
#                             # mac dinh profile dau tien video_source_configuration_token giong nhau se gan la luong mainstream
#                             camera = Camera(state=False, status=False)
#                             camera.profileToken = profile.token
#                             camera.name = str(index)
#                             camera.ipAddress = ipCamera
#                             camera.username = username
#                             camera.password = password
#                             camera.port = port
#                             if len(video_encoder_configurations) != 0:
#                                 target_encoder_configuration = video_encoder_configurations[index]
#                                 if target_encoder_configuration.Encoding == "H264":
#                                     # Lấy các tùy chọn cấu hình encoder
#                                     options = media_service.GetVideoEncoderConfigurationOptions({'ConfigurationToken': target_encoder_configuration.token})
#                                     resolutions = []
#                                     for item in options.H264.ResolutionsAvailable:
#                                         resolution = {'width': item.Width,'height': item.Height}
#                                         resolutions.append(resolution)
#                                     camera.supportedMainResolution = json.dumps(resolutions)
#                                     camera.mainstreamResolution = str(target_encoder_configuration.Resolution.Width) + 'x' + str(target_encoder_configuration.Resolution.Height)

#                                     fps = []
#                                     if options.H264.FrameRateRange != None:
#                                         fps_min = options.H264.FrameRateRange['Min']
#                                         fps_max = options.H264.FrameRateRange['Max']
#                                         for i in range(fps_min, fps_max + 1):
#                                             fps.append(i)
#                                         camera.supportedMainFps = json.dumps(fps)
#                                     camera.mainstreamFps = json.dumps(target_encoder_configuration.RateControl.FrameRateLimit)
                                
#                             stream_setup = {
#                                 'StreamSetup': {
#                                     'Stream': 'RTP-Unicast',
#                                     'Transport': {
#                                         'Protocol': 'RTSP'
#                                     }
#                                 },
#                                 'ProfileToken': profile.token
#                             }
#                             stream_uri = media_service.GetStreamUri(stream_setup)
#                             rtsp = stream_uri.Uri
#                             rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
#                             camera.urlMainstream = rtsp
#                             list_cameras[video_source_configuration_token] = camera
                            
#                         else:
#                             camera = list_cameras[video_source_configuration_token]
#                             if camera.urlSubstream == None:
#                                 # chi get them 1 luong substream thoi
#                                 if len(video_encoder_configurations) != 0:
#                                     target_encoder_configuration = video_encoder_configurations[index]
#                                     if target_encoder_configuration.Encoding == "H264":
#                                         # Lấy các tùy chọn cấu hình encoder
#                                         options = media_service.GetVideoEncoderConfigurationOptions({'ConfigurationToken': target_encoder_configuration.token})
#                                         resolutions = []
#                                         for item in options.H264.ResolutionsAvailable:
#                                             resolution = {'width': item.Width,'height': item.Height}
#                                             resolutions.append(resolution)
#                                         camera.supportedSubResolution = json.dumps(resolutions)
#                                         camera.substreamResolution = str(target_encoder_configuration.Resolution.Width) + 'x' + str(target_encoder_configuration.Resolution.Height)
#                                         fps = []
#                                         if options.H264.FrameRateRange != None:
#                                             fps_min = options.H264.FrameRateRange['Min']
#                                             fps_max = options.H264.FrameRateRange['Max']
#                                             for i in range(fps_min, fps_max + 1):
#                                                 fps.append(i)
#                                             camera.supportedSubFps = json.dumps(fps)
#                                         camera.substreamFps = json.dumps(target_encoder_configuration.RateControl.FrameRateLimit)
#                                 stream_setup = {
#                                     'StreamSetup': {
#                                         'Stream': 'RTP-Unicast',
#                                         'Transport': {
#                                             'Protocol': 'RTSP'
#                                         }
#                                     },
#                                     'ProfileToken': profile.token
#                                 }
#                                 stream_uri = media_service.GetStreamUri(stream_setup)
#                                 rtsp = stream_uri.Uri
#                                 rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
#                                 camera.urlSubstream = rtsp
#             except Exception as e:
#                 logger.debug(f'get_profiles error {e}')
#         input_queue.task_done()
    

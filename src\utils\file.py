import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
from PySide6.QtCore import QObject
from PySide6.QtWidgets import QFileDialog
from openpyxl import load_workbook
from collections import namedtuple
import openpyxl
from src.common.model.camera_model import Camera,CameraModel,camera_model_manager
from src.utils.dump_camera_data import DumpCameraData
from src.common.controller.main_controller import main_controller
import threading
from src.utils.config import Config
class SelectFile(QObject):
    def __init__(self,file_path = None):
        super().__init__()
        self.file_path = file_path
        self.workbook = None
        self.list_objects = []
        if self.file_path is not None:
            try:
                self.workbook = load_workbook(self.file_path)
            except Exception as e:
                logger.debug(e)
    def get_objects(self):
        if self.workbook is not None:
            try:
                sheet = self.workbook['Sheet1']
                for row in sheet.iter_rows(min_row=1, values_only=True):
                    # Thay đổi tên cột không hợp lệ thành tên hợp lệ
                    if row != sheet[1]:
                        column1 = row[0]
                        column2 = row[1]
                        column3 = row[2]
                        column4 = row[3]
                        column5 = row[4]
                        column6 = row[5]
                        column7 = row[6]
                        column8 = row[7]
                        if column7 is not None:
                            # Chuyển đổi hàng thành đối tượng namedtuple
                            RowObject = namedtuple('RowObject', ['camera_name', 'username', 'password', 'ip', 'port', 'manufacturer', 'rtsp', 'group'])
                            row_object = RowObject(column1, column2, column3,column4, column5, column6, column7, column8)
                            self.list_objects.append(row_object)
            except Exception as e:
                logger.debug(e)
        return self.list_objects
    
class SaveFile(QObject):
    def __init__(self,list_cameras = [],parent = None):
        super().__init__(parent)
        self.parent1 = parent
        self.list_cameras = list_cameras
        self.workbook = openpyxl.Workbook()
        self.sheet = self.workbook.active
        self.sheet.title = 'Sheet1'
        self.callback_save_file_result = None
    def add_object_to_file(self):
        try:
            merge_range = 'A1:H1'
            # Hợp nhất các ô trong phạm vi đã chọn
            self.sheet.merge_cells(merge_range)

            # Ghi thông tin vào ô hợp nhất
            merged_cell = self.sheet[merge_range.split(':')[0]]
            merged_cell.value = 'Camera List'
            length_list_cameras = len(self.list_cameras)
            for index, row in enumerate(self.sheet.iter_rows(min_row=1, max_row=length_list_cameras + 2, min_col=1, max_col=8)):
                if row == self.sheet[2]:
                    row[0].value = 'name'
                    row[1].value = 'username'
                    row[2].value = 'password'
                    row[3].value = 'ip'
                    row[4].value = 'port'
                    row[5].value = 'manufacturer'
                    row[6].value = 'rtsp'
                    row[7].value = 'group'
                elif row == self.sheet[1]:
                    pass
                else:
                    camera: Camera = self.list_cameras[index - 2].data 
                    row[0].value = camera.name
                    if camera.username is not None:
                        row[1].value = camera.username
                    if camera.password is not None:
                        row[2].value = camera.password
                    if camera.ipAddress is not None:
                        row[3].value = camera.ipAddress
                    if camera.port is not None:
                        row[4].value = camera.port
                    #row[5].value = 'manufacturer'
                    if camera.urlMainstream is not None:
                        row[6].value = camera.urlMainstream
                    if camera.cameraGroupIds is not None and len(camera.cameraGroupIds) != 0:
                        #logger.debug(f'GroupDTO = {camera.cameraGroupDTOList}')
                        text = ''
                        for item in camera.cameraGroupDTOList:
                            if item != camera.cameraGroupDTOList[-1]:
                                text = text + item['name'] + ', '
                            else:
                                text = text + item['name']
                        row[7].value = text
            return True
        
        except Exception as e:
            logger.debug(f'error = {e}')
            return False
    
    def save_workbook(self):
        # Hiển thị hộp thoại lựa chọn đường dẫn lưu file
        file_dialog = QFileDialog(self.parent1)
        file_dialog.setAcceptMode(QFileDialog.AcceptSave)
        file_dialog.setDefaultSuffix(".xlsx")
        file_dialog.setNameFilters(["Excel Files (*.xlsx)"])
        if file_dialog.exec():
            file_path = file_dialog.selectedFiles()[0]
            # Kiểm tra xem file có phần mở rộng .xlsx hay không
            if not file_path.endswith(".xlsx"):
                file_path += ".xlsx"

            
            # Thực hiện các thao tác lưu file...
            self.workbook.save(file_path)
            if self.callback_save_file_result is not None:
                self.callback_save_file_result(True)
        else:
            if self.callback_save_file_result is not None:
                self.callback_save_file_result(False)
                # Thiết lập các tùy chọn cho QFileDialog
        if Config.ENABLE_DOWNLOAD_DATA:
            file_dialog.setFileMode(QFileDialog.FileMode.Directory)

            # Hiển thị QFileDialog và lấy đường dẫn thư mục được chọn
            selected_folder = file_dialog.getExistingDirectory(None, "Chọn thư mục", "")
            if selected_folder:
                logger.debug("Đường dẫn thư mục được chọn:", selected_folder)
                if len(camera_model_manager.get_camera_list()) > 0:
                    data = DumpCameraData(number=len(camera_model_manager.get_camera_list()),selected_folder=selected_folder)
                    self.data_thread = threading.Thread(target=data.start,args=(camera_model_manager.get_camera_list(),))
                    self.data_thread.start()
            else:
                logger.debug("Không có thư mục được chọn")

import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)
import cv2
from PySide6 import QtGui
from PySide6.QtGui import QImage, QPixmap
import threading


class ImageUtils:

    def get_snapshot_from_rtsp_link(rtsp_link, callback):
        # create run in thread
        def run_in_thread(callback: callable):
            cap = cv2.VideoCapture(rtsp_link)
            ret, frame = cap.read()
            cap.release()
            callback(frame)

        # run thread and callback
        thread = threading.Thread(target=run_in_thread, args=(callback,))
        thread.start()

    # how to use
    # def return_preview_frame(frame):
    #    if frame is not None:
    #         logger.debug('image data: ', len(frame))
    #         end_time = time.time()
    #         logger.debug('time run: ', end_time - start_time)

    # ImageUtils.get_snapshot_from_rtsp_link(
    #     rtsp_link="rtsp://ai_dev:123654789@@@192.168.15.10:554/Streaming/Channels/601", callback=return_preview_frame)

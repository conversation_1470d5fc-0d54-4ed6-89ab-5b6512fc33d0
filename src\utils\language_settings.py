from PySide6.QtCore import QSettings
import json
from src.styles.style import Style


class LanguageQSettings:
    __instance = None

    def __init__(self):
        self.settings = QSettings("GPS", "Language Global")

    @staticmethod
    def get_instance():
        if LanguageQSettings.__instance is None:
            LanguageQSettings.__instance = LanguageQSettings()
        return LanguageQSettings.__instance

    def get_current_language(self):
        if self.settings.contains("currentLanguage"):
            return self.settings.value("currentLanguage")
        else:
            return ''

    def get_current_language_image(self):
        if self.settings.contains("currentImage"):
            return self.settings.value("currentImage")
        else:
            return ''

    def set_current_language(self, current_language):
        self.settings.setValue("currentLanguage", current_language)

    def set_current_image(self, current_image):
        self.settings.setValue("currentImage", current_image)

    def delete_current_language(self):
        self.settings.remove("currentLanguage")

    def delete_current_image(self):
        self.settings.remove("currentImage")



import os
import logging.config
import tempfile
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import sys
import platform

# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Log levels with colors
LOG_LEVELS = {
    'DEBUG': Colors.BLUE,
    'INFO': Colors.GREEN,
    'WARNING': Colors.YELLOW,
    'ERROR': Colors.RED,
    'CRITICAL': Colors.RED + Colors.BOLD
}

def create_dir_if_not_exists(directory: str):
    print(f"Creating directory {directory} if it does not exist")
    if not os.path.exists(directory):
        os.makedirs(directory)

# Set up logging directory
temp_dir = tempfile.gettempdir()
logs_dir = os.path.join(temp_dir, "GPSLogs")
print(f"logs_dir: {logs_dir}")
create_dir_if_not_exists(logs_dir)

# Log file path
log_file = os.path.join(logs_dir, f"log_{datetime.now().strftime('%Y-%m-%d')}.txt")

class ColoredFormatter(logging.Formatter):
    def format(self, record):
        # Add color to log level
        if record.levelname in LOG_LEVELS:
            record.levelname = f"{LOG_LEVELS[record.levelname]}{record.levelname}{Colors.ENDC}"
        
        # Add thread ID
        record.thread_id = f"[Thread-{record.thread}]"
        
        # Add timestamp in milliseconds
        record.msecs = int(record.msecs)
        
        return super().format(record)

# Logging configuration
logging_schema = {
    "version": 1,
    "formatters": {
        "standard": {
            "()": ColoredFormatter,
            "format": "%(asctime)s.%(msecs)03d - %(levelname)s - %(thread_id)s - %(filename)s:%(lineno)d - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "detailed": {
            "()": ColoredFormatter,
            "format": "%(asctime)s.%(msecs)03d - %(levelname)s - %(thread_id)s - %(name)s - %(filename)s:%(lineno)d - %(funcName)s - %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "standard",
            "level": "INFO",  # Changed to DEBUG to show all levels
            "stream": sys.stdout,
        },
        "file": {
            "class": "logging.handlers.TimedRotatingFileHandler",
            "formatter": "detailed",
            "level": "INFO",  # Changed to DEBUG to show all levels
            "filename": log_file,
            "when": "midnight",
            "backupCount": 4,
            "encoding": "utf-8"
        }
    },
    "root": {
        "level": "INFO",  # Changed to DEBUG to show all levels
        "handlers": ["console", "file"],
        "propagate": False
    }
}

logging.config.dictConfig(logging_schema)

# Get the root logger
logger = logging.getLogger()

def log_app_init(app_name, version, platform_info=None):
    """
    Log application initialization information with enhanced formatting
    
    Args:
        app_name: Name of the application
        version: Version of the application
        platform_info: Optional dictionary with platform information
    """
    if platform_info is None:
        platform_info = {
            "os": platform.system(),
            "os_version": platform.version(),
            "python_version": platform.python_version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        }
    
    # Create a visually appealing header
    header = f"{Colors.BOLD}{Colors.BLUE}{'='*20} {app_name} v{version} Initialization {'='*20}{Colors.ENDC}"
    logger.info(header)
    
    # Log system information with colors
    logger.info(f"{Colors.GREEN}Start time:{Colors.ENDC} {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"{Colors.GREEN}OS:{Colors.ENDC} {platform_info['os']} {platform_info['os_version']}")
    logger.info(f"{Colors.GREEN}Python:{Colors.ENDC} {platform_info['python_version']}")
    logger.info(f"{Colors.GREEN}Machine:{Colors.ENDC} {platform_info['machine']}")
    logger.info(f"{Colors.GREEN}Processor:{Colors.ENDC} {platform_info['processor']}")
    logger.info(f"{Colors.GREEN}Log file:{Colors.ENDC} {log_file}")
    
    # Footer
    logger.info(f"{Colors.BOLD}{Colors.BLUE}{'='*60}{Colors.ENDC}")

def log_exception(exc_type, exc_value, exc_traceback):
    """Enhanced exception logging with color coding"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # Log the exception with color coding
    logger.critical(f"{Colors.RED}Unhandled exception:{Colors.ENDC}", exc_info=(exc_type, exc_value, exc_traceback))

# Set the exception hook to log unhandled exceptions
sys.excepthook = log_exception

# Example usage of different log levels
def log_examples():
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")

# Uncomment to test logging
# log_examples()

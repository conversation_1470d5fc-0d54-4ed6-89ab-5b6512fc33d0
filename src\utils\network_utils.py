import src.utils.log_utils as LogUtils
from onvif import ONVIFCamera, exceptions
import socket
import ipaddress
import logging
from wsdiscovery.discovery import ThreadedWSDiscovery as WSDiscovery
from wsdiscovery import Scope
import re
import threading
from src.common.model.camera_model import AddCameraType
logger = logging.getLogger(__name__)
def scan_onvif_cameras1(username, password, ipaddress, port):
    cameras = []

    # Tạo gói multicast để tìm kiếm các dịch vụ ONVIF trên mạng
    multicast_group = '***************'
    multicast_port = 3702
    message = """<?xml version="1.0" encoding="UTF-8"?>
                <e:Envelope xmlns:e="http://www.w3.org/2003/05/soap-envelope"
                    xmlns:w="http://schemas.xmlsoap.org/ws/2004/08/addressing"
                    xmlns:d="http://schemas.xmlsoap.org/ws/2005/04/discovery"
                    xmlns:dn="http://www.onvif.org/ver10/network/wsdl">
                  <e:Header>
                    <w:MessageID>uuid:4a4c7c3d-9f3e-4a51-bc8e-42be0eab99b9</w:MessageID>
                    <w:To>urn:schemas-xmlsoap-org:ws:2005:04:discovery</w:To>
                    <w:Action>
                      http://schemas.xmlsoap.org/ws/2005/04/discovery/Probe
                    </w:Action>
                  </e:Header>
                  <e:Body>
                    <d:Probe>
                      <d:Types>dn:NetworkVideoTransmitter</d:Types>
                    </d:Probe>
                  </e:Body>
                </e:Envelope>"""

    # Tạo socket UDP để gửi gói multicast
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    sock.settimeout(2)
    if username != '' and password != '' and ipaddress != '' and port != '':
        try:
            # Tạo đối tượng ONVIFCamera với tên người dùng và mật khẩu
            camera = ONVIFCamera(ipaddress, 80, username, password)
            # Lấy thông tin thiết bị
            device_info = camera.devicemgmt.GetDeviceInformation()
            # Lấy thông tin mô tả
            manufacturer = device_info.Manufacturer
            model = device_info.Model
            firmware_version = device_info.FirmwareVersion
            rtsp_list = get_list_rtsp(camera = camera,username=username, password=password)
            #data = (ipaddress, username, password, 80,rtsp_list, manufacturer)
            data = {'username': username, 'password': password, 'ip': ipaddress, 'port': 80, 'rtsp_list': rtsp_list, 'manufacturer': manufacturer}
            cameras.append(data)
        except exceptions.ONVIFError as e:
            # Không thành công kết nối hoặc không hỗ trợ ONVIF
            logger.critical(f"ONVIFError = {e}")
    else:
        # Gửi gói multicast và chờ phản hồi
        try:
            sock.sendto(message.encode(), (multicast_group, multicast_port))
            while True:
                try:
                    data, address = sock.recvfrom(4096)
                    ip_address, port = address
                    # Kiểm tra nội dung gói phản hồi
                    if b'NetworkVideoTransmitter' in data:
                        # Trích xuất địa chỉ IP từ gói phản hồi
                        ip_start = data.find(b'XAddrs>') + 7
                        ip_end = data.find(b'</', ip_start)
                        ip = data[ip_start:ip_end].decode()
                        try:
                            # Tạo đối tượng ONVIFCamera với tên người dùng và mật khẩu
                            camera = ONVIFCamera(ip, 80, username, password)
                            # Lấy thông tin thiết bị
                            device_info = camera.devicemgmt.GetDeviceInformation()
                            # Lấy thông tin mô tả
                            manufacturer = device_info.Manufacturer
                            model = device_info.Model
                            firmware_version = device_info.FirmwareVersion
                            rtsp_list = get_list_rtsp(camera = camera,username=username, password=password)
                            #data = (ip_address, username, password, 80,rtsp_list, manufacturer)
                            data = {'username': username, 'password': password, 'ip': ip_address, 'port': 80, 'rtsp_list': rtsp_list, 'manufacturer': manufacturer}
                            cameras.append(data)
                        except exceptions.ONVIFError as e:
                            # Không thành công kết nối hoặc không hỗ trợ ONVIF
                            logger.critical(f"ONVIFError = {e}")
                except socket.timeout as e:
                    logger.critical(f"socket.timeout = {e}")
        finally:
            sock.close()

    return cameras

def scan_onvif_cameras(username, password, ipaddress, port):
    cameras = []
    if username != '' and password != '' and ipaddress != '' and port != '':
        data = {}
        try:
            # Tạo đối tượng ONVIFCamera với tên người dùng và mật khẩu
            camera = ONVIFCamera(ipaddress, 80, username, password)
            # Lấy thông tin thiết bị
            device_info = camera.devicemgmt.GetDeviceInformation()
            # Lấy thông tin mô tả
            manufacturer = device_info.Manufacturer
            model = device_info.Model
            firmware_version = device_info.FirmwareVersion
            temp_data = get_list_rtsp(camera = camera,username=username, password=password)
            if temp_data is not None:
                for key,data in temp_data.items():
                    # if data['profileToken'] == 'MediaProfile01800' or data['profileToken'] == 'MediaProfile04000':
                    data.update({'username': username, 'password': password, 'ip': ipaddress, 'port': 80,'manufacturer': manufacturer,'camera_type':AddCameraType.ONVIF,'camera_model':model})
                    cameras.append(data)
        except exceptions.ONVIFError as e:
            # Không thành công kết nối hoặc không hỗ trợ ONVIF
            logger.critical(f"ONVIFError = {e}")
    else:
        wsd = WSDiscovery()
        scope1 = Scope("onvif://www.onvif.org/Profile")
        wsd.start()
        services = wsd.searchServices(scopes=[scope1])
        ipaddresses = []
        for service in services:
            xaddr = service.getXAddrs()[0] 
            ipaddress = re.search('(\d+|\.)+', str(xaddr)).group(0)
            if ':80/onvif/device_service' in xaddr or ':554/onvif/device_service' in xaddr or not ipaddress + ':' in xaddr:
                ipaddresses.append(ipaddress)
            # logger.debug(display(service.getScopes()))
        wsd.stop()
        
        if len(ipaddresses) != 0:
            thread_list = []
            for ip_camera in ipaddresses:
                thread = threading.Thread(target=check_onvif_ip,args=(ip_camera,80,username,password,cameras,))
                thread.start()
                thread_list.append(thread)
            for thread in thread_list:
                if thread is not None:
                    thread.join()
    return cameras

def check_onvif_ip(ip_camera,port,username,password,cameras):
    data = {}
    try:
         # Tạo đối tượng ONVIFCamera với tên người dùng và mật khẩu
        camera = ONVIFCamera(ip_camera, 80, username, password)
        # Lấy thông tin thiết bị
        device_info = camera.devicemgmt.GetDeviceInformation()
        # Lấy thông tin mô tả
        manufacturer = device_info.Manufacturer
        model = device_info.Model
        firmware_version = device_info.FirmwareVersion
        # rtsp_list, data = get_list_rtsp(camera = camera,username=username, password=password)
        # #data = (ip_address, username, password, 80,rtsp_list, manufacturer)
        
        # data.update({'username': username, 'password': password, 'ip': ip_camera, 'port': 80, 'rtsp_list': rtsp_list, 'manufacturer': manufacturer})
        # cameras.append(data)
        temp_data = get_list_rtsp(camera = camera,username=username, password=password)
        if temp_data is not None:
            for key,data in temp_data.items():
                data.update({'username': username, 'password': password, 'ip': ip_camera, 'port': 80,'manufacturer': manufacturer,'camera_type':AddCameraType.ONVIF,'camera_model':model})
                cameras.append(data)
    except exceptions.ONVIFError as e:
        # Không thành công kết nối hoặc không hỗ trợ ONVIF
        logger.critical(f"ONVIFError = {e}")

def get_list_rtsp(camera:ONVIFCamera = None, username = None, password = None):
    cameras = {}
    try:
        media_service = camera.create_media_service()
        profiles = media_service.GetProfiles()
        if len(profiles) != 0:
            for profile in profiles:
                # logger.debug(f'profile = {profile}')
                # break
                video_source_configuration_token = profile.VideoSourceConfiguration.token
                if video_source_configuration_token not in cameras:
                    camera = {'profileToken':profile.token}
                    stream_setup = {
                        'StreamSetup': {
                            'Stream': 'RTP-Unicast',
                            'Transport': {
                                'Protocol': 'RTSP'
                            }
                        },
                        'ProfileToken': profile.token
                    }
                    stream_uri = media_service.GetStreamUri(stream_setup)
                    rtsp = stream_uri.Uri
                    rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
                    camera['rtsp_list'] = [rtsp]

                    if profile.PTZConfiguration is not None:
                        camera['is_ptz'] = True
                    else:
                        camera['is_ptz'] = None
                    cameras[video_source_configuration_token] = camera
                else:
                    camera = cameras[video_source_configuration_token]
                    if len(camera['rtsp_list']) == 1:
                        stream_setup = {
                            'StreamSetup': {
                                'Stream': 'RTP-Unicast',
                                'Transport': {
                                    'Protocol': 'RTSP'
                                }
                            },
                            'ProfileToken': profile.token
                        }
                        stream_uri = media_service.GetStreamUri(stream_setup)
                        rtsp = stream_uri.Uri
                        rtsp = rtsp.replace("rtsp://", f"rtsp://{username}:{password}@")
                        camera['rtsp_list'].append(rtsp)
        return cameras
    except Exception as e:
        logger.debug(f'error get_list_rtsp {e}')
    return None

def get_ip_list():
    ip_list = []
    # Tạo một socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

    try:
        # Kết nối socket tới một địa chỉ IP tạm thời
        sock.connect(("*******", 80))

        # Lấy địa chỉ IP của máy tính
        ip_address = sock.getsockname()[0]
        ip = ipaddress.IPv4Address(ip_address)

        # Lấy địa chỉ mạng bằng cách xóa các bit cuối của địa chỉ IP
        network_address = ipaddress.IPv4Address(int(ip) & int(ipaddress.IPv4Address('*************')))

        # Tạo đối tượng IPv4Network từ địa chỉ mạng và mặt nạ mạng
        network = ipaddress.IPv4Network(f'{network_address}/24')
        for ip in network:
            ip_list.append(str(ip))
    finally:
        # Đóng socket
        sock.close()

    return ip_list


import platform    # For getting the operating system name
import subprocess  # For executing a shell command

def ping(host):
    """
    Returns True if host (str) responds to a ping request.
    Remember that a host may not respond to a ping (ICMP) request even if the host name is valid.
    """

    # Option for the number of packets as a function of
    param = '-n' if platform.system().lower() == 'windows' else '-c'

    # Building the command. Ex: "ping -n 1 google.com" on Windows
    command = ['ping', param, '1', host]

    # Use CREATE_NO_WINDOW flag on Windows to suppress the command window
    print(f'Ping: ping: {command}')
    creation_flags = subprocess.CREATE_NO_WINDOW if platform.system().lower() == 'windows' else 0
    startupinfo = None
    if platform.system().lower() == 'windows':
        startupinfo = subprocess.STARTUPINFO()
        startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
    # Redirect stdout and stderr to subprocess.DEVNULL to suppress output
    process = subprocess.Popen(command, creationflags=creation_flags, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, startupinfo=startupinfo)
    process.wait()

    return process.returncode == 0

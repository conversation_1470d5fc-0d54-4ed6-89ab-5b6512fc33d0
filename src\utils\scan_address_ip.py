import re
import src.utils.log_utils as LogUtils
import logging
logger = logging.getLogger(__name__)

rtsp = "rtsp://admin:abdb@ff@dd@*************"
def scan_address_ip(rtsp):
    # Phuong phap detect ip username duoi day chi dung khi 'username' va 'password' khong chua ky tu dac biet la (':','/')
    logger.debug(f'rtsp = {rtsp}')
    # detect 1
    # format  rtsp://user:password@<DeviceIP>:port/profile
    try:
        url = re.findall(r'rtsp://(.*?):(.*?):', rtsp)[0]
        if len(url) == 2:
            username = url[0]
            pass_ip = url[1]
            list_string = pass_ip.split('@')
            if len(list_string) > 1:
                ip = list_string[-1]
                password = pass_ip.split('@' + ip)[0]
                logger.debug(f'ip = {ip}')
                logger.debug(f'password = {password}')
                return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
            else:
                pass
        else:
            pass
    except Exception as e:
        pass
    # detect 2
    # format rtsp://user:password@<ip>/
    try:
        url = re.findall(r'rtsp://(.*?)/', rtsp)[0]
        username, pass_ip = url.split(':')
        list_string = pass_ip.split('@')
        if len(list_string) > 1:
            ip = list_string[-1]
            password = pass_ip.split('@' + ip)[0]
            return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
        else:
            pass

    except Exception as e:
        pass
    # detect 3
    # format rtsp://user:password@<ip>
    try:
        # detect 'rtsp://' co trong link khong
        protocol,url2 = rtsp.split('rtsp://')
        username, pass_ip = url2.split(':')
        list_string = pass_ip.split('@')
        if len(list_string) > 1:
            ip = list_string[-1]
            password = pass_ip.split('@' + ip)[0]
            return {'username': username,'ip': ip, 'password': password, 'port': 80,'detect':'success'}
        else:
            pass
    except Exception as e:
        pass
    # detect 4
    # format rtsp://<ip>:<port>/
    try:
        url = re.findall(r'rtsp://(.*?)/', rtsp)[0]
        ip, port = url.split(':')
        return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
    except Exception as e:
        pass

    # detect 5
    # format rtsp://<ip>/
    try:
        ip = re.findall(r'rtsp://(.*?)/', rtsp)[0]
        return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
    except Exception as e:
        pass
    
    # detect 6
    # format rtsp://<ip>
    try:
        protocol, ip = rtsp.split('rtsp://')
        return {'username': None,'ip': ip, 'password': None, 'port': 80,'detect':'success'}
    except Exception as e:
        pass

    return {'username': None,'ip': None, 'password': None, 'port': 80,'detect':'error'}


        
# result = scan_address_ip(rtsp)
# logger.debug(f'result = {result}')
    





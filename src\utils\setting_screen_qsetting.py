from PySide6.QtCore import QSettings

from src.styles.style import Style


class SettingScreenQSettings:
    __instance = None

    def __init__(self):
        self.settings = QSettings("GPS", "Setting Global")

    @staticmethod
    def get_instance():
        if SettingScreenQSettings.__instance is None:
            SettingScreenQSettings.__instance = SettingScreenQSettings()
        return SettingScreenQSettings.__instance

    def clear_all_setting_key(self):
        self.settings.clear()

    def get_current_language(self):
        if self.settings.contains("currentLanguage"):
            return self.settings.value("currentLanguage")
        else:
            return Style.Language.en

    def set_current_language(self, current_language):
        self.settings.setValue("currentLanguage", current_language)

    def delete_current_language(self):
        self.settings.remove("currentLanguage")

    # QSetting Warning Method
    def get_current_warning_method(self):
        if self.settings.contains("warning_method"):
            value = self.settings.value("warning_method")
        else:
            value = []
        return value

    def set_current_warning_method(self, warning_method):
        self.settings.setValue("warning_method", warning_method)

    def delete_current_warning_method(self):
        self.settings.remove("warning_method")

    # QSetting Alert Channel
    def get_current_alert_channel(self):
        if self.settings.contains("current_alert_channel"):
            value = self.settings.value("current_alert_channel")
        else:
            value = []
        return value

    def set_current_alert_channel(self, current_alert_channel):
        self.settings.setValue("current_alert_channel", current_alert_channel)

    def delete_current_alert_channel(self):
        self.settings.remove("current_alert_channel")

    # QSetting Highlight Type
    def get_current_highlight_type(self):
        if self.settings.contains("current_highlight_type"):
            value = self.settings.value("current_highlight_type")
        else:
            value = -1
        return value

    def set_current_highlight_type(self, current_highlight_type):
        self.settings.setValue("current_highlight_type", current_highlight_type)

    def delete_current_highlight_type(self):
        self.settings.remove("current_highlight_type")

    # QSetting Duration 2light
    def get_duration_highlight(self):
        if self.settings.contains("duration_highlight"):
            value = self.settings.value("duration_highlight")
        else:
            value = 30
        return value

    def set_duration_highlight(self, duration_highlight: int):
        self.settings.setValue("duration_highlight", duration_highlight)

    def delete_duration_highlight(self):
        self.settings.remove("duration_highlight")

    # Setting time unit
    def get_time_unit_highlight(self):
        if self.settings.contains("time_unit_highlight"):
            value = self.settings.value("time_unit_highlight")
        else:
            value = 0
        return value

    def set_time_unit_highlight(self, time_unit_highlight: int):
        self.settings.setValue("time_unit_highlight", time_unit_highlight)

    def delete_time_unit_highlight(self):
        self.settings.remove("time_unit_highlight")

    # QSetting switch button disable alert
    def get_state_switch_disable_alert(self):
        if self.settings.contains("switch_disable_alert"):
            value_setting = self.settings.value("switch_disable_alert")
            value = False if (value_setting == 'false' or value_setting == False) else True
        else:
            value = False
        return value

    def set_state_switch_disable_alert(self, switch_disable_alert: bool):
        self.settings.setValue("switch_disable_alert", switch_disable_alert)

    def delete_state_switch_disable_alert(self):
        self.settings.remove("switch_disable_alert")


    
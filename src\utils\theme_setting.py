from src.styles.dark_theme_color import DarkThemeColor
from src.styles.light_theme_color import LightThemeColor
from src.styles.style import Theme
from PySide6.QtCore import QSettings, Qt
from PySide6.QtWidgets import QApplication


DEFAULT_THEME = Theme.LIGHT

class ThemeSettings:
    __instance = None

    def __init__(self):
        self.settings = QSettings("GPS", "Setting Global")

    @staticmethod
    def get_instance():
        if ThemeSettings.__instance is None:
            ThemeSettings.__instance = ThemeSettings()
        
        return ThemeSettings.__instance
    
    def set_theme_color_to_qsetting(self, theme):
        self.settings.setValue("currentTheme", theme)

    def get_theme_from_setting(self):
        if self.settings.contains("currentTheme"):
            theme_value = self.settings.value("currentTheme")
            return self.key_to_theme(theme_value)
        else:
            return DEFAULT_THEME
        
    def get_theme_color_from_qsetting(self):
        if self.settings.contains("currentTheme"):
            theme_value = self.settings.value("currentTheme")
            return theme_value
        else:
            self.settings.setValue("currentTheme", "LIGHT")
            return "LIGHT"
    
    def key_to_theme(self, key):
        match key:
            case "LIGHT":
                return Theme.LIGHT
            case "DARK":
                return Theme.DARK
            case "AUTO":
                return self.device_theme()
            case _:
                return DEFAULT_THEME
            
    def device_theme(self):
        app = QApplication.instance() or QApplication([])
        colorScheme = app.styleHints().colorScheme()
        match colorScheme:
            case Qt.ColorScheme.Light:
                return Theme.LIGHT
            case Qt.ColorScheme.Dark:
                return Theme.DARK
            case _:
                return DEFAULT_THEME

theme_setting = ThemeSettings.get_instance()
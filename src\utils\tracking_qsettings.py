import json
from typing import List

from PySide6.QtCore import QSettings


class TrackingQSettings:
    __instance = None

    def __init__(self):
        self.settings = QSettings("GPS", "Tracking Script")
        self.list_tracking_data = []

    @staticmethod
    def get_instance():
        if TrackingQSettings.__instance is None:
            TrackingQSettings.__instance = TrackingQSettings()
        return TrackingQSettings.__instance

    def clear_tracking_setting_key(self):
        self.settings.clear()

    def save_tracking_dialog_models(self, key=None, list_tracking_data=[]):
        # Neu luu duoi dang dict thi se dung logic nay
        # Convert the list_tracking_data in each item
        # for item in list_tracking_data:
        #     item['list_tracking_data'] = [model.to_dict() for model in item['list_tracking_data']]

        data = [model.to_dict() for model in list_tracking_data]
        new_json_string = json.dumps(data)
        self.settings.setValue(key, new_json_string)

    def get_list_tracking_data(self, server_ip=None, key=None):
        if self.settings.contains(key):
            value = self.settings.value(key)
            self.list_tracking_data = json.loads(value)
            # tracking_data_list = [TrackingDialogModel.from_dict(item) for item in data_list]
        else:
            self.list_tracking_data = []
        return self.list_tracking_data


tracking_qsetting = TrackingQSettings().get_instance()

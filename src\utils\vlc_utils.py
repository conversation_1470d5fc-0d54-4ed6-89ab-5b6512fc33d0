import os
import sys
import platform
import logging

logger = logging.getLogger(__name__)

def init_vlc_environment():
    """Initialize VLC environment variables based on platform"""
    if sys.platform == 'win32':
        return init_vlc_windows()
    elif sys.platform == 'darwin':
        # Get the application bundle path
        if getattr(sys, 'frozen', False):
            # Running in a PyInstaller bundle
            bundle_dir = os.path.normpath(os.path.dirname(sys.executable))
            if '.app' in bundle_dir:
                bundle_dir = os.path.normpath(os.path.join(bundle_dir, '..', '..'))
            
            # Determine architecture
            arch = 'arm64' if platform.machine() == 'arm64' else 'intel'
            vlc_path = os.path.join(bundle_dir, 'Contents', 'Frameworks', 'lib_3rdparty', 'vlc', 'mac', arch)
            logger.debug(f'Checking VLC path RELEASE: {vlc_path}')
        else:
            # Running in development
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
            arch = 'arm64' if platform.machine() == 'arm64' else 'intel'
            vlc_path = os.path.join(project_root, 'lib_3rdparty', 'vlc', 'mac', arch)
            logger.debug(f'Checking VLC path DEV: {vlc_path}')

        if os.path.exists(vlc_path):
            logger.debug(f'VLC path found at {vlc_path}')
            lib_path = os.path.join(vlc_path, 'libvlc.dylib')
            logger.debug(f'Checking VLC lib path: {lib_path}')
            plugins_path = os.path.join(vlc_path, 'plugins')
            logger.debug(f'Checking VLC plugins path: {plugins_path}')
            
            if os.path.exists(lib_path) and os.path.exists(plugins_path):
                # Set environment variables before loading the library
                os.environ['DYLD_LIBRARY_PATH'] = vlc_path
                os.environ['DYLD_FALLBACK_LIBRARY_PATH'] = vlc_path
                os.environ['PYTHON_VLC_MODULE_PATH'] = vlc_path
                os.environ['PYTHON_VLC_LIB_PATH'] = lib_path
                os.environ['VLC_PLUGIN_PATH'] = plugins_path
                
                # Check library dependencies
                try:
                    import ctypes
                    # Load libvlccore first
                    logger.debug('Loading libvlccore.dylib')
                    vlccore_path = os.path.join(vlc_path, 'libvlccore.dylib')
                    ctypes.CDLL(vlccore_path, mode=ctypes.RTLD_GLOBAL)
                    
                    # Then load libvlc
                    logger.debug(f'Loading libvlc.dylib')
                    vlc_lib = ctypes.CDLL(lib_path, mode=ctypes.RTLD_GLOBAL)
                    logger.debug('VLC libraries loaded successfully')
                    return vlc_path, lib_path, plugins_path
                except Exception as e:
                    logger.error(f'Failed to load VLC library: {e}')
                    # Check library dependencies
                    import subprocess
                    try:
                        output = subprocess.check_output(['otool', '-L', lib_path], text=True)
                        logger.debug(f'VLC library dependencies:\n{output}')
                        output = subprocess.check_output(['otool', '-L', vlccore_path], text=True)
                        logger.debug(f'VLC core library dependencies:\n{output}')
                    except Exception as e:
                        logger.error(f'Failed to check VLC dependencies: {e}')
        
        logger.error(f'VLC libraries not found at {vlc_path}')
        return None, None, None
    elif sys.platform.startswith('linux'):
        return init_vlc_linux()
    else:
        logger.warning(f'Unsupported platform: {sys.platform}')
        return None, None, None

def init_vlc_windows():
    """Initialize VLC environment for Windows"""
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
    vlc_path = os.path.join(project_root, 'lib_3rdparty', 'vlc', 'win')
    logger.debug(f'Checking VLC path: {vlc_path}')
    if os.path.exists(vlc_path):
        os.environ['PYTHON_VLC_MODULE_PATH'] = vlc_path
        os.environ['PYTHON_VLC_LIB_PATH'] = os.path.join(vlc_path, 'libvlc.dll')
        os.environ['VLC_PLUGIN_PATH'] = os.path.join(vlc_path, 'plugins')
        return vlc_path, os.environ['PYTHON_VLC_LIB_PATH'], os.environ['VLC_PLUGIN_PATH']
    else:
        logger.warning(f'Warning: VLC path not found at {vlc_path}')
        return None, None, None

def init_vlc_linux():
    """Initialize VLC environment for Linux"""
    if os.path.exists('/usr/lib/x86_64-linux-gnu/libvlc.so'):
        os.environ['PYTHON_VLC_MODULE_PATH'] = '/usr/lib/x86_64-linux-gnu'
        os.environ['PYTHON_VLC_LIB_PATH'] = '/usr/lib/x86_64-linux-gnu/libvlc.so'
        os.environ['VLC_PLUGIN_PATH'] = '/usr/lib/x86_64-linux-gnu/vlc/plugins'
        return (os.environ['PYTHON_VLC_MODULE_PATH'], 
                os.environ['PYTHON_VLC_LIB_PATH'], 
                os.environ['VLC_PLUGIN_PATH'])
    else:
        logger.warning('Warning: VLC libraries not found in expected Linux paths')
        return None, None, None 
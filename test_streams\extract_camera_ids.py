import json

def generate_camera_url(camera_id, stream_number):
    base_url = "https://implacable-ai.ai-vlab.com/camera/"
    return f"{base_url}{camera_id}_{stream_number}.flv"

def extract_camera_ids(file_path):
    try:
        # Read the JSON file
        with open(file_path, 'r') as file:
            cameras = json.load(file)
        
        # Extract IDs for all cameras except AVIGILON
        camera_ids = [camera['id'] for camera in cameras if camera.get('type') != 'AVIGILON']
        
        if not camera_ids:
            print("No non-AVIGILON cameras found in the file")
            return []
        
        # Create a list to store all URLs
        all_urls = []
        
        # Generate and store URLs for each camera
        for camera_id in camera_ids:
            urls = [
                f"Camera ID: {camera_id}",
                f"Stream 0 URL: {generate_camera_url(camera_id, 0)}",
                f"Stream 1 URL: {generate_camera_url(camera_id, 1)}",
                "---"
            ]
            all_urls.extend(urls)
            
            # Print to console
            for url in urls:
                print(url)
        
        # Save to file
        with open('camera_urls.txt', 'w') as f:
            f.write('\n'.join(all_urls))
            
        print("\nURLs have been saved to 'camera_urls.txt'")
        return camera_ids
            
    except FileNotFoundError:
        print(f"Error: File {file_path} not found")
    except json.JSONDecodeError:
        print("Error: Invalid JSON format")
    except Exception as e:
        print(f"An error occurred: {str(e)}")

if __name__ == "__main__":
    # Use the file path
    file_path = "test_streams/all_cameras.txt"
    extract_camera_ids(file_path)

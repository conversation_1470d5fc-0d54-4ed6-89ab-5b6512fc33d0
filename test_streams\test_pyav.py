import sys
import time
import logging
import threading
from queue import Queue
from pyav_wrapper import PyAVWrapper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def read_camera_urls(file_path):
    """Read camera URLs from file."""
    urls = []
    try:
        with open(file_path, 'r') as f:
            for line in f:
                if line.startswith('Stream'):
                    # Extract URL from the line
                    url = line.split('URL: ')[1].strip()
                    urls.append(url)
        return urls
    except Exception as e:
        logger.error(f"Error reading camera URLs: {str(e)}")
        return []

class StreamTester:
    def __init__(self, url):
        self.url = url
        self.wrapper = PyAVWrapper()
        self.is_running = False
        self.thread = None
        self.success = False
        self.current_stream_link = None
        self.stream_link = url
        self.count = 0
        self.start_time = time.time()

    def start(self):
        """Start the stream testing thread."""
        self.is_running = True
        self.thread = threading.Thread(target=self._stream_worker)
        self.thread.daemon = True
        self.thread.start()

    def stop(self):
        """Stop the stream testing thread."""
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=5.0)
        self.wrapper.close_stream()
        self.wrapper.close_container()

    def _stream_worker(self):
        """Worker thread for handling stream operations."""
        logger.debug(f'Starting stream worker for URL: {self.url}')
        
        while self.is_running:
            try:
                # Check if we need to connect
                if self.current_stream_link != self.stream_link or self.wrapper is None:
                    self.current_stream_link = self.stream_link
                    self.count += 1
                    
                    # Try to connect
                    success = self.wrapper.open_stream(self.url)
                    
                    if not success:
                        logger.error(f'Failed to open stream: {self.url}')
                        self.is_running = False
                        break
                    else:
                        logger.debug(f'Successfully opened stream: {self.url}')
                        self.success = True
                        break

            except Exception as e:
                logger.error(f'Stream error: {e}')
                self.wrapper.close_stream()
                self.wrapper.close_container()
                self.is_running = False
                break

        logger.debug(f'Stream worker stopped for URL: {self.url}')
        self.wrapper.close_stream()
        self.wrapper.close_container()

def test_streams(urls, test_duration=30, batch_size=50):
    """Test multiple streams concurrently in batches."""
    total_cameras = len(urls)
    all_successful_streams = []
    total_active_connections = 0
    total_processed = 0  # Counter for total processed cameras
    
    # Process URLs in batches
    for batch_start in range(0, total_cameras, batch_size):
        batch_end = min(batch_start + batch_size, total_cameras)
        batch_urls = urls[batch_start:batch_end]
        total_processed += len(batch_urls)
        logger.debug(f'\nProcessing batch {batch_start//batch_size + 1}: cameras {batch_start+1} to {batch_end} of {total_cameras}')
        logger.debug(f'Total processed so far: {total_processed}/{total_cameras} cameras')
        
        testers = []
        successful_streams = []
        active_connections = 0
        tester_status = {}
        started_count = 0
        
        try:
            # Start all stream testers in current batch
            logger.debug(f'Starting to initialize {len(batch_urls)} cameras in current batch...')
            for url in batch_urls:
                tester = StreamTester(url)
                tester.start()
                testers.append(tester)
                started_count += 1
                tester_status[url] = {
                    'status': 'started',
                    'retry_count': 0,
                    'last_error': None
                }
                logger.debug(f'Started testing stream: {url} ({started_count}/{len(batch_urls)})')
            
            logger.debug(f'Successfully started {started_count}/{len(batch_urls)} cameras in current batch')

            # Wait for test duration
            time.sleep(test_duration)

            # Check results and collect successful streams
            for tester in testers:
                if tester.success:
                    successful_streams.append(tester.url)
                    active_connections += 1
                    tester_status[tester.url]['status'] = 'success'
                else:
                    tester_status[tester.url]['status'] = 'failed'
            
            # Update totals
            all_successful_streams.extend(successful_streams)
            total_active_connections += active_connections
            
            # Log batch results
            logger.debug(f'\nBatch {batch_start//batch_size + 1} completed:')
            logger.debug(f'Successful streams in this batch: {len(successful_streams)}/{len(batch_urls)}')
            logger.debug(f'Active connections in this batch: {active_connections}/{len(batch_urls)}')
            logger.debug(f'Total progress: {total_processed}/{total_cameras} cameras processed')
            logger.debug(f'Total successful so far: {len(all_successful_streams)}/{total_processed}')
            
            # Log detailed status for this batch
            logger.debug('\nDetailed StreamTester Status for this batch:')
            for url, status in tester_status.items():
                logger.debug(f'URL: {url}')
                logger.debug(f'  Status: {status["status"]}')
                logger.debug(f'  Retry Count: {status["retry_count"]}')
                if status['last_error']:
                    logger.debug(f'  Last Error: {status["last_error"]}')
                logger.debug('---')
            
            # Write successful streams to file after each batch
            with open('test_streams/successful_streams.txt', 'w') as f:
                for url in all_successful_streams:
                    f.write(f"{url}\n")
            
            logger.debug(f'Updated successful streams file with {len(all_successful_streams)} total successful streams')

        except KeyboardInterrupt:
            logger.error('Testing interrupted by user')
            break
        finally:
            # Stop all testers in current batch
            for tester in testers:
                tester.stop()
    
    # Final summary
    logger.debug(f'\nFinal Summary:')
    logger.debug(f'Total cameras processed: {total_processed}/{total_cameras}')
    logger.debug(f'Total successful streams: {len(all_successful_streams)}/{total_processed}')
    logger.debug(f'Total active connections: {total_active_connections}/{total_processed}')
    logger.debug(f'All successful streams have been saved to test_streams/successful_streams.txt')

def main():
    """Main function to test all camera streams."""
    # Read camera URLs from file
    urls = read_camera_urls('test_streams/camera_urls.txt')
    
    if not urls:
        logger.error('No camera URLs found in camera_urls.txt')
        return
        
    logger.debug(f'Found {len(urls)} camera URLs to test')
    
    # Test all streams in batches of 50
    test_streams(urls, batch_size=50)

if __name__ == "__main__":
    main() 
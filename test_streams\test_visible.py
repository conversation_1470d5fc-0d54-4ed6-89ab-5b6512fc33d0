from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, 
                              QVBoxLayout, QPushButton, QLabel, 
                              QHBoxLayout, QStackedWidget)
from PySide6.QtCore import Qt, QEvent, Signal
import sys

class VisibleMonitorWidget(QWidget):
    visibilityChanged = Signal(bool)  # Signal emitted when visibility changes
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._is_actually_visible = False
        self.installEventFilter(self)
        
    def eventFilter(self, obj, event):
        if obj == self:
            if event.type() in [QEvent.Show, QEvent.Hide, QEvent.WindowStateChange]:
                self.check_visibility()
        return super().eventFilter(obj, event)
        
    def check_visibility(self):
        """Check if widget is actually visible and emit signal if changed"""
        new_visibility = self.is_actually_visible()
        if new_visibility != self._is_actually_visible:
            self._is_actually_visible = new_visibility
            self.visibilityChanged.emit(new_visibility)
            
    def is_actually_visible(self):
        """Check if widget is actually visible to user"""
        # 1. Check if widget itself is visible
        if not self.isVisible():
            return False
            
        # 2. Check if visible to parent
        if not self.isVisibleTo(self.parentWidget()):
            return False
            
        # 3. Check if window is minimized
        if self.window().windowState() & Qt.WindowMinimized:
            return False
            
        # 4. Check if in stacked widget and is current widget
        if isinstance(self.parentWidget(), QStackedWidget):
            stacked_widget = self.parentWidget()
            if stacked_widget.currentWidget() != self:
                return False
                
        return True
        
    def showEvent(self, event):
        super().showEvent(event)
        self.check_visibility()
        
    def hideEvent(self, event):
        super().hideEvent(event)
        self.check_visibility()
        
    def changeEvent(self, event):
        super().changeEvent(event)
        if event.type() == QEvent.WindowStateChange:
            self.check_visibility()

def is_widget_actually_visible(widget):
    """
    Kiểm tra xem widget có thực sự visible với người dùng không
    """
    # 1. Kiểm tra widget có được set visible không
    if not widget.isVisible():
        return False
        
    # 2. Kiểm tra widget có visible đối với parent không
    if not widget.isVisibleTo(widget.parentWidget()):
        return False
        
    # 3. Kiểm tra widget có nằm trong vùng nhìn thấy của cửa sổ không
    if not widget.rect().intersects(widget.visibleRegion().boundingRect()):
        return False
        
    # 4. Kiểm tra cửa sổ có bị minimize không
    if widget.window().windowState() & Qt.WindowMinimized:
        return False
        
    return True

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Test Overlay Views")
        self.setGeometry(100, 100, 800, 600)
        
        # Tạo central widget
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        # Tạo layout chính
        main_layout = QVBoxLayout()
        self.central_widget.setLayout(main_layout)
        
        # Tạo stacked widget để quản lý các view
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)
        
        # Tạo các view
        self.create_view_a()
        self.create_view_b()
        
        # Set View A as initial view
        self.stacked_widget.setCurrentWidget(self.view_a)
        
        # Tạo control panel
        self.create_control_panel(main_layout)
        
        # Tạo label hiển thị kết quả
        self.result_label = QLabel("View Status: ")
        main_layout.addWidget(self.result_label)
        
    def create_view_a(self):
        # Tạo view A
        self.view_a = VisibleMonitorWidget()
        self.view_a.setStyleSheet("background-color: lightblue;")
        
        # Tạo layout cho view A
        layout_a = QVBoxLayout()
        self.view_a.setLayout(layout_a)
        
        # Thêm các widget vào view A
        label_a = QLabel("View A")
        label_a.setStyleSheet("font-size: 24px; color: blue;")
        layout_a.addWidget(label_a)
        
        # Kết nối signal visibility
        self.view_a.visibilityChanged.connect(lambda visible: self.on_view_a_visibility_changed(visible))
        
        # Thêm view A vào stacked widget
        self.stacked_widget.addWidget(self.view_a)
        
    def create_view_b(self):
        # Tạo view B (overlay)
        self.view_b = VisibleMonitorWidget()
        self.view_b.setStyleSheet("background-color: rgba(255, 200, 200, 0.8);")
        
        # Tạo layout cho view B
        layout_b = QVBoxLayout()
        self.view_b.setLayout(layout_b)
        
        # Thêm các widget vào view B
        label_b = QLabel("View B (Overlay)")
        label_b.setStyleSheet("font-size: 24px; color: red;")
        layout_b.addWidget(label_b)
        
        # Kết nối signal visibility
        self.view_b.visibilityChanged.connect(lambda visible: self.on_view_b_visibility_changed(visible))
        
        # Thêm view B vào stacked widget
        self.stacked_widget.addWidget(self.view_b)
        
    def on_view_a_visibility_changed(self, visible):
        self.update_view_status()
        
    def on_view_b_visibility_changed(self, visible):
        self.update_view_status()
        
    def update_view_status(self):
        # Cập nhật label
        status = f"View A: {'Visible' if self.view_a.is_actually_visible() else 'Not Visible'}, "
        status += f"View B: {'Visible' if self.view_b.is_actually_visible() else 'Not Visible'}"
        print(status)
        self.result_label.setText(f"View Status: {status}")
        
        # Cập nhật màu nền của các view
        if self.view_a.is_actually_visible():
            self.view_a.setStyleSheet("background-color: lightgreen;")
        else:
            self.view_a.setStyleSheet("background-color: lightblue;")
            
        if self.view_b.is_actually_visible():
            self.view_b.setStyleSheet("background-color: rgba(200, 255, 200, 0.8);")
        else:
            self.view_b.setStyleSheet("background-color: rgba(255, 200, 200, 0.8);")

    def create_control_panel(self, layout):
        # Tạo panel điều khiển
        control_panel = QWidget()
        control_layout = QHBoxLayout()
        control_panel.setLayout(control_layout)
        layout.addWidget(control_panel)
        
        # Button để hiển thị view B
        self.show_overlay_button = QPushButton("Show Overlay (View B)")
        self.show_overlay_button.clicked.connect(self.show_view_b)
        control_layout.addWidget(self.show_overlay_button)
        
        # Button để ẩn view B
        self.hide_overlay_button = QPushButton("Hide Overlay (View B)")
        self.hide_overlay_button.clicked.connect(self.hide_view_b)
        control_layout.addWidget(self.hide_overlay_button)
        
    def show_view_b(self):
        # Hiển thị view B
        self.stacked_widget.setCurrentWidget(self.view_b)
        
    def hide_view_b(self):
        # Ẩn view B, hiển thị view A
        self.stacked_widget.setCurrentWidget(self.view_a)

def main():
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
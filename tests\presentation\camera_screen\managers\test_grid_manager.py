import unittest
import uuid
from unittest.mock import Mock, patch
from PySide6.QtCore import QObject, Signal, Slot, QModelIndex
from src.presentation.camera_screen.managers.grid_manager import GridModel, GridManager

class TestGridModel(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        data = {
            "id": uuid.uuid4(),
            "name": "ahihii",
            "type": "Invalid",
            "isShow": True,
            "currentGrid": {},
            "listGridData": {},
            "listGridCustomData": "NewGrid",
            "direction": {}
        }
        self.grid_model = GridModel(data = data)
        self.mock_controller = Mock()
        self.grid_model.controller = self.mock_controller
        
        # Mock camera model
        self.mock_camera_model = Mock()
        self.mock_camera_model.id = "test_camera"
        self.mock_camera_model.name = "Test Camera"
        self.mock_camera_model.url = "rtsp://test.com/stream"
        self.mock_camera_model.supportsPTZ = True
        
        # Mock controller to return camera model
        self.mock_controller.get_camera_model.return_value = self.mock_camera_model

    def test_initialization(self):
        """Test initial state of GridModel."""
        self.assertEqual(self.grid_model._columns, 1)
        self.assertEqual(self.grid_model._rows, 1)
        self.assertEqual(self.grid_model._is_maximized, False)
        self.assertEqual(self.grid_model._active_item_position, None)
        self.assertEqual(self.grid_model._next_position, 0)
        self.assertEqual(self.grid_model._layout, [])
        self.assertEqual(self.grid_model._isSave, True)

    def test_add_camera(self):
        """Test adding a camera to the grid."""
        position = 0
        camera_data = {
            "id": "test_camera",
            "name": "Test Camera",
            "url": "rtsp://test.com/stream"
        }
        
        # Mock the camera model lookup
        with patch('src.presentation.camera_screen.managers.grid_manager.camera_model_manager.get_camera_model', return_value=self.mock_camera_model):
            self.grid_model.addCamera(position, camera_data)
            
            # Verify camera was added
            self.assertIn(position, self.grid_model._active_cells)
            self.assertEqual(self.grid_model._active_cells[position]["id"], "test_camera")
            # Verify controller was called to get camera model
            self.mock_controller.get_camera_model.assert_called_once_with("test_camera")

    def test_remove_video(self):
        """Test removing a video from the grid."""
        # First add a camera
        position = 0
        camera_data = {
            "id": "test_camera",
            "name": "Test Camera",
            "url": "rtsp://test.com/stream"
        }
        self.grid_model.addCamera(position, camera_data)
        
        # Then remove it
        self.grid_model.removeVideo(position)
        
        # Verify camera was removed
        self.assertNotIn(position, self.grid_model._active_cells)

    def test_swap_positions(self):
        """Test swapping positions of two cameras."""
        # Add two cameras
        camera1_data = {"id": "camera1", "name": "Camera 1", "url": "rtsp://test.com/1"}
        camera2_data = {"id": "camera2", "name": "Camera 2", "url": "rtsp://test.com/2"}
        
        self.grid_model.addCamera(0, camera1_data)
        self.grid_model.addCamera(1, camera2_data)
        
        # Swap positions
        self.grid_model.swapPositions(0, 1)
        
        # Verify swap
        self.assertEqual(self.grid_model._active_cells[0]["id"], "camera2")
        self.assertEqual(self.grid_model._active_cells[1]["id"], "camera1")

    def test_maximize_grid(self):
        """Test maximizing a grid position."""
        position = 0
        self.grid_model.maximizeGrid(position)
        
        self.assertTrue(self.grid_model._is_maximized)
        self.assertEqual(self.grid_model._active_item_position, position)

    def test_restore_grid(self):
        """Test restoring grid from maximized state."""
        # First maximize
        position = 0
        self.grid_model.maximizeGrid(position)
        
        # Then restore
        self.grid_model.restoreGrid()
        
        self.assertFalse(self.grid_model._is_maximized)
        self.assertIsNone(self.grid_model._active_item_position)

    def test_set_grid_size(self):
        """Test setting grid size."""
        columns = 2
        rows = 2
        self.grid_model.setGridSize(columns, rows)
        
        self.assertEqual(self.grid_model._columns, columns)
        self.assertEqual(self.grid_model._rows, rows)

    def test_ptz_control(self):
        """Test PTZ control functionality."""
        position = 0
        camera_data = {
            "id": "test_camera",
            "name": "Test Camera",
            "url": "rtsp://test.com/stream",
            "supportsPTZ": True
        }
        self.grid_model.addCamera(position, camera_data)
        
        # Test PTZ control
        self.grid_model.controlPTZ(position, 1, 1)  # Move right and up
        
        # Verify PTZ command was sent
        self.mock_controller.controlPTZ.assert_called_once()

    def test_theme_changes(self):
        """Test theme-related property changes."""
        # Test dark theme
        self.grid_model.isDarkTheme = True
        self.assertTrue(self.grid_model.isDarkTheme)
        
        # Test background color
        test_color = "#000000"
        self.grid_model.backgroundColor = test_color
        self.assertEqual(self.grid_model.backgroundColor, test_color)

class TestGridManager(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.grid_manager = GridManager()

    def test_singleton_pattern(self):
        """Test that GridManager follows singleton pattern."""
        instance1 = GridManager.get_instance()
        instance2 = GridManager.get_instance()
        
        self.assertIs(instance1, instance2)

    def test_add_grid_model(self):
        """Test adding a grid model."""
        grid_model = GridModel()
        self.grid_manager.addGridModel(grid_model)
        
        # Verify grid model was added
        self.assertIn(grid_model, self.grid_manager.data.values())

    def test_remove_grid_model(self):
        """Test removing a grid model."""
        grid_model = GridModel()
        self.grid_manager.addGridModel(grid_model)
        
        self.grid_manager.removeGridModel(grid_model)
        
        # Verify grid model was removed
        self.assertNotIn(grid_model, self.grid_manager.data.values())

if __name__ == '__main__':
    unittest.main() 
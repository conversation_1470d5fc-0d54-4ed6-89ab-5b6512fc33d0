import unittest
import uuid
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtCore import QObject, Signal, Slot, QModelIndex
from src.presentation.camera_screen.managers.grid_manager import GridModel, GridManager

class TestGridOperations(unittest.TestCase):
    """Test cases for grid operations: swap, resize, drop"""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        data = {
            "id": uuid.uuid4(),
            "name": "test_grid",
            "type": "Invalid",
            "isShow": True,
            "currentGrid": {},
            "listGridData": {},
            "listGridCustomData": "NewGrid",
            "direction": {}
        }
        self.grid_model = GridModel(data=data)
        self.mock_controller = Mock()
        self.grid_model.controller = self.mock_controller
        
        # Create mock camera models
        self.camera1 = Mock()
        self.camera1.id = "camera1"
        self.camera1.name = "Camera 1"
        self.camera1.url = "rtsp://test.com/1"
        self.camera1.get_property = Mock(return_value=[])  # No PTZ
        
        self.camera2 = Mock()
        self.camera2.id = "camera2"
        self.camera2.name = "Camera 2"
        self.camera2.url = "rtsp://test.com/2"
        self.camera2.get_property = Mock(return_value=["pan", "tilt"])  # Has PTZ
        
        self.camera3 = Mock()
        self.camera3.id = "camera3"
        self.camera3.name = "Camera 3"
        self.camera3.url = "rtsp://test.com/3"
        self.camera3.get_property = Mock(return_value=[])
        
        # Setup initial grid state
        self.grid_model.setGridSize(3, 3)  # 3x3 grid
        
    def add_test_cameras(self):
        """Helper method to add test cameras to grid"""
        # Add cameras to positions 0, 1, 4
        self.grid_model._active_cells[0] = self.camera1
        self.grid_model._active_cells[1] = self.camera2
        self.grid_model._active_cells[4] = self.camera3
        
        # Set cell dimensions
        self.grid_model._cell_dimensions[0] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[1] = {"width": 2, "height": 1}  # Resized camera
        self.grid_model._cell_dimensions[4] = {"width": 1, "height": 2}  # Resized camera

class TestSwapOperations(TestGridOperations):
    """Test camera swap operations"""
    
    def test_swap_two_regular_cameras(self):
        """Test swapping two regular 1x1 cameras"""
        self.add_test_cameras()
        
        # Swap camera1 (pos 0) with camera3 (pos 4)
        success = self.grid_model.swapCameras(0, 4)
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._active_cells[0], self.camera3)
        self.assertEqual(self.grid_model._active_cells[4], self.camera1)
        
        # Check dimensions were swapped
        self.assertEqual(self.grid_model._cell_dimensions[0], {"width": 1, "height": 2})
        self.assertEqual(self.grid_model._cell_dimensions[4], {"width": 1, "height": 1})
    
    def test_swap_regular_with_resized_camera(self):
        """Test swapping regular camera with resized camera"""
        self.add_test_cameras()
        
        # Swap camera1 (1x1) with camera2 (2x1)
        success = self.grid_model.swapCameras(0, 1)
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._active_cells[0], self.camera2)
        self.assertEqual(self.grid_model._active_cells[1], self.camera1)
        
        # Check dimensions were swapped
        self.assertEqual(self.grid_model._cell_dimensions[0], {"width": 2, "height": 1})
        self.assertEqual(self.grid_model._cell_dimensions[1], {"width": 1, "height": 1})
    
    def test_swap_with_empty_position(self):
        """Test swapping camera with empty position"""
        self.add_test_cameras()
        
        # Swap camera1 (pos 0) with empty position (pos 2)
        success = self.grid_model.swapCameras(0, 2)
        
        self.assertTrue(success)
        self.assertNotIn(0, self.grid_model._active_cells)
        self.assertEqual(self.grid_model._active_cells[2], self.camera1)
        
        # Check dimensions moved
        self.assertNotIn(0, self.grid_model._cell_dimensions)
        self.assertEqual(self.grid_model._cell_dimensions[2], {"width": 1, "height": 1})
    
    def test_swap_invalid_positions(self):
        """Test swapping with invalid positions"""
        self.add_test_cameras()
        
        # Test swap with out-of-bounds position
        success = self.grid_model.swapCameras(0, 10)
        self.assertFalse(success)
        
        # Test swap with negative position
        success = self.grid_model.swapCameras(-1, 1)
        self.assertFalse(success)
    
    def test_swap_same_position(self):
        """Test swapping camera with itself"""
        self.add_test_cameras()
        
        success = self.grid_model.swapCameras(0, 0)
        self.assertTrue(success)  # Should succeed but do nothing
        self.assertEqual(self.grid_model._active_cells[0], self.camera1)

class TestResizeOperations(TestGridOperations):
    """Test camera resize operations"""
    
    def test_resize_camera_width(self):
        """Test resizing camera width"""
        self.add_test_cameras()
        
        # Resize camera1 from 1x1 to 2x1
        success = self.grid_model.resizeCamera(0, 2, 1)
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._cell_dimensions[0], {"width": 2, "height": 1})
        
        # Check secondary position is marked
        self.assertIn(1, self.grid_model._active_cells)
        self.assertEqual(self.grid_model._active_cells[1], self.camera1)
    
    def test_resize_camera_height(self):
        """Test resizing camera height"""
        self.add_test_cameras()
        
        # Resize camera1 from 1x1 to 1x2
        success = self.grid_model.resizeCamera(0, 1, 2)
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._cell_dimensions[0], {"width": 1, "height": 2})
        
        # Check secondary position is marked
        self.assertIn(3, self.grid_model._active_cells)  # Position below (0 + 3 columns)
        self.assertEqual(self.grid_model._active_cells[3], self.camera1)
    
    def test_resize_camera_both_dimensions(self):
        """Test resizing camera both width and height"""
        self.add_test_cameras()
        
        # Resize camera1 from 1x1 to 2x2
        success = self.grid_model.resizeCamera(0, 2, 2)
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._cell_dimensions[0], {"width": 2, "height": 2})
        
        # Check all secondary positions are marked
        expected_positions = [1, 3, 4]  # Right, below, diagonal
        for pos in expected_positions:
            self.assertIn(pos, self.grid_model._active_cells)
            self.assertEqual(self.grid_model._active_cells[pos], self.camera1)
    
    def test_resize_camera_conflict(self):
        """Test resizing camera with conflict"""
        self.add_test_cameras()
        
        # Try to resize camera1 to overlap with camera2
        success = self.grid_model.resizeCamera(0, 3, 1)  # Would overlap position 1 and 2
        
        self.assertFalse(success)
        # Original dimensions should remain
        self.assertEqual(self.grid_model._cell_dimensions[0], {"width": 1, "height": 1})
    
    def test_resize_camera_out_of_bounds(self):
        """Test resizing camera beyond grid bounds"""
        self.add_test_cameras()
        
        # Try to resize camera at position 2 (right edge) to width 2
        self.grid_model._active_cells[2] = self.camera1
        self.grid_model._cell_dimensions[2] = {"width": 1, "height": 1}
        
        success = self.grid_model.resizeCamera(2, 2, 1)  # Would go beyond right edge
        
        self.assertFalse(success)
    
    def test_resize_nonexistent_camera(self):
        """Test resizing camera at empty position"""
        success = self.grid_model.resizeCamera(5, 2, 2)  # Empty position
        
        self.assertFalse(success)
    
    def test_resize_to_smaller_size(self):
        """Test resizing camera to smaller size"""
        self.add_test_cameras()
        
        # Resize camera2 from 2x1 to 1x1
        success = self.grid_model.resizeCamera(1, 1, 1)
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._cell_dimensions[1], {"width": 1, "height": 1})
        
        # Check secondary position was cleared
        # Note: Position 2 should be cleared if it was occupied by camera2

class TestDropOperations(TestGridOperations):
    """Test camera drop operations"""
    
    def test_drop_camera_to_empty_position(self):
        """Test dropping camera to empty position"""
        self.add_test_cameras()
        
        # Drop camera1 from position 0 to position 2
        success = self.grid_model.dropCamera(0, 2)
        
        self.assertTrue(success)
        self.assertNotIn(0, self.grid_model._active_cells)
        self.assertEqual(self.grid_model._active_cells[2], self.camera1)
        
        # Check dimensions moved
        self.assertNotIn(0, self.grid_model._cell_dimensions)
        self.assertEqual(self.grid_model._cell_dimensions[2], {"width": 1, "height": 1})
    
    def test_drop_camera_to_occupied_position(self):
        """Test dropping camera to occupied position (should swap)"""
        self.add_test_cameras()
        
        # Drop camera1 to position 4 (occupied by camera3)
        success = self.grid_model.dropCamera(0, 4)
        
        self.assertTrue(success)
        # Should result in swap
        self.assertEqual(self.grid_model._active_cells[0], self.camera3)
        self.assertEqual(self.grid_model._active_cells[4], self.camera1)
    
    def test_drop_resized_camera(self):
        """Test dropping resized camera"""
        self.add_test_cameras()
        
        # Drop camera2 (2x1) from position 1 to position 5
        success = self.grid_model.dropCamera(1, 5)
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._active_cells[5], self.camera2)
        self.assertEqual(self.grid_model._cell_dimensions[5], {"width": 2, "height": 1})
        
        # Check secondary position moved
        self.assertIn(6, self.grid_model._active_cells)  # Position 5 + 1
        self.assertEqual(self.grid_model._active_cells[6], self.camera2)
    
    def test_drop_camera_out_of_bounds(self):
        """Test dropping camera to out-of-bounds position"""
        self.add_test_cameras()
        
        success = self.grid_model.dropCamera(0, 10)  # Beyond grid
        
        self.assertFalse(success)
        # Camera should remain at original position
        self.assertEqual(self.grid_model._active_cells[0], self.camera1)
    
    def test_drop_camera_with_conflict(self):
        """Test dropping resized camera where it would conflict"""
        self.add_test_cameras()
        
        # Try to drop camera2 (2x1) to position 3 where it would conflict
        success = self.grid_model.dropCamera(1, 3)
        
        # Should fail due to conflict with camera3 at position 4
        self.assertFalse(success)
        # Camera should remain at original position
        self.assertEqual(self.grid_model._active_cells[1], self.camera2)
    
    def test_drop_camera_same_position(self):
        """Test dropping camera to same position"""
        self.add_test_cameras()
        
        success = self.grid_model.dropCamera(0, 0)
        
        self.assertTrue(success)  # Should succeed but do nothing
        self.assertEqual(self.grid_model._active_cells[0], self.camera1)

class TestAutoResizeOperations(TestGridOperations):
    """Test auto resize operations during drop"""
    
    def test_drop_with_auto_resize_enabled(self):
        """Test dropping camera with auto resize enabled"""
        self.grid_model.autoResizeEnabled = True
        self.add_test_cameras()
        
        # Fill the grid and try to drop to position outside current grid
        # This should trigger auto resize
        success = self.grid_model.dropCameraWithAutoResize(0, 10)  # Beyond 3x3 grid
        
        # Should expand grid and succeed
        self.assertTrue(success)
        # Grid should be expanded
        self.assertGreater(self.grid_model._columns * self.grid_model._rows, 9)
    
    def test_drop_with_auto_resize_disabled(self):
        """Test dropping camera with auto resize disabled"""
        self.grid_model.autoResizeEnabled = False
        self.add_test_cameras()
        
        success = self.grid_model.dropCameraWithAutoResize(0, 10)  # Beyond 3x3 grid
        
        # Should fail without expanding grid
        self.assertFalse(success)
        # Grid should remain same size
        self.assertEqual(self.grid_model._columns, 3)
        self.assertEqual(self.grid_model._rows, 3)

if __name__ == '__main__':
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTest(unittest.makeSuite(TestSwapOperations))
    suite.addTest(unittest.makeSuite(TestResizeOperations))
    suite.addTest(unittest.makeSuite(TestDropOperations))
    suite.addTest(unittest.makeSuite(TestAutoResizeOperations))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

import unittest
import uuid
from unittest.mock import Mock, patch, MagicMock
from PySide6.QtCore import QObject, Signal, Slot
from src.presentation.camera_screen.managers.grid_manager import GridModel, GridManager

class TestWheelResize(unittest.TestCase):
    """Test cases for wheel resize operations (Ctrl + Mouse wheel)"""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        data = {
            "id": uuid.uuid4(),
            "name": "test_grid",
            "type": "Invalid",
            "isShow": True,
            "currentGrid": {},
            "listGridData": {},
            "listGridCustomData": "NewGrid",
            "direction": {}
        }
        self.grid_model = GridModel(data=data)
        self.mock_controller = Mock()
        self.grid_model.controller = self.mock_controller
        
        # Create mock camera models
        self.camera1 = Mock()
        self.camera1.id = "camera1"
        self.camera1.name = "Camera 1"
        self.camera1.get_property = Mock(return_value=[])
        
        self.camera2 = Mock()
        self.camera2.id = "camera2"
        self.camera2.name = "Camera 2"
        self.camera2.get_property = Mock(return_value=[])
        
        self.camera3 = Mock()
        self.camera3.id = "camera3"
        self.camera3.name = "Camera 3"
        self.camera3.get_property = Mock(return_value=[])
        
        # Setup signals to track emissions
        self.grid_changed_signals = []
        self.position_changed_signals = []
        self.dimension_changed_signals = []
        
        self.grid_model.gridChanged.connect(self._on_grid_changed)
        self.grid_model.videoPositionChanged.connect(self._on_position_changed)
        self.grid_model.cellDimensionsChanged.connect(self._on_dimension_changed)
    
    def _on_grid_changed(self, columns, rows):
        """Track grid changed signals"""
        self.grid_changed_signals.append((columns, rows))
    
    def _on_position_changed(self, old_pos, new_pos):
        """Track position changed signals"""
        self.position_changed_signals.append((old_pos, new_pos))
    
    def _on_dimension_changed(self, position, width, height):
        """Track dimension changed signals"""
        self.dimension_changed_signals.append((position, width, height))
    
    def add_test_cameras_2x2(self):
        """Helper to add cameras in 2x2 grid"""
        self.grid_model.setGridSize(2, 2)
        
        # Add cameras at positions 0, 1, 2
        self.grid_model._active_cells[0] = self.camera1
        self.grid_model._active_cells[1] = self.camera2
        self.grid_model._active_cells[2] = self.camera3
        
        self.grid_model._cell_dimensions[0] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[1] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[2] = {"width": 1, "height": 1}

class TestWheelIncrease(TestWheelResize):
    """Test wheel increase operations (Ctrl + wheel up)"""
    
    def test_wheel_increase_basic(self):
        """Test basic wheel increase from 2x2 to 3x3"""
        self.add_test_cameras_2x2()
        
        # Clear signal tracking
        self.grid_changed_signals.clear()
        self.position_changed_signals.clear()
        
        # Perform wheel increase
        success = self.grid_model.handleWheelResize(True, True)  # increase=True, with_ctrl=True
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._columns, 3)
        self.assertEqual(self.grid_model._rows, 3)
        
        # Check grid changed signal was emitted
        self.assertEqual(len(self.grid_changed_signals), 1)
        self.assertEqual(self.grid_changed_signals[0], (3, 3))
        
        # Check cameras maintained relative positions
        self.assertEqual(self.grid_model._active_cells[0], self.camera1)  # (0,0) -> (0,0)
        self.assertEqual(self.grid_model._active_cells[1], self.camera2)  # (0,1) -> (0,1)
        self.assertEqual(self.grid_model._active_cells[3], self.camera3)  # (1,0) -> (1,0)
    
    def test_wheel_increase_at_max_columns(self):
        """Test wheel increase when already at max columns"""
        self.grid_model.setGridSize(12, 12)  # Max size
        
        success = self.grid_model.handleWheelResize(True, True)
        
        self.assertFalse(success)
        self.assertEqual(self.grid_model._columns, 12)
        self.assertEqual(self.grid_model._rows, 12)
    
    def test_wheel_increase_square_adjustment(self):
        """Test wheel increase with square grid adjustment"""
        # Start with non-square grid
        self.grid_model.setGridSize(2, 3)
        
        success = self.grid_model.handleWheelResize(True, True)
        
        self.assertTrue(success)
        # Should become square (max of new dimensions)
        self.assertEqual(self.grid_model._columns, 4)  # max(3, 4)
        self.assertEqual(self.grid_model._rows, 4)
    
    def test_wheel_increase_without_ctrl(self):
        """Test wheel increase without Ctrl modifier"""
        self.add_test_cameras_2x2()
        
        success = self.grid_model.handleWheelResize(True, False)  # with_ctrl=False
        
        self.assertFalse(success)
        # Grid should remain unchanged
        self.assertEqual(self.grid_model._columns, 2)
        self.assertEqual(self.grid_model._rows, 2)
    
    def test_wheel_increase_in_maximize_mode(self):
        """Test wheel increase when grid is maximized"""
        self.add_test_cameras_2x2()
        self.grid_model.maximizeGrid(0)  # Maximize position 0
        
        success = self.grid_model.handleWheelResize(True, True)
        
        self.assertFalse(success)
        # Grid should remain unchanged
        self.assertEqual(self.grid_model._columns, 2)
        self.assertEqual(self.grid_model._rows, 2)
    
    def test_wheel_increase_disables_auto_resize(self):
        """Test that wheel increase disables auto resize"""
        self.grid_model.autoResizeEnabled = True
        self.add_test_cameras_2x2()
        
        success = self.grid_model.handleWheelResize(True, True)
        
        self.assertTrue(success)
        self.assertFalse(self.grid_model.autoResizeEnabled)

class TestWheelDecrease(TestWheelResize):
    """Test wheel decrease operations (Ctrl + wheel down)"""
    
    def test_wheel_decrease_basic(self):
        """Test basic wheel decrease from 3x3 to 2x2"""
        # Start with 3x3 grid
        self.grid_model.setGridSize(3, 3)
        
        # Add cameras at positions that won't conflict with reduction
        self.grid_model._active_cells[0] = self.camera1  # (0,0)
        self.grid_model._active_cells[1] = self.camera2  # (0,1)
        self.grid_model._active_cells[3] = self.camera3  # (1,0)
        
        self.grid_model._cell_dimensions[0] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[1] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[3] = {"width": 1, "height": 1}
        
        success = self.grid_model.handleWheelResize(False, True)  # increase=False
        
        self.assertTrue(success)
        self.assertEqual(self.grid_model._columns, 2)
        self.assertEqual(self.grid_model._rows, 2)
        
        # Check cameras maintained relative positions
        self.assertEqual(self.grid_model._active_cells[0], self.camera1)  # (0,0) -> (0,0)
        self.assertEqual(self.grid_model._active_cells[1], self.camera2)  # (0,1) -> (0,1)
        self.assertEqual(self.grid_model._active_cells[2], self.camera3)  # (1,0) -> (1,0)
    
    def test_wheel_decrease_with_camera_in_last_row(self):
        """Test wheel decrease blocked by camera in last row"""
        self.grid_model.setGridSize(3, 3)
        
        # Add camera in last row (position 6 = row 2, col 0)
        self.grid_model._active_cells[6] = self.camera1
        self.grid_model._cell_dimensions[6] = {"width": 1, "height": 1}
        
        success = self.grid_model.handleWheelResize(False, True)
        
        self.assertFalse(success)
        # Grid should remain unchanged
        self.assertEqual(self.grid_model._columns, 3)
        self.assertEqual(self.grid_model._rows, 3)
    
    def test_wheel_decrease_with_camera_in_last_column(self):
        """Test wheel decrease blocked by camera in last column"""
        self.grid_model.setGridSize(3, 3)
        
        # Add camera in last column (position 2 = row 0, col 2)
        self.grid_model._active_cells[2] = self.camera1
        self.grid_model._cell_dimensions[2] = {"width": 1, "height": 1}
        
        success = self.grid_model.handleWheelResize(False, True)
        
        self.assertFalse(success)
        # Grid should remain unchanged
        self.assertEqual(self.grid_model._columns, 3)
        self.assertEqual(self.grid_model._rows, 3)
    
    def test_wheel_decrease_at_base_size(self):
        """Test wheel decrease when already at base size"""
        self.grid_model.setGridSize(1, 1)  # Base size
        
        success = self.grid_model.handleWheelResize(False, True)
        
        self.assertFalse(success)
        self.assertEqual(self.grid_model._columns, 1)
        self.assertEqual(self.grid_model._rows, 1)
    
    def test_wheel_decrease_square_adjustment(self):
        """Test wheel decrease with square grid adjustment"""
        # Start with non-square grid
        self.grid_model.setGridSize(4, 3)
        
        success = self.grid_model.handleWheelResize(False, True)
        
        self.assertTrue(success)
        # Should become square (min of new dimensions, but >= 1)
        self.assertEqual(self.grid_model._columns, 2)  # max(1, min(3, 2))
        self.assertEqual(self.grid_model._rows, 2)

class TestWheelResizePositionCalculation(TestWheelResize):
    """Test position calculation during wheel resize"""
    
    def test_position_calculation_increase(self):
        """Test position calculation during increase"""
        # Setup 2x2 grid with cameras at specific positions
        self.grid_model.setGridSize(2, 2)
        
        # Position mapping for 2x2 -> 3x3:
        # 0 1    ->    0 1 2
        # 2 3          3 4 5
        #              6 7 8
        
        self.grid_model._active_cells[0] = self.camera1  # (0,0) -> (0,0) = 0
        self.grid_model._active_cells[1] = self.camera2  # (0,1) -> (0,1) = 1
        self.grid_model._active_cells[3] = self.camera3  # (1,1) -> (1,1) = 4
        
        self.grid_model._cell_dimensions[0] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[1] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[3] = {"width": 1, "height": 1}
        
        success = self.grid_model.handleWheelResize(True, True)
        
        self.assertTrue(success)
        
        # Check new positions
        self.assertEqual(self.grid_model._active_cells[0], self.camera1)  # (0,0) -> 0
        self.assertEqual(self.grid_model._active_cells[1], self.camera2)  # (0,1) -> 1
        self.assertEqual(self.grid_model._active_cells[4], self.camera3)  # (1,1) -> 4
        
        # Check old positions are cleared
        self.assertNotIn(3, self.grid_model._active_cells)
    
    def test_position_calculation_decrease(self):
        """Test position calculation during decrease"""
        # Setup 3x3 grid
        self.grid_model.setGridSize(3, 3)
        
        # Position mapping for 3x3 -> 2x2:
        # 0 1 2    ->    0 1
        # 3 4 5          2 3
        # 6 7 8
        
        self.grid_model._active_cells[0] = self.camera1  # (0,0) -> (0,0) = 0
        self.grid_model._active_cells[1] = self.camera2  # (0,1) -> (0,1) = 1
        self.grid_model._active_cells[4] = self.camera3  # (1,1) -> (1,1) = 3
        
        self.grid_model._cell_dimensions[0] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[1] = {"width": 1, "height": 1}
        self.grid_model._cell_dimensions[4] = {"width": 1, "height": 1}
        
        success = self.grid_model.handleWheelResize(False, True)
        
        self.assertTrue(success)
        
        # Check new positions
        self.assertEqual(self.grid_model._active_cells[0], self.camera1)  # (0,0) -> 0
        self.assertEqual(self.grid_model._active_cells[1], self.camera2)  # (0,1) -> 1
        self.assertEqual(self.grid_model._active_cells[3], self.camera3)  # (1,1) -> 3
        
        # Check old positions are cleared
        self.assertNotIn(4, self.grid_model._active_cells)

class TestWheelResizeSignals(TestWheelResize):
    """Test signal emissions during wheel resize"""
    
    def test_signals_emitted_on_increase(self):
        """Test that correct signals are emitted during increase"""
        self.add_test_cameras_2x2()
        
        # Clear signal tracking
        self.grid_changed_signals.clear()
        self.position_changed_signals.clear()
        self.dimension_changed_signals.clear()
        
        success = self.grid_model.handleWheelResize(True, True)
        
        self.assertTrue(success)
        
        # Check grid changed signal
        self.assertEqual(len(self.grid_changed_signals), 1)
        self.assertEqual(self.grid_changed_signals[0], (3, 3))
        
        # Check position changed signals for moved cameras
        # Camera at position 2 should move to position 3
        moved_cameras = [signal for signal in self.position_changed_signals if signal[0] != signal[1]]
        self.assertGreater(len(moved_cameras), 0)
        
        # Check dimension changed signals
        self.assertGreater(len(self.dimension_changed_signals), 0)
    
    def test_no_signals_on_failed_resize(self):
        """Test that no signals are emitted when resize fails"""
        self.grid_model.setGridSize(12, 12)  # Max size
        
        # Clear signal tracking
        self.grid_changed_signals.clear()
        
        success = self.grid_model.handleWheelResize(True, True)
        
        self.assertFalse(success)
        self.assertEqual(len(self.grid_changed_signals), 0)

if __name__ == '__main__':
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test classes
    suite.addTest(unittest.makeSuite(TestWheelIncrease))
    suite.addTest(unittest.makeSuite(TestWheelDecrease))
    suite.addTest(unittest.makeSuite(TestWheelResizePositionCalculation))
    suite.addTest(unittest.makeSuite(TestWheelResizeSignals))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

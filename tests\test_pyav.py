import av
import time
from av.codec.hwaccel import hwdevices_available, HWAccel
import cv2
import platform
import subprocess
import re
import numpy as np
from PySide6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QPushButton, QLabel, QDialog, QRadioButton, QDialogButtonBox
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QImage, QPixmap
import logging
import sys

# Add new imports for threading
import threading
from queue import Queue
import traceback

# Set up logging configuration at the top of the file
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('pyav_test.log')
    ]
)
logger = logging.getLogger(__name__)

def check_intel_gpu():
    """Check if Intel GPU is available."""
    if platform.system() == "Darwin":  # macOS
        try:
            # Use system_profiler to get GPU info
            output = subprocess.check_output(['system_profiler', 'SPDisplaysDataType'], text=True)
            return any('Intel' in line for line in output.splitlines())
        except:
            pass
    elif platform.system() == "Windows":
        try:
            # Use Windows Management Instrumentation (WMI) to check for Intel GPU
            import wmi
            w = wmi.WMI()
            for gpu in w.Win32_VideoController():
                if "Intel" in gpu.Name:
                    return True
        except:
            pass
    else:
        try:
            # For Linux, check lspci output
            output = subprocess.check_output(['lspci'], text=True)
            return any('Intel' in line and 'VGA' in line for line in output.splitlines())
        except:
            pass
    return False

def check_qsv_availability():
    """Check if QSV is available through FFmpeg."""
    try:
        logger.debug("Checking QSV availability...")
        # Check hardware devices first
        result = subprocess.run(['ffmpeg', '-hide_banner', '-hwaccels'], 
                              capture_output=True, text=True)
        hwaccels = result.stdout.lower()
        
        # If QSV is not in hardware accelerators, return False
        if 'qsv' not in hwaccels:
            logger.debug("QSV not found in hardware accelerators")
            return False, []

        logger.debug(f"Found hardware accelerators: {hwaccels}")

        # Then check available decoders
        result = subprocess.run(['ffmpeg', '-hide_banner', '-decoders'], 
                              capture_output=True, text=True)
        
        # Check for QSV support
        qsv_decoders = []
        for line in result.stdout.splitlines():
            if '_qsv' in line.lower():
                # Parse the decoder line format: " V..... h264_qsv  H264 video (Intel Quick Sync..."
                parts = line.strip().split()
                if len(parts) >= 2:  # Make sure we have at least codec type and name
                    decoder_name = parts[1]  # Get the second part which is the decoder name
                    if decoder_name.endswith('_qsv'):
                        qsv_decoders.append(decoder_name)
        
        print("Found QSV decoders:", qsv_decoders)
        return bool(qsv_decoders), qsv_decoders
        
    except Exception as e:
        logger.error(f"Failed to check QSV availability: {e}", exc_info=True)
        return False, []

def check_videotoolbox_availability():
    """Check if VideoToolbox is available through FFmpeg."""
    if platform.system() != "Darwin":  # Only check on macOS
        return False, []
        
    try:
        # Check hardware devices
        result = subprocess.run(['ffmpeg', '-hide_banner', '-hwaccels'], 
                              capture_output=True, text=True)
        hwaccels = result.stdout.lower()
        
        # If videotoolbox is not in hardware accelerators, return False
        if 'videotoolbox' not in hwaccels:
            return False, []

        # Check available decoders
        result = subprocess.run(['ffmpeg', '-hide_banner', '-decoders'], 
                              capture_output=True, text=True)
        
        # Check for VideoToolbox support
        vt_decoders = []
        for line in result.stdout.splitlines():
            if '_videotoolbox' in line.lower():
                parts = line.strip().split()
                if len(parts) >= 2:
                    decoder_name = parts[1]
                    if decoder_name.endswith('_videotoolbox'):
                        vt_decoders.append(decoder_name)
        
        print("Found VideoToolbox decoders:", vt_decoders)
        return bool(vt_decoders), vt_decoders
        
    except Exception as e:
        print(f"Failed to check VideoToolbox availability: {e}")
        return False, []

def get_available_hwaccel():
    """Get available hardware acceleration devices."""
    hw_types = list(hwdevices_available())
    print("Initial hardware devices:", hw_types)
    
    # Check if VideoToolbox is available on macOS
    if platform.system() == "Darwin":
        has_vt, vt_decoders = check_videotoolbox_availability()
        if has_vt and vt_decoders:
            if 'videotoolbox' not in hw_types:
                hw_types.append('videotoolbox')
            print("\nVideoToolbox support detected!")
            print("Available VideoToolbox decoders:", vt_decoders)
    
    # Check if QSV is available
    has_qsv, qsv_decoders = check_qsv_availability()
    if has_qsv and qsv_decoders:
        if 'qsv' not in hw_types:
            hw_types.append('qsv')
        print("\nQSV support detected!")
        print("Available QSV decoders:", qsv_decoders)
    else:
        print("\nQSV support not detected")
    
    print("Final hardware acceleration devices:", hw_types)
    return hw_types, qsv_decoders

class HWAccelDialog(QDialog):
    def __init__(self, hw_types, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Choose Hardware Acceleration")
        layout = QVBoxLayout(self)
        
        # Create radio buttons for each option
        self.radio_buttons = []
        
        # Add hardware acceleration options
        for device in hw_types:
            radio = QRadioButton(device)
            self.radio_buttons.append(radio)
            layout.addWidget(radio)
            
        # Add software decoding option
        software_radio = QRadioButton("Software decoding (no hardware acceleration)")
        self.radio_buttons.append(software_radio)
        layout.addWidget(software_radio)
        
        # Select first option by default
        if self.radio_buttons:
            self.radio_buttons[0].setChecked(True)
            
        # Add OK and Cancel buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

def choose_hw_device(hw_types):
    """Let user choose hardware acceleration device using a dialog."""
    dialog = HWAccelDialog(hw_types)
    result = dialog.exec()
    
    if result == QDialog.Accepted:
        # Find which radio button was selected
        for i, radio in enumerate(dialog.radio_buttons):
            if radio.isChecked():
                # Return None for software decoding (last option)
                if i == len(hw_types):
                    return None
                return hw_types[i]
    return None  # Return None if dialog was cancelled

def get_hw_config(hw_types, codec_name, preferred_device=None, qsv_decoders=None):
    print(f'Available hardware types: {av.codecs_available}')
    """Get hardware acceleration configuration based on available devices and codec."""
    # Configuration mapping for different hardware acceleration methods
    hw_configs = []
    
    # Add MF config for Windows
    if platform.system() == "Windows":
        hw_configs.append({
            'device': 'mf',  # Media Foundation
            'codec_map': {
                'h264': 'h264_mf',
                'hevc': 'hevc_mf',
                'mpeg2': 'mpeg2_mf',
                'vp9': 'vp9_mf',
                'av1': 'av1_mf'
            }
        })
    
    # Add QSV config if available
    if 'qsv' in hw_types and qsv_decoders:
        qsv_config = {
            'device': 'qsv',
            'codec_map': {}
        }
        # Map base codecs to their QSV variants
        codec_mapping = {
            'h264': 'h264_qsv',
            'hevc': 'hevc_qsv',
            'mjpeg': 'mjpeg_qsv',
            'mpeg2': 'mpeg2_qsv',
            'mpeg2video': 'mpeg2_qsv',
            'vp8': 'vp8_qsv',
            'vp9': 'vp9_qsv',
            'av1': 'av1_qsv',
            'vc1': 'vc1_qsv',
            'vvc': 'vvc_qsv'
        }
        
        # Add only available QSV decoders
        for base_codec, qsv_codec in codec_mapping.items():
            if qsv_codec in qsv_decoders:
                qsv_config['codec_map'][base_codec] = qsv_codec
        
        print(f'QSV configuration: {qsv_config}')
        hw_configs.append(qsv_config)
    
    # Add other configs
    hw_configs.extend([
        {
            'device': 'cuda',
            'codec_map': {
                'h264': 'h264_cuvid',
                'hevc': 'hevc_cuvid',
                'mjpeg': 'mjpeg_cuvid',
                'mpeg1': 'mpeg1_cuvid',
                'mpeg2': 'mpeg2_cuvid',
                'mpeg4': 'mpeg4_cuvid',
                'vc1': 'vc1_cuvid',
                'vp8': 'vp8_cuvid',
                'vp9': 'vp9_cuvid',
                'av1': 'av1_cuvid'
            }
        },
        {
            'device': 'd3d11va',
            'codec_map': {
                'h264': 'h264',
                'hevc': 'hevc',
                'vp9': 'vp9',
                'av1': 'av1'
            }
        },
        {
            'device': 'dxva2',
            'codec_map': {
                'h264': 'h264',
                'hevc': 'hevc',
                'mpeg2': 'mpeg2',
                'vc1': 'vc1',
                'wmv3': 'wmv3'
            }
        },
        {
            'device': 'vaapi',
            'codec_map': {
                'h264': 'h264',
                'hevc': 'hevc',
                'mjpeg': 'mjpeg',
                'mpeg2': 'mpeg2',
                'vp8': 'vp8',
                'vp9': 'vp9',
                'av1': 'av1'
            }
        },
        # Add VideoToolbox config for macOS
        {
            'device': 'videotoolbox',
            'codec_map': {
                'h264': 'h264_videotoolbox',
                'hevc': 'hevc_videotoolbox'
            }
        },
    ])
    
    if preferred_device:
        # Try preferred device first
        for config in hw_configs:
            if config['device'] == preferred_device and codec_name in config['codec_map']:
                return {
                    'device_type': config['device'],
                    'decoder_name': config['codec_map'][codec_name]
                }
        print(f"Preferred device {preferred_device} not compatible with codec {codec_name}")
        
        # Ask if user wants to try other devices
        try_others = input("Try other available devices? (y/n): ").lower() == 'y'
        if not try_others:
            return None
    
    # Try each hardware configuration in order
    for config in hw_configs:
        if config['device'] in hw_types and codec_name in config['codec_map']:
            return {
                'device_type': config['device'],
                'decoder_name': config['codec_map'][codec_name]
            }
    
    return None

class StreamWorker(QThread):
    frame_ready = Signal(object)  # Signal to emit when new frame is ready
    error_occurred = Signal(str)   # Signal for error handling
    
    def __init__(self, container, decoder_ctx):
        super().__init__()
        self.container = container
        self.decoder_ctx = decoder_ctx
        self.running = False
        
    def run(self):
        self.running = True
        try:
            logger.debug("Starting stream worker thread")
            for packet in self.container.demux():
                if not self.running:
                    break
                    
                # Decode frames
                try:
                    frames = self.decoder_ctx.decode(packet) if self.decoder_ctx else packet.decode()
                    
                    for frame in frames:
                        if not self.running:
                            break
                        # Convert frame to numpy array
                        frame_array = frame.to_ndarray(format='rgb24')
                        self.frame_ready.emit(frame_array)
                        break  # Process only one frame per packet
                except Exception as decode_error:
                    logger.error(f"Error decoding frame: {decode_error}", exc_info=True)
                    
        except Exception as e:
            logger.error(f"Stream worker error: {e}", exc_info=True)
            self.error_occurred.emit(str(e))
        finally:
            logger.debug("Stream worker thread stopped")
            self.running = False
            
    def stop(self):
        self.running = False
        self.wait()

class RTSPViewer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("RTSP Stream Viewer")
        self.setMinimumSize(800, 600)

        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Create preview label
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.preview_label)

        # Create start/stop button
        self.toggle_button = QPushButton("Start Stream")
        self.toggle_button.clicked.connect(self.toggle_stream)
        layout.addWidget(self.toggle_button)

        # Stream state
        self.streaming = False
        self.container = None
        self.decoder_ctx = None
        self.stream_worker = None
        
        # FPS tracking
        self.fps_label = QLabel("FPS: 0.0")
        layout.addWidget(self.fps_label)
        self.frame_count = 0
        self.last_fps_time = time.time()
        self.fps = 0

    def toggle_stream(self):
        if not self.streaming:
            self.start_stream()
            self.toggle_button.setText("Stop Stream")
        else:
            self.stop_stream()
            self.toggle_button.setText("Start Stream")

    def start_stream(self):
        self.streaming = True
        
        # RTSP stream setup
        rtsp_url = "https://implacable-ai.ai-vlab.com/camera/cef99511-0d1b-4801-9935-1e6de4d42cd9_0.flv"
        options = {
            'analyzeduration': '100',
        }

        try:
            logger.debug(f"Opening stream URL: {rtsp_url}")
            self.container = av.open(rtsp_url, container_options=options)
            self.container.flags |= 1 << 6  # NOBUFFER flag
            self.container.flags |= 1 << 7  # DISCARD_CORRUPT flag
            self.container.flags |= 1 << 10  # FLUSH_PACKETS flag

            video_stream = self.container.streams.video[0]
            codec_context = video_stream.codec_context
            codec_name = codec_context.codec.name
            logger.debug(f'Detected codec: {codec_name}')

            # Get hardware acceleration
            hw_types, qsv_decoders = get_available_hwaccel()
            logger.debug(f"Available hardware acceleration types: {hw_types}")
            
            preferred_device = choose_hw_device(hw_types)
            logger.debug(f"Selected hardware device: {preferred_device}")

            if preferred_device:
                hw_config = get_hw_config(hw_types, codec_name, preferred_device, qsv_decoders)
                if hw_config:
                    try:
                        logger.debug(f"Attempting to initialize {preferred_device} acceleration")
                        
                        # For VideoToolbox, we'll use a different initialization approach
                        if hw_config['device_type'] == 'videotoolbox':
                            
                            # Use base codec name (h264) instead of h264_videotoolbox
                            base_codec_name = codec_name  # e.g., 'h264' instead of 'h264_videotoolbox'
                            hw_codec = av.Codec(base_codec_name, 'r')
                            self.decoder_ctx = hw_codec.create()
                            
                            # Set basic parameters
                            self.decoder_ctx.width = codec_context.width
                            self.decoder_ctx.height = codec_context.height
                            self.decoder_ctx.pix_fmt = 'yuv420p'
                            self.decoder_ctx.extradata = codec_context.extradata
                            # Create VideoToolbox hardware context
                            hwaccel = HWAccel(device_type='videotoolbox')
                            self.decoder_ctx.create(base_codec_name, 'r', hwaccel)
                            self.decoder_ctx.open()
                            #print check is_hwaccel
                            print(f'Is HWAccel: {self.decoder_ctx.is_hwaccel}')
                        else:
                            # Original initialization for other hardware accelerators
                            hwaccel = HWAccel(device_type=hw_config['device_type'])
                            hw_codec = av.Codec(hw_config['decoder_name'], 'r')
                            self.decoder_ctx = hw_codec.create()
                            
                            self.decoder_ctx.width = codec_context.width
                            self.decoder_ctx.height = codec_context.height
                            self.decoder_ctx.thread_type = "AUTO"
                            self.decoder_ctx.thread_count = cv2.getNumberOfCPUs()
                            self.decoder_ctx.skip_frame = 'NONKEY'
                            self.decoder_ctx.flags2 |= 1 << 0
                            self.decoder_ctx.flags |= 1 << 16
                            self.decoder_ctx.extradata = codec_context.extradata
                            
                            hwaccel.create(hw_codec)
                            self.decoder_ctx.open()
                        print(f"Successfully initialized {hw_config['device_type']} acceleration")
                        
                    except Exception as e:
                        print(f"Failed to initialize hardware acceleration: {e}")
                        traceback.print_exc()
                        self.decoder_ctx = None

            # Create and start stream worker
            self.stream_worker = StreamWorker(self.container, self.decoder_ctx)
            self.stream_worker.frame_ready.connect(self.update_frame)
            self.stream_worker.error_occurred.connect(self.handle_error)
            self.stream_worker.start()

        except av.error.FFmpegError as e:
            logger.error(f"FFmpeg error opening stream: {e}", exc_info=True)
            self.stop_stream()
        except Exception as e:
            logger.error(f"Unexpected error starting stream: {e}", exc_info=True)
            self.stop_stream()

    def stop_stream(self):
        logger.debug("Stopping stream...")
        self.streaming = False
        
        if self.stream_worker:
            logger.debug("Stopping stream worker...")
            self.stream_worker.stop()
            self.stream_worker = None
        
        if self.decoder_ctx:
            print("Flushing decoder buffers...")
            try:
                # Flush the decoder buffers
                self.decoder_ctx.flush_buffers()
                # Process any remaining frames
                frames = self.decoder_ctx.decode(None)
                print(f"Flushed {len(frames)} remaining frames")
            except Exception as e:
                print(f"Error while flushing decoder: {e}")
        
        if self.container:
            print("Closing container...")
            self.container.close()
            self.container = None
        
        print("Clearing decoder context...")
        self.decoder_ctx = None
        self.preview_label.clear()
        self.fps_label.setText("FPS: 0.0")
        print("Stream stopped successfully")

    def update_frame(self, frame_array):
        # Calculate FPS
        current_time = time.time()
        self.frame_count += 1
        
        if current_time - self.last_fps_time >= 1.0:
            self.fps = self.frame_count / (current_time - self.last_fps_time)
            self.last_fps_time = current_time
            self.frame_count = 0
            self.fps_label.setText(f"FPS: {self.fps:.2f}")

        # Convert numpy array to QImage
        height, width = frame_array.shape[:2]
        bytes_per_line = 3 * width
        
        q_image = QImage(
            frame_array.data,
            width,
            height,
            bytes_per_line,
            QImage.Format_RGB888
        )
        
        # Scale image to fit the label while maintaining aspect ratio
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(
            self.preview_label.size(),
            Qt.KeepAspectRatio,
            Qt.SmoothTransformation
        )
        
        self.preview_label.setPixmap(scaled_pixmap)

    def handle_error(self, error_msg):
        print(f"Stream error: {error_msg}")
        self.stop_stream()

    def closeEvent(self, event):
        self.stop_stream()
        super().closeEvent(event)

if __name__ == "__main__":
    app = QApplication([])
    window = RTSPViewer()
    window.show()
    app.exec()
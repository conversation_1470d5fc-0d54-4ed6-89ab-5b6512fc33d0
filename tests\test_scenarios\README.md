# Grid Operations Test Scenarios

## Tổng quan

Đ<PERSON><PERSON> là tập hợp các test scenarios chi tiết cho các operations chính của Grid System:

1. **Swap Operations** - Ho<PERSON> đổi vị trí cameras
2. **Resize Operations** - Thay đổi kích thước cameras  
3. **Drop Operations** - Kéo thả cameras
4. **Wheel Resize Operations** - Thay đổi kích thước grid bằng Ctrl + Mouse wheel

## Cấu trúc Test Scenarios

Mỗi test case được viết theo format:

```
## Test Case X: Tên Test Case
**Mục tiêu:** <PERSON><PERSON> tả mục tiêu của test

**Precondition:**
- <PERSON><PERSON><PERSON><PERSON> kiện ban đầu
- Trạng thái grid
- Vị trí cameras

**Steps & Expected Result:**
1. **Step 1**
   - Action thực hiện
   - Expected result

2. **Step 2** 
   - Action thực hiện
   - Expected result

3. **Verification**
   - <PERSON><PERSON><PERSON> tra kết quả cuối cùng
   - Signals emitted
   - State changes
```

## <PERSON><PERSON> sách Test Scenarios

### 1. Swap Operations (swap_operations_scenarios.md)

- **Test Case 1:** Swap Hai Camera Thường (1x1)
- **Test Case 2:** Swap Camera Thường với Camera Resize  
- **Test Case 3:** Swap Camera với Vị Trí Trống
- **Test Case 4:** Swap Hai Camera Resize
- **Test Case 5:** Swap Không Thành Công - Conflict
- **Test Case 6:** Swap Với Vị Trí Invalid
- **Test Case 7:** Swap Camera Với Chính Nó
- **Test Case 8:** Swap Với Signal Emission
- **Test Case 9:** Swap Trong Maximize Mode
- **Test Case 10:** Swap Performance với Grid Lớn

**Tổng cộng:** 10 test cases

### 2. Resize Operations (resize_operations_scenarios.md)

- **Test Case 1:** Resize Camera Tăng Width
- **Test Case 2:** Resize Camera Tăng Height
- **Test Case 3:** Resize Camera Tăng Cả Width và Height
- **Test Case 4:** Resize Camera Giảm Size
- **Test Case 5:** Resize Conflict với Camera Khác
- **Test Case 6:** Resize Out of Bounds
- **Test Case 7:** Resize Camera Không Tồn Tại
- **Test Case 8:** Resize Với Auto Resize Enabled
- **Test Case 9:** Resize Với Auto Resize Disabled
- **Test Case 10:** Resize Trong Maximize Mode
- **Test Case 11:** Resize Performance với Camera Lớn
- **Test Case 12:** Resize Với Signal Emission
- **Test Case 13:** Resize Chain Operations

**Tổng cộng:** 13 test cases

### 3. Drop Operations (drop_operations_scenarios.md)

- **Test Case 1:** Drop Camera Đến Vị Trí Trống
- **Test Case 2:** Drop Camera Đến Vị Trí Có Camera (Swap)
- **Test Case 3:** Drop Resized Camera
- **Test Case 4:** Drop Camera Out of Bounds
- **Test Case 5:** Drop Camera Với Conflict
- **Test Case 6:** Drop Camera Đến Chính Vị Trí Của Nó
- **Test Case 7:** Drop Với Auto Resize Enabled
- **Test Case 8:** Drop Với Auto Resize Disabled
- **Test Case 9:** Drop Trong Maximize Mode
- **Test Case 10:** Drop Multi-Selection
- **Test Case 11:** Drop Với Grid Expansion Logic
- **Test Case 12:** Drop Performance với Grid Lớn
- **Test Case 13:** Drop Với Signal Emission
- **Test Case 14:** Drop Chain Operations

**Tổng cộng:** 14 test cases

### 4. Wheel Resize Operations (wheel_resize_scenarios.md)

- **Test Case 1:** Wheel Increase Cơ Bản (2x2 → 3x3)
- **Test Case 2:** Wheel Decrease Cơ Bản (3x3 → 2x2)
- **Test Case 3:** Wheel Increase Đến Max Size
- **Test Case 4:** Wheel Decrease Bị Block Bởi Camera
- **Test Case 5:** Wheel Decrease Đến Base Size
- **Test Case 6:** Wheel Resize Không Có Ctrl
- **Test Case 7:** Wheel Resize Trong Maximize Mode
- **Test Case 8:** Wheel Resize Square Grid Adjustment
- **Test Case 9:** Wheel Resize Với Resized Cameras
- **Test Case 10:** Wheel Resize Auto Resize Disable
- **Test Case 11:** Wheel Resize Position Calculation Complex
- **Test Case 12:** Wheel Resize Performance
- **Test Case 13:** Wheel Resize Signal Emission
- **Test Case 14:** Wheel Resize isSave State
- **Test Case 15:** Wheel Resize Error Handling

**Tổng cộng:** 15 test cases

## Tổng kết

**Tổng số test cases:** 52 test cases

**Coverage areas:**
- ✅ Basic operations (swap, resize, drop, wheel)
- ✅ Edge cases (out of bounds, conflicts, invalid inputs)
- ✅ Complex scenarios (resized cameras, multi-selection)
- ✅ Performance testing (large grids, many cameras)
- ✅ Signal emission verification
- ✅ State management (maximize mode, auto resize)
- ✅ Error handling và recovery

## Cách sử dụng

1. **Đọc scenarios trước khi implement tests**
2. **Implement test code theo từng scenario**
3. **Verify tất cả expected results**
4. **Run performance tests với data lớn**
5. **Check signal emissions và state changes**

## Notes

- Tất cả test cases được thiết kế để cover edge cases và real-world usage
- Performance requirements được specify rõ ràng
- Signal emission patterns được document chi tiết
- Error handling scenarios được include đầy đủ
- Test cases có thể được extend thêm khi cần

## Implementation Priority

**High Priority:**
- Basic operations (Cases 1-3 của mỗi category)
- Error handling và edge cases
- Signal emission verification

**Medium Priority:**  
- Complex scenarios với resized cameras
- Performance testing
- Chain operations

**Low Priority:**
- Maximize mode interactions
- Multi-selection scenarios
- Advanced error recovery

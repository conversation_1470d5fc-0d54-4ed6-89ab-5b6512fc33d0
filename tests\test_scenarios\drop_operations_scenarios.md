# Test Scenarios: Drop Operations

## Test Case 1: Drop Camera Đến Vị Trí <PERSON>ống
**Mục tiêu:** <PERSON><PERSON><PERSON> bảo drop camera đến empty position hoạt động như move

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Position 2 (0,2) trống

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Position 2 = empty

2. **Thực hiện drop**
   - Gọi `dropCamera(0, 2)`
   - Return: `true`

3. **Kiểm tra sau drop**
   - Position 0 = empty
   - Camera A = position 2, size (1,1)
   - `videoPositionChanged(0, 2)` signal emitted

---

## Test Case 2: Drop Camera Đến Vị Trí Có Camera (Swap)
**Mục tiêu:** <PERSON><PERSON><PERSON> bảo drop camera đến occupied position thực hiện swap

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Camera B tại position 4 (1,1) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Camera B = position 4, size (1,1)

2. **Thực hiện drop (swap)**
   - Gọi `dropCamera(0, 4)`
   - Return: `true`

3. **Kiểm tra sau drop**
   - Camera A = position 4, size (1,1)
   - Camera B = position 0, size (1,1)
   - `videoPositionChanged(0, 4)` và `videoPositionChanged(4, 0)` signals emitted

---

## Test Case 3: Drop Resized Camera
**Mục tiêu:** Đảm bảo drop camera resize hoạt động đúng với secondary positions

**Precondition:**
- Grid 4x4
- Camera A tại position 0 (0,0) với size (2,2) - chiếm positions 0,1,4,5
- Positions 10,11,14,15 trống (bottom-right area)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (2,2)
   - Secondary positions: 1, 4, 5
   - Target area (10,11,14,15) = empty

2. **Thực hiện drop resized camera**
   - Gọi `dropCamera(0, 10)`
   - Return: `true`

3. **Kiểm tra sau drop**
   - Positions 0,1,4,5 = empty (cleared)
   - Camera A = position 10, size (2,2)
   - Secondary positions: 11, 14, 15
   - `videoPositionChanged(0, 10)` signal emitted

---

## Test Case 4: Drop Camera Out of Bounds
**Mục tiêu:** Đảm bảo drop fail khi target position out of bounds

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)

2. **Thực hiện drop out of bounds**
   - Gọi `dropCamera(0, 10)` (beyond 3x3 grid)
   - Return: `false`

3. **Thực hiện drop negative position**
   - Gọi `dropCamera(0, -1)`
   - Return: `false`

4. **Kiểm tra sau drop failed**
   - Camera A vẫn ở position 0, size (1,1)
   - Không có signal nào được emit

---

## Test Case 5: Drop Camera Với Conflict
**Mục tiêu:** Đảm bảo drop fail khi resized camera conflict với existing cameras

**Precondition:**
- Grid 4x4
- Camera A tại position 0 (0,0) với size (2,2) - chiếm positions 0,1,4,5
- Camera B tại position 10 (2,2) với size (1,1)
- Camera C tại position 11 (2,3) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (2,2)
   - Camera B = position 10, size (1,1)
   - Camera C = position 11, size (1,1)

2. **Thực hiện drop với conflict**
   - Gọi `dropCamera(0, 10)` (would conflict with Camera C at position 11)
   - Return: `false`

3. **Kiểm tra sau drop failed**
   - Tất cả cameras vẫn ở vị trí cũ
   - Camera A = position 0, size (2,2)
   - Camera B = position 10, size (1,1)
   - Camera C = position 11, size (1,1)

---

## Test Case 6: Drop Camera Đến Chính Vị Trí Của Nó
**Mục tiêu:** Đảm bảo drop camera đến chính vị trí của nó không gây lỗi

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)

2. **Thực hiện drop to same position**
   - Gọi `dropCamera(0, 0)`
   - Return: `true` (success but no change)

3. **Kiểm tra sau drop**
   - Camera A vẫn ở position 0, size (1,1)
   - Không có signal position change

---

## Test Case 7: Drop Với Auto Resize Enabled
**Mục tiêu:** Đảm bảo drop trigger auto resize khi cần thiết

**Precondition:**
- Grid 2x2
- Camera A tại position 0 (0,0) với size (1,1)
- `autoResizeEnabled = true`

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x2
   - Camera A = position 0, size (1,1)
   - `autoResizeEnabled = true`

2. **Thực hiện drop beyond grid**
   - Gọi `dropCameraWithAutoResize(0, 6)` (beyond 2x2 grid)
   - Return: `true`

3. **Kiểm tra sau drop**
   - Grid size expanded to accommodate position 6
   - Camera A = position 6, size (1,1)
   - Position 0 = empty
   - `autoResizeEnabled = false` (disabled after manual operation)

---

## Test Case 8: Drop Với Auto Resize Disabled
**Mục tiêu:** Đảm bảo drop fail khi auto resize disabled và position out of bounds

**Precondition:**
- Grid 2x2
- Camera A tại position 0 (0,0) với size (1,1)
- `autoResizeEnabled = false`

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x2
   - Camera A = position 0, size (1,1)
   - `autoResizeEnabled = false`

2. **Thực hiện drop beyond grid**
   - Gọi `dropCameraWithAutoResize(0, 6)` (beyond 2x2 grid)
   - Return: `false`

3. **Kiểm tra sau drop failed**
   - Grid size vẫn 2x2
   - Camera A vẫn ở position 0, size (1,1)

---

## Test Case 9: Drop Trong Maximize Mode
**Mục tiêu:** Đảm bảo drop hoạt động trong maximize mode

**Precondition:**
- Grid 3x3 trong maximize mode
- Camera A tại position 0 với size (1,1)
- Camera B tại position 4 với size (1,1)
- Grid đang maximize position 0

**Steps & Expected Result:**
1. **Kiểm tra trạng thái maximize**
   - `isMaximized = true`
   - `activeItemPosition = 0`

2. **Thực hiện drop maximized camera**
   - Gọi `dropCamera(0, 4)` (swap with Camera B)
   - Return: `true`

3. **Kiểm tra sau drop**
   - Camera A = position 4, size (1,1)
   - Camera B = position 0, size (1,1)
   - `activeItemPosition = 4` (updated to follow Camera A)

---

## Test Case 10: Drop Multi-Selection
**Mục tiêu:** Đảm bảo drop multiple cameras hoạt động đúng

**Precondition:**
- Grid 4x4
- Camera A tại position 0 với size (1,1) - selected
- Camera B tại position 1 với size (1,1) - selected
- Camera C tại position 2 với size (1,1) - not selected
- Target area positions 8,9 trống

**Steps & Expected Result:**
1. **Kiểm tra selection state**
   - Camera A, B = selected
   - Camera C = not selected

2. **Thực hiện multi-drop**
   - Gọi `dropMultipleSelected(8)` (drop to position 8)
   - Return: `true`

3. **Kiểm tra sau multi-drop**
   - Camera A = position 8, size (1,1)
   - Camera B = position 9, size (1,1)
   - Camera C vẫn ở position 2 (not moved)
   - Positions 0,1 = empty

---

## Test Case 11: Drop Với Grid Expansion Logic
**Mục tiêu:** Đảm bảo drop trigger grid expansion đúng cách

**Precondition:**
- Grid 3x3 đầy (9 cameras)
- Camera A tại position 0 với size (1,1)
- `autoResizeEnabled = false` nhưng có expansion logic

**Steps & Expected Result:**
1. **Kiểm tra grid đầy**
   - All positions 0-8 occupied
   - Grid size = 3x3

2. **Thực hiện drop outside grid**
   - Gọi `dropCameraWithExpansion(0, 12)` (trigger expansion)
   - Return: `true`

3. **Kiểm tra sau expansion**
   - Grid size expanded to accommodate position 12
   - Camera A = position 12, size (1,1)
   - Position 0 = empty

---

## Test Case 12: Drop Performance với Grid Lớn
**Mục tiêu:** Đảm bảo drop performance tốt với grid lớn

**Precondition:**
- Grid 10x10 (100 positions)
- 50 cameras đã được add
- Camera A tại position 0 với size (2,2)

**Steps & Expected Result:**
1. **Measure performance**
   - Record start time

2. **Thực hiện drop across grid**
   - Gọi `dropCamera(0, 96)` (far corner)
   - Return: `true`

3. **Kiểm tra performance**
   - Drop operation < 100ms
   - Camera A = position 96, size (2,2)
   - All secondary positions correctly updated

---

## Test Case 13: Drop Với Signal Emission
**Mục tiêu:** Đảm bảo signals được emit đúng khi drop

**Precondition:**
- Grid 3x3
- Camera A tại position 0 với size (1,1)
- Camera B tại position 4 với size (1,1)
- Signal listeners đã được setup

**Steps & Expected Result:**
1. **Setup signal tracking**
   - Connect to `videoPositionChanged` signal
   - Connect to `cellDimensionsChanged` signal
   - Connect to `gridChanged` signal (if expansion)

2. **Thực hiện drop (swap)**
   - Gọi `dropCamera(0, 4)`
   - Return: `true`

3. **Kiểm tra signals emitted**
   - `videoPositionChanged(0, 4)` - Camera A moved
   - `videoPositionChanged(4, 0)` - Camera B moved
   - `cellDimensionsChanged(0, 1, 1)` - Camera B at new position
   - `cellDimensionsChanged(4, 1, 1)` - Camera A at new position

---

## Test Case 14: Drop Chain Operations
**Mục tiêu:** Đảm bảo multiple drop operations hoạt động đúng

**Precondition:**
- Grid 4x4
- Camera A tại position 0 với size (1,1)
- Camera B tại position 5 với size (1,1)
- Camera C tại position 10 với size (1,1)

**Steps & Expected Result:**
1. **Drop operation 1**
   - Gọi `dropCamera(0, 5)` (A swaps with B)
   - Camera A = position 5, Camera B = position 0

2. **Drop operation 2**
   - Gọi `dropCamera(5, 10)` (A swaps with C)
   - Camera A = position 10, Camera C = position 5

3. **Drop operation 3**
   - Gọi `dropCamera(10, 0)` (A swaps with B)
   - Camera A = position 0, Camera B = position 10

4. **Kiểm tra final state**
   - Camera A = position 0 (back to start)
   - Camera B = position 10
   - Camera C = position 5

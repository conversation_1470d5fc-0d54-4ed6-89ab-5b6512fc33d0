# Test Scenarios: Resize Operations

## Test Case 1: Resize Camera Tăng Width
**<PERSON><PERSON><PERSON> tiêu:** <PERSON><PERSON><PERSON> b<PERSON><PERSON> resize camera từ 1x1 thành 2x1 đúng

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Position 1 (0,1) trống

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Position 1 = empty

2. **Thực hiện resize width**
   - Gọi `resizeCamera(0, 2, 1)` (width=2, height=1)
   - Return: `true`

3. **Kiểm tra sau resize**
   - Camera A = position 0, size (2,1)
   - Position 1 = occupied by Camera A (secondary)
   - `cellDimensionsChanged(0, 2, 1)` signal emitted

---

## Test Case 2: Resize Camera Tăng Height
**M<PERSON><PERSON> tiêu:** <PERSON><PERSON><PERSON> b<PERSON><PERSON> resize camera từ 1x1 thành 1x2 đúng

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Position 3 (1,0) trống

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Position 3 = empty

2. **Thực hiện resize height**
   - Gọi `resizeCamera(0, 1, 2)` (width=1, height=2)
   - Return: `true`

3. **Kiểm tra sau resize**
   - Camera A = position 0, size (1,2)
   - Position 3 = occupied by Camera A (secondary)
   - `cellDimensionsChanged(0, 1, 2)` signal emitted

---

## Test Case 3: Resize Camera Tăng Cả Width và Height
**Mục tiêu:** Đảm bảo resize camera từ 1x1 thành 2x2 đúng

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Positions 1, 3, 4 trống

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Positions 1, 3, 4 = empty

2. **Thực hiện resize both dimensions**
   - Gọi `resizeCamera(0, 2, 2)` (width=2, height=2)
   - Return: `true`

3. **Kiểm tra sau resize**
   - Camera A = position 0, size (2,2)
   - Position 1 = occupied by Camera A (secondary)
   - Position 3 = occupied by Camera A (secondary)
   - Position 4 = occupied by Camera A (secondary)
   - `cellDimensionsChanged(0, 2, 2)` signal emitted

---

## Test Case 4: Resize Camera Giảm Size
**Mục tiêu:** Đảm bảo resize camera từ 2x2 thành 1x1 đúng

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (2,2) - chiếm positions 0,1,3,4

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (2,2)
   - Secondary positions: 1, 3, 4

2. **Thực hiện resize smaller**
   - Gọi `resizeCamera(0, 1, 1)` (width=1, height=1)
   - Return: `true`

3. **Kiểm tra sau resize**
   - Camera A = position 0, size (1,1)
   - Positions 1, 3, 4 = empty (cleared)
   - `cellDimensionsChanged(0, 1, 1)` signal emitted

---

## Test Case 5: Resize Conflict với Camera Khác
**Mục tiêu:** Đảm bảo resize fail khi conflict với camera khác

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Camera B tại position 1 (0,1) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Camera B = position 1, size (1,1)

2. **Thực hiện resize conflict**
   - Gọi `resizeCamera(0, 2, 1)` (would overlap position 1)
   - Return: `false`

3. **Kiểm tra sau resize failed**
   - Camera A vẫn ở position 0, size (1,1)
   - Camera B vẫn ở position 1, size (1,1)
   - Không có signal nào được emit

---

## Test Case 6: Resize Out of Bounds
**Mục tiêu:** Đảm bảo resize fail khi vượt quá grid bounds

**Precondition:**
- Grid 3x3
- Camera A tại position 2 (0,2) với size (1,1) - ở right edge

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 2, size (1,1)

2. **Thực hiện resize beyond right edge**
   - Gọi `resizeCamera(2, 2, 1)` (would go beyond column 2)
   - Return: `false`

3. **Thực hiện resize beyond bottom edge**
   - Camera A move to position 6 (2,0) - bottom edge
   - Gọi `resizeCamera(6, 1, 2)` (would go beyond row 2)
   - Return: `false`

4. **Kiểm tra sau resize failed**
   - Camera A vẫn ở original position với original size

---

## Test Case 7: Resize Camera Không Tồn Tại
**Mục tiêu:** Đảm bảo resize fail với empty position

**Precondition:**
- Grid 3x3
- Position 5 trống

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Position 5 = empty

2. **Thực hiện resize empty position**
   - Gọi `resizeCamera(5, 2, 2)`
   - Return: `false`

3. **Kiểm tra sau resize failed**
   - Position 5 vẫn empty
   - Không có thay đổi nào

---

## Test Case 8: Resize Với Auto Resize Enabled
**Mục tiêu:** Đảm bảo resize hoạt động với auto resize enabled

**Precondition:**
- Grid 2x2
- Camera A tại position 0 (0,0) với size (1,1)
- `autoResizeEnabled = true`

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x2
   - Camera A = position 0, size (1,1)
   - `autoResizeEnabled = true`

2. **Thực hiện resize beyond grid**
   - Gọi `resizeCamera(0, 3, 1)` (would need 3 columns)
   - Return: `true` (auto resize triggered)

3. **Kiểm tra sau resize**
   - Grid size expanded to accommodate resize
   - Camera A = position 0, size (3,1)
   - `autoResizeEnabled = false` (disabled after manual resize)

---

## Test Case 9: Resize Với Auto Resize Disabled
**Mục tiêu:** Đảm bảo resize fail khi auto resize disabled

**Precondition:**
- Grid 2x2
- Camera A tại position 0 (0,0) với size (1,1)
- `autoResizeEnabled = false`

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x2
   - Camera A = position 0, size (1,1)
   - `autoResizeEnabled = false`

2. **Thực hiện resize beyond grid**
   - Gọi `resizeCamera(0, 3, 1)` (would need 3 columns)
   - Return: `false`

3. **Kiểm tra sau resize failed**
   - Grid size vẫn 2x2
   - Camera A vẫn position 0, size (1,1)

---

## Test Case 10: Resize Trong Maximize Mode
**Mục tiêu:** Đảm bảo resize hoạt động trong maximize mode

**Precondition:**
- Grid 3x3 trong maximize mode
- Camera A tại position 0 với size (1,1)
- Grid đang maximize position 0

**Steps & Expected Result:**
1. **Kiểm tra trạng thái maximize**
   - `isMaximized = true`
   - `activeItemPosition = 0`

2. **Thực hiện resize**
   - Gọi `resizeCamera(0, 2, 1)`
   - Return: `true`

3. **Kiểm tra sau resize**
   - Camera A = position 0, size (2,1)
   - `isMaximized = true` (vẫn maximize)
   - `activeItemPosition = 0` (không đổi)

---

## Test Case 11: Resize Performance với Camera Lớn
**Mục tiêu:** Đảm bảo resize performance tốt với camera lớn

**Precondition:**
- Grid 10x10
- Camera A tại position 0 với size (1,1)
- Positions 0-99 available

**Steps & Expected Result:**
1. **Measure performance**
   - Record start time

2. **Thực hiện resize to large size**
   - Gọi `resizeCamera(0, 5, 5)` (25 cells)
   - Return: `true`

3. **Kiểm tra performance**
   - Resize operation < 50ms
   - Camera A = position 0, size (5,5)
   - 24 secondary positions correctly marked

---

## Test Case 12: Resize Với Signal Emission
**Mục tiêu:** Đảm bảo signals được emit đúng khi resize

**Precondition:**
- Grid 3x3
- Camera A tại position 0 với size (1,1)
- Signal listeners đã được setup

**Steps & Expected Result:**
1. **Setup signal tracking**
   - Connect to `cellDimensionsChanged` signal
   - Connect to `autoResizeStatusChanged` signal (if applicable)

2. **Thực hiện resize**
   - Gọi `resizeCamera(0, 2, 2)`
   - Return: `true`

3. **Kiểm tra signals emitted**
   - `cellDimensionsChanged(0, 2, 2)` signal emitted
   - No other unexpected signals

---

## Test Case 13: Resize Chain Operations
**Mục tiêu:** Đảm bảo multiple resize operations hoạt động đúng

**Precondition:**
- Grid 4x4
- Camera A tại position 0 với size (1,1)

**Steps & Expected Result:**
1. **Resize lần 1**
   - Gọi `resizeCamera(0, 2, 1)`
   - Camera A = position 0, size (2,1)

2. **Resize lần 2**
   - Gọi `resizeCamera(0, 2, 2)`
   - Camera A = position 0, size (2,2)

3. **Resize lần 3 (shrink)**
   - Gọi `resizeCamera(0, 1, 1)`
   - Camera A = position 0, size (1,1)

4. **Kiểm tra final state**
   - Camera A = position 0, size (1,1)
   - Tất cả secondary positions đã được cleared

# Test Scenarios: Swap Operations

## Test Case 1: <PERSON>wap Hai Camera Thường (1x1)
**<PERSON><PERSON><PERSON> tiêu:** <PERSON><PERSON><PERSON> bảo swap 2 camera 1x1 đúng vị trí

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Camera B tại position 4 (1,1) với size (1,1)

**Steps & Expected Result:**
1. **<PERSON><PERSON>m tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Camera B = position 4, size (1,1)

2. **Thực hiện swap(A, B)**
   - Gọi `swapCameras(0, 4)`
   - Return: `true`

3. **Kiểm tra sau swap**
   - Camera A = position 4, size (1,1)
   - Camera B = position 0, size (1,1)
   - Không có camera nào khác bị ảnh hưởng

---

## Test Case 2: Swap Camera Thường với Camera Resize
**<PERSON><PERSON><PERSON> tiêu:** <PERSON><PERSON><PERSON> bảo swap camera 1x1 với camera 2x2 đúng vị trí và kích thước

**Precondition:**
- Grid 4x4
- Camera A tại position 0 (0,0) với size (1,1)
- Camera B tại position 5 (1,1) với size (2,2) - chiếm positions 5,6,9,10

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Camera B = position 5, size (2,2), secondary positions: 6,9,10

2. **Thực hiện swap(A, B)**
   - Gọi `swapCameras(0, 5)`
   - Return: `true`

3. **Kiểm tra sau swap**
   - Camera A = position 5, size (2,2), secondary positions: 6,9,10
   - Camera B = position 0, size (1,1)
   - Tất cả secondary positions được update đúng

---

## Test Case 3: Swap Camera với Vị Trí Trống
**Mục tiêu:** Đảm bảo swap camera với empty position hoạt động như move

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Position 2 (0,2) trống

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)
   - Position 2 = empty

2. **Thực hiện swap(A, empty)**
   - Gọi `swapCameras(0, 2)`
   - Return: `true`

3. **Kiểm tra sau swap**
   - Position 0 = empty
   - Camera A = position 2, size (1,1)

---

## Test Case 4: Swap Hai Camera Resize
**Mục tiêu:** Đảm bảo swap 2 camera resize đúng vị trí và kích thước

**Precondition:**
- Grid 5x5
- Camera A tại position 0 (0,0) với size (2,1) - chiếm positions 0,1
- Camera B tại position 7 (1,2) với size (1,2) - chiếm positions 7,12

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (2,1), secondary: 1
   - Camera B = position 7, size (1,2), secondary: 12

2. **Thực hiện swap(A, B)**
   - Gọi `swapCameras(0, 7)`
   - Return: `true`

3. **Kiểm tra sau swap**
   - Camera A = position 7, size (1,2), secondary: 12
   - Camera B = position 0, size (2,1), secondary: 1
   - Tất cả secondary positions được clear và set lại đúng

---

## Test Case 5: Swap Không Thành Công - Conflict
**Mục tiêu:** Đảm bảo swap fail khi có conflict

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (2,2) - chiếm positions 0,1,3,4
- Camera B tại position 2 (0,2) với size (1,1)
- Camera C tại position 5 (1,2) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (2,2)
   - Camera B = position 2, size (1,1)
   - Camera C = position 5, size (1,1)

2. **Thực hiện swap(A, B) - sẽ conflict với C**
   - Gọi `swapCameras(0, 2)`
   - Return: `false`

3. **Kiểm tra sau swap failed**
   - Tất cả cameras vẫn ở vị trí cũ
   - Camera A = position 0, size (2,2)
   - Camera B = position 2, size (1,1)
   - Camera C = position 5, size (1,1)

---

## Test Case 6: Swap Với Vị Trí Invalid
**Mục tiêu:** Đảm bảo swap fail với invalid positions

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)

2. **Thực hiện swap với position out of bounds**
   - Gọi `swapCameras(0, 10)` (beyond 3x3 grid)
   - Return: `false`

3. **Thực hiện swap với negative position**
   - Gọi `swapCameras(-1, 0)`
   - Return: `false`

4. **Kiểm tra sau swap failed**
   - Camera A vẫn ở position 0, size (1,1)

---

## Test Case 7: Swap Camera Với Chính Nó
**Mục tiêu:** Đảm bảo swap camera với chính nó không gây lỗi

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra vị trí ban đầu**
   - Camera A = position 0, size (1,1)

2. **Thực hiện swap(A, A)**
   - Gọi `swapCameras(0, 0)`
   - Return: `true` (success but no change)

3. **Kiểm tra sau swap**
   - Camera A vẫn ở position 0, size (1,1)

---

## Test Case 8: Swap Với Signal Emission
**Mục tiêu:** Đảm bảo signals được emit đúng khi swap

**Precondition:**
- Grid 3x3
- Camera A tại position 0 với size (1,1)
- Camera B tại position 4 với size (1,1)
- Signal listeners đã được setup

**Steps & Expected Result:**
1. **Setup signal tracking**
   - Connect to `videoPositionChanged` signal
   - Connect to `cellDimensionsChanged` signal

2. **Thực hiện swap(A, B)**
   - Gọi `swapCameras(0, 4)`
   - Return: `true`

3. **Kiểm tra signals emitted**
   - `videoPositionChanged(0, 4)` - Camera A moved
   - `videoPositionChanged(4, 0)` - Camera B moved
   - `cellDimensionsChanged(0, 1, 1)` - Camera B dimensions at new position
   - `cellDimensionsChanged(4, 1, 1)` - Camera A dimensions at new position

---

## Test Case 9: Swap Trong Maximize Mode
**Mục tiêu:** Đảm bảo swap hoạt động trong maximize mode

**Precondition:**
- Grid 3x3 trong maximize mode
- Camera A tại position 0 với size (1,1)
- Camera B tại position 4 với size (1,1)
- Grid đang maximize position 0

**Steps & Expected Result:**
1. **Kiểm tra trạng thái maximize**
   - `isMaximized = true`
   - `activeItemPosition = 0`

2. **Thực hiện swap(A, B)**
   - Gọi `swapCameras(0, 4)`
   - Return: `true`

3. **Kiểm tra sau swap**
   - Camera A = position 4, size (1,1)
   - Camera B = position 0, size (1,1)
   - `activeItemPosition = 4` (updated to follow Camera A)

---

## Test Case 10: Swap Performance với Grid Lớn
**Mục tiêu:** Đảm bảo swap performance tốt với grid lớn

**Precondition:**
- Grid 10x10 (100 positions)
- 50 cameras đã được add vào grid
- Camera A tại position 0 với size (1,1)
- Camera B tại position 99 với size (1,1)

**Steps & Expected Result:**
1. **Measure performance**
   - Record start time

2. **Thực hiện swap(A, B)**
   - Gọi `swapCameras(0, 99)`
   - Return: `true`

3. **Kiểm tra performance**
   - Swap operation < 100ms
   - Camera A = position 99, size (1,1)
   - Camera B = position 0, size (1,1)
   - Tất cả cameras khác không bị ảnh hưởng

# Test Scenarios: Wheel Resize Operations

## Test Case 1: Wheel Increase Cơ Bản (2x2 → 3x3)
**M<PERSON><PERSON> tiêu:** <PERSON><PERSON><PERSON> bảo Ctrl + wheel up tăng grid size đúng

**Precondition:**
- Grid 2x2
- Camera A tại position 0 (0,0) với size (1,1)
- Camera B tại position 1 (0,1) với size (1,1)
- Camera C tại position 2 (1,0) với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x2
   - Camera A = position 0, Camera B = position 1, Camera C = position 2

2. **Thực hiện wheel increase**
   - Gọi `handleWheelResize(true, true)` (increase=true, with_ctrl=true)
   - Return: `true`

3. **Kiểm tra sau wheel resize**
   - Grid size = 3x3
   - Camera A = position 0 (0,0) - unchanged
   - Camera B = position 1 (0,1) - unchanged
   - Camera C = position 3 (1,0) - moved from 2 to 3
   - `gridChanged(3, 3)` signal emitted
   - `videoPositionChanged(2, 3)` signal emitted

---

## Test Case 2: Wheel Decrease Cơ Bản (3x3 → 2x2)
**Mục tiêu:** Đảm bảo Ctrl + wheel down giảm grid size đúng

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Camera B tại position 1 (0,1) với size (1,1)
- Camera C tại position 3 (1,0) với size (1,1)
- Không có camera ở last row/column

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 3x3
   - Camera A = position 0, Camera B = position 1, Camera C = position 3

2. **Thực hiện wheel decrease**
   - Gọi `handleWheelResize(false, true)` (increase=false, with_ctrl=true)
   - Return: `true`

3. **Kiểm tra sau wheel resize**
   - Grid size = 2x2
   - Camera A = position 0 (0,0) - unchanged
   - Camera B = position 1 (0,1) - unchanged
   - Camera C = position 2 (1,0) - moved from 3 to 2
   - `gridChanged(2, 2)` signal emitted
   - `videoPositionChanged(3, 2)` signal emitted

---

## Test Case 3: Wheel Increase Đến Max Size
**Mục tiêu:** Đảm bảo wheel increase fail khi đã đạt max size

**Precondition:**
- Grid 12x12 (max size)
- Một số cameras trong grid

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 12x12 (maxColumns = 12)

2. **Thực hiện wheel increase at max**
   - Gọi `handleWheelResize(true, true)`
   - Return: `false`

3. **Kiểm tra sau wheel resize failed**
   - Grid size vẫn 12x12
   - Tất cả cameras vẫn ở vị trí cũ
   - Không có signal nào được emit

---

## Test Case 4: Wheel Decrease Bị Block Bởi Camera
**Mục tiêu:** Đảm bảo wheel decrease fail khi có camera ở last row/column

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (1,1)
- Camera B tại position 8 (2,2) với size (1,1) - last row & column

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 3x3
   - Camera B ở last row (row 2) và last column (col 2)

2. **Thực hiện wheel decrease with blocking camera**
   - Gọi `handleWheelResize(false, true)`
   - Return: `false`

3. **Kiểm tra sau wheel resize failed**
   - Grid size vẫn 3x3
   - Tất cả cameras vẫn ở vị trí cũ
   - Không có signal nào được emit

---

## Test Case 5: Wheel Decrease Đến Base Size
**Mục tiêu:** Đảm bảo wheel decrease fail khi đã ở base size

**Precondition:**
- Grid 1x1 (base size)
- Camera A tại position 0 với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 1x1 (baseColumns = 1)

2. **Thực hiện wheel decrease at base**
   - Gọi `handleWheelResize(false, true)`
   - Return: `false`

3. **Kiểm tra sau wheel resize failed**
   - Grid size vẫn 1x1
   - Camera A vẫn ở position 0
   - Không có signal nào được emit

---

## Test Case 6: Wheel Resize Không Có Ctrl
**Mục tiêu:** Đảm bảo wheel resize fail khi không có Ctrl modifier

**Precondition:**
- Grid 2x2
- Camera A tại position 0 với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x2

2. **Thực hiện wheel without Ctrl**
   - Gọi `handleWheelResize(true, false)` (with_ctrl=false)
   - Return: `false`

3. **Kiểm tra sau wheel resize failed**
   - Grid size vẫn 2x2
   - Camera A vẫn ở position 0
   - Không có signal nào được emit

---

## Test Case 7: Wheel Resize Trong Maximize Mode
**Mục tiêu:** Đảm bảo wheel resize fail khi grid đang maximize

**Precondition:**
- Grid 2x2 trong maximize mode
- Camera A tại position 0 với size (1,1)
- Grid đang maximize position 0

**Steps & Expected Result:**
1. **Kiểm tra trạng thái maximize**
   - `isMaximized = true`
   - `activeItemPosition = 0`

2. **Thực hiện wheel resize in maximize**
   - Gọi `handleWheelResize(true, true)`
   - Return: `false`

3. **Kiểm tra sau wheel resize failed**
   - Grid size vẫn 2x2
   - `isMaximized = true` (vẫn maximize)
   - Không có signal nào được emit

---

## Test Case 8: Wheel Resize Square Grid Adjustment
**Mục tiêu:** Đảm bảo grid được adjust thành square sau wheel resize

**Precondition:**
- Grid 2x3 (non-square)
- Camera A tại position 0 với size (1,1)

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x3 (columns=2, rows=3)

2. **Thực hiện wheel increase**
   - Gọi `handleWheelResize(true, true)`
   - newColumns = 3, newRows = 4
   - Square adjustment: max(3, 4) = 4
   - Return: `true`

3. **Kiểm tra sau square adjustment**
   - Grid size = 4x4 (square)
   - Camera positions adjusted accordingly
   - `gridChanged(4, 4)` signal emitted

---

## Test Case 9: Wheel Resize Với Resized Cameras
**Mục tiêu:** Đảm bảo wheel resize hoạt động đúng với resized cameras

**Precondition:**
- Grid 3x3
- Camera A tại position 0 (0,0) với size (2,1) - chiếm positions 0,1
- Camera B tại position 3 (1,0) với size (1,2) - chiếm positions 3,6

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 3x3
   - Camera A = position 0, size (2,1), secondary: 1
   - Camera B = position 3, size (1,2), secondary: 6

2. **Thực hiện wheel increase**
   - Gọi `handleWheelResize(true, true)`
   - Return: `true`

3. **Kiểm tra sau wheel resize**
   - Grid size = 4x4
   - Camera A = position 0, size (2,1), secondary: 1 (unchanged)
   - Camera B = position 4, size (1,2), secondary: 8 (moved from 3→4, 6→8)
   - Position calculations correct for resized cameras

---

## Test Case 10: Wheel Resize Auto Resize Disable
**Mục tiêu:** Đảm bảo wheel resize disable auto resize sau khi thực hiện

**Precondition:**
- Grid 2x2
- Camera A tại position 0 với size (1,1)
- `autoResizeEnabled = true`

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - Grid size = 2x2
   - `autoResizeEnabled = true`

2. **Thực hiện wheel resize**
   - Gọi `handleWheelResize(true, true)`
   - Return: `true`

3. **Kiểm tra sau wheel resize**
   - Grid size = 3x3
   - `autoResizeEnabled = false` (disabled after manual resize)
   - `autoResizeStatusChanged(false, "disabled_by_user_action")` signal emitted

---

## Test Case 11: Wheel Resize Position Calculation Complex
**Mục tiêu:** Đảm bảo position calculation đúng với grid phức tạp

**Precondition:**
- Grid 4x4
- Cameras tại positions: 0,1,2,4,5,6,8,9,10,12,13,14

**Steps & Expected Result:**
1. **Kiểm tra mapping 4x4 → 5x5**
   - Position 0 (0,0) → 0 (0,0)
   - Position 1 (0,1) → 1 (0,1)
   - Position 2 (0,2) → 2 (0,2)
   - Position 4 (1,0) → 5 (1,0)
   - Position 5 (1,1) → 6 (1,1)
   - Position 6 (1,2) → 7 (1,2)
   - Position 8 (2,0) → 10 (2,0)
   - Position 9 (2,1) → 11 (2,1)
   - Position 10 (2,2) → 12 (2,2)
   - Position 12 (3,0) → 15 (3,0)
   - Position 13 (3,1) → 16 (3,1)
   - Position 14 (3,2) → 17 (3,2)

2. **Thực hiện wheel increase**
   - Gọi `handleWheelResize(true, true)`
   - Return: `true`

3. **Kiểm tra position mapping**
   - Tất cả cameras moved to correct new positions
   - Relative positions (row,col) maintained
   - All `videoPositionChanged` signals emitted correctly

---

## Test Case 12: Wheel Resize Performance
**Mục tiêu:** Đảm bảo wheel resize performance tốt với grid lớn

**Precondition:**
- Grid 8x8 (64 positions)
- 40 cameras distributed across grid
- Mix of regular và resized cameras

**Steps & Expected Result:**
1. **Measure performance**
   - Record start time

2. **Thực hiện wheel increase**
   - Gọi `handleWheelResize(true, true)`
   - Return: `true`

3. **Kiểm tra performance**
   - Wheel resize operation < 200ms
   - Grid size = 9x9
   - All 40 cameras moved to correct positions
   - All signals emitted correctly

---

## Test Case 13: Wheel Resize Signal Emission
**Mục tiêu:** Đảm bảo signals được emit đúng thứ tự và đầy đủ

**Precondition:**
- Grid 2x2
- Camera A tại position 0, Camera B tại position 2
- Signal listeners đã được setup

**Steps & Expected Result:**
1. **Setup signal tracking**
   - Connect to `gridChanged` signal
   - Connect to `videoPositionChanged` signal
   - Connect to `cellDimensionsChanged` signal
   - Connect to `columnsChanged` và `rowsChanged` signals

2. **Thực hiện wheel increase**
   - Gọi `handleWheelResize(true, true)`
   - Return: `true`

3. **Kiểm tra signal order và content**
   - `columnsChanged(3)` emitted
   - `rowsChanged(3)` emitted
   - `videoPositionChanged(2, 3)` emitted (Camera B moved)
   - `cellDimensionsChanged(3, 1, 1)` emitted (Camera B at new position)
   - `gridChanged(3, 3)` emitted last

---

## Test Case 14: Wheel Resize isSave State
**Mục tiêu:** Đảm bảo isSave state được handle đúng trong wheel resize

**Precondition:**
- Grid 2x2
- Camera A tại position 0
- `isSave = true`

**Steps & Expected Result:**
1. **Kiểm tra trạng thái ban đầu**
   - `isSave = true`

2. **Thực hiện wheel resize**
   - Gọi `handleWheelResize(true, true)`
   - During operation: `isSave = false`
   - Return: `true`

3. **Kiểm tra sau wheel resize**
   - `isSave = true` (restored after operation)
   - Grid changes applied successfully

---

## Test Case 15: Wheel Resize Error Handling
**Mục tiêu:** Đảm bảo error handling đúng khi có exception

**Precondition:**
- Grid 2x2
- Camera A tại position 0
- Mock exception trong position calculation

**Steps & Expected Result:**
1. **Setup error condition**
   - Mock `_calculateNewPositionsForResize` to throw exception

2. **Thực hiện wheel resize with error**
   - Gọi `handleWheelResize(true, true)`
   - Return: `false`

3. **Kiểm tra error handling**
   - Grid size vẫn 2x2 (unchanged)
   - `isSave` restored to original value
   - Error logged properly
   - No partial state changes
